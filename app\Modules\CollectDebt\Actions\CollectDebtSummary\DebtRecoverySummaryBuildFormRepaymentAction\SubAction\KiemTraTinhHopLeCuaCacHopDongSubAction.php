<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryBuildFormRepaymentAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;

class KiemTraTinhHopLeCuaCacHopDongSubAction {
	public function run(array $listHopDongV1=[]) {
		$contractCodes = collect($listHopDongV1['data'])->pluck('contract_code')->toArray();
		
		$collectDebtSummaries = CollectDebtSummary::query()
																							->whereIn('contract_code', $contractCodes)
																							->get();
		
		$hopDongHopLe = $collectDebtSummaries->filter(function (CollectDebtSummary $summary) {
			return !$summary->isHopDongDaTatToan();
		})->pluck('contract_code')->values()->toArray();


		$listHopDongV1['data'] = collect($listHopDongV1['data'])->filter(function ($item) use ($hopDongHopLe) {
			return in_array($item['contract_code'], $hopDongHopLe);
		})->values()->toArray();

		return $listHopDongV1;
	}
}