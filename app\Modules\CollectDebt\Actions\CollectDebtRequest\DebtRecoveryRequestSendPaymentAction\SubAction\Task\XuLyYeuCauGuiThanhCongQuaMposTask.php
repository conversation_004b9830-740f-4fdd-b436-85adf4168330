<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task;

use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Utils\CommonVar;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class XuLyYeuCauGuiThanhCongQuaMposTask
{
  /**
   * Bối cảnh: y/c hiện tại gửi phát thành công luôn
   *
   * @param CollectDebtRequest $collectDebtRequest
   * @param array $sendDebtResult
   * array:6 [▼
     * "api_request_id" => null
     * "success" => true
     * "checksum" => "4c0d9399f1fb986c9ccfcbd27a865abb"
     * "result_code" => 200
     * "message" => "[] - Request success"
     * "data" => array:3 [▼
     * "error_code" => "00"
     * "description" => "SUCCESS"
     * "partner_transaction_id" => "39370246"
     * ]
   * ]
   *
   * @return CollectDebtRequest
   */
  public function run(CollectDebtRequest $collectDebtRequest, array $sendDebtResult): CollectDebtRequest
  {
    $collectDebtRequest->partner_transaction_id = $sendDebtResult['data']['partner_transaction_id'];
    $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_GUI;
    $collectDebtRequest->other_data = $collectDebtRequest->putOtherData([
      'type' => 'SENDER',
      'time_modified' => now()->timestamp,
      'data' => $sendDebtResult,
      'note' => 'Gửi lệnh LẦN ĐẦU sang MPOS thành công'
    ]);

    $collectDebtRequest->sended_by = Helper::getCronJobUser();
    $collectDebtRequest->time_sended = time();
    $collectDebtRequest->time_checked = time(); // Thêm cái này để 30' sau hẵng check
    $collectDebtRequest->status = CollectDebtEnum::REQUEST_STT_DA_DUYET;

    if (empty($collectDebtRequest->time_approved)) {
      $collectDebtRequest->time_approved = time();
      $collectDebtRequest->approved_by = Helper::getCronJobUser();
    }

    $collectDebtRequest->save();
    return $collectDebtRequest;
  }
} // End class