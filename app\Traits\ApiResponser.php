<?php

namespace App\Traits;

use App\Lib\Logs;
use App\Utils\Result;
use Illuminate\Support\Str;

trait ApiResponser
{
	public function successResponse($data = [], $request, $code = Result::REQUEST_SUCCESS, $message = null)
	{
		$tokenTime = $request->json('time_request');
		$Version = $request->json('version');
		$ChannelCode = $request->json('channel_code');
		$dataCheckSum = json_encode($data, JSON_UNESCAPED_UNICODE);
		$secretKey = env('CHECKSUMKEY');
		$checksum = md5($dataCheckSum . $Version . $ChannelCode . $tokenTime . $secretKey);

		$checkArray = false;
		if (is_array($data) || is_object($data)) {
			if (is_object($data)) {
				$data = (array)$data;
			}
			$checkArray = $this->replace_null_with_empty_string($data);
		}

		$apiRequestUUID = (string) Str::uuid();

		$apiRequestId = request()->input('api_request_id', $apiRequestUUID);
		$message = $message ?? Result::$resultMessage[$code] ?? '';
		$message = sprintf('[%s] - %s', $apiRequestId, $message);

		$response = [
			'api_request_id' => $apiRequestId,
			'success' => true,
			'checksum' => $checksum,
			'result_code' => $code,
			'message' => $message,
			'data' => ($checkArray) ?: (object) [],
		];

		return response()->json($response, 200, [], JSON_UNESCAPED_UNICODE);
	}

	public function errorResponse($code = Result::ERROR, $message = null, $errors = [])
	{
		$resultMessages = Result::$resultMessage;

		if (!is_null($errors)) {
			(new Logs())->writeFileLog($errors);
		} else {
			if (!array_key_exists($code, $resultMessages)) {
				(new Logs())->writeFileLog("Error: " . $message);
			} else {
				(new Logs())->writeFileLog("Error: " . $resultMessages[$code]);
			}
		}

		// if (!array_key_exists($code, $resultMessages)) {
		//     $code = Result::REQUEST_FALSE;
		// }

		$apiRequestUUID = (string) Str::uuid();

		$apiRequestId = request()->input('api_request_id', $apiRequestUUID);
		$message = !empty($message) ? $message : 'Đã có lỗi xảy ra, bạn vui lòng đợi kỹ thuật hỗ trợ xử lý.';
		//$message = sprintf('[%s] - %s', $apiRequestId, $message);

		$response = [
			'api_request_id' => $apiRequestId,
			'success' => false,
			'checksum' => '',
			'result_code' => $code,
			'message' => $message,
		];

		if (empty($errors)) {
			$errors['errors'] = [$message ?? [$message] ?? 'Error'];
		}

		$response['data'] = $errors;
		return response()->json($response, 200, [], JSON_UNESCAPED_UNICODE);
	}

	public  function replace_null_with_empty_string($array)
	{
		foreach ($array as $key => $value) {
			if (is_object($value)) {
				$value = $this->castObject($value);
			} elseif (is_array($value)) {
				$array[$key] = $this->replace_null_with_empty_string($value);
			} else {
				$array[$key]  = (string) $value;
			}
		}

		return $array;
	}

	public function castObject($object)
	{
		foreach ($object as $k => $val) {
			if (is_object($val)) {
				$object->$k = $this->castObject($val);
			}

			if (is_array($val)) {
				$object->$k = $this->replace_null_with_empty_string($val);
			}

			if (!is_object($val) && !is_array($val)) {
				$object->$k = (string) $val;
			}
		}

		return $object;
	}
} // End class
