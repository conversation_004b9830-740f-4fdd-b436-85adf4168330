<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task;

use App\Lib\Helper;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;

class FillPlanTask
{
  private array $idLichThuSeTaoYeuCau = [];

  public $soTienTrichTay = 0;

  /**
   * Phân bổ số tiền mong muốn trích thành công vào các lịch tương ứng
   *
   * @param Collection $danhSachToanBoLichThu (Danh sách các lịch thu hiện tại và tương lai)
   * @param DebtRecoveryRequestCreateManualRequest $request
   * @return void
   */
  public function run(Collection $danhSachToanBoLichThu, DebtRecoveryRequestCreateManualRequest $request)
  {
    $soTienMuonGhiNhanTrichTay = $request->json('data.amount_request');
    $this->soTienTrichTay = $request->json('data.amount_request');


    foreach ($danhSachToanBoLichThu as &$plan) {
      if ($soTienMuonGhiNhanTrichTay <= 0) {
        break;
      }

      $this->idLichThuSeTaoYeuCau[] = $plan->id;

      $soTienCoTheThanhToan = min($soTienMuonGhiNhanTrichTay, $plan->getSoTienConPhaiThanhToan());
  
      $soTienMuonGhiNhanTrichTay -= $soTienCoTheThanhToan;
    }

    $danhSachLichThuDuocTaoYeuCau = $danhSachToanBoLichThu->filter(function (CollectDebtSchedule $plan) {
      return in_array($plan->id, $this->idLichThuSeTaoYeuCau);
    });
    
    $tongSoTienPhaiThuCuaCacLich = $danhSachLichThuDuocTaoYeuCau->sum(function (CollectDebtSchedule $plan) {
      return $plan->getSoTienConPhaiThanhToan();
    });
    

    $soTienMangDiThanhToan = 0;
    
    if ($request->isYeuCauKhongCanTrichQuaDoiTac()) {
      $soTienMangDiThanhToan = min($this->soTienTrichTay, $tongSoTienPhaiThuCuaCacLich);
    }

    return [
      'plan_ids' => implode(',', $this->idLichThuSeTaoYeuCau),
      'plan_data' => Helper::getPlanCompact($danhSachLichThuDuocTaoYeuCau),
      'total_amount_receiver' => $soTienMangDiThanhToan,
      'total_amount_request' => $tongSoTienPhaiThuCuaCacLich
    ];
  }
}
