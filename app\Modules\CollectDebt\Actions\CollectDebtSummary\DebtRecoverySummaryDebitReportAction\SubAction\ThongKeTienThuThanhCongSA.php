<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;

class ThongKeTienThuThanhCongSA
{
	/**
	 * Chứa thông tin Tổng tiền nhận và TT sau giảm trừ
	 *
	 * @param CollectDebtSummary $collectDebtSummary
	 * @return void
	 */
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$currency = $collectDebtSummary->getCurrency();

		$returnData = [
			[
				'name' => 'T. nhận',
				'value' => $collectDebtSummary->total_amount_receiver,
				'currency' => $currency,
			],

			[
				'name' => 'TT sau giảm trừ',
				'value' => $collectDebtSummary->total_amount_paid + $collectDebtSummary->total_fee_paid,
				'currency' => $currency,
			]
		];

		return $returnData;
	}
}
