<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayCoChungTuSubAction;

use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayCoChungTuSubAction\TrichMotPhanCoChungTuSubAction;

class TaoYeuCauTrichTayCoChungTuSubAction
{
  public string $paymentMethodCode;

  public function __construct(string $paymentMethodCode)
  {
    $this->paymentMethodCode = $paymentMethodCode;
  }

  public function run(
    CollectDebtShare $collectDebtShare,
    Collection $danhSachToanBoLichThu,
    DebtRecoveryRequestCreateManualRequest $request
  ) {
    return app(TrichMotPhanCoChungTuSubAction::class, ['paymentMethodCode' => $this->paymentMethodCode])->run(
      $collectDebtShare,
      $danhSachToanBoLichThu,
      $request
    );
  }
} // End class