<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TaoLichThuVetSoPhiConLaiTrongNgayST
{
  public function run(CollectDebtSchedule $lichDangHachToan, float $soPhiConPhaiThuTiep = 0)
  {
		$paramThuPhi = [];

    // Trong này other_data phải thể hiện rõ, số tiền phí cần thu tiếp là phí nào
    if ($lichDangHachToan->isLichThuPhiChamKy()) {
      $paramThuPhi[] = [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_CHAM_KY,
          'fee_config' => [],
          'request_created_channel' => '',
        ],
        'note' => 'Thu tiếp phí chậm kỳ trong ngày (inside time)'
      ];
    }

    if ($lichDangHachToan->isLichThuPhiQuaHan()) {
      $paramThuPhi[] = [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_QUA_HAN,
          'fee_config' => [],
          'request_created_channel' => '',
        ],
        'note' => 'Thu tiếp phí quá hạn trong ngày (inside time)'
      ];
    }


    if ($soPhiConPhaiThuTiep > 0) {
      $scheduleParam = [
        'profile_id'           => $lichDangHachToan->profile_id,
        'contract_code'        => $lichDangHachToan->contract_code,
        'contract_type'        => $lichDangHachToan->contract_type,
        'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
        'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
        'debit_begin'          => $soPhiConPhaiThuTiep,
        'debit_end'            => 0,
        'rundate'              => $lichDangHachToan->rundate,
        'time_start'           => $lichDangHachToan->time_start_as_date->copy()->setTime(date('H'), date('i'))->timestamp,
        'time_end'             => $lichDangHachToan->time_end,
        'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
        'request_amount_debit' => $soPhiConPhaiThuTiep,
        'success_amount_debit' => 0,
        'other_data'           => json_encode($paramThuPhi),
        'description'          => $lichDangHachToan->collectDebtSchedule,
        'is_settlement'        => $lichDangHachToan->is_settlement,
        'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
        'created_by'           => $lichDangHachToan->created_by,
        'time_created'         => time(),
        'cycle_number'         => $lichDangHachToan->cycle_number,
        'master_id'         => $lichDangHachToan->master_id,
      ];

      return CollectDebtSchedule::forceCreate($scheduleParam);
    }
  }
} // End class