<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiQuaHanAction;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtDigitalTmp;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;

class DigitalNotiQuaHanAction
{
	public array $listHopDongQuaHan = [];

	public function run()
	{
		$listSummary = CollectDebtSummary::query()
																		 ->with('collectDebtShare:contract_code,profile_data')
																		 ->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
																		 ->where('is_send_noti_overdue', CollectDebtEnum::SUMMARY_CO_GUI_NOTI_QUA_HAN)
																		 ->get();
		
		if ($listSummary->isEmpty()) {
			return;
		}

		$listSummary->each(function (CollectDebtSummary $collectDebtSummary) {
			$title = sprintf('Khoản ứng đã quá hạn');
			$body = sprintf('Hợp đồng %s của bạn đang quá hạn thanh toán, vui lòng thanh toán để tránh phát sinh thêm phí phạt.', $collectDebtSummary->contract_code);
			
			$content = view('trichno.noti.qua-han', [
				'collectDebtSummary' => $collectDebtSummary,
				'noGocConPhaiTra' => Helper::numberFormat($collectDebtSummary->getDuNoGocPhaiTra()),
				'phiConPhaiTra' => Helper::numberFormat($collectDebtSummary->getPhiConPhaiTra())
			])->render();

			$notifyData = CollectDebtDigitalNoti::buildNotiData(
				$title,
				$body,
				$content,
				'DIGITAL_QUAHANTHANHTOAN',
				$collectDebtSummary->collectDebtShare->getMposMcId(),
				'Hợp đồng ứng đã quá hạn'
			);

			$digitalNoti = CollectDebtDigitalNoti::query()->forceCreate([
				'contract_code' => $collectDebtSummary->contract_code,
				'type' => 'over_due',
				'object_model' => CollectDebtShare::class,
				'object_id' => $collectDebtSummary->id,
				'digital_request' => json_encode($notifyData),
				'status' => CollectDebtDigitalNoti::STT_DA_BUILD_PARAM,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp
			]);

			if (!$digitalNoti) {
				mylog(['Loi khong tao duoc noti qua han cho hd' => $collectDebtSummary->contract_code]);
				return $digitalNoti;
			}

			$this->listHopDongQuaHan[] = $collectDebtSummary->contract_code;
			return $digitalNoti;
		});

		if (!empty($this->listHopDongQuaHan)) {
			CollectDebtSummary::query()
											  ->where('is_send_noti_overdue', CollectDebtEnum::SUMMARY_CO_GUI_NOTI_QUA_HAN)
											  ->whereIn('contract_code', $this->listHopDongQuaHan)
											  ->update(['is_send_noti_overdue' => CollectDebtEnum::SUMMARY_KHONG_GUI_NOTI_QUA_HAN]);
		}

		return $this->listHopDongQuaHan;
	}
} // End class
