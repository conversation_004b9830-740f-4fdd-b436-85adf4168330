<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction;

use App\Lib\Helper;
use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;

class DuyetYeuCauTrichTayQuaDoiTacSA
{
  public function run(CollectDebtRequest $collectDebtRequest, $request): CollectDebtRequest
  {
    $rq = app(SendRequestViaMposSubAction::class)->run($collectDebtRequest);

    throw_if(!$rq->isSentPayment(), new Exception('Không thể gửi yêu cầu trích ngay qua đối tác MPOS'));

    $latestOtherData = $collectDebtRequest->putOtherData([
      'type' => 'APPROVED',
      'time_modified' => time(),
      'note' => $request->json('data.description', 'Duyệt yc trích không có chứng từ'),
      'data' => [
        'user' => $request->json('data.user_request_id')
      ]
    ]);

    $rq->forceFill([
      'status' => CollectDebtEnum::REQUEST_STT_DA_DUYET,
      'status_payment' => CollectDebtEnum::REQUEST_STT_PM_DA_GUI,
      'completed_by' => $request->json('data.user_request_id'),
      'time_completed' => time(),
      'approved_by'    => $request->json('data.user_request_id'),
      'time_approved' => time(),
      'other_data' => $latestOtherData
    ])->update();

    $rq->refresh();

		// Cap nhat lich la dang xu ly
		try {
			$planIds = $collectDebtRequest->getPlanIds();

			$updatedIsProcesVeDangXuLy = CollectDebtSchedule::query()
																											->where('contract_code', $collectDebtRequest->contract_code)
																											->whereIn('id', $planIds)
																											->where('status', '!=', CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH)
																											->update([
																												'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
																											]);
			if (!$updatedIsProcesVeDangXuLy) {
				// chi duoc ghi log, ko dc throw loi
				mylog([
					'[LOI]' => 'Cap nhat trang thai lich trich ngay ve DANG XU LY that bai',
					'chi tiet' => $updatedIsProcesVeDangXuLy
				]);
			}
		}catch(\Throwable $th) {
			mylog(['[Loi]' => Helper::traceError($th)]);
		}
		

    $rq->__apiMessage = 'Đã duyệt và gửi yêu cầu sang hệ thống đối tác thành công';
    return $rq;
  }
}