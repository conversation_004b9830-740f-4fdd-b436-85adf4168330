<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentCreateAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentCreateRequest;

class DebtRecoveryRequestAdjustmentCreateAction
{
  public function run(DebtRecoveryRequestAdjustmentCreateRequest $request): CollectDebtRequestAdjustment
  {
    $params = $request->json('data');
    $collectDebtRequestAdjustment = CollectDebtRequestAdjustment::query()->firstOrCreate([
      'reference_id' => $params['reference_id'],
    ], $params);

    if ($collectDebtRequestAdjustment->wasRecentlyCreated) {
      $collectDebtRequestAdjustment->other_data = $collectDebtRequestAdjustment->putRAOtherData([
        'type' => 'RA_CREATED',
        'data' => $collectDebtRequestAdjustment->makeHidden(['other_data', 'reference_data']),
        'time_modified' => time(),
        'note' => !empty($request->json('data.description')) ? $request->json('data.description') : 'Ghi nhận đối tác báo trích nợ không đủ tiền yc trích ngay'
      ]);
    }

		if ($collectDebtRequestAdjustment->status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_MOI_TAO) {
			$collectDebtRequestAdjustment->status = CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_2;
		}

		$collectDebtRequestAdjustment->save();
    return $collectDebtRequestAdjustment;
  }
}
