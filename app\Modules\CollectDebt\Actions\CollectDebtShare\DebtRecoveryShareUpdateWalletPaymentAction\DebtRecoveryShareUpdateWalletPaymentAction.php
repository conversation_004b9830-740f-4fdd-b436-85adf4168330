<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareUpdateWalletPaymentAction;

use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoveryShareUpdateWalletPaymentAction
{
	public function run()
	{
		$guides = CollectDebtGuide::all();
		return $guides->map(function (CollectDebtGuide $collectDebtGuide) {
			$paymentGuides = json_decode($collectDebtGuide->payment_guide, true);
			$isCoVi = collect($paymentGuides)->contains(function ($item) {
				return $item['payment_method_code'] == 'WALLET';
			});

			if ( !$isCoVi ) {
				$walletItemPayment = [
					"payment_method_code" => "WALLET",
   			  "payment_channel_code" => "WALLET",
    			"payment_account_id" => $collectDebtGuide->contract_code,
					'other_data' => [
						"payment_account_name" => $collectDebtGuide->contract_code,
						"payment_account_number" => $collectDebtGuide->contract_code,
						"payment_account_branch" => "",
						"payment_account_bank_code" => ""
					]
				];
				$paymentGuides[] = $walletItemPayment;

				$paymentGuideAsString = json_encode($paymentGuides);
				CollectDebtGuide::query()->where('contract_code', $collectDebtGuide->contract_code)->update(['payment_guide' => $paymentGuideAsString]);
				CollectDebtShare::query()->where('contract_code', $collectDebtGuide->contract_code)->update(['payment_guide' => $paymentGuideAsString]);
				
				$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $collectDebtGuide->contract_code)->first();
				$summaryOtherData = $collectDebtSummary->getSummaryOtherData();

				$paymentMethodIndex = $collectDebtSummary->getSummaryOtherDataIndex('PAYMENT_METHOD');
				$paymentMethodItem = $collectDebtSummary->getSummaryOtherDataItem('PAYMENT_METHOD');

				$walletItemPayment['closed'] = $collectDebtSummary->isHopDongDaTatToan() ? 'YES' : 'NO';
				$paymentMethodItem['data'][] = $walletItemPayment;
				
				$summaryOtherData[$paymentMethodIndex] = $paymentMethodItem;
				CollectDebtSummary::query()->where('contract_code', $collectDebtGuide->contract_code)
																	 ->update([
																		'other_data' => json_encode($summaryOtherData)
																	 ]);
			}
			return $collectDebtGuide;
		});
	}
} // End class
