<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class GetNoTheoLoaiPhaiThuHomNaySA
{
  public function run(string $contractCode = '', int $isfee=0): float
  {
    $tongNoGoc = CollectDebtSchedule::where('contract_code', $contractCode)
                                    ->where('isfee', $isfee) // 0: No goc: 1: Phi
                                    ->whereIn('status', [CollectDebtEnum::SCHEDULE_STT_MOI, CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN])
                                    ->sum('request_amount_debit');
    return $tongNoGoc;
  }
}
 