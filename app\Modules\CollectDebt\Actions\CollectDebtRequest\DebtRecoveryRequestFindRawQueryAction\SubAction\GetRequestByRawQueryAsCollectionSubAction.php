<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Exception;
use Illuminate\Database\Eloquent\Collection;

class GetRequestByRawQueryAsCollectionSubAction
{
  public function run(string $whereRawQueryString = '', int $limit = 0, array $fields=['*']): Collection
  {
    throw_if(empty($whereRawQueryString), new Exception('Điều kiện truy vấn không được để trống'));
    $collectDebtRequest = CollectDebtRequest::whereRaw($whereRawQueryString);
    if (!empty($limit)) {
      $collectDebtRequest = $collectDebtRequest->limit($limit);
    }
    $collectDebtRequest =  $collectDebtRequest->select($fields)->get();
    return $collectDebtRequest;
  }
}  // End class