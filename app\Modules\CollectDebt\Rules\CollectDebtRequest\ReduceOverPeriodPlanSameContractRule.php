<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtRequest;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Arr;

class ReduceOverPeriodPlanSameContractRule implements Rule
{
  public string $contractCode;
  /**
   * Create a new rule instance.
   *
   * @return void
   */
  public function __construct(string $contractCode='')
  {
    $this->contractCode = $contractCode;
  }

  /**
   *
   * @param string $prepayPlansAttribute
   * @param array $reduceOverPeriodPlans
   * @return void
   */
  public function passes($prepayPlansAttribute, $reduceOverPeriodPlans)
  {
    if (empty($reduceOverPeriodPlans)) {
      return true;
    }
    
    $ids = Arr::pluck($reduceOverPeriodPlans, 'plan_id');

    $count = CollectDebtSchedule::whereIn('id', $ids)
                                ->where('isfee', CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI)
                                ->where('contract_code', $this->contractCode)
                                ->count();
                                
    return $count == count($ids);
  }

  /**
   * Get the validation error message.
   *
   * @return string
   */
  public function message()
  {
    return 'ID lịch giảm phí chậm kỳ không đúng với hợp đồng';
  }
}
