<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST;

use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;

class GetHopDongDangDungJobTuDongST
{
  public function run(): array
  {
    $configAuto = CollectDebtConfigAuto::query()
                                        ->where('time_expired', '>', time())
                                        ->get();
    return $configAuto->pluck('contract_code')->toArray();
  }
}
