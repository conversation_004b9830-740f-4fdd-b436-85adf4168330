<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\GetRequestByRawQueryAsCollectionSubAction;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class DebtRecoveryRequestCheckPaymentAction
{

/**
 * Thêm time_receivered IS NULL để đảm bảo, yc đã trích được tiền rồi sẽ ko gọi check nữa (tránh duplicate)
 */
  public function run(Request $request): Collection
  {
    $whereRaw = sprintf(
      "payment_method_code = 'MPOS' AND (status = %s OR status = %s) 
                                    AND status_payment = %s 
                                    AND time_receivered IS NULL 
                                    AND (time_checked IS NULL OR time_checked + %d < %d)",

      CollectDebtEnum::REQUEST_STT_MOI_TAO, CollectDebtEnum::REQUEST_STT_DA_DUYET,

      CollectDebtEnum::REQUEST_STT_PM_DA_GUI,
      
      env('THOI_GIAN_CHECK_LAI_YEU_CAU_TRICH', 30) * 60, time()
    );

    $collectDebtRequests = app(GetRequestByRawQueryAsCollectionSubAction::class)->run(
      $whereRaw,
      30,
      ['*']
    );

    throw_if($collectDebtRequests->isEmpty(), new Exception('Không có yêu cầu cân check trạng thái trích nợ'));
		
		$collectDebtRequests->load('collectDebtPartner');

    $collectDebtRequests->each(function (CollectDebtRequest $collectDebtRequest) use ($request) {
			// chua co partner thi goi check
			if (!$collectDebtRequest->collectDebtPartner) {
				$collectDebtRequestChecked = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request);
				$collectDebtRequest->mpos_debt_result = $collectDebtRequestChecked;
			}

			// co partner roi thi update time_checked
			if ($collectDebtRequest->collectDebtPartner) {
				$collectDebtRequest->forceFill(['time_checked' => time()])->update();
				CacheEnum::removeCheckedRequest($collectDebtRequest->partner_request_id);
			}
     
      return $collectDebtRequest;
    });

    return $collectDebtRequests;
  }
}  // End class