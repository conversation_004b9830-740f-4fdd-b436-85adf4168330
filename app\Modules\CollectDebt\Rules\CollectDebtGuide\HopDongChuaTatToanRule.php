<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtGuide;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use Illuminate\Contracts\Validation\Rule;

class HopDongChuaTatToanRule implements Rule
{
  private string $__errorMessage = '';
  /**
   * Create a new rule instance.
   *
   * @return void
   */
  public function __construct()
  {
    //
  }

  /**
   * Determine if the validation rule passes.
   *
   * @param  string  $attribute
   * @param  mixed  $value
   * @return bool
   */
  public function passes($contractCodeField, $contractCodeValue)
  {
    // Sau này nếu có rủi ro trong việc bị client spam thì sẽ dùng cái này chặn hết

    return true;
  }

  /**
   * Get the validation error message.
   *
   * @return string
   */
  public function message()
  {
    return $this->__errorMessage;
  }
}
