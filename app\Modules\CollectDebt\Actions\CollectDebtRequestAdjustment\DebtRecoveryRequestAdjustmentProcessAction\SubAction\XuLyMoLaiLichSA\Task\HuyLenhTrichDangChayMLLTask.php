<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use Exception;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtWaitProcess;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;

class HuyLenhTrichDangChayMLLTask
{
	public function run(CollectDebtRequestAdjustment $ra, Collection $listLichThuDuocSinhRa): bool
	{
		if ($listLichThuDuocSinhRa->isEmpty()) {
			mylog(['Khong co thong tin lich thu duoc sinh ra' => 'Yes']);
			return true;
		}

		$contractCode = $listLichThuDuocSinhRa->first()->contract_code;
		
		$listRunDate = $listLichThuDuocSinhRa->pluck('rundate')->toArray();
		mylog(['List rundate' => $listRunDate]);

		$rangeExpired = [];
		
		foreach ($listRunDate as $rd) {
			$carbon = Carbon::createFromFormat('Ymd', $rd);
			
			$rangeExpired[] = [
				$carbon->copy()->startOfDay()->timestamp,
				$carbon->copy()->endOfDay()->timestamp,
			];

		}

		// Lay ra lich thu MPOS dang chay va huy no di
		$yeuCauMposDangChay = CollectDebtRequest::query()
																						->where('contract_code', $contractCode)
																						->where(function (Builder $q) use ($rangeExpired) {
																							foreach ($rangeExpired as $range) {
																								$q->orWhereBetween('time_expired', $range);
																							}
																						})
																						->where('payment_method_code', 'MPOS')
																						->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
																						->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
																						->first();
		
		if (!$yeuCauMposDangChay) {
			mylog(['Khong co yeu cau trich tu dong mpos dang chay' => 'ok']);
			return true;
		}

		$maYeuCauTrichNgay = $ra->getMaYeuCauTrich();
		mylog(['Ma yc trich la' => $maYeuCauTrichNgay]);

		$yeuCauTrichNgay = CollectDebtRequest::query()->where('partner_request_id', $maYeuCauTrichNgay)->first();
		
		if (!$yeuCauTrichNgay) {
			mylog(['Loi khong tim thay ban ghi yc TRICH NGAY' => 'ok']);
			throw new Exception('Loi khong tim thay ban ghi yc TRICH NGAY');
		}
		

		// Co roi thi kiem tra partner
		$paramCreateProcess = [
			'source_id' => $yeuCauTrichNgay->id, // Ban ghi nay phai hach toan
			'obj_id' => $yeuCauMposDangChay->id, // Ban ghi yc muon huy
			'obj_type' => $yeuCauMposDangChay->getTable(),
			'action_code' => 'CANCEL',
			'time_created' => now()->timestamp
		];

		mylog(['Create wait process la: ' => $paramCreateProcess]);

		$result = CollectDebtWaitProcess::forceCreate($paramCreateProcess);
		if (!$result) {
			mylog(['Loi tao wait process' => $result]);
			throw new Exception('Loi tao wait process');
		}

		return true;
	}
}
