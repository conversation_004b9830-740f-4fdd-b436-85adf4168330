<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryDebitReportRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKePhiSA;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeThoiGianSA;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeUserLienQuanSA;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeThongTinHopDongSA;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeTinhTrangThuHoiSA;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeTrangThaiThuHoiSA;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeTienThuThanhCongSA;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeTienThuDuocQuaTungKenhSA;

class DebtRecoverySummaryDebitReportAction
{
	public function run(DebtRecoverySummaryDebitReportRequest $request)
	{
		$collectDebtSummaries = CollectDebtSummary::query()
																							->with([
																								'collectDebtRequests' => function ($query) {
																									return $query->select([
																										'id',
																										'contract_code',
																										'status',
																										'payment_method_code',
																										'amount_receiver',
																									]);
																								},
																								'collectDebtShare' => function ($query) {
																									return $query->select([
																										'id',
																										'contract_code',
																										'profile_data',
																										'contract_data'
																									]);
																								}
																							]);

		if ($request->json('data.filter.contract_codes')) {
			$collectDebtSummaries = $collectDebtSummaries->whereIn('contract_code', $request->json('data.filter.contract_codes'));
		}

		$collectDebtSummaries = $collectDebtSummaries->orderBy('time_updated', 'DESC')
																							->select([
																								'id',
																								'contract_code',
																								'contract_data',
																								'total_amount_paid',
																								'total_fee_paid',
																								'other_data',
																								'contract_amount',
																								'number_day_overdue',
																								'number_over_cycle',
																								'fee_overdue',
																								'fee_overdue_cycle',
																								'total_amount_receiver',
																								'status_contract',
																								'time_settlement'
																							])
																							->paginate(
																								$request->json('data.limit', 10),
																								['*'],
																								'page',
																								$request->json('data.page', 1)
																							);

		$collection = $collectDebtSummaries->getCollection();

		$results = $collection->map(function (CollectDebtSummary $collectDebtSummary) {
			$collectDebtSummary->contract_statistic = app(ThongKeThongTinHopDongSA::class)->run($collectDebtSummary);
			$collectDebtSummary->timing_statistic = app(ThongKeThoiGianSA::class)->run($collectDebtSummary);
			$collectDebtSummary->debit_status_statistic = app(ThongKeTinhTrangThuHoiSA::class)->run($collectDebtSummary);
			$collectDebtSummary->success_debit_statistic = app(ThongKeTienThuThanhCongSA::class)->run($collectDebtSummary);
			$collectDebtSummary->payment_method_statistic = app(ThongKeTienThuDuocQuaTungKenhSA::class)->run($collectDebtSummary);
			$collectDebtSummary->status_statistic = app(ThongKeTrangThaiThuHoiSA::class)->run($collectDebtSummary);
			$collectDebtSummary->fee_statistic = app(ThongKePhiSA::class)->run($collectDebtSummary);
			$collectDebtSummary->user_action_statistic = app(ThongKeUserLienQuanSA::class)->run($collectDebtSummary);
			
			unset($collectDebtSummary->contract_code);
			unset($collectDebtSummary->contract_data);
			unset($collectDebtSummary->total_amount_paid);
			unset($collectDebtSummary->total_fee_paid);
			unset($collectDebtSummary->other_data);
			unset($collectDebtSummary->contract_amount);
			unset($collectDebtSummary->number_day_overdue);
			unset($collectDebtSummary->number_over_cycle);
			unset($collectDebtSummary->fee_overdue);
			unset($collectDebtSummary->fee_overdue_cycle);
			unset($collectDebtSummary->total_amount_receiver);
			unset($collectDebtSummary->status_contract);
			unset($collectDebtSummary->time_settlement);

			unset($collectDebtSummary->collectDebtRequests);
			unset($collectDebtSummary->collectDebtShare);
			return $collectDebtSummary;
		});
		
		$collectDebtSummaries->setCollection($collection);
		return $collectDebtSummaries;
	}
} // End class
