<?php

namespace App\Modules\CollectDebt\Model;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\CollectDebt\Model\Traits\CollectDebtGuide\GuideStatusable;
use App\Modules\CollectDebt\Model\Traits\CollectDebtGuide\ContractDataMethodTrait;
use App\Modules\CollectDebt\Model\Traits\CollectDebtGuide\VirtualAccountMethodTrait;

class CollectDebtGuide extends Model
{
	use GuideStatusable;
	
	protected $table   = 'debt_recovery_contract_guide';
	public $timestamps = false;
	protected $guarded = [];
	protected $appends = [
		'time_start_as_date', 
		'time_end_as_date', 
		'time_start_as_vn_date', 
		'time_end_as_vn_date'
	];

/* -------------------- Relationship ----------------- */
	public function collectDebtDigitalNoti() {
		return $this->hasOne(CollectDebtDigitalNoti::class, 'contract_code', 'contract_code');
	}

	public function collectDebtDigitalTmp() {
		return $this->hasOne(CollectDebtDigitalTmp::class, 'contract_code', 'contract_code');
	}

	public function collectDebtSchedules() {
		return $this->hasMany(CollectDebtSchedule::class, 'contract_code', 'contract_code');
	}

	public function configPauseJob() {
		return $this->hasOne(CollectDebtConfigAuto::class, 'contract_code', 'contract_code');
	}

	public function collectDebtSummary() {
		return $this->hasOne(CollectDebtSummary::class, 'contract_code', 'contract_code');
	}

	public function collectDebtShare() {
		return $this->hasOne(CollectDebtShare::class, 'contract_code', 'contract_code');
	}

	public function collectDebtEvents() {
		return $this->hasMany(CollectDebtContractEvent::class, 'contract_code', 'contract_code');
	}
/* -------------------- Appends ----------------- */
	public function getTimeStartAsDateAttribute()
	{
		return Carbon::createFromTimestamp($this->contract_time_start)->copy()->startOfDay();
	}

	public function getTimeEndAsDateAttribute()
	{
		return Carbon::createFromTimestamp($this->contract_time_end)->copy()->endOfDay();
	}

	public function getTimeStartAsVnDateAttribute()
	{
		return Carbon::createFromTimestamp($this->contract_time_start)->copy()->startOfDay()->format('H:i, d/m/Y');
	}

	public function getTimeEndAsVnDateAttribute()
	{
		return Carbon::createFromTimestamp($this->contract_time_end)->copy()->endOfDay()->format('H:i, d/m/Y');
	}

	public function getReasonAttribute($value='') {
    $attributes = $this->getAttributes();
    if (isset($attributes['reason'])) {
      return $attributes['reason'];
    }

    if (!empty($value)) {
      return $value;
    }

    return '';
  }
/* -------------------- Method ----------------- */
	public function isChiDanHopDongTrichNgay(): bool {
		return $this->contract_type == CollectDebtEnum::GUIDE_HD_TRICH_NGAY;
	}

	public function isChiDanHopDongTrichKy(): bool {
		return $this->contract_type == CollectDebtEnum::GUIDE_HD_TRICH_KY;
	}

	public function isHopDongMoiTinh(): bool {
		$guideOtherData = json_decode($this->other_data, true);
		return empty($guideOtherData['company']);
	}

	public function isHopDongDangThuCuaVer1(): bool {
		return !$this->isHopDongMoiTinh();
	}

	public function isTodayDenHanHoacQuaHanHopDong() {
		return now()->gt($this->time_end_as_date) && !now()->isSameDay($this->time_end_as_date);
	}

	public function getQrCodeAttr(): string {
		$paymentGuides = json_decode($this->payment_guide, true);
		$qrCodeAttr = collect($paymentGuides)->where('payment_method_code', 'VIRTUALACCOUNT')->first();
		return $qrCodeAttr['other_data']['qrCode'];
	}

	public function getMposMcId(): string {
		$profileData = json_decode($this->profile_data, true);
		return $profileData['merchant']['partner_merchant_code'];
	}

	public function isHopDongNextlend(): bool {
		return $this->partner_code == 'NEXTLEND';
	}
} // End class
