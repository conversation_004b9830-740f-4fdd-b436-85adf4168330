<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtContractPriority;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction\DongBangSoTienTrichNoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction\TaoCongNoThanhToanQuaViSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction\GetTaiKhoanCoSoDuKhaDungSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction\TaoLogDongBangViSubAction;

class DebtRecoveryContractCreateRequestViaWalletAction
{  
	public $contractPriority;

	/**
	 * Tạo yêu cầu thu qua kênh ví
	 */
  public function run(Request $request)
  {
		$profileAccount = app(GetTaiKhoanCoSoDuKhaDungSubAction::class)->run();
		mylog(['profileAccount' => $profileAccount]);

		$profileId = $profileAccount['data']['profile_id'] ?? false;
		mylog(['profileId' => $profileId]);

		throw_if(!$profileId, new Exception('Khong biet duoc thong tin profileId'));

		// Tìm HĐ ưu tiên
		$collectDebtContractPriority = CollectDebtContractPriority::query()
																															->where('profile_id', $profileId)
																															->where('status', CollectDebtEnum::CONTRACT_PRIORITY_STT_MOI_TAO)
																															->first();
		
		if (!$collectDebtContractPriority) {
			mylog(['[EMPTY]' => 'khong co thong tin HD nhan thanh toan tu nguon tien thu thua']);
			throw new Exception('khong co thong tin HD nhan thanh toan tu nguon tien thu thua');
		}
		
		mylog([ 'Thong tin HD uu tien' => $collectDebtContractPriority ]);

		if ($collectDebtContractPriority) {
			mylog(['co thong tin hd uu tien' => 'yes']);

			// update len dang xu ly
			$updateLenThanhDangXuLy = CollectDebtContractPriority::query()
																													 ->where('id', $collectDebtContractPriority->id)
																													 ->where('status', CollectDebtEnum::CONTRACT_PRIORITY_STT_MOI_TAO)
																													 ->update([
																														'status' => CollectDebtEnum::CONTRACT_PRIORITY_STT_DANG_XU_LY
																													 ]);
			
			if (!$updateLenThanhDangXuLy) {
				mylog([
					'[LOI UPDATE DANG XU LY]' => $updateLenThanhDangXuLy
				]);

				throw new Exception('LOI UPDATE HOP DONG UU TIEN LEN DANG XU LY');
			}

			$this->contractPriority = CollectDebtContractPriority::query()->find($collectDebtContractPriority->id);

			if ($this->contractPriority->status != CollectDebtEnum::CONTRACT_PRIORITY_STT_DANG_XU_LY) {
				mylog(['[LOI STATUS]' => 'hd uu tien dang khong o trang thai dang xu ly']);
				throw new Exception('hd uu tien dang khong o trang thai dang xu ly');
			}

			$collectDebtSummary = CollectDebtSummary::query()
																							->where('contract_code', $collectDebtContractPriority->contract_code)
																							->first();
		}
		
		if (!$collectDebtContractPriority) {
			mylog(['empty' => 'khong co thong tin hop dong uu tien']);

			$lichDangThuCuaMC = CollectDebtSchedule::query()
																						->whereIn('status', [
																							CollectDebtEnum::SCHEDULE_STT_MOI, 
																							CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN
																						])
																						->where('rundate', '>=', now()->format('Ymd'))
																						->where('profile_id', $profileId)
																						->first();
			
			if (!$lichDangThuCuaMC) {
				mylog(['[empty]' => 'khong co thong tin lich thu de thanh toan']);
				throw new Exception('Khong ton tai lich dang thu cua MC');
			}

			$collectDebtSummary = CollectDebtSummary::query()
																							->where('contract_code', $lichDangThuCuaMC->contract_code)
																							->first();
		}

		mylog(['Summary xu ly la' => optional($collectDebtSummary)->contract_code]);

		throw_if(!$collectDebtSummary, new Exception('không có thông tin HĐ cần trích để thanh toán qua ví'));
		
		// Kiểm tra HĐ hiện tại, nếu đang có công nợ chưa xử lý hoặc có yc trích kênh ví nhưng chưa hạch toán
		$collectDebtShare = CollectDebtShare::query()->where('contract_code', $collectDebtSummary->contract_code)->first();

		$plans = CollectDebtSchedule::query()
																->where('contract_code', $collectDebtSummary->contract_code)
																->where('profile_id', $profileId)
																->whereIn('status', [CollectDebtEnum::SCHEDULE_STT_MOI, CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN])
																->where('rundate', '>=', now()->format('Ymd'))
																->get();
		
		mylog(['Danh sach lich thu la' => $plans->pluck('id')]);
		
		throw_if(
			$plans->isEmpty(), 
			new Exception('Khong co thong tin lich thu: ' . $collectDebtSummary->contract_code)
		);
		
		DB::beginTransaction();
		try {
			if ($this->contractPriority) {
				// Cai nay lam truoc: danh dau hop dong uu tien la da xu ly
				$updated = $this->contractPriority->forceFill([
					'status' => CollectDebtEnum::CONTRACT_PRIORITY_STT_DA_XU_LY,
					'time_processed' => time(),
					'processed_by' => Helper::getCronJobUser()
				])->update();

				if (!$updated) {
					mylog([
						'[LOI CAP NHAT VE DA XU LY]' => $updated,
						'Co thong tin hop dong uu tien' => 'yes'
					]);

					throw new Exception('LOI CAP NHAT VE DA XU LY');
				}
			}

			// Tao partner 
			$collectDebtPartner = app(TaoCongNoThanhToanQuaViSubAction::class)->run(
				$collectDebtSummary,
				$plans,
				$collectDebtShare,
				$profileAccount
			);

			if (!$collectDebtPartner) {
				mylog(['[LOI TAO PARTNER]' => 'khong the tao partner']);
				throw new Exception('khong the tao partner');
			}

			mylog([ '[TAO PARTNER THANH CONG]' => $collectDebtPartner->only(['id', 'contract_code']) ]);
			
			// Tao log tru tien
			$taoLogDongBang = app(TaoLogDongBangViSubAction::class)->run($collectDebtPartner, $collectDebtSummary);
			if (!$taoLogDongBang) {
				mylog(['[LOI TAO LOG DONG BANG]' => $taoLogDongBang]);
				throw new Exception('LOI TAO LOG DONG BANG');
			}

			// Goi api thuc hien dong bang vi
			$dongBangSoTienTrichNoResult = app(DongBangSoTienTrichNoSubAction::class)->run($profileAccount, $collectDebtPartner->amount_receiver);
			throw_if(
				empty($dongBangSoTienTrichNoResult['data']['id']),
				new Exception('không thể đóng băng được số tiền của yc trích. Xem lại ngay!!!')
			);

			DB::commit();
			return $collectDebtSummary;
		}catch(\Throwable $th) {
			mylog(['Error' => Helper::traceError($th)]);
			
			DB::rollBack();

			// Loi thi rollback hop dong uu tien ve trang thai moi tao
			if ($this->contractPriority) {
				mylog(['co thong tin hd uu tien' => 'rollBack lai ve stt moi tao']);
				$updateVeMoiTao = CollectDebtContractPriority::query()
																										 ->where('id', $this->contractPriority->id)
																										 ->where('status', CollectDebtEnum::CONTRACT_PRIORITY_STT_DANG_XU_LY)
																										 ->update([
																											'status' => CollectDebtEnum::CONTRACT_PRIORITY_STT_MOI_TAO
																										 ]);

				mylog(['KET QUA UPDATE VE MOI TAO KHI CO LOI' => $updateVeMoiTao]);
			}

			throw $th;
		}
  } // End method
} // End class