<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\GetReduceAndPrepayPlanAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class GetReduceAndPrepayPlanAction
{
  public function run(string $contractCode = '', array $fields = ['*'])
  {
    $returnData = [
      'reduce_over_period_plans' => [],
      'reduce_over_due_fee' => [],
    ];

    $plans = CollectDebtSchedule::query()
                                ->where('contract_code', $contractCode)
                                ->whereNotFinalStatus()
                                ->select($fields)
                                ->orderByRaw('time_start ASC, id ASC')
                                ->get();


    $returnData['reduce_over_due_fee'] = $plans->sum(function (CollectDebtSchedule $plan) {
      return 0;
    });

    // phí quá hạn đã thanh toán
    $returnData['reduce_over_due_fee_collected'] = $plans->sum(function (CollectDebtSchedule $plan) {
      if ($plan->isLichThuPhiQuaHan()) {
        return $plan->success_amount_debit;
      }

      return 0;
    });


    $returnData['reduce_over_period_plans'] = $plans->map(function (CollectDebtSchedule $plan) {
      $loaiLichThu = "Thu gốc";
      if ($plan->isLichThuPhiChamKy()) {
        $loaiLichThu = "Thu phí CK";
      }

      if ($plan->isLichThuPhiQuaHan()) {
        $loaiLichThu = "Thu phí QK";
      }

      return [
        'period' => $plan->time_start_as_date->format('d/m/Y'),
        'amount' => $plan->request_amount_debit,
        'plan_type_name' => $loaiLichThu
      ];
    })->toArray();

    return $returnData;
  }
}  // End class