<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtRequest;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;

trait UpdateOtherDataTrait
{
	public $checkPaymentFirstIndex = -1;

  public function buildOtherDataToSave(string $type, string $note=''): string {
    $otherData = json_decode($this->other_data, true);
    $otherData[] = [
      'type' => $type,
      'time_modified' => time(),
      'data' => $this->only(['id', 'status', 'status_payment', 'status_recored', 'partner_request_id', 'partner_transaction_id']),
      'note' => $note,
    ];

    return json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
  }

  public function updateOtherData(string $type, string $note=''): self {
    $this->other_data = $this->buildOtherDataToSave($type, $note);
    $this->save();

    return $this;
  }

  public function initOtherData(string $type='CREATED', string $note='Hệ thống tạo yêu cầu trích'): string {
    $init = [
      [
        'type' => $type,
        'time_modified' => time(),
        'data' => $this->only(['id', 'status', 'status_payment', 'status_recored', 'partner_request_id', 'partner_transaction_id']),
        'note' => $note
      ]
    ];

    $planData = CollectDebtSchedule::hydrate(json_decode($this->plan_data, true));
    
    $lichThuPhiChamKy = $planData->filter(function (CollectDebtSchedule $plan) {
      return $plan->isLichThuPhiChamKy();
    });

    $lichThuPhiQuaHan = $planData->filter(function (CollectDebtSchedule $plan) {
      return $plan->isLichThuPhiQuaHan();
    });

    $this->load('collectDebtShare');
    $cauHinhPhiChamKy = $this->collectDebtShare->getFeeByType(CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY);
    $cauHinhPhiQuaHan = $this->collectDebtShare->getFeeByType(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN);

    if ($lichThuPhiChamKy->isNotEmpty()) {
      $ck = [
        'type' => 'FEE_OVERRIDE',
        'time_modified' => time(),
        'data' => [
          'contract_fee' => [
            'percent_fee'         => '',
            'flat_fee'            => '',

            // Quá hạn
            'overdue_flat_fee'    => $cauHinhPhiQuaHan['flat_fee'],
            'overdue_percent_fee' => $cauHinhPhiQuaHan['percent_fee'],

            // Chậm kỳ
            'flat_fee_cycle'      => $cauHinhPhiChamKy['flat_fee'],
            'percent_fee_cycle'   => $cauHinhPhiChamKy['percent_fee'],
          ],

          'reduced_fee' => [
            'flat_fee' => $lichThuPhiChamKy->sum(function (CollectDebtSchedule $plan) {
              return $plan->reduce_amount_debit ?? 0;
            }),
            'percent_fee' => 0
          ]
        ],
        'note' => 'Phí chậm kỳ phải thu'
      ];

      $init[] = $ck;
    }

    if ($lichThuPhiQuaHan->isNotEmpty()) {
      $qh = [
        'type' => 'FEE_OVERDUE',
        'time_modified' => time(),
        'data' => [
          'contract_fee' => [
            'percent_fee'         => '',
            'flat_fee'            => '',

            // Quá hạn
            'overdue_flat_fee'    => $cauHinhPhiQuaHan['flat_fee'],
            'overdue_percent_fee' => $cauHinhPhiQuaHan['percent_fee'],

            // Chậm kỳ
            'flat_fee_cycle'      => $cauHinhPhiChamKy['flat_fee'],
            'percent_fee_cycle'   => $cauHinhPhiChamKy['percent_fee'],
          ],

          // giảm phí chậm kỳ
          'reduced_fee' => [
            'flat_fee' => $lichThuPhiQuaHan->sum(function (CollectDebtSchedule $plan) {
              return $plan->reduce_amount_debit ?? 0;
            }),
            'percent_fee' => 0
          ]
        ],
        'note' => 'Phí quá hạn kỳ phải thu'
      ];

      $init[] = $qh;
    }

    return json_encode($init, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
  }

	public function checkAndReplaceCheckPaymentType($params=[], $type='CHECK_PAYMENT') {
		$requestOtherData = $this->getRequestOtherData();
		$count = collect($requestOtherData)->where('type', $type)->count();

		foreach ($requestOtherData as $index => $item) {
			if ($item['type'] == $type) {
				$this->checkPaymentFirstIndex = $index;
				break;
			}
		}
		
		if ($count >= 3 && $this->checkPaymentFirstIndex != -1) {
			unset($requestOtherData[$this->checkPaymentFirstIndex]);
		}

		$requestOtherData[] = $params;
		$requestOtherData = array_values($requestOtherData);
		return json_encode($requestOtherData, JSON_UNESCAPED_UNICODE);
	}

	public function getSoTienDuocGiamPhi() {
		$otherData = $this->getRequestOtherData();
		$typeGiamPhi = collect($otherData)->where('type', 'CREATE_REDUCE')->first();
		mylog(['type giam phi la' => $typeGiamPhi]);
		
		throw_if(empty($typeGiamPhi), new Exception('khong tim thay thong tin so tien giam phi'));

		$soTienPhiDuocGiam = $typeGiamPhi['data']['reduce_fee_amount'];
		mylog(['soTienPhiDuocGiam' => $soTienPhiDuocGiam]);
		
		return $soTienPhiDuocGiam;
	}
} // End class
