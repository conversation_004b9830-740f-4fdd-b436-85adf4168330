<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction\XuLyYeuCauTrichQuaDoiTacSubAction;

class CutOffTimeByRedisAction
{
	private int $__limit = 30;

	public $log = [];

	private array $__yeuCauDaCutOff = [];

	public function initCutOff()
	{
		$now = now()->timestamp;
		$key = CacheEnum::NLV4_REDIS_LIST_CUTOFF;

		$partnerRequestIds = Redis::zrangebyscore($key, '-inf', $now, [
			'limit' => [0, $this->__limit]
		]);

		if (empty($partnerRequestIds)) {
			return "Khong co yeu cau nao can cutoff";
		}

		$collectDebtRequests = CollectDebtRequest::query()->whereIn('partner_request_id', $partnerRequestIds)->get();

		foreach ($collectDebtRequests as $collectDebtRequest) {
			try {
				$this->run($collectDebtRequest);
			} catch (\Throwable $th) {
				continue;
			}
		}
	}

	public function run(CollectDebtRequest $collectDebtRequest)
	{
		$logContext = ['YeuCauThucHienCutOff' => $collectDebtRequest->partner_request_id];

		// Update len dang xu ly
		$updatedDangXuLy = CollectDebtRequest::query()
			->where('id', $collectDebtRequest->id)
			->where('status_cutoff', CollectDebtEnum::REQUEST_CHUA_CUT_OFF)
			->update(['status_cutoff' => CollectDebtEnum::REQUEST_DANG_CUT_OFF]);


		if (!$updatedDangXuLy) {
			$logContext['UpdateLenDangXuLy'] = false;
			throw new Exception('Khong the xu ly cut of cho yc: ' . $collectDebtRequest->partner_request_id);
		}

		$collectDebtRequest->load(['collectDebtShare', 'collectDebtPartner']);

		DB::beginTransaction();
		try {
			// yc da cut off -> loi luon
			if ($collectDebtRequest->isYeuCauCutOff()) {
				$logContext['YcDaThucHienCutOff'] = 'No need processing';
				$this->__updateVeDaXuLyCutOff($collectDebtRequest);

				$this->__yeuCauDaCutOff[] = $collectDebtRequest->id;

				DB::commit();

				$this->__pullFromRedis($collectDebtRequest);

				return $collectDebtRequest;
			}

			// chua co partner -> can goi qua doi tac check lan cuoi
			if (!$collectDebtRequest->collectDebtPartner || empty($collectDebtRequest->partner_transaction_id)) {
				$logContext['HasPartner'] = false;
				$coreContract = $collectDebtRequest->collectDebtShare->getCoreContractByGuide();
				$rq = app(XuLyYeuCauTrichQuaDoiTacSubAction::class)->run($collectDebtRequest, $coreContract);

				$this->__updateVeDaXuLyCutOff($collectDebtRequest);

				$this->__yeuCauDaCutOff[] = $collectDebtRequest->id;

				DB::commit();

				$this->__pullFromRedis($collectDebtRequest);

				return $collectDebtRequest;
			}

			// Da co partner
			if ($collectDebtRequest->collectDebtPartner) {
				$logContext['HasPartner'] = true;
				$this->__updateVeDaXuLyCutOff($collectDebtRequest);

				$this->__yeuCauDaCutOff[] = $collectDebtRequest->id;
				DB::commit();

				$this->__pullFromRedis($collectDebtRequest);
				return $collectDebtRequest;
			}
		} catch (\Throwable $th) {

			$message = traceErr($th);
			$logContext['ErrCutOff'] = $message;
			$logContext['LogId'] = request('api_request_id');

			DB::rollBack();

			CollectDebtRequest::query()
				->where('id', $collectDebtRequest->id)
				->where('status_cutoff', CollectDebtEnum::REQUEST_DANG_CUT_OFF)
				->update(['status_cutoff' => CollectDebtEnum::REQUEST_CHUA_CUT_OFF]);

			throw new Exception($message);
		} finally {
			Log::info("[CutOff][$collectDebtRequest->partner_request_id] Cutoff lenh trich", $logContext);
		}
	}

	private function __updateVeDaXuLyCutOff(CollectDebtRequest $collectDebtRequest)
	{
		$updated = CollectDebtRequest::query()->where('id', $collectDebtRequest->id)
			->update([
				'status_cutoff' => CollectDebtEnum::REQUEST_DA_XL_CUT_OFF
			]);
		if (!$updated) {
			throw new Exception('khong the cap nhat ve trang thai da cut off');
		}

		return $updated;
	}

	private function __pullFromRedis(CollectDebtRequest $collectDebtRequest)
	{
		CacheEnum::removeCheckedRequest($collectDebtRequest->partner_request_id);
		CacheEnum::removeFromListCutOff($collectDebtRequest->partner_request_id);
	}
} // End class