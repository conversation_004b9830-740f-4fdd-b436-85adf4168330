<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\Task;

use Carbon\Carbon;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class GetLichThuCoRunDateLaHomSauST
{  
  /**
   * Tìm ra bản ghi ngày chu kỳ trích nợ <lịch thu> tiếp theo từ lịch hiện tại 
   *
   * @param CollectDebtSchedule $collectDebtSchedule [explicite description]
   * @param int $isfee [explicite description]
   *
   * @return CollectDebtSchedule
   */
  public function run(CollectDebtSchedule $collectDebtSchedule, int $isfee = 0)
  {
    $nextScheduleRundate = $collectDebtSchedule->rundate_as_date->copy()->addDay()->format('Ymd');

    $nextSchedule = CollectDebtSchedule::where('contract_code', $collectDebtSchedule->contract_code)
                                       ->where('rundate', $nextScheduleRundate)
                                       ->where('isfee', $isfee)
                                       ->first();

    return $nextSchedule;
  }
}
