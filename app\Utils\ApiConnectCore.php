<?php

/**
 * Created by: <PERSON><PERSON><PERSON>
 * Date: 13/08/2020
 * Time: 10:55 AM
 */
namespace App\Utils;

use Illuminate\Support\Facades\Log;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

class ApiConnectCore {

    protected $_apiUrl = '';
    protected $Log;
    protected $_response;
    protected $_apiResult;
    protected $_inputs;
    protected $_checksum;
    protected $_params;
    protected $_usernameRequest = 'appRequest';
    protected $_language = 'vi';

    public function __construct() {
        $this->_apiUrl = env('URL_CORE', 'http://***********:8003/request.php');
        //$this->_apiUrl = env('URL_CORE', 'https://service.nextlend.vn/v1/request.php');
        $this->_apiResult = false;
        $this->_inputs = array();
        $this->_checksum = '';
        $this->_params = array();

        $this->_response = array(
            'errorCode' => 'API_ERROR_CODE_DEFAULT',
            'errorDescription' => $this->_getErrorDescription(),
            'data' => false,
            'remote_ip' => '',
        );
        $log = new Logger('api');
        $this->Log = $log->pushHandler(new StreamHandler(storage_path().'/api/core/request/'.date('Y-m-d').'-core.log', Logger::INFO));

    }

    public function callApi( $paramjson, $method = 'GET') {

        $this->Log->info('[API] param_json  '. json_encode( $paramjson));
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->_apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_POSTFIELDS =>$paramjson,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Cookie: __cfduid=d5797c13833d8cc0f132a158d65a08e4f1618813088'
            ),
        ));

        $this->_apiResult = curl_exec($curl);
        $resultCode = curl_getinfo($curl);

        //$this->Log->info('[API] [API] resultCode '. json_encode($resultCode));
        $this->Log->info('[API] [API] _apiResult '. json_encode($this->_apiResult));

        if (!(json_decode($this->_apiResult))) {
            //echo '<pre>';
            print_r($this->_apiResult);
            die();

        }

        $this->Log->info('[API] [API] _response '. json_encode($this->_response));

        return $this->_apiResult;
    }



    /*
     * Kiểm tra data có bị thay đổi hay không
     * Kiểm tra mã checksum
     * Thực hiện giải mã
     *
     * return array
     */

    protected function _checkDataResponse() {
        $this->_response = $this->_apiResult;
        if (isset($this->_apiResult) && !empty($this->_apiResult)) {
            print_r($this->_apiResult);die;
            if ($this->_apiResult['checksum'] == MD5($this->_apiResult['data'] . API_APP_SECRET_KEY)) {
                $this->_response['data'] = Encryption::Decrypt($this->_apiResult['data'], API_KET_ENCRYPTION_DATA);
                if (!empty($this->_response['data'])) {
                    if (!empty(json_decode($this->_response['data'], true)) && is_array(json_decode($this->_response['data'], true))) {
                        $this->_response['data'] = json_decode($this->_response['data'], true);
                    }
                }

                return $this->_response;
            }
        }

        //Neu checksum khong dung
        $this->_response['errorCode'] = 'ERROR_CHECKSUM';

        if (!isset($this->_apiResult['errorDescription'])) {
            $this->_getErrorDescription();
        }

        return $this->_response;
    }

    protected function _getErrorDescription() {
        if (isset($this->_language) && strtolower($this->_language) == 'vi') {
            $this->_response['errorDescription'] = 'Lỗi không xác định.';
        }
        if (isset($language) && strtolower($this->_language) == 'vi') {
            $this->_response['errorDescription'] = 'Unknown error.';
        }
    }

}
