<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanGetByIdAction;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanGetByIdRequest;
use Exception;

class DebtRecoveryContractPlanGetByIdAction
{  
  public function run(DebtRecoveryContractPlanGetByIdRequest $request): CollectDebtSchedule
  {
    $collectDebtSchedule = CollectDebtSchedule::find($request->json('data.id'));
    throw_if(!$collectDebtSchedule, new Exception('Không tìm thấy lịch thu'));
    return $collectDebtSchedule;
  }
} // End class