<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\FeeHandler;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\PushSoLieuBangTongHopTask;

class XuLyThanhToanPhiFH
{
  public array $metaData = [];

  public function run(CollectDebtSchedule $lichThuPhiDangHachToan, float $soTienPhiThuThanhCong=0) 
  {
    $otherData = $lichThuPhiDangHachToan->getPlanOtherData();
    
    $phiChamKy = $phiQuaHan = 0;
    
    if ($lichThuPhiDangHachToan->isLichThuPhiChamKy()) {
      $phiChamKy = $lichThuPhiDangHachToan->request_amount_debit;
    }

    if ($lichThuPhiDangHachToan->isLichThuPhiQuaHan()) {
      $phiQuaHan = $lichThuPhiDangHachToan->request_amount_debit;
    }

    $soTienMangDiThanhToanPhiChamKy = min($soTienPhiThuThanhCong, $phiChamKy);
    // $this->metaData[] = ['label' => CollectDebtEnum::METADATA_THANH_TOAN_PHI_CK, 'value' => $soTienMangDiThanhToanPhiChamKy];

    // Xu ly lic thu qua han
    $soTienConLaiCoTheThanhToanTiep = $soTienPhiThuThanhCong - $soTienMangDiThanhToanPhiChamKy;
    $soTienMangDiThanhToanPhiQuaHan = min($soTienConLaiCoTheThanhToanTiep, $phiQuaHan);
    

    $updated = CollectDebtSchedule::where('id', $lichThuPhiDangHachToan->id)
                       ->update([
                        'other_data' => json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
                       ]);

    // $this->metaData[] = ['label' => CollectDebtEnum::METADATA_THANH_TOAN_PHI_QH, 'value' => $soTienMangDiThanhToanPhiQuaHan];
    return $this->metaData;
  }
} // End class