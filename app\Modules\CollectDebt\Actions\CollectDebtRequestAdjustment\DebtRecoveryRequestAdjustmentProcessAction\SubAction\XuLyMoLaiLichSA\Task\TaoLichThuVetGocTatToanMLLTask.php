<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TaoLichThuVetGocTatToanMLLTask
{
  public function run(CollectDebtSchedule $lichDangMoLai, float $soTienNoGocConPhaiTra): CollectDebtSchedule
  {
    $otherData = json_encode([
      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'request_created_channel' => '',
        ],
        'note' => 'Thu vét tất toán nợ gốc'
      ]
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

    $scheduleParam = [
      'profile_id'           => $lichDangMoLai->profile_id,
      'contract_code'        => $lichDangMoLai->contract_code,
      'contract_type'        => $lichDangMoLai->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
      'debit_begin'          => $soTienNoGocConPhaiTra,
      'debit_end'            => 0,
      'rundate'              => now()->format('Ymd'),
      'time_start'           => now()->timestamp,
      'time_end'             => now()->endOfDay()->timestamp,
      'amount_period_debit'  => $lichDangMoLai->amount_period_debit,
      'request_amount_debit' => $soTienNoGocConPhaiTra,
      'success_amount_debit' => 0,
      'other_data'           => $otherData,
      'description'          => $lichDangMoLai->description,
      'is_settlement'        => $lichDangMoLai->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangMoLai->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangMoLai->cycle_number,
      'master_id'            => $lichDangMoLai->master_id,
    ];

    return CollectDebtSchedule::forceCreate($scheduleParam);
  }
} // End class
