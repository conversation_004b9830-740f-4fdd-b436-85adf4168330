<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Database\Eloquent\Collection;

class GomNhomYeuCauVaoLichThuTask
{
  /**
   * Hàm này sẽ gom nhóm các yêu cầu thu vào các lịch thu đã được SELECT
   *
   * @param Collection $collectDebtSchedules [Danh sách lịch thu]
   * @param Collection $collectDebtRequests [Danh sách yêu cầu thu của các lịch đó]
   *
   * @return Collection <CollectDebtSchedule> [Danh sách lịch thu đã được map các yêu cầu]
   */
  public function run(Collection $collectDebtSchedules, Collection $collectDebtRequests): Collection
  {
    return $collectDebtSchedules->transform(function (CollectDebtSchedule $collectDebtSchedule) use ($collectDebtRequests) {
      $collectDebtSchedule->requests = Collection::make();

      $collectDebtRequests->each(function (CollectDebtRequest $collectDebtRequest) use (&$collectDebtSchedule) {
        $planIdsOfRq = $collectDebtRequest->getPlanIds();
        if (in_array($collectDebtSchedule->id, $planIdsOfRq)) {
          $collectDebtSchedule->requests->push($collectDebtRequest);
        }

        return  $collectDebtRequest;
      });

      return $collectDebtSchedule;
    });
  }
} // End class