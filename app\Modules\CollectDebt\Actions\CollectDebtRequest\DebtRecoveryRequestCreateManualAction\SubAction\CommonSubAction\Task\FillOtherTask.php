<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;

class FillOtherTask
{
  public function run(DebtRecoveryRequestCreateManualRequest $request, $danhSachLichThuPhiDuocGiamTru=null)
  {
    $otherData = [];

    if ($request->isDeXuatGiamPhi()) {
      $otherData[] = [
        'type' => 'CREATE_REDUCE',
        'time_modified' => time(),
        'note' => $request->json('data.description', 'Tạo đề xuất giảm phí'),
        'data' => [
					'reduce_fee_amount' => $danhSachLichThuPhiDuocGiamTru->sum(function (CollectDebtSchedule $plan) {
						return $plan->getSoTienConPhaiThanhToan();
					})
        ]
      ];
    }

    if (!$request->isDeXuatGiamPhi()) {
      if ($request->json('data.payment_method_code') == 'MPOS') {
        if ($request->isTrichNgay()) {
          $otherData[] = [
            'type' => 'CREATE_DEBT_NOW',
            'time_modified' => time(),
            'note' => $request->json('data.description', 'Tạo yêu cầu trích ngay'),
            'data' => [
              'user' => $request->json('data.created_by')
            ]
          ];
        }

        if (!$request->isTrichNgay()) {
          $otherData[] = [
            'type' => 'CREATE_DEBT_MANUAL',
            'time_modified' => time(),
            'note' => $request->json('data.description', 'Tạo yêu cầu trích nợ thủ công'),
            'data' => [
              'user' => $request->json('data.created_by')
            ]
          ];
        }
      }

      if ($request->json('data.payment_method_code') != 'MPOS') {
        $otherData[] = [
          'type' => 'CREATE_DEBT_DOCUMENT',
          'time_modified' => time(),
          'note' => $request->json('data.description', 'Tạo yêu cầu trích nợ có chứng từ'),
          'data' => [
            'user' => $request->json('data.created_by')
          ]
        ];
      }
    } // End if không phải đề xuất giảm phí

    $otherData[] = [
      'type' => 'OTHER',
      'time_modified' => time(),
      'note' => 'Dữ liệu liên quan đến yêu cầu',
      'data' => [
        'manual_request_type' => $request->json('data.manual_request_type'),
        'manual_request_attachment' => $request->json('data.manual_request_attachment'),
        'is_debt_now' => $request->json('data.is_debt_now'),
      ]
    ];

    return json_encode($otherData, JSON_UNESCAPED_UNICODE);
  }
}
