<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class DanhDauLaLichDangHachToanTask
{
  public function run(CollectDebtSchedule $plan)
  {
    return CollectDebtSchedule::where('id', $plan->id)->update(['status' => CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN]);
  }
}
