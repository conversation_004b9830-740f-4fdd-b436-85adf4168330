<?php

return [
   
   'mpos_gateway_response_success' => '00',
   'mpos_gateway_response_waitting' => '90001',
   
   'mpos_gateway_reponse_name' => [
      '00' => 'SUCCESS',
      '01' => 'Input error',
      '02' => 'Channel error',
      '03' => 'IP invalid',
      '04' => 'Channel not exit or function error',
      '05' => 'Checksum invalid',
      '06' => 'EncData error',
      '07' => 'Config partner not exits',
      '99' => 'Error',
      '-46001' => 'Record had exsit',
      '90001' => 'Waitting for response partner',
      '-46005' => 'Debt is not exist',
      '-1' => 'Debt is not exist',
   ],

   'payment_channel' => [
      'mpos' => 'MPOS',
   ]
];