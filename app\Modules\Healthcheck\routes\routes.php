<?php
use Illuminate\Support\Facades\Route;
$namespace = 'App\Modules\Healthcheck\Controllers';
Route::group(['middleware' => ['checktoken'/*,'checkip'*/],'namespace' => $namespace ], function () {
    Route::get('healthcheck/liveness',['uses'=>'DefaultController@getLiveness'])->name('healthcheck.liveness');
    Route::get('healthcheck/readiness',['uses'=>'DefaultController@getReadiness'])->name('healthcheck.readiness');
});