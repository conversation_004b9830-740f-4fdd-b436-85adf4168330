<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtNotification\PushNotificationAction;

use Illuminate\Support\Str;
use App\Modules\CollectDebt\Model\CollectDebtNotification;

class PushNotificationAction
{
	public static function run($modelInstance, string $type, string $message = '', $data = [])
	{
		return @CollectDebtNotification::forceCreate([
			'id' => (string) Str::uuid(),
			'contract_code' => $modelInstance->contract_code,
			'type' => $type,
			'notifiable_type' => get_class($modelInstance),
			'notifiable_id' => $modelInstance->id,
			'group_icon' => 'fa fa-exclamation',
			'data' => [
				'message' => $message,
				'data' => $data
			]
		]);
	}
} // End class