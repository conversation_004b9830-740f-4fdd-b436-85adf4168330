<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction;

use App\Lib\ApiCall;
use App\Utils\CommonVar;
use Exception;

class GetTaiKhoanCoSoDuKhaDungSubAction {
	
	/**
	 * array:6 [
			"api_request_id" => "DEBT_03271331_8CC2qS"
			"success" => true
			"checksum" => "b3c8ba59c87eaa4f4968935624c6d326"
			"result_code" => 200
			"message" => "Request success"
			"data" => array:27 [
				"id" => "8701"
				"type" => "1"
				"profile_id" => "418"
				"balance" => "********"
				"freezing_balance" => "0"
				"s_pending_balance" => "0"
				"r_pending_balance" => "0"
				"currency" => "VND"
				"status" => "2"
				"end_blockade" => ""
				"lock_admin_user_id" => "0"
				"unlock_admin_user_id" => "0"
				"freeze_admin_user_id" => "0"
				"open_freeze_admin_user_id" => "0"
				"created_admin_user_id" => ""
				"updated_admin_user_id" => ""
				"deleted_admin_user_id" => ""
				"description" => ""
				"time_created" => ""
				"time_updated" => "**********"
				"time_locked" => ""
				"time_unlocked" => ""
				"time_actived" => ""
				"time_deleted" => ""
				"time_openfreeze" => ""
				"other_data" => ""
				"available_balance" => "********"
			]
		]
	 *
	 * @param string $profileId
	 * @return void
	 */
	public function run() {
    $payload = [
      'module' => CommonVar::API_PROFILE_MODULE,
      'path' => '/ProfileAccountsGetByAvailabelBalance',
      'params' => [],
      'method' => 'POST'
    ];

    $profileAccountResult = (new ApiCall())->callFunctionApi($payload, true);
		
		throw_if(
			empty($profileAccountResult['data']['id']),
			new Exception('Không tìm thấy tk thanh toán quá ví')
		);
		
		return $profileAccountResult;
	}
}