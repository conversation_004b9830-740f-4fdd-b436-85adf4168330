<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\NextlendCompany;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;
use Carbon\Carbon;

class EmailRemindDueDateSubAction
{

  public function run($collectEvent)
  {
    $dataResponse = '';

    $buildParams = $this->buildParams($collectEvent);
    if (!empty($buildParams)) {
      if ($buildParams['[hop_dong_loai]'] == CollectDebtEnum::GUIDE_HD_TRICH_KY) {
        $categoryCareCode = 'NOTIFY_CONTRACT_DUE_PERIOD';
      } else {
        $categoryCareCode = 'NOTIFY_CONTRACT_DUE';
      }
      $params = [
        'customer_category_care_code' => $categoryCareCode,
        'customer_service_care_code' => $collectEvent->service_care_code,
      ];
      $template = app(GetTemplateMailSubAction::class)->run($params);
      if (!empty($template)) {
        $mailContent = $template['content'];
        $mailContent = str_replace('%5', '[', $mailContent);
        $mailContent = str_replace('%5D', ']', $mailContent);
        foreach ($buildParams as $keyword => $value) {
          $mailContent = str_replace($keyword, $value, $mailContent);
        }
        $dataResponse = $mailContent;
      }
    }

    return $dataResponse;
  }

  protected function buildParams($collectEvent)
  {
    $otherData = json_decode($collectEvent->other_data, true);
    $data = json_decode($collectEvent->data, true);
    $merchant = isset($data['profile']) && isset($data['profile']['merchant']) ? $data['profile']['merchant'] : [];
    $contract = isset($data['contract']) ? $data['contract'] : [];
    $company = isset($data['company']) ? $data['company'] : [];
    $payment = isset($data['payment']) ? $data['payment'] : [];
    $summary = isset($otherData['summary']) ? $otherData['summary'] : [];
    $plans = isset($summary['other_data']) ? json_decode($summary['other_data'], true) : [];

    if (empty($merchant) && empty($contract) && empty($company) && empty($summary)) {
      return '';
    }

    $company = new NextlendCompany($company);

    $paymentVa = collect($payment)->where('other_data', '!=', '')->where('payment_method_code', 'VIRTUALACCOUNT')->map(function ($items) {
      return isset($items['other_data']) ? $items['other_data'] : '';
    })->first();

    $userAdmin = isset($contract['users_admin']) ? $contract['users_admin'] : [];
    $isDueDate = $this->__SAP_TOI_HAN($summary, $contract);

    if (!$isDueDate || $isDueDate['time'] < time()) {
      return '';
    }

    $planDueDate = [];
    $debtAmountEndPeriod = '';
    $feeOverPeriod = '';
    $amountPaymentMin = '';

    $planDueDate = collect($plans)->where('type', 'PLAN')->map(function ($items) use ($isDueDate) {
      if (isset($items['data'])) {
        $amountArr = [];
        $feeArr = [];
        $timeCycle = '';
        foreach ($items['data'] as $key => $amount) {
          if ($amount['time_cycle'] <= $isDueDate['time']) {
            if (isset($items['data'][$key]['amount']) && isset($items['data'][$key]['fee']) && isset($items['data'][$key]['amount_paid']) && isset($items['data'][$key]['fee_paid'])) {
              $totalAmount = ($items['data'][$key]['amount'] + $items['data'][$key]['fee']) - ($items['data'][$key]['amount_paid'] + $items['data'][$key]['fee_paid']);
              $amountArr[] = $totalAmount;
              $feeArr[] = $items['data'][$key]['fee'] - $items['data'][$key]['fee_paid'];
              $timeCycle = $items['data'][$key]['time_cycle'];
            }

            while ($key > 0) {
              $i = $key - 1;
              if (isset($items['data'][$i]['amount']) && isset($items['data'][$i]['fee']) && isset($items['data'][$i]['amount_paid']) && isset($items['data'][$i]['fee_paid'])) {
                $totalAmount = ($items['data'][$i]['amount'] + $items['data'][$i]['fee']) - ($items['data'][$i]['amount_paid'] + $items['data'][$i]['fee_paid']);
                $amountArr[] = $totalAmount;
                $feeArr[] = $items['data'][$i]['fee'] - $items['data'][$i]['fee_paid'];
              }
              if ($i == 0) {
                break;
              }
            }

            break;
          }
        }
        return [
          'total_amount' => array_sum($amountArr),
          'total_fee' => array_sum($feeArr),
          'time_cycle' => $timeCycle
        ];
      }
      return ['errors' => 'error'];
    })->first();


    if (isset($planDueDate['errors'])) {
      return '';
    }
    // số tiền còn nợ = số tiền tối thiều: (contract_amount + phí quá hạn + phí quá kỳ + số tiền hoàn"hoàn số tiền đã thu gốc + phí") - (total_amount_paid + total_fee_paid + fee_overdue_reduction + fee_overdue_cycle_reduction)
    // dư nợ cuối kỳ = số tiền còn nợ;
    $feeOverPeriod = $planDueDate['total_fee'];
    $amountPaymentMin = $planDueDate['total_amount'] - CaculateAmountSubAction::getTotalFeeReduction($summary);
    $debtAmountEndPeriod = CaculateAmountSubAction::getDebtAmountEndPeriod($summary);
    // dd($amountPaymentMin, $debtAmountEndPeriod);
    $businessRepresentative = isset($merchant['business_representative']) ? $merchant['business_representative'] : '';
    if ($summary['contract_type'] == CollectDebtEnum::GUIDE_HD_TRICH_KY) {
      $timeDueDate = date('d/m/Y', $planDueDate['time_cycle']);
    } else {
      $timeDueDate = date('d/m/Y', $isDueDate['time']);
    }

    $params = [
      '[hop_dong_khach_hang]' => isset($merchant['fullname']) ? $merchant['fullname'] : '',
      '[hop_dong_nguoi_dai_dien]' => $businessRepresentative,
      '[hop_dong_ma]' => isset($contract['contract_code']) ? $contract['contract_code'] : '',
      '[hop_dong_so_tien_ung]' => isset($contract['amount']) ? Helper::makeVndCurrency($contract['amount']) : '',
      '[hop_dong_so_ngay_vay]' => isset($contract['cycle']) ? $contract['cycle'] : '',
      '[hop_dong_tu_ngay]' => isset($contract['time_start']) && !empty($contract['time_start']) ? date('d/m/Y', $contract['time_start']) : '',
      '[hop_dong_den_ngay]' => isset($contract['time_end']) && !empty($contract['time_end']) ? Carbon::createFromTimestamp($contract['time_end'], 'UTC')->format('d/m/Y') : '',
      '[hop_dong_hinh_thuc_trich_no]' => isset($contract['intervals']) ? $contract['intervals'] : '',
      '[hop_dong_loai]' => isset($summary['contract_type']) ? $summary['contract_type'] : '',
      '[hop_dong_so_tien_hoan]' => isset($summary['total_amount_receiver']) ? Helper::makeVndCurrency($summary['total_amount_receiver']) : '',
      '[hop_dong_du_no_cuoi_ky]' => Helper::makeVndCurrency($debtAmountEndPeriod),
      '[hop_dong_phi_cham_ky]' => Helper::makeVndCurrency($feeOverPeriod),
      '[hop_dong_so_tien_toi_thieu]' => Helper::makeVndCurrency($amountPaymentMin),
      '[hop_dong_ngay_chu_ky_trich_no]' => $timeDueDate,
      '[hop_dong_stk_cong_ty]' => isset($company['company_bank_account_1']) ? $company['company_bank_account_1'] : '',
      '[hop_dong_ten_ngan_hang_cong_ty]' => isset($company['company_bank_name_1']) ? $company['company_bank_name_1'] : '',
      '[hop_dong_ten_chu_tk_cong_ty]' => isset($company['company_bank_holder_1']) ? $company['company_bank_holder_1'] : '',
      '[hop_dong_nd_chuyen_khoan]' => sprintf('NAP TIEN TK MC MA %s - %s', $company->getNoiDungChuyenKhoanMaNapTien(), $company->getCuPhapFullName($merchant)),
      '[hop_dong_ma_qr_va]' => isset($paymentVa['qrImage']) ? $paymentVa['qrImage'] : '',
      '[hop_dong_ten_ngan_hang_va_cong_ty]' => isset($paymentVa['payment_account_bank_code']) ? $paymentVa['payment_account_bank_code'] : '',
      '[hop_dong_stk_va_cong_ty]' => isset($paymentVa['payment_account_number']) ? $paymentVa['payment_account_number'] : '',
      '[hop_dong_ten_chu_tk_va_cong_ty]' => isset($paymentVa['payment_account_name']) ? $paymentVa['payment_account_name'] : '',
      '[hop_dong_nguoi_tao]' => isset($userAdmin['fullname']) ? $userAdmin['fullname'] : '',
      '[hop_dong_sdt_nguoi_tao]' => isset($userAdmin['mobile']) ? $userAdmin['mobile'] : '',
      '[hop_dong_email_nguoi_tao]' => isset($userAdmin['email']) ? $userAdmin['email'] : '',
      '[hop_dong_sdt_cong_ty]' => $company->getPhoneNumber(),
      '[hop_dong_email_cong_ty]' => $company->getEmail(),
      '[hop_dong_dia_chi_cong_ty]' => $company->getAddress(),
      '[hop_dong_website_1_cong_ty]' => isset($company['company_url_1']) ? $company['company_url_1'] : '',
      '[hop_dong_website_2_cong_ty]' => isset($company['company_url_2']) ? $company['company_url_2'] : '',
      '[hop_dong_ten_cong_ty]' => isset($company['company_fullname']) ? $company['company_fullname'] : '',
      '[hop_dong_ten_ngan_cong_ty]' => isset($company['company_subname']) ? $company['company_subname'] : '',
    ];
    return $params;
  }

  protected function __SAP_TOI_HAN($dataSummary, $contract)
  {
    $sapToiHanArr = [1, 3, 5]; // ngay toi han
    if ($contract) {
      if ($dataSummary['contract_type'] == CollectDebtEnum::GUIDE_HD_TRICH_KY) {
        $other_data = json_decode($dataSummary['other_data'], true);
        if ($other_data) {
          if ($contract['time_end'] > time()) {
            $timeEnd = $contract['time_end'];
            $origin = date_create(date('Y-m-d', $timeEnd));
            $target = date_create(date('Y-m-d'));
            $interval = date_diff($origin, $target);
            $days = $interval->format('%a');
            if (in_array($days, $sapToiHanArr)) {
              return [
                'time' => $timeEnd,
              ];
            }
          }

          foreach ($other_data as $key => $value) {
            if (isset($value['type']) && $value['type'] == 'PLAN') {
              foreach ($value['data'] as $key1 => $value1) {
                if ($value1['time_cycle'] > time()) {
                  $timeEnd = $value1['time_cycle'];
                  $origin = date_create(date('Y-m-d', $timeEnd));
                  $target = date_create(date('Y-m-d'));
                  $interval = date_diff($origin, $target);
                  $days = $interval->format('%a');
                  if (in_array($days, $sapToiHanArr)) {
                    return [
                      'time' => $timeEnd,
                    ];
                  }
                }
              }
            }
          }
        }
      } else {
        $timeEnd = $contract['time_end'];
        $origin = date_create(date('Y-m-d', $timeEnd));
        $target = date_create(date('Y-m-d'));
        $interval = date_diff($origin, $target);
        $days = $interval->format('%a');
        if (in_array($days, $sapToiHanArr)) {
          return [
            'time' => $timeEnd,
          ];
        }
      }
    }
    return false;
  }
} // End class
