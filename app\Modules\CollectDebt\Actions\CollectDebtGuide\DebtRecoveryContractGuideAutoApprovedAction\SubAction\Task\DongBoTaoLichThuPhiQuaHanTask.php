<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\Task;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class DongBoTaoLichThuPhiQuaHanTask
{
  public function run(CollectDebtSchedule $plan, CollectDebtGuide $collectDebtGuide, $phiQuaHan)
  {
    $collectDebtShare = CollectDebtShare::where('contract_code', $collectDebtGuide->contract_code)->first();
    
    $paramPlans = [
      'master_id'            => $plan->master_id,
      'cycle_number'         => $plan->cycle_number,
      'profile_id'           => $plan->profile_id,
      'contract_code'        => $plan->contract_code,
      'contract_type'        => $plan->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
      'debit_begin'          => $phiQuaHan,
      'debit_end'            => 0,
      'rundate'              => $plan->rundate,
      'time_start'           => $plan->time_start,
      'time_end'             => $plan->time_end,
      'amount_period_debit'  => $phiQuaHan,
      'request_amount_debit' => $phiQuaHan,
      'success_amount_debit' => 0,
      'is_settlement'        => $plan->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $plan->created_by,
      'time_created'         => time(),
      'other_data'           => json_encode([
        [
          'type' => 'OTHER',
          'data' => [
            'fee_config' => $collectDebtShare->getFeeByType(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN),
            'information_code' => 'SINH_PHI_QUA_HAN',
            'request_created_channel' => ''
          ],
          'time_modified' => time(),
          'note' => 'Thu phí quá hạn'
        ]
      ])
    ];

    $plan = CollectDebtSchedule::forceCreate($paramPlans);

    return $plan;
  }
}
