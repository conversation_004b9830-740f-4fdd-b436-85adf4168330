<?php

namespace App\Modules\Mock\Controllers;

use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Modules\Mock\Requests\StoreQuickRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\Mock\Enums\MockDataEnums;

class DefaultController extends \App\Http\Controllers\Controller
{

	public function index(Request $request)
	{
		$guides = CollectDebtGuide::orderBy('id', 'DESC')->paginate(30);
		return view('guide.index', [
			'guides' => $guides
		]);
	}

	public function create(Request $request)
	{
		return view('guide.create', [
			'randomContractCode' => sprintf('TEST-%s-L%s', strtoupper(Str::random(6)), rand(1, 100)),
			'ib_off_rand' => rand(********, 99999999)
		]);
	}

	public function store(StoreQuickRequest $request)
	{
		$params = $request->except('api_request_id');
		$params['contract_data'] = json_encode(MockDataEnums::contractData($params), JSON_UNESCAPED_UNICODE);
		$profileData = [
			'id' => $params['profile_id'],
			'merchant' => MockDataEnums::merchantData($request),
			'borrower' => MockDataEnums::borrowerData(),
		];
		$params['profile_data'] = json_encode($profileData, JSON_UNESCAPED_UNICODE);
		
		$paymentGuide = json_decode($params['payment_guide'], true);
		
		$paymentGuide = collect($paymentGuide)->transform(function ($item) {
			if ($item['payment_method_code'] == 'VIRTUALACCOUNT') {
				$item['other_data'] = MockDataEnums::infomationVa()['other_data'];
				$item['other_data']['payment_account_number'] = $item['payment_account_id'];
			}

			return $item;
		})->values()->toArray();

		$params['payment_guide'] = json_encode($paymentGuide, JSON_UNESCAPED_UNICODE);
		$params['other_data'] = '{"company": {"data": {"company_code": "VIMO", "company_url_1": "https://mpos.vn", "company_url_2": "https://nextlend.vn", "company_email_1": "<EMAIL>", "company_email_2": "", "company_subname": "Công ty Vi Mô", "company_fullname": "Công ty Cổ phần Công nghệ Vi Mô", "company_address_1": "Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam", "company_address_2": "", "company_bank_name_1": "Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai", "company_bank_holder_1": "Công ty Cổ phần Công nghệ Vi Mô", "company_bank_account_1": "************", "company_phone_number_1": "************", "company_phone_number_2": "", "company_product_code_1": "NEXTLEND", "company_product_group_1": "Next360"}, "note": "", "type": "OTHER", "time_modified": **********}, "overdue_cycle": {"02-01-2024": {"status": 1, "time_end": "03-01-2024", "debit_end": 0, "time_begin": "02-01-2024", "debit_begin": 5000000, "fee_deferred": 0, "amount_fee_minus": 0, "amount_debit_minus": 0, "fee_deferred_cycle": 0, "amount_period_debit": 5000000, "request_amount_debit": 5000000, "amount_current_outstanding": 0}, "18-12-2023": {"status": 2, "time_end": "20-12-2023", "debit_end": 0, "time_begin": "18-12-2023", "debit_begin": ********, "fee_deferred": 0, "amount_fee_minus": 0, "amount_debit_minus": 0, "fee_deferred_cycle": 105000, "amount_period_debit": 5000000, "request_amount_debit": 5105000, "amount_current_outstanding": ********}, "23-12-2023": {"status": 1, "time_end": "24-12-2023", "debit_end": 0, "time_begin": "23-12-2023", "debit_begin": 15000000, "fee_deferred": 0, "amount_fee_minus": 0, "amount_debit_minus": 0, "fee_deferred_cycle": 0, "amount_period_debit": 5000000, "request_amount_debit": 5000000, "amount_current_outstanding": 0}, "28-12-2023": {"status": 1, "time_end": "29-12-2023", "debit_end": 0, "time_begin": "28-12-2023", "debit_begin": ********, "fee_deferred": 0, "amount_fee_minus": 0, "amount_debit_minus": 0, "fee_deferred_cycle": 0, "amount_period_debit": 5000000, "request_amount_debit": 5000000, "amount_current_outstanding": 0}}, "total_account_payment": {"amount_debit": ********, "fee_deferred": 0, "amount_refund": 0, "fee_cycle_deferred": 105000, "discount_fee_deferred": null, "discount_fee_cycle_deferred": 0}}';

		unset($params['currency']);

		if (!empty($params['contract_partner'])) {
			$contractData = json_decode($params['contract_data'], true);
			$contractData['contract_partner'] = [
				"registrationId" => "f5a00a25f44544e7b9e747c1745474f4",
				"idNo" => "0101.02.005825",
				"signedTime" => "2025-04-16T14:30:24.*********",
				"contractRequestId" => "250416143015117444",
				"partner_code" => "TNEX"
			];

			$params['contract_data'] = json_encode($contractData, JSON_UNESCAPED_UNICODE);
			$params['partner_code'] = 'TNEX';
		}

		if (isset($params['contract_partner'])) {
			unset($params['contract_partner']);
		}

		$params['profile_id'] = $request->input('profile_id');
		$guide = CollectDebtGuide::forceCreate($params);

		return redirect()->to(config('app.url') . '/mock/detail-guide/' . $guide->id);
	}

	public function detail($id)
	{
		$guide = CollectDebtGuide::find($id);
		return view('guide.detail', ['guide' =>  $guide]);
	}
} // End class
