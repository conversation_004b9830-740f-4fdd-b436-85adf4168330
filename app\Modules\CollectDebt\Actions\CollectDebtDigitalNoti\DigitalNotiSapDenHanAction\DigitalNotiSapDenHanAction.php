<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiSapDenHanAction;

use App\Lib\Helper;
use Exception;
use Carbon\Carbon;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtDigitalTmp;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;

class DigitalNotiSapDenHanAction
{
	public array $listContractSapDenHan = [];

	public function run()
	{
		$collectDebtGuides = CollectDebtGuide::query()
			->whereHas('collectDebtSummary', function ($query) {
				return  $query->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
			})
			->where('contract_time_end', '>', now()->timestamp)
			->whereRaw("contract_time_end <= ? + (3 * 86400)", [now()->endOfDay()->timestamp])
			->where('contract_time_end', '>=', now()->addDay()->timestamp)
			->whereDoesntHave('collectDebtDigitalTmp', function ($query) {
				return $query->where('type', 'upcoming_deadline');
			})
			->get();

		if ($collectDebtGuides->isEmpty()) {
			return;
		}

		$collectDebtGuides->map(function (CollectDebtGuide $collectDebtGuide) {
			$soNgaySapDenHan = Carbon::parse($collectDebtGuide->time_end_as_date)->diffInDays(now());
			$collectDebtSummary = $collectDebtGuide->collectDebtSummary;
			
			$title = 'Nhắc đến hạn thanh toán';
			$body = sprintf('Khoản ứng %s của bạn sắp đến hạn thanh toán, vui lòng chuẩn bị trả nợ', $collectDebtSummary->contract_code);
			
			$content = view('trichno.noti.sap-den-han', [
				'soNgaySapDenHan' => $soNgaySapDenHan,
				'tongDuNo' => Helper::makeVndCurrency($collectDebtSummary->getTongTienConPhaiTra()),	
				'collectDebtGuide' => $collectDebtGuide,
			])->render();

			if ($soNgaySapDenHan == 0) {
				return;
			}

			$notifyData = CollectDebtDigitalNoti::buildNotiData(
				$title,
				$body,
				$content,
				'DIGITAL_SAPDENHANTHANHTOAN',
				$collectDebtGuide->getMposMcId(),
				'Hợp đồng sắp đến hạn'
			);

			$digitalNoti = CollectDebtDigitalNoti::query()->forceCreate([
				'contract_code' => $collectDebtGuide->contract_code,
				'type' => 'upcoming_deadline',
				'object_model' => CollectDebtGuide::class,
				'object_id' => $collectDebtGuide->id,
				'digital_request' => json_encode($notifyData),
				'status' => CollectDebtDigitalNoti::STT_DA_BUILD_PARAM,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp
			]);

			if (!$digitalNoti) {
				throw new Exception('Lỗi không build tạo được noti');
			}

			$digitalTmp = CollectDebtDigitalTmp::query()->forceCreate([
				'contract_code' => $collectDebtGuide->contract_code,
				'type' => 'upcoming_deadline',
				'channel' => 'NOTI',
			]);

			if (!$digitalTmp) {
				throw new Exception('Lỗi không tạo được noti tmp');
			}

			$this->listContractSapDenHan[] = $collectDebtGuide->contract_code;
			return $digitalNoti;
		});

		return $this->listContractSapDenHan;
	}
} // End class
