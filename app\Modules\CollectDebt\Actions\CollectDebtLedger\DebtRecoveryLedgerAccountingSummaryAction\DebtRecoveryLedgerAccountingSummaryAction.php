<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\SubAction\HandlerAccountingSummarySA;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\SubAction\GetLedgerNeedToAccountingSummarySA;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerSetStatusActionRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSetStatusAction\DebtRecoveryLedgerSetStatusAction;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummarySetByAccountingRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySetByAccounting\DebtRecoverySummarySetByAccounting;

class DebtRecoveryLedgerAccountingSummaryAction {

    public function run(CollectDebtLedger $ledgersWithTheirSchedules) {
			if (!$ledgersWithTheirSchedules) {
					$this->__ReturnError('Không có thông tin sổ cần hạch toán summary');
			}

			$process = $this->__process($ledgersWithTheirSchedules);
			
			if (!$process) {
					$this->__ReturnError('PROCESS ERROR');
			}

			return $this->__processData($ledgersWithTheirSchedules);
    }

    protected function __processData($ledgersWithTheirSchedules) {
        $inputs = $this->__setInputs($ledgersWithTheirSchedules);
        $process = $this->__processRequestInput($inputs);
        if (!$process) {
            $this->__fail($ledgersWithTheirSchedules);
            $this->__ReturnError('Cập nhật thông tin lỗi');
        } else {
            $this->__complete($ledgersWithTheirSchedules);
        }
        return $inputs;
    }

    protected function __setInputs($ledgersWithTheirSchedules) {
        /*
         * Tính ra dữ liệu rồi gọi vào phần tịnh tiến
         * Tổng tiền thu được
         * Tổng phí thu được 
         * Có giảm phí hay ko 
         * Tổng phí QH đã giảm 
         * Tổng phí QK đã giảm
         */
        $checkInfo = false;
        $summary = $ledgersWithTheirSchedules->getOtherDataItem('SUMMARY');

        $plan = [];
        if ($summary) {
//            var_dump($ledgersWithTheirSchedules->id);die();
            $info = $summary['data'];
            $contractCode = $ledgersWithTheirSchedules->contract_code;
            $totalAmount = $info['total_amount_paid'];
            $amountPaid = $info['amount_paid'];
            $feeOverdueCycle = $info['fee_overdue_cycle'];
            $feeOverdue = $info['fee_overdue'];
            $feeOverdueReduction = $info['fee_overdue_reduction'];
            $feeOverdueCycleReduction = $info['fee_overdue_cycle_reduction'];
            $feeOuduePaid = $info['fee_overdue_paid'];
            $feeCyclePaid = $info['fee_overdue_cycle_paid'];
            $totalAmountReceiver = $info['total_amount_receiver'];
            $totalAmountExcessRevenue = $info['total_amount_excess_revenue'];
            $totalAmountRefund = isset($info['total_amount_refund']) ? $info['total_amount_refund'] : '';
            $checkInfo = true;
            $plandata = $ledgersWithTheirSchedules->getOtherDataItem('PLAN');
            if ($plandata) {
                foreach ($plandata['data'] as $key1 => $value1) {
                    if(!$value1['master_id']) {
                        $master_id = $value1['id'];
                    }else {
                        $master_id = $value1['master_id'];
                    }
                    // tien ở đây làm tiền như thế nào
                    $plan[$master_id] = [
                        'id' => $master_id,
                        'amount' => 0,
                        'amount_paid' => 0,
                        'fee_paid' => 0,
                        'overdue_cycle' > 0,
                    ];
                }
            }
        }
        $totalFeePaid = ($feeOuduePaid + $feeCyclePaid);
        if (!$checkInfo) {
            $this->__ReturnError('Thông tin không đầy đủ không thể sử lý');
        }
        $inputs['data'] = [
						'ledger_id' => $ledgersWithTheirSchedules->id,
            'contract_code' => $contractCode,
            'total_amount_paid' => $amountPaid, // so tien thanh toan cho nợ gốc
            'total_fee_paid' => $totalFeePaid, // so phi đã trả
            'fee_overdue_paid' => $feeOuduePaid,
            'fee_overdue_cycle_paid' => $feeCyclePaid,
            'fee_overdue_reduction' => $feeOverdueReduction,
            'fee_overdue_cycle_reduction' => $feeOverdueCycleReduction,
            'fee_overdue_cycle' => $feeOverdueCycle,
            'fee_overdue' => $feeOverdue,
//            'data_over_cycle' => $data_over_cycle,
//            'data_giam_phi_qua_ky' => $data_giam_phi_qua_ky,
            'plan' => $plan,
            'total_amount_receiver' => $totalAmountReceiver,
            'total_amount_excess_revenue' => $totalAmountExcessRevenue,
            'total_amount_refund' => $totalAmountRefund,
        ];
        return $inputs;
    }

    protected function __processRequestInput($inputs) {
        $requestData = new DebtRecoverySummarySetByAccountingRequest();
        $requestData->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoverySummarySetByAccounting())->run($requestData);
        if (get_class($update) == \App\Modules\CollectDebt\Model\CollectDebtSummary::class) {
            return true;
        }
        return false;
    }

    protected function __ReturnError($code) {
        throw new Exception($code);
    }

    protected function __process($ledgersWithTheirSchedules) {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_summary' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

    protected function __complete($ledgersWithTheirSchedules) {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_summary' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

    protected function __fail($ledgersWithTheirSchedules) {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_summary' => CollectDebtEnum::LEDGER_STT_ACTION_LOI,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

}

// End class
