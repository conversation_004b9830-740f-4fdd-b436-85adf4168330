<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEmail\DebtRecoveryEmailGetByContractAction;

use Carbon\Carbon;
use App\Lib\NextlendCore;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class DebtRecoveryEmailGetByContractAction
{
  public function run(Request $request)
  {
    $limit = $request->json('data.limit', 10);
    $payload = [
      'limit' => $limit,
      'condition' => 1,
      'start' => ($request->json('data.page', 1) - 1) * $limit,
      'field' => "
        id, 
        source, 
        partner_code, 
        partner_data, 
        category_care_code, 
        category_care_data, 
        request_id, 
        content_type, 
        title, 
        attachment,
        sender_info,
        receiver_list,
        maximum_number_submissions,
        number_run,
        customer_message_id,
        description,
        status,
        created_by,
        time_created,
        time_failure,
        time_canceled,
        time_approved,
        time_request_approved,
        time_updated
      ",
      'orderby' => 'time_created desc',
      'groupby' => '',
      'as_like' => [
        'request_id' => $request->json('data.contract_code', 'TEST_240126_LHYE1'),
      ],
      'user_id_request' => 4,
      'export' => false
    ];

    $query = [];

    if (!empty($request->json('data.category_care_code'))) {
      $cates = array_map(function ($item) {
        return "'" . $item . "'";
      }, $request->json('data.category_care_code'));

      $categoryEmail = implode(', ', $cates);
      $query[] = sprintf("category_care_code IN(%s)", $categoryEmail);
    }


    $createFrom = $request->json('data.create_from');
    $createTo = $request->json('data.create_to');

    if (!empty($createFrom)) {
      $from = Carbon::createFromFormat('d-m-Y', $createFrom)->startOfDay()->timestamp;
      $query[] = sprintf('time_created >= %s', $from);
    }

    if (!empty($createTo)) {
      $to = Carbon::createFromFormat('d-m-Y', $createTo)->endOfDay()->timestamp;
      $query[] = sprintf('time_created <= %s', $to);
    }

    if (!empty($query)) {
      $payload['condition'] .= ' AND ' . implode('  AND ', $query);
    }

    $payload['condition'] = trim($payload['condition']);
    
    $nextlendCore = app(NextlendCore::class)->callRequest($payload, 'CustomerRequestCare_getPageData', 'get');

    $result = $nextlendCore->decryptData();

    $data = $result['data'];
    
    // Current page
    $currentPage = $request->json('data.page', 1);

    // Slice the array based on the current page and per page limit
    $currentItems = $data;

    // Create a LengthAwarePaginator instance
    $paginator = new LengthAwarePaginator($currentItems, $result['rows'], $limit, $currentPage);

    return $paginator;
  }
} // End class