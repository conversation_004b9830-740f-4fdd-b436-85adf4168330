<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryRequestListOfTodayRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * 
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.contract_code' => ['required', 'string', 'max:50'],
    ];
  }
} // End class
