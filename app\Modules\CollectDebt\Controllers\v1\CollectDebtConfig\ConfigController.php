<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtConfig;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;


class ConfigController extends Controller
{
	public function index(Request $request)
	{
		try {
      $configs = [
				'lending_cashin' => [
					'cashin' => [
						'status' => [
							['id' => 0, 'name' => 'Không có phiếu thu'],
							['id' => 1, 'name' => 'Mới tạo'],
							['id' => 2, 'name' => 'Đã duyệt'],
							['id' => 3, 'name' => 'Đang xử lý'],
							['id' => 4, 'name' => 'Đã từ chối'],
							['id' => 5, 'name' => 'Đã nạp tiền vào ví'],
						]
					],

					'cashin_request' => [
						'status' => [
							['id' => 1, 'name' => 'Mới tạo'],
							['id' => 2, 'name' => 'Đã duyệt'],
							['id' => 3, 'name' => 'Đang xử lý'],
							['id' => 4, 'name' => 'Đã từ chối'],
							['id' => 5, 'name' => 'Đã tạo phiếu thu'],
						]
					],
				],
				'lending_profile' => [
					'profile_account' => [
						'status' => [
							['id' => 1, 'name' => 'Mới tạo'],
							['id' => 2, 'name' => 'Đang hoạt động'],
							['id' => 3, 'name' => 'Đã khóa'],
							['id' => 4, 'name' => 'Đã xóa'],
						],

						'type' => [
							['id' => 1, 'name' => 'TK MASTER'],
							['id' => 2, 'name' => 'TK KHUYẾN MẠI'],
						]
					],
				],
				'payment_method_code' => [
					['id' => 'MPOS', 'name' => 'MPOS'],
					['id' => 'IB_OFF', 'name' => 'Chuyển khoản ngoài'],
					['id' => 'VIRTUALACCOUNT', 'name' => 'VIRTUALACCOUNT'],
					['id' => 'WALLET', 'name' => 'Ví Merchant'],
				],

        'guides' => [
          'status' => [
            [
              'id' => CollectDebtEnum::GUIDE_STT_MOI_TAO,
              'name' => 'Chưa duyệt',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::GUIDE_STT_DA_DUYET,
              'name' => 'Đã duyệt',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::GUIDE_STT_DANG_TAO_LICH,
              'name' => 'Đang tạo lịch',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::GUIDE_STT_DA_TU_CHOI,
              'name' => 'Đã từ chối',
              'color' => '',
              'bg_color' => ''
            ],

            // [
            //   'id' => CollectDebtEnum::GUIDE_STT_DA_THU_HET_TIEN,
            //   'name' => 'Đã thu hết tiền',
            //   'color' => '',
            //   'bg_color' => ''
            // ],

            [
              'id' => CollectDebtEnum::GUIDE_STT_DA_TAO_LICH_THANH_CONG,
              'name' => 'Đã tạo lịch',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          'status_contract' => [
            [ 'id' => 1, 'name' => 'Chờ duyệt', 'class' => 'badge bg-gray' ],
            [ 'id' => 2, 'name' => 'Đã duyệt', 'class' => 'badge bg-orange' ],
            [ 'id' => 3, 'name' => 'Từ chối', 'class' => 'badge bg-red' ],
            [ 'id' => 4, 'name' => 'Đã tạo yêu cầu giải ngân', 'class' => 'badge bg-primary' ],
            [ 'id' => 5, 'name' => 'Đã duyệt yêu cầu giải ngân', 'class' => 'badge bg-warning' ],
            [ 'id' => 6, 'name' => 'Đã giải ngân', 'class' => 'badge bg-green' ],
            [ 'id' => 7, 'name' => 'Chờ thẩm định', 'class' => 'badge bg-red' ],
            [ 'id' => 8, 'name' => 'Hồ sơ nháp', 'class' => 'badge bg-yellow' ],
            [ 'id' => 9, 'name' => 'Hồ sơ sai thông tin', 'class' => 'badge bg-purple' ],
          ],

          'fee_config' => [
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_HOP_DONG,
              'name' => 'Phí hợp đồng',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_THAM_DINH_HO_SO,
              'name' => 'Phí thẩm định hồ sơ',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN,
              'name' => 'Phí quá hạn',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_UU_DAI_THAM_DINH_HO_SO,
              'name' => 'Phí ưu đãi thẩm định hồ sơ',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_GIAI_NGAN,
              'name' => 'Phí giải ngân',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_THU_HOI,
              'name' => 'Phí thu hồi',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_GIA_HAN,
              'name' => 'Phí gia hạn',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY,
              'name' => 'Phí chậm kỳ',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_UU_DAI_PHI_THAM_GIA,
              'name' => 'Ưu đãi phí tham gia',
            ],
            [
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_HOAN,
              'name' => 'Phí hoàn',
            ],
						[
              'id' => CollectDebtEnum::GUIDE_LOAI_PHI_PHAT_TRA_CHAM,
              'name' => 'Phí phạt trả chậm',
            ],
          ],

          'type' => [
            [
              'id' => CollectDebtEnum::GUIDE_HD_TRICH_NGAY,
              'name' => 'Trích ngày'
            ],
            [
              'id' => CollectDebtEnum::GUIDE_HD_GIA_HAN,
              'name' => 'Gia hạn'
            ],
            [
              'id' => CollectDebtEnum::GUIDE_HD_TRICH_KY,
              'name' => 'Trích theo chu kỳ'
            ],
          ]
        ],

        'schedules' => [
          'contract_type' => [
            [
              'id' => CollectDebtEnum::SCHEDULE_LOAI_HD_THUONG,
              'name' => 'Hợp đồng khoản ứng theo ngày'
            ],

            [
              'id' => CollectDebtEnum::SCHEDULE_LOAI_HD_GIA_HAN,
              'name' => 'Hợp đồng gia hạn'
            ],

            [
              'id' => CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY,
              'name' => 'Hợp đồng khoản ứng chu kỳ'
            ],
          ],

          'type' => [
            [
              'id' => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
              'name' => 'Thu chính'
            ],

            [
              'id' => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
              'name' => 'Thu phụ'
            ],
          ],

          'isfee' => [
            [
              'id' => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
              'name' => 'Thu phí'
            ],
            [
              'id' => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
              'name' => 'Thu gốc'
            ],
          ],

          'status' => [
            [
              'id' => CollectDebtEnum::SCHEDULE_STT_MOI,
              'name' => 'Mới tạo',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN,
              'name' => 'Đang hạch toán',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH,
              'name' => 'Đã hoàn thành',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::SCHEDULE_STT_DA_HUY,
              'name' => 'Đã hủy',
              'color' => '',
              'bg_color' => ''
            ],
          ],
					
					'process' => [
						[
              'id' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY,
              'name' => 'Chưa xử lý',
              'color' => '',
              'bg_color' => ''
            ],

						[
              'id' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY,
              'name' => 'Đang xử lý',
              'color' => '',
              'bg_color' => ''
            ],

						[
              'id' => CollectDebtEnum::SCHEDULE_PROCESS_DA_XU_LY,
              'name' => 'Đã xử lý',
              'color' => '',
              'bg_color' => ''
            ],
					]
        ],

        'requests' => [
          'type' => [
            [
              'id' => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
              'name' => 'Type thanh toán trích nợ'
            ],
          ],

          'is_payment' => [
            [
              'id' => CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT,
              'name' => 'Có gửi y/c sang đối tác thu hồi'
            ],

            [
              'id' => CollectDebtEnum::REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT,
              'name' => 'Không gửi y/c sang đối tác thu hồi'
            ],
          ],

          'status' => [
            [
              'id' => CollectDebtEnum::REQUEST_STT_MOI_TAO,
              'name' => 'Mới tạo',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_DA_DUYET,
              'name' => 'Đã duyệt',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
              'name' => 'Đã hoàn thành',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_TU_CHOI,
              'name' => 'Đã từ chối',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA,
              'name' => 'Cần hoàn thành và kiểm tra',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_DA_DUYET_GIAM_PHI_CAP_1,
              'name' => 'Duyệt giảm phí cấp 1',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_DA_DUYET_GIAM_PHI_CAP_2,
              'name' => 'Duyệt giảm phí cấp 2',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          'status_payment' => [
            [
              'id' => CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI,
              'name' => 'Chưa gửi',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::REQUEST_STT_PM_DANG_GUI,
              'name' => 'Đang gửi',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::REQUEST_STT_PM_TU_CHOI,
              'name' => 'Từ chối',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::REQUEST_STT_PM_GUI_LOI,
              'name' => 'Gửi thanh toán lỗi',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_PM_DA_GUI,
              'name' => 'Đã gửi',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA,
              'name' => 'Đã nhận kết quả',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          'status_recored' => [
            [
              'id' => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO,
              'name' => 'Chưa ghi sổ',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO,
              'name' => 'Đang ghi sổ',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO,
              'name' => 'Đã ghi sổ',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::REQUEST_STT_RC_GHI_SO_LOI,
              'name' => 'Ghi sổ lỗi',
              'color' => '',
              'bg_color' => ''
            ],
          ],
          
          'manual_request_type' => [
            [ 'id' => 'TRICH_MOT_PHAN', 'name' => 'Trích một phần' ],
            [ 'id' => 'TAT_TOAN', 'name' => 'Tất toán' ],
            [ 'id' => 'GIAM_PHI', 'name' => 'Giảm phí' ],
					],

					'automatic_pause_debit' => [
						'duration' => ['id' => CollectDebtEnum::SO_PHUT_DUNG_JOB_TRICH_TU_DONG, 'name' => 'phút']
					]
        ],

        'partners' => [
          'request_exists' => [
            [
              'id' => CollectDebtEnum::PARTNER_REQUEST_DA_CO_YC_THANH_TOAN,
              'name' => 'Đã có y/c thanh toán'
            ],

            [
              'id' => CollectDebtEnum::PARTNER_REQUEST_CHUA_CO_YC_THANH_TOAN,
              'name' => 'Chưa có y/c thanh toán'
            ],
          ],

          'status' => [
            [
              'id' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
              'name' => 'Chưa xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::PARTNER_STT_DANG_XU_LY,
              'name' => 'Đang xử lý',
              'color' => '',
              'bg_color' => ''
            ],

            [
              'id' => CollectDebtEnum::PARTNER_STT_DA_XU_LY,
              'name' => 'Đã xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::PARTNER_STT_XU_LY_LOI,
              'name' => 'Xử lý lỗi',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::PARTNER_STT_CHO_DUYET,
              'name' => 'Chờ duyệt',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::PARTNER_STT_DA_TU_CHOI,
              'name' => 'Đã từ chối',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          'status_refund' => [
            ['id' => 1, 'name' => 'Chưa hoàn'],
            ['id' => 2, 'name' => 'Đang xử lý'],
            ['id' => 3, 'name' => 'Đã hoàn tiền'],
            ['id' => 4, 'name' => 'Xử lý lỗi'],
          ],

          'partner_refund' => [
            ['id' => 'MPOS', 'name' => 'MPOS'],
            ['id' => 'IB_OFF', 'name' => 'IB_OFF (Chuyển khoản ngoài)'],
            ['id' => 'VIRTUALACCOUNT', 'name' => 'VIRTUALACCOUNT'],
          ]
        ], 
        
        'ledgers' => [
          'status' => [
            [
              'id' => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY,
              'name' => 'Chưa xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN,
              'name' => 'Đang hạch toán',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN,
              'name' => 'Đã hạch toán',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_DA_TU_CHOI,
              'name' => 'Đã từ chối',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_KHONG_PHAI_XU_LY,
              'name' => 'Không phải xử lý',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          'status_plan' => [
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY,
              'name' => 'Không xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
              'name' => 'Chưa xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
              'name' => 'Đang xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
              'name' => 'Đã xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_LOI,
              'name' => 'Xử lý lỗi',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          'status_summary' => [
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY,
              'name' => 'Không xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
              'name' => 'Chưa xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
              'name' => 'Đang xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
              'name' => 'Đã xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_LOI,
              'name' => 'Xử lý lỗi',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          'status_email' => [
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY,
              'name' => 'Không xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
              'name' => 'Chưa xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
              'name' => 'Đang xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
              'name' => 'Đã xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_LOI,
              'name' => 'Xử lý lỗi',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          'status_sms' => [
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY,
              'name' => 'Không xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
              'name' => 'Chưa xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
              'name' => 'Đang xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
              'name' => 'Đã xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_LOI,
              'name' => 'Xử lý lỗi',
              'color' => '',
              'bg_color' => ''
            ],
          ],

          
          'status_contract' => [
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY,
              'name' => 'Không xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
              'name' => 'Chưa xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
              'name' => 'Đang xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
              'name' => 'Đã xử lý',
              'color' => '',
              'bg_color' => ''
            ],
            [
              'id' => CollectDebtEnum::LEDGER_STT_ACTION_LOI,
              'name' => 'Xử lý lỗi',
              'color' => '',
              'bg_color' => ''
            ],
          ],
        ],

        'setting' => [
          'type' => [
            [
              'id' => 1,
              'name' => 'Thuộc tính - Giá trị',
            ],
            [
              'id' => 2,
              'name' => 'Bật / Tắt',
            ]
          ],

          'status' => [
            [ 'id' => 1, 'name' => 'Đang chạy', ],
            [ 'id' => 2, 'name' => 'Đã dừng', ]
          ],

          'daily_run' => [
            [ 'id' => 'Sun', 'name' => 'Chủ nhật', ],
            [ 'id' => 'Mon', 'name' => 'Thứ hai', ],
            [ 'id' => 'Tue', 'name' => 'Thứ ba', ],
            [ 'id' => 'Wed', 'name' => 'Thứ tư', ],
            [ 'id' => 'Thu', 'name' => 'Thứ năm', ],
            [ 'id' => 'Fri', 'name' => 'Thứ sáu', ],
            [ 'id' => 'Sat', 'name' => 'Chủ nhật', ],
          ]
        ],

        'request_adjustment' => [
          'status' => [
            ['id' => 1, 'name' => 'Mới tạo'],
            ['id' => 2, 'name' => 'Đã duyệt 1'],
            ['id' => 3, 'name' => 'Đã duyệt 2'],
            ['id' => 4, 'name' => 'Từ chối'],
            ['id' => 5, 'name' => 'Đang xử lý'],
            ['id' => 6, 'name' => 'Đã xử lý'],
          ],

          'partner' => [
            ['id' => 'MPOS', 'name' => 'MPOS'],
            ['id' => 'IB_OFF', 'name' => 'IB_OFF (Chuyển khoản ngoài)'],
            ['id' => 'VIRTUALACCOUNT', 'name' => 'VIRTUALACCOUNT'],
          ]
				],

				'contract_event' => [
          'status' => [
            ['id' => 1, 'name' => 'Chưa xử lý'],
            ['id' => 2, 'name' => 'Đang tạo content'],
            ['id' => 3, 'name' => 'Đã tạo content'],
            ['id' => 4, 'name' => 'Đang gửi yêu cầu'],
            ['id' => 5, 'name' => 'Gửi event thành công'],
            ['id' => 6, 'name' => 'Tạo yêu cầu lỗi'],
            ['id' => 7, 'name' => 'Đã từ chối'],
          ],

					'service_code' => [
						[ 'code' => 'MAIL', 'name' => 'Gửi mail' ],
						[ 'code' => 'SMS', 'name' => 'Gửi SMS' ],
						[ 'code' => 'ZALO', 'name' => 'Gửi Zalo' ],
						[ 'code' => 'SYNC', 'name' => 'Đồng bộ' ],
					]
				],

				'collect_debt_summary' => [
					'status_contract' => [
						['id' => CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN, 'name' => 'Chưa tất toán'],
						['id' => CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN, 'name' => 'Đã tất toán'],
						['id' => CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DANG_TAM_DUNG, 'name' => 'Đang tạm dừng'],
					]
				]
      ];

			return $this->successResponse($configs, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

  
} // End class
