<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoverySendBaoCaoTrichNoTnexAction;

use Exception;
use App\Lib\Helper;
use App\Lib\NextlendCore;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtTnexTransaction;

class DebtRecoverySendBaoCaoTrichNoTnexAction
{
	private array $__gdDaXuLy = [];

	private array $__exceptTranIds = [];

	public function initSendBaoCao()
	{
		for ($i = 1; $i <= 30; $i++) {
			try {
				$collectDebtReportTnex = $this->run();

				if ($collectDebtReportTnex == 'EMPTY') {
					$this->__gdDaXuLy[] = 'Khong co yc nao can day sang tnex';
					break;
				}

				if ($collectDebtReportTnex && optional($collectDebtReportTnex)->id) {
					$this->__gdDaXuLy[] = $collectDebtReportTnex->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi dong bo giao dich' => Helper::traceError($th)]);
				@TelegramAlert::sendAccouting(Helper::traceError($th));
				continue;
				// throw $th; // sau phai bo dong nay
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $this->__gdDaXuLy;
	}

	public function run()
	{
		try {
			$transaction = CollectDebtTnexTransaction::query()
				->where('status', CollectDebtTnexTransaction::MOI_TAO)
				->first();

			if (!$transaction) {
				return 'EMPTY';
			}

			$this->__exceptTranIds[] = $transaction->id;

			// Update to processing status
			$updated = CollectDebtTnexTransaction::query()
				->where('id', $transaction->id)
				->where('status', CollectDebtTnexTransaction::MOI_TAO)
				->update([
					'status' => CollectDebtTnexTransaction::DANG_XU_LY,
					'time_updated' => now()->timestamp
				]);

			if (!$updated) {
				throw new Exception('Khong the cap nhat thanh dang xu ly');
				return;
			}

			// Build params for API call
			$params = [
				'partner_code' => $transaction->partner_code,
				'contract_code' => $transaction->contract_code,
				'transaction_id' => $transaction->transaction_id,
				'amount' => $transaction->amount,
				'type' => 'PAY',
				'description' => ''
			];

			// Call partner API
			$response = app(NextlendCore::class)->callRequest(
				$params,
				'ContractV3_notiPartnerDebtRecovery',
				'POST'
			);

			$data = $response->decryptData(true);
			
			if (isset($data['errorCode']) && strtoupper($data['errorCode']) == 'SUCCESS') {
				// Cap nhat thanh cong
				$wasSuccessed = CollectDebtTnexTransaction::query()
																									->where('id', $transaction->id)
																									->where('status', CollectDebtTnexTransaction::DANG_XU_LY)
																									->update([
																										'status' => CollectDebtTnexTransaction::DA_DONG_BO,
																										'time_updated' => now()->timestamp,
																										'other_data' => json_encode($data),
																									]);
				if (!$wasSuccessed) {
					throw new Exception('Khong the cap nhat ve trang thai da dong bo');
				}

				return $transaction;
			}else {
				$wasFailed = CollectDebtTnexTransaction::query()
																									->where('id', $transaction->id)
																									->where('status', CollectDebtTnexTransaction::DANG_XU_LY)
																									->update([
																										'status' => CollectDebtTnexTransaction::DONG_BO_THAT_BAI,
																										'time_updated' => now()->timestamp,
																										'other_data' => json_encode($data),
																									]);
				if (!$wasFailed) {
					throw new Exception('Khong the cap nhat ve trang thai dong bo that bai');
				}

				return $transaction;
			}
		} catch (\Throwable $th) {
			mylog(['[ERROR]' => Helper::traceError($th)]);

			$wasUpdateToNew = CollectDebtTnexTransaction::query()
			->where('id', $transaction->id)
			->where('status', CollectDebtTnexTransaction::DANG_XU_LY)
			->update([
				'status' => CollectDebtTnexTransaction::MOI_TAO,
				'time_updated' => now()->timestamp,
				'other_data' => json_encode(@$data ?? ''),
			]);

			@TelegramAlert::sendAccouting(Helper::traceError($th));

			throw $th;
		}
	}
} // End class