<?php

namespace App\Modules\Mock\Enums;

use Carbon\Carbon;

class MockDataEnums
{
    public static function merchantData($request)
    {
    
        return [
            'id'    =>    421,
            'cid'    =>    '',
            'sync'    =>    '',
            'email'    =>    '<EMAIL>',
            'phone'    =>    '',
            'mobile'    =>    '***********',
            'source'    =>    1,
            'status'    =>    2,
            'address'    =>    'Phuc DOng, Long Bien, Ha Noi',
            'city_id'    =>    11871,
            'sale_id'    =>    '',
            'address1'    =>    '',
            'address2'    =>    '',
            'checksum'    =>    '9f31ed96dacda674d895b7a9b3dd1908',
            'fullname'    =>    'Thanh Trần Coffee',
            'latitude'    =>    '',
            'passport'    =>    '08877666',
            'amount_to'    =>    '',
            'issued_by'    =>    'Ha Noi Viet Nam',
            'longitude'    =>    '',
            'detail_fee'    =>    '',
            'issue_date'    =>    '31-01-2019',
            'loan_money'    =>    10000000,
            'partner_id'    =>    1,
            'profile_id'    =>    978976865756765,
            'sale_email'    =>    '',
            'short_name'    =>    'TTC',
            'amount_from'    =>    '',
            'description'    =>    '',
            'district_id'    =>    11935,
            'status_care'    =>    1,
            'time_locked'    =>    '',
            'wait_active'    =>    2,
            'belong_group'    =>    4,
            'detail_trans'    =>    '',
            'evaluate_max'    =>    '',
            'evaluate_min'    =>    '',
            'time_actived'    =>    '',
            'time_created'    =>    '**********',
            'time_deleted'    =>    '',
            'time_updated'    =>    '1696833289',
            'status_action'    =>    1,
            'time_handover'    =>    '',
            'time_unlocked'    =>    '',
            'advance_number'    =>    5,
            'certificate_id'    =>    '**********',
            'converted_data'    =>    '',
            'number_updated'    =>    '',
            'operation_year'    =>    '2019',
            'sale_region_id'    =>    '',
            'sync_mynextpay'    =>    1,
            'users_admin_id'    =>    4,
            'average_revenue'    =>    '',
            'avg_month_count'    =>    12,
            'avg_trans_value'    =>    1000000,
            'condition_valid'    =>    2,
            'avg_trans_number'    =>    10,
            'belong_group_new'    =>    '',
            'business_address'    =>    '',
            'business_type_id'    =>    1,
            'evaluate_to_time'    =>    '',
            'evaluation_month'    =>    '',
            'business_areas_id'    =>    9,
            'time_set_profiled'    =>    '',
            'evaluate_from_time'    =>    '',
            'locked_admin_user_id'    =>    '',
            'partner_payment_code'    =>    '',
            'time_partner_actived'    =>    '1548953999',
            'actived_admin_user_id'    =>    '',
            'created_admin_user_id'    =>    '',
            'deleted_user_admin_id'    =>    '',
            'partner_merchant_code'    =>    1801,
            'status_create_profile'    =>    '',
            'updated_admin_user_id'    =>    '',
            'contract_user_admin_id'    =>    '',
            'unlocked_admin_user_id'    =>    '',
            'business_representative'    =>    'visa tu',
            'contract_past_number_day'    =>    '',
            'time_updated_status_action'    =>    '',
            'transaction_count_per_month'    =>    '',
        ];
    }

    public static function contractData($params) {
        return [
            'id'      => $params['id'] ?? 1, 
            'type'    =>    $params['contract_type'],
            'cycle'    =>    $params['contract_cycle'],
            'amount'    =>    $params['amount'],
            'number'    =>    $params['contract_intervals'],
            'signed'    =>    1,
            'source'    =>    1,
            'status'    =>    6,
            'content'    =>    'aaaaa',
            'urlfile'    =>    '',
            'version'    =>    3,
            'currency'    =>    $params['currency'],
            'deferred'    =>    2,
            'fee_data'    =>    json_encode([
                'contract_fee' => [
                    'contract_fee_id' => '20230224092807',
                    'package_detail_id' => '1',
                    'percent_fee' => '2',
                    'flat_fee' => '0',
                    'overdue_flat_fee' => '0',
                    'overdue_percent_fee' => '0.3',
                    'flat_fee_cycle' => '100000',
                    'percent_fee_cycle' => '0.1',
                ],
                'promotion_data' => [],
                'contract_fee_incentive' => [],
                'amount_fee_evaluation' => [
                    'percent_fee' => '1',
                    'flat_fee' => '0',
                ],
                'contract_fee_evaluation_incentive' => [],
                'package_name' => 'Gói vay nhóm 1,2,3',
                'contract_not_fee_intervals' => [
                    '20231007' => '20231007'
                ]
            ], JSON_UNESCAPED_UNICODE),
            'finished'    =>    2,
            'flat_fee'    =>    '',
            'linkfile'    =>    '',
            'location'    =>    '',
            'time_end'    =>    $params['contract_time_end'],
            'time_end_as_date'    => Carbon::createFromTimestamp($params['contract_time_end'])->format('d-m-Y H:i:s'),
            'intervals'    =>    $params['contract_intervals'],
            'number_run'    =>    '',
            'partner_id'    =>    1,
            'profile_id'    =>    (int) $params['profile_id'],
            'renew_type'    =>    1,
            'time_start'    =>    $params['contract_time_start'],
            'time_start_as_date'    =>    Carbon::createFromTimestamp($params['contract_time_start'])->format('d-m-Y H:i:s'),
            'borrower_id'    =>    438,
            'contract_id'    =>    '',
            'merchant_id'    =>    421,
            'percent_fee'    =>    2,
            'report_data'    =>    '',
            'status_debt'    =>    4,
            'url_content'    =>    '',
            'users_admin' => [
                'email'    =>    '<EMAIL>',
                'mobile'    =>    '84356879134',
                'fullname'    =>    'Administrator',
            ],
						'user_action' => [
							'merchant' => [
								[
									'id' => 14,
									'name' => 'Telesales',
									'code' => 'NV_TELE_SALE',
									'user' => [
										[
											'id' => 172,
											'username' => 'loanht',
											'email' => '<EMAIL>',
											'mobile' => '0985371625',
											'fullname' => "Hà Thị Loan",
											'address' => '',
										]
									]
								]
							],

							'contract' => [
								[
									'id' => 14,
									'name' => 'Telesales',
									'code' => 'NV_TELE_SALE',
									'user' => [
										[
											'id' => 172,
											'username' => 'loanht',
											'email' => '<EMAIL>',
											'mobile' => '0985371625',
											'fullname' => "Hà Thị Loan",
											'address' => '',
										]
									]
								]
							]
						],
            'fee_deferred'    =>    1,
            'investors_id'    =>    '',
            'promotion_id'    =>    '',
            'status_query'    =>    1,
            'status_renew'    =>    2,
            'time_created'    =>    1696574658,
            'time_queried'    =>    '',
            'time_renewed'    =>    '',
            'time_updated'    =>    1696833288,
            'active_reason'    =>    'ttttttt',
            'amount_refund'    =>    '',
            'contract_code'    =>    $params['contract_code'],
            'reject_reason'    =>    '',
            'status_refund'    =>    1,
            'time_finished'    =>    1696583818,
            'time_rejected'    =>    '',
            'time_verified'    =>    1696574857,
            'user_admin_id'    =>    4,
            'converted_data'    =>    '',
            'percent_refund'    =>    '',
            'promotion_data'    =>    '',
            'renew_flat_fee'    =>    '',
            'status_overdue'    =>    1,
            'status_partner'    =>    1,
            'time_completed'    =>    **********,
            'avg_month_count'    =>    '',
            'avg_trans_value'    =>    '',
            'status_bad_debt'    =>    1,
            'avg_trans_number'    =>    '',
            'borrower_bank_id'    =>    410,
            'overdue_flat_fee'    =>    '',
            'number_get_update'    =>    6,
            'renew_percent_fee'    =>    '',
            'time_verify_email'    =>    '',
            'number_day_overdue'    =>    365,
            'amount_fixed_refund'    =>    '',
            'contract_package_id'    =>    1,
            'his_start_deduction'    =>    '',
            'number_get_deferred'    =>    1,
            'overdue_percent_fee'    =>    0.3,
            'status_send_partner'    =>    1,
            'number_rum_promotion'    =>    '',
            'status_created_guide'    =>    '',
            'amount_applied_refund'    =>    '',
            'amount_fee_evaluation'    =>    '',
            'created_admin_user_id'    =>    4,
            'merchant_belong_group'    =>    4,
            'merchant_verify_email'    =>    3,
            'status_overdue_period'    =>    2,
            'updated_admin_user_id'    =>    4,
            'contract_fee_policy_id'    =>    '',
            'number_day_status_debt'    =>    '',
            'number_submit_document'    =>    1,
            'pay_number_day_overdue'    =>    11,
            'rejected_admin_user_id'    =>    '',
            'time_setdayoverduefree'    =>    '',
            'verified_admin_user_id'    =>    4,
            'contract_renewed_amount'    =>    '',
            'number_day_overdue_free'    =>    '',
            'contract_past_number_day'    =>    '',
            'account_disbursement_type'    =>    1,
            'pay_number_period_overdue'    =>    '',
            'mobilecheck_finished_rejected'    =>    '',
            'setdayoverduefree_admin_user_id'    =>    '',
            'amount_fee'    =>    800000,
            'contract_receiver'    =>    $params['amount'] - 800000,
        ];
    }

    public static function infomationVa()
    {
        return [
            'other_data' => [
                'qrCode' => '00020101021138580010A000000727012800069704540114**************0208QRIBFTTA53037045802VN62270823MCNGUWC82N4GJNP TK UTMC63048842',
                'qrImage' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHsAAAB7AQMAAABuCW08AAAABlBMVEX///8AAABVwtN+AAAACXBIWXMAAA7EAAAOxAGVKw4bAAABaElEQVRIid3Usa2EMAwGYKMUdGGBSFkjXVY6FuBgAVgpXdZAygLQpYji93N371HGrz2UAn1FbDm2ib7pG5gX5tWr6rnKQJPaMthoh38Z+LT6tIR9cryKYYmli+ZfsAW7OhwxkHqHpTv1BqAeq0vXuQvUgOsaZ7doHnfhGzBEdfT0cPvk0yEDJPgM1EV78jvTNgyRj95WxwepKoRsRkbkNHt7yIB6riieU2fmWQbaG93TyGljKRA6tFd4KE1S0O7VPt4un8K3oWO75F37vct2lQHmYMhqi2XqPwVqAhGKt4/vPpJBx4a8PYNFCasMrpicTk544VkGRAXTxsiXy0MG6CAOSBYr4a+lGtCF8ujRdwUpTzLQ3la8Eu9P/h31Frwi78+ImIVkgMneMBAOA5FmGWB/zA4zR9PnDgFg3zCf8ar9KgYsjzHwkmkSw5JNF8uY70sbgF1IV5NWLB4ZXPXAgHozBDvL4Hu+HyHdZLKPzPvNAAAAAElFTkSuQmCC',
                'payment_account_name' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'payment_account_branch' => '',
                'payment_account_number' => '**************',
                'payment_account_bank_code' => 'VCCB',
            ],
            'payment_account_id' => '**************',
            'payment_method_code' => 'VIRTUALACCOUNT',
            'payment_channel_code' => 'PAYON',

        ];
    }

    public static function borrowerData()
    {
        return [
            'id'    =>    438,
            'email'    =>    '<EMAIL>',
            'phone'    =>    '',
            'mobile'    =>    '***********',
            'status'    =>    2,
            'address'    =>    'test visa tu',
            'birthday'    =>    '',
            'checksum'    =>    '9f31ed96dacda674d895b7a9b3dd1908',
            'fullname'    =>    'visa tu',
            'issued_by'    =>    'Ha Noi Viet Nam',
            'issue_date'    =>    '31-01-2019',
            'partner_id'    =>    1,
            'profile_id'    =>    '',
            'description'    =>    '',
            'id_verified'    =>    '**********',
            'merchant_id'    =>    421,
            'time_locked'    =>    '',
            'time_actived'    =>    '',
            'time_created'    =>    **********,
            'time_updated'    =>    '',
            'time_unlocked'    =>    '',
            'number_updated'    =>    '',
            'number_set_profile_id'    =>    '',
        ];
    }
}
