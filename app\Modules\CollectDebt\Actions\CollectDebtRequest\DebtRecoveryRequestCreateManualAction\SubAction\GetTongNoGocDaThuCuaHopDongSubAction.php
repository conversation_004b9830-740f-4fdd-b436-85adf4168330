<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class GetTongNoGocDaThuCuaHopDongSubAction
{
  public function run(string $contractCode): float
  {
    $tongSoNoGocDaThu = CollectDebtSchedule::query()
                                           ->where('contract_code', $contractCode)
                                           ->where('isfee', CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC)
                                           ->sum('success_amount_debit');
    return $tongSoNoGocDaThu;
  }
}
