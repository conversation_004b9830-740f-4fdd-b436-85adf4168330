<?php

namespace App\Modules\EmailRemind\Request\CollectDebtContractEvent;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DebtRecoveryContractEventSetStatusRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {

    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'integer'],
      'data.status' => ['required', 'integer', Rule::in(array_values(config('collect_debt_email_remind_config.debt_contract_event_status')))],
      'data.user_request_id' => ['nullable', 'string', 'max:255'],
      'data.description' => ['nullable', 'string', 'max:255'],
    ];
  }
} // End class
