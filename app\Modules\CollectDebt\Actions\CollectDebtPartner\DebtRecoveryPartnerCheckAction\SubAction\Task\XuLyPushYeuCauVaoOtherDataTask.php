<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;

class XuLyPushYeuCauVaoOtherDataTask
{
  public function run($partnerOtherData = [], CollectDebtRequest $collectDebtRequest): string
  {
    $requestItem = collect($partnerOtherData)->where('type', 'REQUEST')->first();

    if (!$requestItem) {
      $partnerOtherData[] = [
        'type' => 'REQUEST',
        'time_modified' => time(),
        'data' => [
          StandardizedDataFilter::getRequestCompactAttribute($collectDebtRequest)
        ],
        'note' => 'Thanh toán từ công nợ'
      ];
    }

    if ($requestItem) {
      foreach ($partnerOtherData as &$p) {
        if ($p['type'] == 'REQUEST') {
          $p['data'][] = StandardizedDataFilter::getRequestCompactAttribute($collectDebtRequest);
        }
      }
    }

    return json_encode($partnerOtherData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
  }
}
