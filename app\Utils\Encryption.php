<?php

namespace App\Utils;

class Encryption {
    
    /*
     * Ma hoa dap ung version php 5 - php7
     * @inputs: data, key, method default = 'AES-256-ECB'
     *
     * @return string
     */

    public static function Encrypt($data, $encryptkey, $method = 'AES-256-CBC') {
        $key = hash('sha256', $encryptkey);
        $ivSize = openssl_cipher_iv_length($method);
        $iv = openssl_random_pseudo_bytes($ivSize);
        $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
        // For storage/transmission, we simply concatenate the IV and cipher text
        $encrypted = base64_encode($iv . $encrypted);

        return $encrypted;
    }

    /*
     * Ma hoa dap ung version php 5 - php7
     *
     * @inputs: data, key, method default = 'AES-256-ECB'
     *
     *
     * return string
     */

    public static function Decrypt($data, $encryptkey, $method = 'AES-256-CBC') {
        $key = hash('sha256', $encryptkey);
        $data = base64_decode($data);
        $ivSize = openssl_cipher_iv_length($method);
        $iv = substr($data, 0, $ivSize);
        $data = openssl_decrypt(substr($data, $ivSize), $method, $key, OPENSSL_RAW_DATA, $iv);

        return $data;
    }

}

?>
