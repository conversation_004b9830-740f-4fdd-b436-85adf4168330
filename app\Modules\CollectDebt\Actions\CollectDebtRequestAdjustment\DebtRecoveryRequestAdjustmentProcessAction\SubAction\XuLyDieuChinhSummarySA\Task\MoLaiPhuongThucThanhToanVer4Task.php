<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhSummarySA\Task;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class MoLaiPhuongThucThanhToanVer4Task
{
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$summaryOtherData   = $collectDebtSummary->getSummaryOtherData();
		$paymentMethod      = $collectDebtSummary->getSummaryOtherDataItem('PAYMENT_METHOD');
		$paymentMethodIndex = $collectDebtSummary->getSummaryOtherDataIndex('PAYMENT_METHOD');

		foreach ($paymentMethod['data'] as &$pm) {
			$pm['closed'] = 'NO';
		}

		$summaryOtherData[$paymentMethodIndex] = $paymentMethod;

		$result = CollectDebtSummary::query()->where('id', $collectDebtSummary->id)->update([
			'other_data' 			=> json_encode($summaryOtherData),
			'status_contract' => CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN,
			'status' 					=> CollectDebtEnum::SUMMARY_STT_DANG_CAP_NHAP
		]);

		if (!$result) {
			mylog(['Loi cap nhat mo lai phuong thuc thu hoi summary' => $result]);
			throw new Exception('Loi cap nhat mo lai phuong thuc thu hoi summary');
		}

		return $collectDebtSummary;
	}
} // End class