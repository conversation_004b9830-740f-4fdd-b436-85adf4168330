# Tầm nhìn dự án

Chúng tôi xây dựng một hệ thống thu hồi nợ trực tuyến nhằm tự động hóa và tối ưu hóa toàn bộ quy trình thu hồi nợ theo nghiệp vụ kinh doanh đặc thù. 

Hệ thống cần có khả năng xử lý logic phức tạp theo từng tình huống nợ, từng loại hợp đồng, và từng nhóm khách hàng, với độ chính xác và hiệu quả cao, nhằm hỗ trợ tăng tỷ lệ thu hồi, giảm nợ xấu và đảm bảo trải nghiệm minh bạch cho người dùng cuối.

---

## Mục tiêu cốt lõi

- Xử lý chính xác các logic nghiệp vụ thu hồi nợ đã được thiết lập
- Hỗ trợ thu hồi đồng thời cho nhiều khoản vay của cùng một khách hàng hoặc nhóm khách hàng
- <PERSON><PERSON> bổ công việc thu hồi hợp lý cho nhân viên
- Đảm bảo hệ thống hoạt động ổn định với khối lượng xử lý lớn

---

## Đối tượng sử dụng

- **Nhân viên vận hành hệ thống**: theo dõi tiến trình thu hồi, cấu hình logic, kiểm tra kết quả
- **Nhân viên Telesale / Thu hồi**: tiếp nhận danh sách khách hàng cần xử lý, cập nhật trạng thái
- **Khách hàng cuối (người vay)**: nhận cảnh báo, thông báo trạng thái nợ, tra cứu lịch sử

---

## Hiện trạng hệ thống

- Hệ thống đã được triển khai và hoạt động thực tế
- Hiện xử lý khoảng **500 hợp đồng mỗi ngày**
- Có dữ liệu lịch sử, nghiệp vụ thực tế đang vận hành
- Yêu cầu tiếp tục mở rộng để đáp ứng khối lượng lớn hơn và nghiệp vụ phức tạp hơn

---

## Nguyên tắc & Ưu tiên thiết kế

- **Ổn định**: hệ thống phải đảm bảo không treo, không bỏ sót bản ghi, không chặn luồng xử lý
- **Đúng logic**: phản ánh đúng nghiệp vụ thu hồi nợ theo từng giai đoạn và quy định công ty
- **Hiệu suất cao**: xử lý khối lượng lớn (hàng nghìn hợp đồng/ngày) mà không ảnh hưởng trải nghiệm
- **Mở rộng được**: thiết kế có thể tích hợp với hệ thống khác như CRM, tổng đài, hoặc hệ thống cảnh báo

---

## Giai đoạn hiện tại

- Tập trung củng cố hệ thống lõi đang vận hành
- Chuẩn hóa các logic nghiệp vụ, mô hình hóa thành rule engine (nếu cần)
- Chuẩn bị mở rộng sang xử lý tự động (auto SMS, voice call, cảnh báo hệ thống)

---

## Định hướng dài hạn

- Tự động hóa tối đa các bước thu hồi nợ (phân tích dữ liệu, gửi thông báo, phân công công việc)
- Phân tích hiệu suất thu hồi theo nhân sự, thời gian, phân khúc khách hàng
- Tích hợp với hệ thống đối tác (ví dụ đơn vị pháp lý, tổng đài ngoài)
