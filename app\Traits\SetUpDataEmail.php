<?php

namespace App\Traits;

use App\Lib\Helper;

trait SetUpDataEmail
{
    public function requestSuccess()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'merchant' => [
                'business_representative' => 'PHẠM ĐỨC HUY',
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
        ];
    }

    public function approveContract()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'merchant' => [
                'business_representative' => 'PHẠM ĐỨC HUY',
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
        ];
    }

    public function cancelContract()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_address' => '18 Tam Trinh',
                'company_email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'PHẠM ĐỨC HUY',
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
        ];
    }

    public function confirmContract()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'Nguyễn Việt Hiển',
                'full_name' => 'D.R.TÁO',
                'email' => '<EMAIL>',
                'business_address' => '20 Hoang Mai',//địa điểm đang kinh doanh
                'address' => '20 Hoang Mai',//đăng ký địa chỉ kinh doanh tại
                'certificate_id' => '41C8020576',
                'passport' => '************',
                'issue_date' => '25-05-2017',
                'issued_by' => 'Cục Quản lý xuất nhập cảnh',
            ],
            'borrower_bank' => [ // Tài khoản ngân hàng của merchant
                'account_number' => '*************',
                'account_holder' => 'DO THI VAN ANH',
                'bank_trade_name' => 'Vietcombank',
                'bank_branch' => 'Hải Phòng',
            ],
            'contract' => [
                'code' => 'MPOS-*************-L4',
                'cycle' => '60',
                'time_start' => '**********',
                'time_end' => '**********',
                'intervals' => '20',
                'amount' => Helper::makeVndCurrency('********'),
                'amount_fee_evaluation' => Helper::makeVndCurrency('********'), //Phí hỗ trợ thẩm định
                'percent_fee_joinning' => '1', //Phí tham gia
                'flat_fee_joinning' => '10000',//Phí tham gia
                'overdue_percent_fee' => '1',//Phí chậm
                'percent_refund' => '1',//% hoàn tiền cho HĐ
                'total_fee_joinning' => $this['contract']['percent_fee_joinning']/100 * $this['contract']['amount'] + $this['contract']['flat_fee_joinning'],//Phí tham gia
                'amount_receive_bank' => $this['contract']['amount'] - $this['contract']['amount_fee_evaluation'] - $this['contract']['total_fee_joinning'],//Số tiền sẽ nhận được qua tài khoản ngân hàng
                'flat_fee_cycle' => Helper::makeVndCurrency('10000'),//phí phạt chậm của kỳ
                'percent_fee_cycle' => '1',//phí phạt chậm của kỳ
                'type' => '3',
                'percent_refund' => 1,// % hoàn tiền cho HĐ
                'time_joining' => (int)'**********' + 24 * 60 * 60,//thời gian tham gia ứng vốn,
                'amount_refund_received' => Helper::makeVndCurrency($this['contract']['percent_fee_joinning'] * 0.5 * $this['contract']['amount'] / 100), //tổng số tiền hoàn là 50% của phí ứng tiền
            ],
            'schedule_guide' => [
                0 => [
                    'amount_period_debit' => Helper::makeVndCurrency('100000'),// Số tiền trích nợ
                    'time_begin' => '08/06/2023', //Ngày trích nợ
                    'total_amount_period_debit' => Helper::makeVndCurrency('100000'),// Số tiền trích nợ luỹ kế
                ],
                2 => [
                    'amount_period_debit' => Helper::makeVndCurrency('100000'),// Số tiền trích nợ
                    'time_begin' => '09/06/2023', //Ngày trích nợ
                    'total_amount_period_debit' => Helper::makeVndCurrency('200000'),// Số tiền trích nợ luỹ kế
                ],
                3 => [
                    'amount_period_debit' => Helper::makeVndCurrency('100000'),// Số tiền trích nợ
                    'time_begin' => '10/06/2023', //Ngày trích nợ
                    'total_amount_period_debit' => Helper::makeVndCurrency('300000'),// Số tiền trích nợ luỹ kế
                ],
            ],
        ];
    }

    public function confirmContractVideo()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'Nguyễn Việt Hiển',
                'full_name' => 'D.R.TÁO',
                'email' => '<EMAIL>',
                'business_address' => '20 Hoang Mai',//địa điểm đang kinh doanh
                'address' => '20 Hoang Mai',//đăng ký địa chỉ kinh doanh tại
                'certificate_id' => '41C8020576',
                'passport' => '************',
                'issue_date' => '25-05-2017',
                'issued_by' => 'Cục Quản lý xuất nhập cảnh',
            ],
            'borrower_bank' => [ // Tài khoản ngân hàng của merchant
                'account_number' => '*************',
                'account_holder' => 'DO THI VAN ANH',
                'bank_trade_name' => 'Vietcombank',
                'bank_branch' => 'Hải Phòng',
            ],
            'contract' => [
                'code' => 'MPOS-*************-L4',
                'cycle' => '60',
                'time_start' => '**********',
                'time_end' => '**********',
                'intervals' => '20',
                'amount' => Helper::makeVndCurrency('********'),
                'amount_fee_evaluation' => Helper::makeVndCurrency('********'), //Phí hỗ trợ thẩm định
                'percent_fee_joinning' => '1', //Phí tham gia
                'flat_fee_joinning' => '10000',//Phí tham gia
                'overdue_percent_fee' => '1',//Phí chậm
                'percent_refund' => '1',//% hoàn tiền cho HĐ
                'total_fee_joinning' => $this['contract']['percent_fee_joinning']/100 * $this['contract']['amount'] + $this['contract']['flat_fee_joinning'],//Phí tham gia
                'amount_receive_bank' => $this['contract']['amount'] - $this['contract']['amount_fee_evaluation'] - $this['contract']['total_fee_joinning'],//Số tiền sẽ nhận được qua tài khoản ngân hàng
                'flat_fee_cycle' => Helper::makeVndCurrency('10000'),//phí phạt chậm của kỳ
                'percent_fee_cycle' => '1',//phí phạt chậm của kỳ
                'type' => '3',
                'percent_refund' => 1,// % hoàn tiền cho HĐ
                'time_joining' => (int)'**********' + 24 * 60 * 60,//thời gian tham gia ứng vốn,
                'amount_refund_received' => Helper::makeVndCurrency($this['contract']['percent_fee_joinning'] * 0.5 * $this['contract']['amount'] / 100), //tổng số tiền hoàn là 50% của phí ứng tiền
            ],
            'schedule_guide' => [
                0 => [
                    'amount_period_debit' => Helper::makeVndCurrency('100000'),// Số tiền trích nợ
                    'time_begin' => '08/06/2023', //Ngày trích nợ
                    'total_amount_period_debit' => Helper::makeVndCurrency('100000'),// Số tiền trích nợ luỹ kế
                ],
                2 => [
                    'amount_period_debit' => Helper::makeVndCurrency('100000'),// Số tiền trích nợ
                    'time_begin' => '09/06/2023', //Ngày trích nợ
                    'total_amount_period_debit' => Helper::makeVndCurrency('200000'),// Số tiền trích nợ luỹ kế
                ],
                3 => [
                    'amount_period_debit' => Helper::makeVndCurrency('100000'),// Số tiền trích nợ
                    'time_begin' => '10/06/2023', //Ngày trích nợ
                    'total_amount_period_debit' => Helper::makeVndCurrency('300000'),// Số tiền trích nợ luỹ kế
                ],
            ],
        ];
    }

    public function cashoutRequest()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
                'company_product_group' => 'Merchant 360',
                'company_instructions_view_infomation' => 'https://nextlend.vn/application/public/hdsd/lich-su-ung-von',// default value
            ],
            'merchant' => [
                'business_representative' => 'PHẠM ĐỨC HUY',
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
        ];
    }

    public function dueDate()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'Nguyễn Việt Hiển',
                'full_name' => 'D.R.TÁO',
            ],
            'contract' => [
                'code' => 'MPOS-*************-L4',
                'cycle' => '60',
                'time_start' => '**********',
                'time_end' => '**********',
                'intervals' => '20',
                'amount' => Helper::makeVndCurrency('********'),
                'total_amount_paid' => Helper::makeVndCurrency('********'), //Số tiền đã hoàn trả
                'debit_begin' => Helper::makeVndCurrency('********'), //Dư nợ cuối kỳ
                'request_amount_debit' => Helper::makeVndCurrency('********'), //Thanh toán tối thiểu
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('********'), //Phí phạt chậm thanh toán kỳ
            ]
        ];
    }

    public function overdueC1()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'NGUYỄN THỊ THUỲ TIÊN',
                'full_name' => 'HỘ KINH DOANH DOTTY SUMMER',
                'certificate_id' => '41C8020576',
                'passport' => '************',
                'issue_date' => '25-05-2017',
                'issued_by' => 'Cục Quản lý xuất nhập cảnh',
            ],
            'contract' => [
                'code' => 'MPOS-*************-L3',
                'cycle' => '60',
                'time_start' => '**********',
                'time_end' => '**********',
                'intervals' => '20',
                'amount' => Helper::makeVndCurrency('********'),
                'total_amount_paid' => Helper::makeVndCurrency('********'), //Số tiền đã hoàn trả
                'debit_begin' => Helper::makeVndCurrency('********'), //Dư nợ cuối kỳ
                'request_amount_debit' => Helper::makeVndCurrency('********'), //Thanh toán tối thiểu
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('********'), //Phí phạt chậm thanh toán kỳ
                'pay_number_day_overdue' => '2', //Số ngày quá hạn
            ],
            'debt' => [
                'amount_paid' => Helper::makeVndCurrency('********'), //Số vốn ứng đã hoàn trả
                'amount_debit' => Helper::makeVndCurrency('********'), //Số vốn ứng còn phải hoàn trả
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('100000'), //Phí phạt chậm thanh toán kỳ
                'total_fee_deferred' => Helper::makeVndCurrency('112902'), //Số phí dịch vụ quá hạn
                'va_account_id' => '',
                'va_data' => [
                    'va_qrcode_image' => '',
                    'channelCode' => '',
                    'name' => '',
                ],
            ]
        ];
    }

    public function overdueC2()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'NGUYỄN THỊ THUỲ TIÊN',
                'full_name' => 'HỘ KINH DOANH DOTTY SUMMER',
                'certificate_id' => '41C8020576',
                'passport' => '************',
                'issue_date' => '25-05-2017',
                'issued_by' => 'Cục Quản lý xuất nhập cảnh',
            ],
            'contract' => [
                'code' => 'MPOS-*************-L3',
                'cycle' => '60',
                'time_start' => '**********',
                'time_end' => '**********',
                'intervals' => '20',
                'amount' => Helper::makeVndCurrency('********'),
                'total_amount_paid' => Helper::makeVndCurrency('********'), //Số tiền đã hoàn trả
                'debit_begin' => Helper::makeVndCurrency('********'), //Dư nợ cuối kỳ
                'request_amount_debit' => Helper::makeVndCurrency('********'), //Thanh toán tối thiểu
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('********'), //Phí phạt chậm thanh toán kỳ
                'pay_number_day_overdue' => '2', //Số ngày quá hạn
            ],
            'debt' => [
                'amount_paid' => Helper::makeVndCurrency('********'), //Số vốn ứng đã hoàn trả
                'amount_debit' => Helper::makeVndCurrency('********'), //Số vốn ứng còn phải hoàn trả
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('100000'), //Phí phạt chậm thanh toán kỳ
                'total_fee_deferred' => Helper::makeVndCurrency('112902'), //Số phí dịch vụ quá hạn
                'va_account_id' => '',
                'va_data' => [
                    'va_qrcode_image' => '',
                    'channelCode' => '',
                    'name' => '',
                ],
            ]
        ];
    }

    public function overdueC3()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'NGUYỄN THỊ THUỲ TIÊN',
                'full_name' => 'HỘ KINH DOANH DOTTY SUMMER',
                'certificate_id' => '41C8020576',
                'passport' => '************',
                'issue_date' => '25-05-2017',
                'issued_by' => 'Cục Quản lý xuất nhập cảnh',
            ],
            'contract' => [
                'code' => 'MPOS-*************-L3',
                'cycle' => '60',
                'time_start' => '**********',
                'time_end' => '**********',
                'intervals' => '20',
                'amount' => Helper::makeVndCurrency('********'),
                'total_amount_paid' => Helper::makeVndCurrency('********'), //Số tiền đã hoàn trả
                'debit_begin' => Helper::makeVndCurrency('********'), //Dư nợ cuối kỳ
                'request_amount_debit' => Helper::makeVndCurrency('********'), //Thanh toán tối thiểu
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('********'), //Phí phạt chậm thanh toán kỳ
                'pay_number_day_overdue' => '2', //Số ngày quá hạn
            ],
            'debt' => [
                'amount_paid' => Helper::makeVndCurrency('********'), //Số vốn ứng đã hoàn trả
                'amount_debit' => Helper::makeVndCurrency('********'), //Số vốn ứng còn phải hoàn trả
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('100000'), //Phí phạt chậm thanh toán kỳ
                'total_fee_deferred' => Helper::makeVndCurrency('112902'), //Số phí dịch vụ quá hạn
                'va_account_id' => '',
                'va_data' => [
                    'va_qrcode_image' => '',
                    'channelCode' => '',
                    'name' => '',
                ],
            ]
        ];
    }

    public function overduePeriod()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'NGUYỄN THỊ THUỲ TIÊN',
                'full_name' => 'HỘ KINH DOANH DOTTY SUMMER',
                'certificate_id' => '41C8020576',
                'passport' => '************',
                'issue_date' => '25-05-2017',
                'issued_by' => 'Cục Quản lý xuất nhập cảnh',
            ],
            'contract' => [
                'code' => 'MPOS-*************-L3',
                'cycle' => '60',
                'time_start' => '**********',
                'time_end' => '**********',
                'intervals' => '20',
                'amount' => Helper::makeVndCurrency('********'),
                'total_amount_paid' => Helper::makeVndCurrency('********'), //Số tiền đã hoàn trả
                'debit_begin' => Helper::makeVndCurrency('********'), //Dư nợ cuối kỳ
                'request_amount_debit' => Helper::makeVndCurrency('********'), //Thanh toán tối thiểu
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('********'), //Phí phạt chậm thanh toán kỳ
                'pay_number_day_overdue' => '2', //Số ngày quá hạn
            ],
            'debt' => [
                'amount_paid' => Helper::makeVndCurrency('********'), //Số vốn ứng đã hoàn trả
                'amount_debit' => Helper::makeVndCurrency('********'), //Số vốn ứng còn phải hoàn trả
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('100000'), //Phí phạt chậm thanh toán kỳ
                'total_fee_deferred' => Helper::makeVndCurrency('112902'), //Số phí dịch vụ quá hạn
                'amount' => Helper::makeVndCurrency('********'), //Số vốn ứng
                'va_account_id' => '',
                'va_data' => [
                    'va_qrcode_image' => '',
                    'channelCode' => '',
                    'name' => '',
                ],
                'time_begin' => '**********', //Thời gian hoàn trả kỳ ngày
                'request_amount_debit' => Helper::makeVndCurrency('********'), //So tien yeu cau trich no
                'amount_unpaid_period' => Helper::makeVndCurrency($this['debt']['amount_paid'] - $this['debt']['request_amount_debit']), //Số tiền chưa hoàn trả của kỳ
            ]
        ];
    }

    public function overdue()
    {
        return [
            'company' => [
                'company_fullname' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_subname' => 'Công ty Vi Mô',
                'company_product_code' => 'VIMO',
                'company_url' => 'https://mpos.vn',
                'company_phone_number' => '0978.676.660',
                'company_email' => '<EMAIL>',
                'company_address' => '18 Tam Trinh',
                'company_bank_account' => '************',
                'company_bank_name' => 'Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai',
                'company_bank_holder' => 'Công ty Cổ phần Công nghệ Vi Mô',
                'company_bank_content_transfer' => 'Nap tien TK MC ma 1119326', // default value
            ],
            'users_admin' => [
                'fullname' => 'Hoàng Yến Chi',
                'mobile' => '***********',
                'email' => '<EMAIL>',
            ],
            'merchant' => [
                'business_representative' => 'NGUYỄN THỊ THUỲ TIÊN',
                'full_name' => 'HỘ KINH DOANH DOTTY SUMMER',
                'certificate_id' => '41C8020576',
                'passport' => '************',
                'issue_date' => '25-05-2017',
                'issued_by' => 'Cục Quản lý xuất nhập cảnh',
            ],
            'contract' => [
                'code' => 'MPOS-*************-L3',
                'cycle' => '60',
                'time_start' => '**********',
                'time_end' => '**********',
                'intervals' => '20',
                'amount' => Helper::makeVndCurrency('********'),
                'total_amount_paid' => Helper::makeVndCurrency('********'), //Số tiền đã hoàn trả
                'debit_begin' => Helper::makeVndCurrency('********'), //Dư nợ cuối kỳ
                'request_amount_debit' => Helper::makeVndCurrency('********'), //Thanh toán tối thiểu
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('********'), //Phí phạt chậm thanh toán kỳ
                'pay_number_day_overdue' => '2', //Số ngày quá hạn
            ],
            'debt' => [
                'amount_paid' => Helper::makeVndCurrency('********'), //Số vốn ứng đã hoàn trả
                'amount_debit' => Helper::makeVndCurrency('********'), //Số vốn ứng còn phải hoàn trả
                'total_fee_cycle_deferred' => Helper::makeVndCurrency('100000'), //Phí phạt chậm thanh toán kỳ
                'total_fee_deferred' => Helper::makeVndCurrency('112902'), //Số phí dịch vụ quá hạn
                'va_account_id' => '',
                'va_data' => [
                    'va_qrcode_image' => '',
                    'channelCode' => '',
                    'name' => '',
                ],
            ]
        ];
    }
}
