<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DebtRecoveryContractPlanCreateRqRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['array'],
      'data.contract_code' => ['nullable', 'string', 'max:50'],
      'data.id' => ['nullable', 'numeric', 'min:0'],
      'data.profile_id' => ['nullable', 'numeric', 'min:0'],
      'data.amount_debit' => ['nullable', 'numeric', 'min:0'],
      'data.partner_id' => ['nullable', 'numeric', 'min:0']
    ];
  }

  public function getAmountReceiverSuccess(): float {
    return $this->json('data.amount_debit');
  }

  // Job chủ động tạo yêu cầu từ lịch và không có tham số gì
  public function isCreateRequestFromProactiveFlow(): bool {
    return empty($this->json('data.contract_code')) && empty($this->json('data.id'))
                                                    && empty($this->json('data.profile_id'))
                                                    && empty($this->json('data.amount_debit'));
  }

  // Tạo yêu cầu từ luồng partnerCheck
  public function isCreateRequestFromPartnerCheckFlow(): bool {
    return !$this->isCreateRequestFromProactiveFlow();
  }
} // End class
