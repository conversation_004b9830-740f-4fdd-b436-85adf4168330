<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide;

use App\Lib\Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideGetPaymentStatisticRequest;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetPaymentStatisticAction\DebtRecoveryContractGuideGetPaymentStatisticAction;

class CollectDebtGuidePaymentController extends Controller
{
	public function getPaymentStatistic(DebtRecoveryContractGuideGetPaymentStatisticRequest $request)
	{
		try {
			$statistic = app(DebtRecoveryContractGuideGetPaymentStatisticAction::class)->run($request);
			return $this->successResponse($statistic, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
