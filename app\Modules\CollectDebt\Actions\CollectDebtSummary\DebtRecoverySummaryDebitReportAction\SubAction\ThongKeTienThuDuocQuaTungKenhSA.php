<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;

class ThongKeTienThuDuocQuaTungKenhSA
{
	public $phanTramDaThu = 0;

	/**
	 * Trả các thông tin: 
	 * 		Mã kênh thu (payment method_code)
	 * 		Số tiền thu đc qua từng kênh
	 * 		Phần trăm của tiền thu được
	 *
	 * @param CollectDebtSummary $collectDebtSummary
	 * @return array
	 * array:3 [
		⁡⁣⁢⁢	0 => array:3 [
				"payment_method_code" => "MPOS"
				"total_amount_receiver" => 0.0
				"percent" => 0.0
			]
			1 => array:3 [
				"payment_method_code" => "IB_OFF"
				"total_amount_receiver" => 0
				"percent" => 0.0
			]
			2 => array:3 [
				"payment_method_code" => "VIRTUALACCOUNT"
				"total_amount_receiver" => 0
				"percent" => 0.0
			]
		]⁡
	 */
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$currency = $collectDebtSummary->getCurrency();

		$paymentMethods = $collectDebtSummary->getSummaryOtherDataItem('PAYMENT_METHOD');
		$listKenhThu = collect($paymentMethods['data'])->pluck('payment_method_code')->toArray();
		$thongKeTienThuQuaTungKenh = [];

		$count = count($listKenhThu);

		foreach ($listKenhThu as $index => $kt) {
			$tongTienThuHoiDuocQuaKenhHienTai =  $collectDebtSummary->collectDebtRequests->where('payment_method_code', $kt)->sum('amount_receiver');
			$phanTramThuDuoc = round($tongTienThuHoiDuocQuaKenhHienTai / $collectDebtSummary->contract_amount * 100);
			
			if ($count == $index-1) {
				$phanTramThuDuoc = 100 - $this->phanTramDaThu;
			}else {
				$this->phanTramDaThu += $phanTramThuDuoc;
			}

			$thongKeTienThuQuaTungKenh[] = [
				'payment_method_code' => $kt,
				'total_amount_receiver' => $tongTienThuHoiDuocQuaKenhHienTai,
				'percent' => $phanTramThuDuoc,
				'currency' => $currency
			];
		}

		return $thongKeTienThuQuaTungKenh;
	}
}
