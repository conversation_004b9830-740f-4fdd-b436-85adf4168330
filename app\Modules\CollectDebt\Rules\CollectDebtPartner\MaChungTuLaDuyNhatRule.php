<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtPartner;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Contracts\Validation\Rule;

class MaChungTuLaDuyNhatRule implements Rule
{
    private string  $__errorMessgae;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $collectDebtPartner = CollectDebtPartner::query()->where('partner_transaction_id', $value)->first();
        if ($collectDebtPartner) {
            $this->__errorMessgae = 'Mã chứng từ bạn nhập đã tồn tại trong lịch sử nhận tiền';
            return false;
        }

        $collectDebtRequest = CollectDebtRequest::query()->where('partner_transaction_id', $value)->first();
        if ($collectDebtRequest) {
            $this->__errorMessgae = 'Mã chứng từ bạn nhập đã tồn tại trong lịch sử thu hồi';
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->__errorMessgae;
    }
}
