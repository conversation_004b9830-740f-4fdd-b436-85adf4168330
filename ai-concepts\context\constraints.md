# constraints.md – <PERSON><PERSON><PERSON><PERSON> hạn & <PERSON><PERSON><PERSON> buộc hệ thống

## 1. <PERSON>ạ tầng triển khai

- <PERSON>ệ thống hiện tại được triển khai **on-premise trên Data Center (DC)** nội bộ.
- Sử dụng pipeline CI/CD: **build Docker image** và chạy container thông qua **Kubernetes (k8s)**.
- <PERSON><PERSON> tầng có thiết lập **High Availability (HA)** cho **MySQL Database** để đảm bảo tính sẵn sàng và an toàn dữ liệu.

---

## 2. Năng lực kỹ thuật & nhân sự

- **Ngôn ngữ chính** của đội phát triển là **PHP/Laravel**, chưa có kinh nghiệm sâu về Golang hoặc các ngôn ngữ backend hiệu năng cao.
- <PERSON><PERSON><PERSON> ngũ **chưa mạnh về hạ tầng (infrastructure)**, thường cần phối hợp với bộ phận DevOps khi triển khai hoặc điều chỉnh hệ thống.
- <PERSON><PERSON> thống cần thiết kế sao cho **dễ triển khai, dễ debug**, ưu tiên các công cụ/giải pháp quen thuộc với team hiện tại.

---

## 3. Yêu cầu nghiệp vụ & dữ liệu

- Dữ liệu thu hồi có tính **chính xác tuyệt đối**: yêu cầu "thu đúng – đủ tiền", và **tính đúng phí** theo từng loại khoản vay.
- Giao dịch thu được sử dụng để:
  - **Đánh giá tín dụng** cho lần vay tiếp theo của khách hàng.
  - **Đối soát** với các đối tác đã giải ngân khoản vay, do đó phải **minh bạch, truy vết được**.
- Không được sai số kể cả **1 đồng**, cần đảm bảo logic **hạch toán rõ ràng, chuẩn xác**.

---

## 4. Hiệu suất & Khả năng mở rộng

- Hệ thống hiện tại có thể xử lý **~500 hợp đồng vay**, với khả năng mở rộng tới **~2000 hợp đồng/ngày**.
- Các batch job hiện tại còn **chạy chậm**, chủ yếu do **truy vấn chưa tối ưu** và **thiếu caching**.
- Cần thiết kế lại theo hướng:
  - Có thể scale (ưu tiên scale ngang).
  - Dễ refactor sang **ngôn ngữ hiệu năng cao hơn** nếu cần (ưu tiên: **Golang**).

---

## 5. Thời gian ưu tiên / Milestone

- Hệ thống đã **vận hành production hơn 1 năm**.
- Hiện nay do tốc độ tăng trưởng nhanh (nhiều khoản vay hơn), cần **scale hệ thống** và **tối ưu lại toàn bộ pipeline thu hồi** trong **giai đoạn tới**.
- Ưu tiên: **ổn định – chính xác – dễ maintain**, hơn là tích hợp tính năng phức tạp.

