<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCreateAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryCreateRequest;

class DebtRecoverySummaryCreateAction
{
  /**
   * Method run
   *
   * @param DebtRecoverySummaryCreateRequest $request [explicite description]
   *
   * @return array [\
   *  'id'
   * ]
   */
  public function run(DebtRecoverySummaryCreateRequest $request)
  {
    // Comment vi khong dung
    // $inputs = $this->__mergeParams($request->json('data'));

    // $insertResult = CollectDebtSummary::insertGetId($inputs);

    // return ['id' => $insertResult];
  }

  protected function __mergeParams($request)
  {
    $data = $request;

    $data['time_created'] = time();
    if (!isset($data['status']) || empty($data['status'])) {
      $data['status'] = CollectDebtEnum::SUMMARY_STT_DANG_CAP_NHAP;
    }

    return $data;
  }
} // End class
