<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\Merchant;
use App\Modules\CollectDebt\Model\NextlendCompany;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;
use Carbon\Carbon;

class EmailRemindOverdueC2SubAction
{
    public function run($collectEvent)
    {
        $dataResponse = '';

        $buildParams = $this->buildParams($collectEvent);
        if (!empty($buildParams)) {
            $params = [
                'customer_category_care_code' => $collectEvent->category_care_code,
                'customer_service_care_code' => $collectEvent->service_care_code,
            ];
            $template = app(GetTemplateMailSubAction::class)->run($params);
            if (!empty($template)) {
                $mailContent = $template['content'];
                $mailContent = str_replace('%5', '[', $mailContent);
                $mailContent = str_replace('%5D', ']', $mailContent);
                foreach ($buildParams as $keyword => $value) {
                    $mailContent = str_replace($keyword, $value, $mailContent);
                }
                $dataResponse = $mailContent;
            }
        }

        return $dataResponse;
    }

    public function buildParams($collectEvent)
    {
        $otherData = json_decode($collectEvent->other_data, true);
        $data = json_decode($collectEvent->data, true);
        $merchant = isset($data['profile']) && isset($data['profile']['merchant']) ? $data['profile']['merchant'] : [];
        $contract = isset($data['contract']) ? $data['contract'] : [];
        $company = isset($data['company']) ?  $data['company'] : [];
        $payment = isset($data['payment']) ? $data['payment'] : [];
        $summary = isset($otherData['summary']) ? $otherData['summary'] : [];

        if (empty($merchant) && empty($contract) && empty($company) && empty($summary)) {
            return '';
        }

        $company = new NextlendCompany($company);
        $merchant = new Merchant($merchant);

        $paymentVa = collect($payment)->where('other_data', '!=', '')->where('payment_method_code', 'VIRTUALACCOUNT')->map(function ($items) {
            return isset($items['other_data']) ? $items['other_data'] : '';
        })->first();

        $businessRepresentative = isset($merchant['business_representative']) ? $merchant['business_representative'] : '';
        $feeOverdue = CaculateAmountSubAction::getTotalFeeOverdue($summary);
        $feeOverdueCycle = CaculateAmountSubAction::getTotalFeeOverdueCycle($summary);
        $amountRemainingPayment = CaculateAmountSubAction::getAmountRemainingPayment($summary);

        $params = [
            '[hop_dong_khach_hang]' => isset($merchant['fullname']) ? $merchant['fullname'] : '',
            '[hop_dong_nguoi_dai_dien]' => $businessRepresentative,
            '[hop_dong_so_dkkd_kh]' => isset($merchant['certificate_id']) ? $merchant['certificate_id'] : '',
            '[hop_dong_so_cccd_kh]' => isset($merchant['passport']) ? $merchant['passport'] : '',
            '[hop_dong_cccd_ngay_cap_kh]' => isset($merchant['issue_date']) ? $merchant['issue_date'] : '',
            '[hop_dong_cccd_noi_cap_kh]' => isset($merchant['issued_by']) ? $merchant['issued_by'] : '',
            '[hop_dong_dia_chi_kh]' => $merchant->getAddress(),
            '[hop_dong_ma]' => isset($contract['contract_code']) ? $contract['contract_code'] : '',
            '[hop_dong_so_tien_ung]' => isset($contract['amount']) ? Helper::makeVndCurrency($contract['amount']) : '',
            '[hop_dong_so_ngay_vay]' => isset($contract['cycle']) ? $contract['cycle'] : '',
            '[hop_dong_tu_ngay]' => isset($contract['time_start']) && !empty($contract['time_start']) ? date('d/m/Y', $contract['time_start']) : '',
            '[hop_dong_den_ngay]' => isset($contract['time_end']) && !empty($contract['time_end']) ? Carbon::createFromTimestamp($contract['time_end'], 'UTC')->format('d/m/Y') : '',
            '[hop_dong_so_tien_hoan]' => isset($summary['total_amount_receiver']) ? Helper::makeVndCurrency($summary['total_amount_receiver']) : '',
            '[hop_dong_so_tien_con_phai_tra]' => Helper::makeVndCurrency($amountRemainingPayment),
            '[hop_dong_so_ngay_qua_han]' => isset($summary['number_day_overdue']) ? $summary['number_day_overdue'] : '',
            '[hop_dong_phi_cham_ky]' => Helper::makeVndCurrency($feeOverdueCycle),
            '[hop_dong_phi_qua_han]' => Helper::makeVndCurrency($feeOverdue),
            '[hop_dong_stk_cong_ty]' => isset($company['company_bank_account_1']) ? $company['company_bank_account_1'] : '',
            '[hop_dong_ten_ngan_hang_cong_ty]' => isset($company['company_bank_name_1']) ? $company['company_bank_name_1'] : '',
            '[hop_dong_ten_chu_tk_cong_ty]' => isset($company['company_bank_holder_1']) ? $company['company_bank_holder_1'] : '',
            '[hop_dong_nd_chuyen_khoan]' => sprintf('NAP TIEN TK MC MA %s - %s', $company->getNoiDungChuyenKhoanMaNapTien(), $company->getCuPhapFullName($merchant)),
            '[hop_dong_ma_qr_va]' => isset($paymentVa['qrImage']) ? $paymentVa['qrImage'] : '',
            '[hop_dong_ten_ngan_hang_va_cong_ty]' => isset($paymentVa['payment_account_bank_code']) ? $paymentVa['payment_account_bank_code'] : '',
            '[hop_dong_stk_va_cong_ty]' => isset($paymentVa['payment_account_number']) ? $paymentVa['payment_account_number'] : '',
            '[hop_dong_ten_chu_tk_va_cong_ty]' => isset($paymentVa['payment_account_name']) ? $paymentVa['payment_account_name'] : '',
            '[hop_dong_sdt_cong_ty]' => $company->getPhoneNumber(),
            '[hop_dong_email_cong_ty]' => $company->getEmail(),
            '[hop_dong_dia_chi_cong_ty]' => $company->getAddress(),
            '[hop_dong_website_1_cong_ty]' => isset($company['company_url_1']) ? $company['company_url_1'] : '',
            '[hop_dong_website_2_cong_ty]' => isset($company['company_url_2']) ? $company['company_url_2'] : '',
            '[hop_dong_ten_cong_ty]' => isset($company['company_fullname']) ? $company['company_fullname'] : '',
            '[hop_dong_ten_ngan_cong_ty]' => isset($company['company_subname']) ? $company['company_subname'] : '',
        ];

        return $params;
    }
}
