<?php

namespace App\Modules\EmailRemind\Rules\CollectDebtContractEvent;

use Illuminate\Contracts\Validation\Rule;
use App\Lib\ApiCall;
use App\Utils\CommonVar;

class ValidateCustomerCategoryCareCode implements Rule
{

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {

        return collect($this->__getAll())->filter(function($items) use ($value){
            return isset($items['code']) && $items['code'] == $value;
        })->isNotEmpty();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Giá trị không tồn tại trong danh mục chăm sóc';
    }

    protected function __getAll()
    {
        $params = [
            'fields' => ['code'],
        ];


        $payload = [
            'module' => CommonVar::API_CUSTOMER_MODULE,
            'path' => '/customer-category-care/get-all',
            'params' => $params,
            'method' => 'GET'
        ];

        $result = (new ApiCall())->callFunctionApi($payload, false);

        return $result;
    }
}
