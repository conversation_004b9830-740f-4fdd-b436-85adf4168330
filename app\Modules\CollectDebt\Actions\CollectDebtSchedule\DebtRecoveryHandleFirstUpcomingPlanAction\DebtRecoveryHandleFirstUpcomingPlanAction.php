<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction\ReplaceNoiDungEmaiToiHanSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction\TaoEventBuildNoiDungMailSubAction;

class DebtRecoveryHandleFirstUpcomingPlanAction
{
	private array $__hopDongSapToiKy = [];

	/**
	 * Xu ly mail sap toi ky thanh toan
	 */
	public function run(Request $request)
	{
		$info = sprintf('Type: MAIL_SAP_TOI_KY_THANH_TOAN --- LogId: %s',  request('api_request_id'));
		@TelegramAlert::sendEmails($info);
		

		$currentDate = now()->format('Ymd');
		
		// Sap toi ky, cach 5-3-2-1 ngay (+1 do tinh theo time `rundate` T+1)
		$whereRawRundate = " (
			 DATEDIFF(STR_TO_DATE(rundate, '%Y%m%d'), STR_TO_DATE('$currentDate', '%Y%m%d')) = 6
			 OR DATEDIFF(STR_TO_DATE(rundate, '%Y%m%d'), STR_TO_DATE('$currentDate', '%Y%m%d')) = 4
			 OR DATEDIFF(STR_TO_DATE(rundate, '%Y%m%d'), STR_TO_DATE('$currentDate', '%Y%m%d')) = 3
			 OR DATEDIFF(STR_TO_DATE(rundate, '%Y%m%d'), STR_TO_DATE('$currentDate', '%Y%m%d')) = 2
		) ";

		$whereRawRundate = str_replace(["\n", "\r", "\t"], '', $whereRawRundate);
		
		$plans = CollectDebtSchedule::query()
																->whereColumn('id', '=', 'master_id') // Lich (C)
																->whereRaw($whereRawRundate)
																->where('contract_type', CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY)
																->where('is_settlement', '!=', CollectDebtEnum::SCHEDULE_LA_LICH_TAT_TOAN) // khong phai lich tat toan
																->whereHas('collectDebtSummary', function ($q) {
																	return $q->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
																})
																->get();
		
		if ($plans->isEmpty()) {
			$this->__hopDongSapToiKy[] = 'khong co thong tin Hop dong sap toi ky thanh toan';
			return $this->__hopDongSapToiKy;
		}
		
		$plans = $plans->load(['collectDebtShare', 'collectDebtSummary']);
		
		$emailTemplateContentTheoKy = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => 'NOTIFY_CONTRACT_DUE_PERIOD',
			'customer_service_care_code' => 'MAIL',
		]);

		if (empty($emailTemplateContentTheoKy['content'])) {
			mylog(['[EMPTY MAIL CONTENT]' => 'khong co thong tin mail template content']);
			throw new Exception('khong co thong tin mail template content');
		}
		

		foreach ($plans as $plan) {
			mylog([
				'--------------------------' => sprintf('%s --------------------------', $plan->contract_code),
				'Hop Dong Sap Toi Ky' => $plan->contract_code
			]);

			if ($plan->is_settlement == CollectDebtEnum::SCHEDULE_LA_LICH_TAT_TOAN) {
				mylog([
					'LichID' => $plan->id,
					'ContractCode' => $plan->contract_code,
					'Loi Logic' => 'La Lich Tat Toan. Khong xu ly'
				]);

				continue;
			}

			try {
				$collectDebtContractEvent = app(TaoEventBuildNoiDungMailSubAction::class)->run($plan);

				if (!$collectDebtContractEvent) {
					mylog(['[LOI CREATED EVENT]' => $collectDebtContractEvent]);
					throw new Exception('Loi create event');
				}
					
				$mailContent = app(ReplaceNoiDungEmaiToiHanSubAction::class)->run(
					$plan,
					$collectDebtContractEvent,
					$emailTemplateContentTheoKy
				);

				if (empty($mailContent)) {
					mylog(['[LOI MAIL CONTENT]' => 'Khong tao duoc mail content']);
					throw new Exception('Loi khong tao duoc mail content');
				}

				$updateNoiDungMail = CollectDebtContractEvent::query()
																										->where('id', $collectDebtContractEvent->id)
																										->update([ 'status' => 3, 'content' => $mailContent ]);
				if (!$updateNoiDungMail) {
					mylog([
						'[LOI CAP NHAT NOI DUNG MAIL]' => $updateNoiDungMail
					]);

					throw new Exception('Loi cap nhat noi dung mail');
				}

				$this->__hopDongSapToiKy[] = $plan->only(['contract_code']);

			}catch(\Throwable $th) {
				mylog(['[LOI VONG LAP]' => Helper::traceError($th)]);
				//throw $th;
				continue;
			}
		};

		@TelegramAlert::sendEmails("Sap Toi Ky Thanh Toan: " . json_encode($this->__hopDongSapToiKy, JSON_UNESCAPED_UNICODE));

		return $this->__hopDongSapToiKy;
	}
} // End class