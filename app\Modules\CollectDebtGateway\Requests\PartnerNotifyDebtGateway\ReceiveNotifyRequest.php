<?php

namespace App\Modules\CollectDebtGateway\Requests\PartnerNotifyDebtGateway;

use App\Modules\CollectDebtGateway\Requests\MposCollectDebtGateway\ReceiveNotifyRequest as MposCollectDebtGatewayReceiveNotifyRequest;
use Illuminate\Foundation\Http\FormRequest;

class ReceiveNotifyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $dataValidate = [];

        $paymentChannel = request()->json('data.gw_partner_code', '');

        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataValidate = app(MposCollectDebtGatewayReceiveNotifyRequest::class)->rules();
                break;
            default:
                $dataValidate = $this->__paymentChannel();
        }

        return $dataValidate;
    }

    public function messages()
    {

        $dataMessage = [
            'data.gw_partner_code.required' => 'Trường gw_partner_code không để trống',
            'data.gw_partner_code.string' => 'Trường gw_partner_code phải đúng định dạng',
            'data.gw_partner_code.max' => 'Trường gw_partner_code không được lớn hơn :max',
            'data.gw_partner_code.in' => 'Trường gw_partner_code phải thuộc các kênh đã khai báo',
        ];
        $paymentChannel = request()->json('data.gw_partner_code', '');
        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataMessage = app(MposCollectDebtGatewayReceiveNotifyRequest::class)->messages();
                break;
            default:
                $dataMessage;
        }

        return $dataMessage;
    }



    protected function __paymentChannel()
    {
        $paymentChannel = array_values(config('collect_debt_gateway_config.payment_channel'));

        return [
            'data' => 'required|array',
            'data.gw_partner_code' => 'required|string|max:50|in:' . implode(',', $paymentChannel),
        ];
    }
}
