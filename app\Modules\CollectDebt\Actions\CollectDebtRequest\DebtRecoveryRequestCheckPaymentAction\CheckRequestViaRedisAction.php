<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use Exception;
use Illuminate\Support\Facades\Redis;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;

class CheckRequestViaRedisAction
{
	private int $__limit = 30;

	public function run()
	{
		$request = request();

		$now = now()->timestamp;
		$key = CacheEnum::NLV4_REDIS_LIST_LENH_TRICH_CAN_CHECK;

		$requestIds = Redis::zrangebyscore($key, '-inf', $now, [
			'limit' => [0, $this->__limit]
		]); // score <= now

		if (empty($requestIds)) {
			return "Khong co yeu cau nao can check";
		}

		$collectDebtRequests = CollectDebtRequest::query()
			->with('collectDebtPartner')
			->whereIn('partner_request_id', $requestIds)
			->get();

		throw_if($collectDebtRequests->isEmpty(), new Exception('Không có yêu cầu cân check trạng thái trích nợ'));

		$collectDebtRequests->each(function (CollectDebtRequest $collectDebtRequest) use ($request) {
			$partnerRequestId = $collectDebtRequest->partner_request_id;

			// chua co partner thi goi check
			if (!$collectDebtRequest->collectDebtPartner) {
				$collectDebtRequestChecked = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request);
				$collectDebtRequest->mpos_debt_result = $collectDebtRequestChecked;
				CacheEnum::putRequestToCheck($partnerRequestId);
			}

			// co partner roi thi update time_checked
			if ($collectDebtRequest->collectDebtPartner) {
				$collectDebtRequest->forceFill(['time_checked' => time()])->update();
				CacheEnum::removeCheckedRequest($partnerRequestId);
			}

			return $collectDebtRequest;
		});

		return $collectDebtRequests->pluck(['partner_request_id']);
	}
}  // End class