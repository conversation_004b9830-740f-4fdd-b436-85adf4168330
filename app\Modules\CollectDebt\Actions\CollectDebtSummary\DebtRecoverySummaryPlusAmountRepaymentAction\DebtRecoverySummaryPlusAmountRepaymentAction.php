<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetAmountCanRefundAction\DebtRecoverySummaryGetAmountCanRefundAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentAction\SubAction\TaoYeuCauNapTienSubAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use DB;

class DebtRecoverySummaryPlusAmountRepaymentAction
{
	public function run(DebtRecoverySummaryPlusAmountRepaymentRequest $request)
	{
		$collectDebtSummary = CollectDebtSummary::query()
			->where('contract_code', $request->json('data.contract_code'))
			->lock('FOR UPDATE SKIP LOCKED')
			->first();

		throw_if(!$collectDebtSummary, new Exception('HĐ không tồn tại trong hệ thống'));

		// Đây là số tiền thu thừa có thể hoàn hoặc thanh toán tiếp
		$result = app(DebtRecoverySummaryGetAmountCanRefundAction::class)->run(['contract_code' => $collectDebtSummary->contract_code]);
		$soTienDaThuThua = $result['total_amount_can_refund'] ?? 0;

		throw_if(
			$soTienDaThuThua < $request->getSoTienMuonThanhToanTiep(),
			new Exception('Số tiền thu thừa không đủ để thanh toán tiếp. Từ chối xử lý')
		);


		$affectedRow = CollectDebtSummary::query()
																		 ->where('id', $collectDebtSummary->id)
																		 ->update([
																				'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO,
																				'total_amount_repayment_debt' => DB::raw('total_amount_repayment_debt + ' . $request->getSoTienMuonThanhToanTiep())
																			]);

		throw_if($affectedRow == 0, new Exception('Lỗi không cập nhật được số tiền chuyển ngân (repayment)'));

		$collectDebtShare = CollectDebtShare::query()->where('contract_code', $collectDebtSummary->contract_code)->first();
		return $collectDebtShare;
	}
} // End class