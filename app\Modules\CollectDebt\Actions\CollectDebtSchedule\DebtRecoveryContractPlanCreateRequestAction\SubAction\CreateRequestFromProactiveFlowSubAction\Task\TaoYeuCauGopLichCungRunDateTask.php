<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\InsertBatchCollectDebtRequestTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Exception;

class TaoYeuCauGopLichCungRunDateTask
{
  public function run(Collection $plans): Collection
  {
    $plans->load('collectDebtShare');

    $params = [];
    $contracts = $plans->groupBy('contract_code');
    
    
    mylog(['Lịch sau khi gộp là' => $contracts->implode('id', ',')]);
    
    try {
      foreach ($contracts as $contractCode => $listPlans) {
        if (CollectDebtConfigAuto::isPauseContractJob($contractCode)) {
          continue;
        }

        $isAllPlanUsingMpos = $listPlans->every(function (CollectDebtSchedule $plan) {
          return collect($plan->getPaymentGuide())->contains(function ($pm) {
            return isset($pm['payment_method_code']) && $pm['payment_method_code'] == 'MPOS';
          });
        });

        if ($isAllPlanUsingMpos) {
          $params[] = $this->__buildParamThuGop($listPlans);
        }
      }

      $collectDebtRequestInserted = app(InsertBatchCollectDebtRequestTask::class)->run($params);
      return $collectDebtRequestInserted;
    } catch(\Throwable $th) {
      throw $th;
    }
  }

  private function __buildParamThuGop(Collection $plans)
  {
    $firstPlan = $plans->first()->replicate(); // Clone model đầu tiên

    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $firstPlan->contract_code)->first();

    $paymentGuide = $firstPlan->getSpecialPaymentGuide('MPOS');

    $returnData = [
      'type'                        => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
      'profile_id'                  => $firstPlan->profile_id,
      'contract_code'               => $firstPlan->contract_code,
      'plan_ids'                    => $plans->implode('id', ','),
      'payment_method_code'         => $paymentGuide['payment_method_code'] ?? '',
      'payment_channel_code'        => $paymentGuide['payment_channel_code'] ?? '',
      'payment_account_id'          => $paymentGuide['payment_account_id'] ?? '',
      'payment_account_holder_name' => $paymentGuide['other_data']['payment_account_holder_name'] ?? '',
      'payment_account_bank_code'   => $paymentGuide['other_data']['payment_account_bank_code'] ?? '',
      'payment_account_bank_branch' => $paymentGuide['other_data']['payment_account_bank_branch'] ?? '',
      'partner_request_id'          => '',
      'partner_transaction_id'      => '',
      'time_begin'                  => $firstPlan->rundate_as_date->copy()->timestamp,
      'time_expired'                => $firstPlan->rundate_as_date->copy()->setTimeFromTimeString(Helper::getCutOffTime())->timestamp,
      'is_payment'                  => '1', // 1: Có gửi yc qua đối tác
      'version'                     => CollectDebtEnum::REQUEST_VERSION_4,
      'status'                      => CollectDebtEnum::REQUEST_STT_MOI_TAO,
      'status_payment'              => CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI,
      'status_recored'              => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO,
      'currency'                    => $collectDebtSummary->getCurrency(),
      // Tổng số tiền yêu cầu thanh toán của các lịch
      'amount_request'              => $plans->sum('request_amount_debit'), 
      'amount_payment'              => 0, // Số tiền đã thu hồi được
      'amount_receiver'             => 0,
      'fee'                         => 0,
      'plan_data'                   => Helper::getPlanCompact($plans),
      'description'                 => '',
      'created_by'                  => Helper::getCronJobUser(),
      'time_created'                => time(),
      'create_from'                 => CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG
    ];

    app(DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST::class)->run($plans, $paymentGuide['payment_method_code']);
  
    return $returnData;
  }

  public function buildAmountReceiverFillPlan(Collection $plans): string {
    $returnData = [];
    foreach ($plans as $plan) {
      $returnData[] = [
        'plan_id' => $plan->id,
        'request_amount' => $plan->request_amount_debit,
        'success_amount_debit' => 0
      ];
    }

    return json_encode($returnData, JSON_UNESCAPED_UNICODE);
  }
} // End class