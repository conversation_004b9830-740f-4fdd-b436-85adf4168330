<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Support\Arr;
use PhpParser\Node\Expr\Cast\Array_;

class GetHopDongCoYeuCauChuaVeTrangThaiCuoiTask {


  public function run() 
  {
    $contractCodes = CollectDebtRequest::query()
                                      // Chưa về trạng thái cuối
                                      ->whereNotIn('status', [
                                        CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
                                        CollectDebtEnum::REQUEST_STT_TU_CHOI,
                                      ])
                                      ->orWhere(function ($q) {
                                        // Đã về trạng thái cuối nhưng chưa ghi sổ
                                        $q->where('status', CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH)
                                          ->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO);
                                      })
                                      ->orWhere(function ($q) {
                                        // Ghi sổ nhưng chưa hạch toán
                                        $q->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO)
                                          ->whereHas('collectDebtLedger', function ($subQuery) {
                                            $subQuery->whereNull('time_accounting');
                                          });
                                      })
                                      ->select(['contract_code', 'id', 'plan_ids'])
                                      ->groupBy('contract_code')
                                      ->get()
                                      ->pluck('contract_code')
                                      ->toArray();
    return $contractCodes;
  }
} // End class