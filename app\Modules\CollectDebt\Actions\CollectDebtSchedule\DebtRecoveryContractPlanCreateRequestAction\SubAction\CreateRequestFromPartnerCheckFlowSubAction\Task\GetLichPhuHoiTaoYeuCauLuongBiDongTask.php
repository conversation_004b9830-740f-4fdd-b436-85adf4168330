<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromPartnerCheckFlowSubAction\Task;

use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanCreateRqRequest;

class GetLichPhuHoiTaoYeuCauLuongBiDongTask
{
  public function run(DebtRecoveryContractPlanCreateRqRequest $request, $exceptPlanProcessingIds=[]): Collection
  {
    $plans = CollectDebtSchedule::query()->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
                                         ->whereNotIn('id', $exceptPlanProcessingIds)
                                         ->where('rundate', '>=', date('Ymd')) // Tránh tạo cho yc quá khứ
                                         ->where('contract_code', $request->json('data.contract_code')); // Luồng bị động thì phải có HĐ; 

    if (!empty($request->json('data.profile_id'))) {
      $plans->where('profile_id', $request->json('data.profile_id'));
    }

    if (!empty($request->json('data.id'))) {
      $plans->where('id', $request->json('data.id'));
    }

    $plans = $plans->get();
    $plans = app(PlanSortableCollectionByRule::class)->sortCollection($plans);

    mylog(['Danh sách lịch sau truy vấn luồng CreateRequestFromPartnerCheckFlowSubAction là: ' => $plans->pluck('id')]);

    if ($plans->isNotEmpty()) {
      $scheduleAsCollection = Collection::make();

      $totalAmountDebit = 0;
      foreach ($plans as $collectDebtSchedule) {
        $totalAmountDebit += $collectDebtSchedule->request_amount_debit;
        $scheduleAsCollection->push($collectDebtSchedule);

        if ($totalAmountDebit > $request->getAmountReceiverSuccess()) {
          break;
        }
      }

      mylog(['Các lịch đạt đủ điều kiện là: ' => $scheduleAsCollection]);

      return $scheduleAsCollection;
    }

    return $plans;
  }

  
} // End class
