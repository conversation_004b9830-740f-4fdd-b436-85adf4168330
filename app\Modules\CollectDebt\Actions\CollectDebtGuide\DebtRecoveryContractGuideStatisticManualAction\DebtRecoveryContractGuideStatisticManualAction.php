<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction;

use Exception;
use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Utils\CommonVar;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\GetReduceAndPrepayPlanAction\GetReduceAndPrepayPlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction\GetNoTheoLoaiPhaiThuHomNaySA;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction\KiemTraLenhTrichNgayVaRaCanhBaoSA;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class DebtRecoveryContractGuideStatisticManualAction
{

  public function run(DebtRecoveryContractGuideStatisticManualRequest $request)
  {
    $contractCode = $request->json('data.contract_code');

    $collectDebtShare = CollectDebtShare::where('contract_code', $contractCode)
                                        ->first();

    throw_if(!$collectDebtShare, new Exception('Không tìm thấy thông tin chỉ dẫn'));

    $reduceAndPrepayPlan = app(GetReduceAndPrepayPlanAction::class)->run($contractCode);
    $collectDebtShareArray = $collectDebtShare->toArray();
    $collectDebtShareArray['time_start_as_vn_date'] = $collectDebtShare->time_start_as_date->format('d/m/Y');
    $collectDebtShareArray['time_end_as_vn_date'] =  $collectDebtShare->time_end_as_date->format('d/m/Y');
    
    $tongNoGocHomNayPhaiThu = app(GetNoTheoLoaiPhaiThuHomNaySA::class)->run($contractCode, CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC);
    $tongPhiHomNayConPhaiThu = app(GetNoTheoLoaiPhaiThuHomNaySA::class)->run($contractCode, CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI);

    $returnData = [
      // hợp đồng
      'contract'                      => $collectDebtShareArray,

      // tổng phí quá hạn
      'reduce_over_due_fee'           => $reduceAndPrepayPlan['reduce_over_due_fee'],

      // phí quá hạn đã thu
      'reduce_over_due_fee_collected' => $reduceAndPrepayPlan['reduce_over_due_fee_collected'],

      // lịch thu phí chậm kỳ
      'reduce_over_period_plans'      => $reduceAndPrepayPlan['reduce_over_period_plans'],

      // tổng nợ gốc
      'total_root_debt'               => $tongNoGocHomNayPhaiThu,

      // tổng phí
      'total_fee_debt'                => $tongPhiHomNayConPhaiThu,

      // tổng nợ
      'total_debt'                    => $tongNoGocHomNayPhaiThu + $tongPhiHomNayConPhaiThu,
    ]; 

		if ($request->json('data.form_type', '') == 'GIAM_PHI') {
			$returnData['warning_message'] = app(KiemTraLenhTrichNgayVaRaCanhBaoSA::class)->run($contractCode);
		}
    
    return $returnData;
  }
} // End class