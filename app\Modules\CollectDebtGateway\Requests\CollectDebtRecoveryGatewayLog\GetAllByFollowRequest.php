<?php

namespace App\Modules\CollectDebtGateway\Requests\CollectDebtRecoveryGatewayLog;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Rule;

class GetAllByFollowRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data' => ['required', 'array'],
            'data.request_id' => 'nullable|integer',
            'data.partner_request_id' => 'nullable|string|max:50',
            'data.partner_transaction_id' => 'nullable|string|max:50',
            'data.fields' => 'nullable|string',

        ];
    }

    public function messages()
    {
        return [
            'data.request_id.required' => 'Trường request_id không để trống',
            'data.request_id.integer' => 'Trường request_id phải đúng định dạng',
            'data.partner_request_id.required' => 'Trường partner_request_id không để trống',
            'data.partner_request_id.string' => 'Trường partner_request_id phải đúng định dạng',
            'data.partner_request_id.max' => 'Trường partner_request_id không được lớn hơn :max',
            'data.partner_transaction_id.required' => 'Trường partner_transaction_id không để trống',
            'data.partner_transaction_id.string' => 'Trường partner_transaction_id phải đúng định dạng',
            'data.partner_transaction_id.max' => 'Trường partner_transaction_id không được lớn hơn :max',
            'data.fields.string' => 'Trường fields phải đúng định dạng',
            'at_least_one_field.required' => 'Ít nhất một trong ba trường phải được điền(request_id,partner_request_id,partner_transaction_id).'
        ];
    }

    public function withValidator(Validator $validator)
    {
        $validator->sometimes('at_least_one_field','required',function($input){
            return !isset($input->data['partner_request_id']) && !isset($input->data['request_id']) && !isset($input->data['partner_transaction_id']);
        });
    }
}
