<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestOpenAndMinusFreezeAction\SubAction;

use App\Lib\ApiCall;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Utils\CommonVar;

class GiamTruSoDuVaDongBangViSubAction {
	const PROFILE_ACCOUNT_TK_MASTER = 1;

	public function run(CollectDebtRequest $collectDebtRequest) {
		
		$params = [
      'amount' => $collectDebtRequest->amount_receiver,
      'users_admin_id' => 'cronjob',
      'profile_id' => $collectDebtRequest->profile_id,
			'type' => self::PROFILE_ACCOUNT_TK_MASTER,
			'id_refer' => $collectDebtRequest->contract_code
    ];

		
    $payload = [
      'module' => CommonVar::API_PROFILE_MODULE,
      'path' => '/ProfileAccountsOpenFreezeMinusMoney',
      'params' => $params,
      'method' => 'POST'
    ];

    $profileAccountResult = (new ApiCall())->callFunctionApi($payload, true);
		return $profileAccountResult;
	}
}