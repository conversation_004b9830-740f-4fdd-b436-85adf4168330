<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\Task;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class GetLichThuPhiCuoiCungCuaHopDongST
{
  public function run(CollectDebtSchedule $collectDebtSchedule)
  {
    $lastFeeSchedule = CollectDebtSchedule::where('isfee', 1)
                                          ->where('contract_code', $collectDebtSchedule->contract_code)
                                          ->orderBy('id', 'DESC')
                                          ->first();
    return $lastFeeSchedule;
  }
} // End class