<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByIdAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtLedger;
use Exception;
use Illuminate\Database\Eloquent\Collection;

class DebtRecoveryLedgerGetWhereRawSubAction
{
  public function run(string $whereRaw = '', string $orderByRaw = 'id asc', int $limit = 0): Collection
  {
    throw_if(empty($whereRaw), new Exception('Thiếu điều kiện truy vấn sổ', 500));
    
    $collectDebtLedgers = CollectDebtLedger::whereRaw($whereRaw);

    if ($limit > 0) {
      $collectDebtLedgers = $collectDebtLedgers->limit($limit);
    }

    return $collectDebtLedgers->orderByRaw($orderByRaw)->get();
  }
} // End class