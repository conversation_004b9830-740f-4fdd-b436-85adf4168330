<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\Task;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueC3SubAction;

class DigitalNotiQuaHanTask
{
	public function run(CollectDebtContractEvent $collectEvent)
	{
		return ;
		// $params = app(EmailRemindOverdueC3SubAction::class)->buildParams($collectEvent);

		// $content = sprintf(
		// 	'Khoản vay đã quá hạn%sBạn đang quá hạn thanh toán, vui lòng thanh toán để tránh phát sinh phí.',
		// 	PHP_EOL
		// );

		// $notifyData = [
		// 	"partner_code" => 'AppsDigital',
		// 	'fn' => 'Notification_createNotify',
		// 	'params' => [
		// 		'channel' => 'DIGITAL',
		// 		'list_merchant_id' => [
		// 			$collectEvent->getMposMcId()
		// 		],
		// 		'for_sub' => 2,
		// 		'sub_category_code' => 'DIGITAL_QUAHANTHANHTOAN',
		// 		'type' => 'push',
		// 		'sent_to' => 'ONE',
		// 		'os' => 'all',
		// 		'title' => sprintf('[%s] - Khoản vay đã quá hạn!', $collectEvent->contract_code),
		// 		'body' => $content,
		// 		'content' =>  $content,
		// 		'time_actived' => now()->timestamp,
		// 		'other_data' => json_encode([
		// 			'contract_code' => $collectEvent->contract_code,
		// 			'contract_amount' => $params['[hop_dong_so_tien_ung]'],
		// 			'contract_time_start' => $params['[hop_dong_tu_ngay]'],
		// 			'contract_time_end' => $params['[hop_dong_den_ngay]'],
		// 			'total_day_overdue' => $params['[hop_dong_so_ngay_qua_han]'],
		// 			'fee_overdue' => $params['[hop_dong_phi_qua_han]'],
		// 			'status' => 'Quá hạn',
		// 		])
		// 	],
		// 	'time_request' => now()->timestamp
		// ];

		// $digitalNoti = CollectDebtDigitalNoti::query()->forceCreate([
		// 	'contract_code' => $collectEvent->contract_code,
		// 	'type' => 'over_due',
		// 	'object_model' => CollectDebtContractEvent::class,
		// 	'object_id' => $collectEvent->id,
		// 	'digital_request' => json_encode($notifyData),
		// 	'status' => CollectDebtDigitalNoti::STT_DA_BUILD_PARAM,
		// 	'time_created' => now()->timestamp,
		// 	'time_updated' => now()->timestamp
		// ]);

		// if (!$digitalNoti) {
		// 	throw new Exception('Lỗi không build tạo được noti');
		// }

		// return $digitalNoti;
	}
} // End class
