<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task;

use App\Lib\TelegramAlert;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;

class DanhDauLichThuLaDangProcessTask
{
	public function run(Collection $plans)
	{
		$planIds = $plans->pluck('id')->toArray();
		$firstPlan = $plans->first();

		mylog([
			'PlanIds xu ly process' => $planIds,
			'contract_code' => $firstPlan->contract_code
		]);

		$updateLenDangXuLy = CollectDebtSchedule::query()
																						->where('contract_code', $firstPlan->contract_code)
																						->whereIn('id', $planIds)
																						->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
																						->update([
																							'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY,
																							'time_updated' => now()->timestamp
																						]);

		mylog(['Ket qua cap nhat lich thu ve dang xu ly' => $updateLenDangXuLy]);
		
		if (!$updateLenDangXuLy) {
			mylog(['Loi khong cap nhat duoc len dang xu ly' => $updateLenDangXuLy]);
		}

		return $updateLenDangXuLy;
	}
}
