<?php

namespace App\Modules\CollectDebtGateway\Service;


class PartnerNotifyCollectDebtGatewayService
{

    public function receiveNotify($request)
    {
        $dataResponse = [];
        $paymentChannel = $request['gw_partner_code'];
        $request['payment_channel'] = $paymentChannel;
        
        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataResponse = app(MposCollectDebtGatewayService::class)->receiveNotify($request);
                break;
            default:
                $dataResponse = [];
        }

        return $dataResponse;
    }
}
