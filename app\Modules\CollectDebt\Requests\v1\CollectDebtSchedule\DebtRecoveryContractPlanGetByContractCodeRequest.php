<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DebtRecoveryContractPlanGetByContractCodeRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.contract_code' => ['required', 'string', 'max:255'],
      'data.order_by' => ['nullable', 'string']
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    if (empty($params['data']['order_by'])) {
      $params['data']['order_by'] = 'id ASC';
    }

    $params['data']['contract_code'] = trim($params['data']['contract_code'] ?? '');
    
    $this->merge($params);
  }
} // End class
