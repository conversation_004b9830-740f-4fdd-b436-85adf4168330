<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\LichQuaKhu;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class GoiLichThuPhiSangHomSauST
{  
  /**
   * Gối lịch số tiền nợ gốc của lịch thu hiện tại vào lịch có rundate là hôm sau
   *
   * @param CollectDebtSchedule $collectDebtSchedule [explicite description]
   * @param CollectDebtSchedule $lichCoRunDateLaHomSau [explicite description]
   *
   * @return void
   */
  public function run(CollectDebtSchedule $lichDangHachToan, ?CollectDebtSchedule $lichCoRunDateLaHomSau, float $soPhiConPhaiThuTiep=0)
  {
    if ($lichCoRunDateLaHomSau) {
      $feeScheduleParam = [
        'profile_id'           => $lichCoRunDateLaHomSau->profile_id,
        'contract_code'        => $lichCoRunDateLaHomSau->contract_code,
        'contract_type'        => $lichCoRunDateLaHomSau->contract_type,
        'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
        'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
        'debit_begin'          => $soPhiConPhaiThuTiep,
        'debit_end'            => 0,
        'rundate'              => $lichCoRunDateLaHomSau->rundate,
        'time_start'           => $lichCoRunDateLaHomSau->time_start,
        'time_end'             => $lichCoRunDateLaHomSau->time_end,
        'amount_period_debit'  => $lichCoRunDateLaHomSau->amount_period_debit,
        'request_amount_debit' => $soPhiConPhaiThuTiep,
        'success_amount_debit' => 0,
        'other_data'           => $lichCoRunDateLaHomSau->other_data,
        'description'          => $lichCoRunDateLaHomSau->collectDebtSchedule,
        'is_settlement'        => $lichCoRunDateLaHomSau->is_settlement,
        'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
        'created_by'           => $lichCoRunDateLaHomSau->created_by,
        'time_created'         => time(),
        'cycle_number'         => $lichCoRunDateLaHomSau->cycle_number,
        'master_id'         => $lichCoRunDateLaHomSau->master_id,
      ];
    }else {
      $feeScheduleParam = [
        'profile_id'           => $lichDangHachToan->profile_id,
        'contract_code'        => $lichDangHachToan->contract_code,
        'contract_type'        => $lichDangHachToan->contract_type,
        'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
        'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
        'debit_begin'          => $soPhiConPhaiThuTiep,
        'debit_end'            => 0,
        'rundate'              => $lichDangHachToan->rundate,
        'time_start'           => $lichDangHachToan->time_start,
        'time_end'             => $lichDangHachToan->time_end,
        'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
        'request_amount_debit' => $soPhiConPhaiThuTiep,
        'success_amount_debit' => 0,
        'other_data'           => $lichDangHachToan->other_data,
        'description'          => $lichDangHachToan->collectDebtSchedule,
        'is_settlement'        => $lichDangHachToan->is_settlement,
        'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
        'created_by'           => $lichDangHachToan->created_by,
        'time_created'         => time(),
        'cycle_number'         => $lichDangHachToan->cycle_number,
        'master_id'         => $lichDangHachToan->master_id,
      ];
    }

    $feeSchedule = CollectDebtSchedule::forceCreate($feeScheduleParam);
    return $feeSchedule;
  }
}
