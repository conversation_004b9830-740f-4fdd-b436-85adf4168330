<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use PhpParser\PrettyPrinter\Standard;

class TaoCongNoSubAction
{
  public function run(CollectDebtRequest $collectDebtRequest, Request $request, bool $isCongNoBaoMuon=false)
  {
    $paramInsert = $request->only([
      'data.payment_channel_code',
      'data.payment_method_code',
      'data.payment_account_id',
      'data.partner_request_id',
      'data.partner_transaction_id',
      'data.amount_payment',
      'data.amount_receiver',
      'data.fee',
      'data.request_exists',
      'data.response',
      'data.description',

			// Luồng recheck có thể có thêm các thông tin sau:
      'data.created_by',
      'data.time_created',

		
			'data.time_updated',
			'data.updated_by',

			'data.time_complated',
			'data.complated_by',

			'data.time_created_request',
			'data.created_request_by',

			'data.time_processing',
			'data.processing_by',

      'data.status',
    ])['data'];
    
    // yc đã bị hủy rồi thì tạo partner với trạng thái là TỪ CHỐI XỬ LÝ
    if ($collectDebtRequest->isCanceled()) {
      $paramInsert['status'] = CollectDebtEnum::PARTNER_STT_DA_TU_CHOI;
    }

    if ($isCongNoBaoMuon) {
      $paramInsert['description'] = 'TRICH_MUON';
    }
    
    // Dùng guide để build model CoreContract, sau đọc từ partner sẽ không cần join sang chỉ dẫn nữa
    $collectDebtShare = CollectDebtShare::where('contract_code', $collectDebtRequest->contract_code)->first();

    if ($paramInsert['payment_method_code'] == 'MPOS') {
      $paramInsert['other_data'] = json_encode([
        [
          'type' => 'CONTRACT',
          'time_modified' => time(),
          'data' => [
            'profile_id'          => $collectDebtShare->profile_id,
            'contract_code'       => $collectDebtShare->contract_code,
            'contract_time_start' => $collectDebtShare->contract_time_start,
            'contract_type'       => $collectDebtShare->getContractTypev1(),
            'contract_cycle'      => $collectDebtShare->contract_cycle,
            'contract_intervals'  => $collectDebtShare->contract_intervals,
            'amount'              => $collectDebtShare->amount,
            'contract_time_end'   => $collectDebtShare->contract_time_end,
            'other_data'          => '{}',
            'description'         => 'Hợp đồng v4',
            'id'                  => $collectDebtShare->getContractId()
          ],
          'note' => 'Thông tin hợp đồng'
        ],

        [
          'type' => 'REQUEST',
          'time_modified' => time(),
          'data' => [
            StandardizedDataFilter::getRequestCompactAttribute($collectDebtRequest)
          ],
          'note' => 'Thông tin yêu cầu'
        ]
      ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

		$paramInsert['contract_code'] = $collectDebtShare->contract_code;

    $collectDebtPartner = CollectDebtPartner::firstOrCreate([
			'partner_transaction_id' => $paramInsert['partner_transaction_id']
		], $paramInsert);

    if ($collectDebtPartner && !$isCongNoBaoMuon) {
      $collectDebtRequest->updateOtherData('RECEIVER', 'Nhận kết kết quả trích từ đối tác');
    }

    return $collectDebtPartner;
  }
} // End class
