<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use phpDocumentor\Reflection\Types\Boolean;

trait PlanStatusable
{
  public function isNew(): bool
  {
    return $this->status == CollectDebtEnum::SCHEDULE_STT_MOI;
  }

  public function isAccounting(): bool
  {
    return $this->status == CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN;
  }

  public function isFinished(): bool
  {
    return $this->status == CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH;
  }

  public function isCanceled(): bool
  {
    return $this->status == CollectDebtEnum::SCHEDULE_STT_DA_HUY;
  }

  public function isFinalStatus(): bool
  {
    return $this->isFinished() || $this->isCanceled();
  }

  public static function listStatus(): array
  {
    return [
      CollectDebtEnum::SCHEDULE_STT_MOI => 'Mới tạo',
      CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN => 'Đang hạch toán',
      CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH => 'Đã hoàn thành',
      CollectDebtEnum::SCHEDULE_STT_DA_HUY => 'Đã hủy',
    ];
  }

  /**
   * Đoạn code này đc viết ra để sau này sẽ thay thế hết các đoạn cũ
   * được biết ở phía trên
   */
  public function isLichThuMoi(): bool
  {
    return $this->status == CollectDebtEnum::SCHEDULE_STT_MOI;
  }

  public function isLichThuDangHachToan(): bool
  {
    return $this->status == CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN;
  }

  public function isLichThuDaHoanThanh(): bool
  {
    return $this->status == CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH;
  }

  public function isDaHuy(): bool
  {
    return $this->status == CollectDebtEnum::SCHEDULE_STT_DA_HUY;
  }

  public function isTrangThaiCuoi(): bool
  {
    return $this->isLichThuDaHoanThanh() || $this->isDaHuy();
  }
} // End class
