<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventSearchDataAction;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class DebtRecoveryContractEventSearchDataAction
{
	public function run(Request $request)
	{
		$events = CollectDebtContractEvent::query();

		$contractCode = $request->json('data.filter.contract_code');

		if (!empty($contractCode)) {
			$events = $events->where('contract_code', $contractCode);
		}

		// category care code
		$categoryCareCode = $request->json('data.filter.category_care_code');
		if (!empty($categoryCareCode)) {
			$events = $events->where('category_care_code', $categoryCareCode);
		}

		// service care code
		$serviceCareCode = $request->json('data.filter.service_care_code');
		if (!empty($serviceCareCode)) {
			$events = $events->where('service_care_code', $serviceCareCode);
		}

		// Status
		$status = $request->json('data.filter.status');
		if (!empty($status)) {
			$events = $events->where('status', $status);
		}

		// Not In Status
		$notInStatus = $request->json('data.filter.not_in_status');
		if (!empty($notInStatus)) {
			$events = $events->whereNotIn('status', $notInStatus);
		}

		if (!empty($request->json('data.filter.from_date'))) {
			$fromDate = Carbon::createFromFormat('d-m-Y', $request->json('data.filter.from_date'))->startOfDay()->timestamp;
			$events = $events->where('time_created', '>=', $fromDate);
		}

		if (!empty($request->json('data.filter.to_date'))) {
			$toDate = Carbon::createFromFormat('d-m-Y', $request->json('data.filter.to_date'))->endOfDay()->timestamp;
			$events = $events->where('time_created', '<=', $toDate);
		}


		$fields = [
			'id',
			'category_care_code',
			'service_care_code',
			'description',
			'status',
			'time_start',
			'number',
			'time_expired',
			'time_sented',
			'time_created_content',
			'time_created',
			'time_updated',
			'contract_code',
			'other_data->ledger as ledger_id'
		];

		$events = $events->orderBy('id', 'DESC')
			->select($fields)
			->paginate(
				$request->json('data.limit', 20),
				['*'],
				'page',
				$request->json('data.page', 1)
			);

		return $events;
	}
} // End class