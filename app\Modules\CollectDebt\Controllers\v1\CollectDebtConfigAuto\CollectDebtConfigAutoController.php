<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtConfigAuto;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Requests\v1\CollectDebtConfigAuto\DebtRecoveryPauseContractJobRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtConfigAuto\DebtRecoveryResumeContractJobRequest;
use Illuminate\Support\Facades\Cache;

class CollectDebtConfigAutoController extends Controller
{
	public function pauseContractJob(DebtRecoveryPauseContractJobRequest $request)
	{
		try {
			$currentPauseJob = CollectDebtConfigAuto::where('contract_code', $request->json('data.contract_code'))->first();
			
			if (!$currentPauseJob) {
				$createByDecode = json_decode($request->json('data.created_by'), true);
				$createByDecode['type'] = 'PAUSE_JOB';
				$createByDecode['time_modified_as_date'] = now()->toDateTimeString();
				$otherData[] = $createByDecode;

				$expiredTime = now()->addMinutes(CollectDebtEnum::SO_PHUT_DUNG_JOB_TRICH_TU_DONG)->timestamp;
				
				if ($request->json('data.type_dung_job') == 'TAT_TOAN_GIAM_PHI') {
					$expiredTime = now()->addMinutes(60)->timestamp;
					$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $request->json('data.contract_code'))->first();
					
					if (!$collectDebtSummary) {
						throw new Exception('Không tìm thấy thông tin hợp đồng');
					}

					if (!$collectDebtSummary->isTongTienTrichThanhCongLonHonGiaTriHopDong()) {
						throw new Exception('Tổng số tiền đã thu được CHƯA BẰNG giá trị của hợp đồng. Từ chối dừng job');
					}
				}
				
				$currentPauseJob = CollectDebtConfigAuto::forceCreate([
					'contract_code' => $request->json('data.contract_code'),
					'time_created'  => now()->timestamp,
					'time_expired'  => $expiredTime,
					'created_by'    => $request->json('data.created_by'),
					'other_data'	  => json_encode($otherData, JSON_UNESCAPED_UNICODE)
				]);
			}else {
				$currentPauseJob->time_expired =  now()->addMinutes(CollectDebtEnum::SO_PHUT_DUNG_JOB_TRICH_TU_DONG)->timestamp;
				$currentPauseJob->updated_by =  $request->json('data.created_by');
				$otherData = json_decode($currentPauseJob->other_data, true);

				$createByDecode = json_decode($request->json('data.created_by'), true);
				$createByDecode['type'] = 'PAUSE_JOB';
				$createByDecode['time_modified_as_date'] = now()->toDateTimeString();
				$otherData[] = $createByDecode;

				$currentPauseJob->other_data = json_encode($otherData, JSON_UNESCAPED_UNICODE);
				$currentPauseJob->save();
			}

			$message = sprintf('Đã dừng job thu tự động cho HĐ: %s', $request->json('data.contract_code'));
			
			// Delete cache
			Cache::forget(CacheEnum::LIST_HOP_DONG_DUNG_JOB);
			CollectDebtConfigAuto::getHopDongDungJobCache();
			
			return $this->successResponse($currentPauseJob->toArray(), $request, 200, $message);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function resumeContractJob(DebtRecoveryResumeContractJobRequest $request)
	{
		try {
			$currentPauseJob = CollectDebtConfigAuto::where('contract_code', $request->json('data.contract_code'))->first();
			
			if (!$currentPauseJob) {
				$createByDecode = json_decode($request->json('data.created_by'), true);
				$createByDecode['type'] = 'PAUSE_JOB';
				$createByDecode['time_modified_as_date'] = now()->toDateTimeString();
				$otherData[] = $createByDecode;

				$currentPauseJob = CollectDebtConfigAuto::forceCreate([
					'contract_code' => $request->json('data.contract_code'),
					'time_created'  => now()->timestamp,
					'time_expired'  => 0,
					'created_by'    => $request->json('data.created_by'),
					'other_data'	  => json_encode($otherData, JSON_UNESCAPED_UNICODE)
				]);
			}else {
				$currentPauseJob->time_expired = 0;
				$currentPauseJob->updated_by =  $request->json('data.created_by');
				$otherData = json_decode($currentPauseJob->other_data, true);

				$createByDecode = json_decode($request->json('data.created_by'), true);
				$createByDecode['type'] = 'RESUMSE_JOB';
				$createByDecode['time_modified_as_date'] = now()->toDateTimeString();
				$otherData[] = $createByDecode;

				$currentPauseJob->other_data = json_encode($otherData, JSON_UNESCAPED_UNICODE);
				$currentPauseJob->save();
			}

			return $this->successResponse($currentPauseJob->toArray(), $request, 200, 'Đã khởi động lại job thu tự động');
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
