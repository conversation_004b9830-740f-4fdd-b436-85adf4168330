<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class GetHopDongDangCoYeuCauTrichTayST
{
  public function run(): array
  {
    $collectDebtRequests = CollectDebtRequest::where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
                                             ->where('status', CollectDebtEnum::REQUEST_STT_MOI_TAO)
                                             ->select(['id', 'plan_ids', 'contract_code'])
                                             ->get();
    if ($collectDebtRequests->isEmpty()) {
      return [
        'plan_ids' => [],
        'contract_code' => []
      ];
    }

    $planIds = [];
    foreach ($collectDebtRequests as $rq) {
      $planIds = array_merge($planIds, $rq->getPlanIds());
    }

    return [
      'plan_ids' => array_unique($planIds),
      'contract_code' => $collectDebtRequests->pluck('contract_code')->toArray()
    ];
  }
}
