<?php

namespace App\Modules\CollectDebt\Middleware;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Traits\ApiResponser;
use Closure;
use Exception;

class CheckDuplicateManualRequestTodayMiddleware
{
  use ApiResponser;
  /**
   * Handle an incoming request.
   *
   * @param  \Illuminate\Http\Request  $request
   * @param  \Closure  $next
   * @return mixed
   */

  public function handle($request, Closure $next)
  {
    $requestsTrichTayChuaXuLy = CollectDebtRequest::where('contract_code', $request->json('data.contract_code'))
                                          ->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
                                          ->where('time_begin', '<=', time())
                                          ->where('time_expired', '>=', time())
                                          ->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
                                          ->whereIn('status', [
                                            CollectDebtEnum::REQUEST_STT_MOI_TAO,
                                            CollectDebtEnum::REQUEST_STT_DA_DUYET,
                                            CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA,
                                          ])
                                          ->get();
    
    if ($requestsTrichTayChuaXuLy->count() != 0) {
      $errorMessage = sprintf('Hợp đồng `%s` đang có yêu cầu trích tay chưa xử lý xong. Bạn không thể tạo thêm bất kỳ yêu cầu trích nào cho HĐ này nữa', $request->json('data.contract_code'));
      return $this->errorResponse(500, $errorMessage);
    }
    
    return $next($request);
  }
} // End class
