<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\Task;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TinhTongPhiConPhaiThuST
{  
  /**
   * Tính tổng phí còn phải thu của lịch thu gần nhất
   * Cần get ra bản ghi phí mới nhất
   *
   * @param CollectDebtSchedule $collectDebtSchedule [explicite description]
   *
   * @return float
   */
  public function run(CollectDebtSchedule $collectDebtSchedule): float {
    $totalAmount = CollectDebtSchedule::where('contract_code', $collectDebtSchedule->contract_code)
                                      ->where('isfee', 1)
                                      ->selectRaw("(request_amount_debit - success_amount_debit) as total_fee_hasnt_paid")
                                      ->orderBy('id', 'DESC')
                                      ->limit(1)
                                      ->first();

    return $totalAmount['total_fee_hasnt_paid'] ?? 0;
  }
} // End class