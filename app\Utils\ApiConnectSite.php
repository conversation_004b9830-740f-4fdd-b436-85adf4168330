<?php


namespace App\Utils;

use App\Lib\Logs;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use GuzzleHttp\Client;

class ApiConnectSite {

    protected $Log;
    protected $_usernameRequest = 'appRequest';
    protected $_language = 'vi';

    protected $_time_request;
    protected $_version;
    protected $_channel_code;
    protected $_secret_key;
    protected $_checksum;

    public function __construct($url) {
        $this->_apiUrl = $url;
        $this->_params = array();
        $this->_time_request = time();
        $this->_channel_code = 'WEBPARTNER';
        $this->_version = '1.0';
        $this->_secret_key = '342423423423423423423423';
        $this->Log = new Logs();

    }

    public function callApi( $param ,  $method = 'POST') {
        $this->Log->writeFileLog('[API] param '. json_encode( $param,JSON_UNESCAPED_SLASHES));
        $client = new Client([
            'headers' => [ 'Content-Type' => 'application/json' ]
        ]);
        try {
            $this->_checksum = $this->_createCheckSum($param);
            $response = $client->request($method, $this->_apiUrl, [
                    'body' => json_encode(
                        [
                            'data' => $param,
                            'checksum'=>$this->_checksum,
                            'channel_code' => $this->_channel_code,
                            'time_request' => $this->_time_request,
                            'version' => $this->_version,
                        ]
                    )
                ]
            );
            $result =$response->getBody()->getContents();
            $this->Log->writeFileLog('[API] response '. json_encode( $result,JSON_UNESCAPED_SLASHES));
            return $result;

        } catch (RequestException $re) {
            $this->Log->writeFileLog('[API] response error '. json_encode( $re ,JSON_UNESCAPED_SLASHES));
            // For handling exception.
        }

        return false;

    }
    protected function _createCheckSum($params) {
        $data = json_encode($params, JSON_UNESCAPED_UNICODE);
        $this->_checksum = md5($data.$this->_version . $this->_channel_code .$this->_time_request. $this->_secret_key);
        return $this->_checksum;
    }





}
