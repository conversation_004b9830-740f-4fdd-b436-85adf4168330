<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestReCancelAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestReCancelRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction\DebtRecoveryRequestCancelPaymentAction;

class DebtRecoveryRequestReCancelAction
{
	public function run(DebtRecoveryRequestReCancelRequest $request)
	{
		$collectDebtRequest = CollectDebtRequest::query()->find($request->json('data.id'));

		if (!$collectDebtRequest) {
			throw new Exception('Lỗi: không tìm thấy thông tin yêu cầu trích nợ');
		}


		mylog([
			'Yc thuc hien huy lai' => $collectDebtRequest->only(['id', 'contract_code', 'partner_request_id', 'partner_transaction_id'])
		]);

		if (!$collectDebtRequest->isPaymentViaChannelMpos()) {
			throw new Exception('Lệnh trích này không đi qua kênh MPOS. Từ chối xử lý');
		}

		$checkResult = app(CheckRequestViaMposSubAction::class)->run(
			$collectDebtRequest,
			$request,
			true
		);

		mylog([
			'Ket qua kiem tra lenh mpos' => $checkResult
		]);

		if (!empty($checkResult['data']['status']) && $checkResult['data']['status'] == 'CANCEL') {
			$msg = sprintf(
				'Lệnh trích: %s (chứng từ: %s) đã được CANCEL, từ chối thao tác',
				$collectDebtRequest->partner_request_id,
				$collectDebtRequest->partner_transaction_id
			);

			throw new Exception($msg);
		}

		// Thực hiện canceled luôn
		$cancelMposRequest = app(DebtRecoveryRequestCancelPaymentAction::class)->run(
			$collectDebtRequest
		);

		mylog(['Ket qua thuc hien CANCEL' => $cancelMposRequest]);
		
		if (
			!empty($cancelMposRequest['data']['error_code']) && $cancelMposRequest['data']['error_code'] == '00'
		) {
			return $collectDebtRequest;
		}

		// Cancel thất bại
		$msg = sprintf(
			'Từ chối lệnh: %s (chứng từ: %s) thất bại. Mpos Err: %s',
			$collectDebtRequest->partner_request_id,
			$collectDebtRequest->partner_transaction_id,
			$cancelMposRequest['data']['description'] ?? 'Không xác định nguyên nhân'
		);

		throw new Exception($msg);
	}
}  // End class