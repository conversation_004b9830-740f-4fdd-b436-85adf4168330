<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanUpdateAction;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanUpdateRequest;
use Exception;

class DebtRecoveryContractPlanUpdateAction
{  

  public function run(DebtRecoveryContractPlanUpdateRequest $request): CollectDebtSchedule
  {
    $collectDebtSchedule = CollectDebtSchedule::find($request->json('data.id'));
    throw_if(!$collectDebtSchedule, new Exception('Không tìm thấy lịch thu'));
    throw_if(!$collectDebtSchedule->isNew(), new Exception('Không thể cập nhật do lịch thu không ở trạng thái "<PERSON>ới tạo"'));

    $params = $request->only([
      'data.profile_id',
      'data.contract_code',
      'data.contract_type',
      'data.type',
      'data.isfee',
      'data.debit_begin',
      'data.debit_end',
      'data.rundate',
      'data.time_start',
      'data.time_end',
      'data.amount_period_debit',
      'data.request_amount_debit',
      'data.success_amount_debit',
      'data.other_data',
      'data.profile_data',
      'data.description',
      'data.is_settlement',
      'data.updated_by',
      'data.time_updated',
    ])['data'];
    
    $collectDebtSchedule->update($params); 
    
    return $collectDebtSchedule->refresh();
  }
} // End class