<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtGuide;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\CollectDebtTrichNgayRule;

class DebtRecoveryContractGuideUpdateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:1'],
      'data.profile_id' => ['required', 'string', 'max:25'],
      'data.contract_code' => ['required', 'string', 'max:50', 'unique:debt_recovery_contract_guide,contract_code'],
      'data.contract_cycle' => ['required', 'numeric', 'min:1'],
      
      'data.contract_type' => [
        'required', 
        'numeric', 
        Rule::in([
          CollectDebtEnum::GUIDE_HD_TRICH_NGAY,
          CollectDebtEnum::GUIDE_HD_TRICH_KY,
          CollectDebtEnum::GUIDE_HD_GIA_HAN,
        ]),
        new CollectDebtTrichNgayRule()
      ],

      'data.contract_intervals' => ['required', 'numeric', 'min:1'],
      'data.contract_time_start' => ['required', 'numeric'],
      'data.contract_time_end' => ['required', 'numeric'],
      'data.amount' => ['required', 'numeric', 'min:1000000'],

      'data.payment_guide' => ['required', 'array'],
      'data.payment_guide.*.payment_method_code' => ['required', 'string', Rule::in(['MPOS', 'IB_OFF', 'VIRTUALACCOUNT'])],
      'data.payment_guide.*.payment_channel_code' => ['required', 'string'],
      'data.payment_guide.*.payment_account_id' => ['required', 'string'],
      'data.payment_guide.*.other_data' => ['required', 'array'],
      'data.payment_guide.*.other_data.payment_account_holder_name' => ['nullable', 'string', 'max:255'],
      'data.payment_guide.*.other_data.payment_account_branch' => ['nullable', 'string', 'max:255'],
      'data.payment_guide.*.other_data.payment_account_bank_code' => ['nullable', 'string', 'max:255'],

      'data.list_fee' => ['required', 'array'],
      'data.list_fee.*.type' => [
        'required', 
        'numeric', 
        Rule::in([
          CollectDebtEnum::GUIDE_LOAI_PHI_HOP_DONG,
          CollectDebtEnum::GUIDE_LOAI_PHI_THAM_DINH_HO_SO,
          CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_UU_DAI_THAM_DINH_HO_SO,
          CollectDebtEnum::GUIDE_LOAI_PHI_GIAI_NGAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_THU_HOI,
          CollectDebtEnum::GUIDE_LOAI_PHI_GIA_HAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY,
          CollectDebtEnum::GUIDE_LOAI_UU_DAI_PHI_THAM_GIA,
          CollectDebtEnum::GUIDE_LOAI_PHI_HOAN,
        ])
      ],

      'data.list_fee.*.percent_fee' => [ 'required', 'numeric', 'min:0'],
      'data.list_fee.*.flat_fee' => [ 'required', 'numeric', 'min:0'],
      'data.list_fee.*.fee_max' => [ 'required', 'numeric', 'min:0'],
      'data.list_fee.*.fee_min' => [ 'required', 'numeric', 'min:0'],

      'data.other_data' => ['required', 'json'],
      'data.profile_data' => ['required', 'json'],
      'data.description' => ['nullable', 'string', 'max:255'],
      'data.updated_by' => ['required', 'string', 'max:255'],
    ];
  }

  protected function passedValidation()
  {
    $params = $this->all();
    $params['data']['time_updated'] = time();

    $params['data']['payment_guide'] = json_encode($params['data']['payment_guide'], JSON_UNESCAPED_UNICODE);
    $params['data']['list_fee'] = json_encode($params['data']['list_fee'], JSON_UNESCAPED_UNICODE);
    $this->merge($params);
  }
} // End class
