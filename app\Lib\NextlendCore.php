<?php

namespace App\Lib;

use Illuminate\Support\Str;
use App\Utils\ApiConnectPartner;
use Exception;
use Illuminate\Support\Facades\Http;

class NextlendCore
{
  protected $_API_URL            = '';
  protected $_API_APP_ID         = '';
  protected $_API_APP_SECRET_KEY = '';
  protected $_USERNAME_REQUEST   = 'guest';
  protected $_USERID_REQUEST     = '1';
  protected $_API_KET_ENCRYPTION_DATA = '';

  protected $_log;
  public $uniqueKeyLog = '';

  public $timeStart;
  public $apiDuring;

  private $__coreResponseResult;

  public function __construct()
  {
    $this->timeStart = now();
    $this->apiDuring = microtime(true);

    $this->_API_URL = env('NEXTLEND_CORE_API_URL');
    $this->_API_APP_SECRET_KEY = env('NEXTLEND_CORE_API_APP_SECRET_KEY');
    $this->_API_APP_ID = env('NEXTLEND_CORE_API_APP_ID');
    $this->_API_KET_ENCRYPTION_DATA = env('NEXTLEND_CORE_API_KET_ENCRYPTION_DATA');
  }

  /**
   * Method call
   *
   * @param array $inputs Là các tham số trong request muốn truyền để truy vấn
   *
   * @return void
   */
  public function callRequest(array $inputs = [], string $functionName = '', string $method='post')
  {
    $this->uniqueKeyLog = sprintf('[API-CORE]--%s--|%s-%s', $functionName, Str::random(32), $method);
    $this->_log[$this->uniqueKeyLog]['api_url'] = $this->_API_URL;
    $this->_log[$this->uniqueKeyLog]['time_start_at'] = $this->timeStart;
    $this->_log[$this->uniqueKeyLog]['func']       = $functionName;
    $this->_log[$this->uniqueKeyLog]['method']     = $method;


    $requestInput = $this->_makeRequestInput($inputs, $functionName, ['id' => $inputs['admin_user_id'] ?? $this->_USERID_REQUEST]);
    
		$this->_log[$this->uniqueKeyLog]['requestInput'] = $requestInput;

		$headerParam = [
      'app_id' => $this->_API_APP_ID, 
      'app_secret_key' => $this->_API_APP_SECRET_KEY,
      'cache-control' => 'no-cache'
    ];

		$this->_log[$this->uniqueKeyLog]['headerParam'] = $headerParam;

    $response = Http::withHeaders($headerParam)->withOptions([
			'debug' => false,
			'verify' => false
		])->retry(1, 100);
    
		$result = "START";

		try {
			$method = trim(strtolower($method));

			switch ($method) {
				case 'post': 
					$result = $response->asForm()->post($this->_API_URL, $requestInput);
					break;
	
				case 'get': 
					$result = $response->get($this->_API_URL, $requestInput);
					break;
	
				case 'put': 
					$result = $response->asForm()->put($this->_API_URL, $requestInput);
					break;
	
				default: 
					$result = $response->asForm()->post($this->_API_URL, $requestInput);
					break;
			}
		}catch(\Throwable $th) {
			mylog(['traceNextLend' => Helper::traceError($th)]);
			throw $th;	
		}
    

		if (gettype($result) != 'string') {
			$body = @$result->body();
			mylog([
				'Body is' => $body,
				'Is Ok' => $result->ok(),
				'Is Successfully' => $result->successful()
			]);
		}else {
			mylog([
				'Loi logic get' => 'result van bang string',
			]);
		}
		
		if ($result->clientError()) {
			mylog(['ClientError' => $body]);
		}

		if ($result->serverError()) {
			mylog(['ServerError' => $body]);
		}

		if ($result->failed()) {
			mylog(['Failed' => $body]);
		}

		if (!$result) {
			mylog([
				'Error_Empty_Result' => 'ok',
				'Result' => $result,
				'Type' => gettype($result)
			]);
		}

    $this->__coreResponseResult = $result->json();
    $this->_log[$this->uniqueKeyLog]['result'] = $this->__coreResponseResult;
    return $this;
  }

  public function _makeRequestInput(array $params=[], string $func='', array $userHandler=[]): array {
    $userRequestId = !empty($userHandler['id']) ? $userHandler['id'] : $this->_USERID_REQUEST;
    $hash          = $this->_buildHash($params, $userRequestId);

    $chuoiTruocMd5 = $func . $hash . $this->_API_APP_SECRET_KEY;
    $this->_log[$this->uniqueKeyLog]['before_md5'] = $chuoiTruocMd5;
    $checkSum      = md5($chuoiTruocMd5);
    
    $this->_log[$this->uniqueKeyLog]['params_raw'] = $params;

    return [
      'func'             => $func,
      'checksum'         => $checkSum,
      'params'           => $hash,
      'username_request' => !empty($userHandler['name']) ? $userHandler['name'] : $this->_USERNAME_REQUEST,
      'users_request_id' => $userRequestId,
      'language'         => 'vi',
      'client_ip'        => request()->ip()
    ];
  }

  protected function _buildHash(array $params=[], $userRequestId=''): string {
    $params['site'] = 1;
    $params['admin_user_id'] = $userRequestId;

    $paramsJsonEncode = json_encode($params, true);

    $paramsEncrypt = Encryption::Encrypt($paramsJsonEncode, $this->_API_KET_ENCRYPTION_DATA);
    return $paramsEncrypt;
  }

  public function decryptData($needAnalysis=false) {
    $this->_log[$this->uniqueKeyLog]['time_end_at'] = now();
    $this->_log[$this->uniqueKeyLog]['api_during'] = microtime(true) - $this->apiDuring;

    if ( !$this->__coreResponseResult || !$this->__coreResponseResult['errorCode'] || $this->__coreResponseResult['errorCode'] != strtolower('success')) {
      // Dùng khi xử lý verify OTP
      if ($needAnalysis) {
        return $this->__coreResponseResult;
      }

			$this->_log[$this->uniqueKeyLog]['Loi call api'] = $this->__coreResponseResult;
			app('mylog')->cc($this->_log);
			
      throw new \Exception('Lỗi gọi hệ thống Core đối tác: ' . $this->__coreResponseResult['errorDescription'], 500);
    }

    $value = [];

    try {
      $result = Encryption::Decrypt($this->__coreResponseResult['data'], $this->_API_KET_ENCRYPTION_DATA);
      $value = json_decode($result, true);
      $this->_log[$this->uniqueKeyLog]['result_descrypt_success'] = $value;
    }catch(\Throwable $e) {
      $this->_log[$this->uniqueKeyLog]['result_descrypt_error'] = $e->getTrace();
      app('mylog')->cc($this->_log);
      throw new Exception("Can not decrypt value: " . $e->getMessage());
    }finally {
			$this->_log[$this->uniqueKeyLog]['before_md5'] = @Str::limit($this->_log[$this->uniqueKeyLog]['before_md5'], 16);
			$this->_log[$this->uniqueKeyLog]['requestInput']['params'] = @Str::limit($this->_log[$this->uniqueKeyLog]['requestInput']['params'], 16);
			$this->_log[$this->uniqueKeyLog]['headerParam'] = ['Đã được hidden'];
      app('mylog')->cc($this->_log);
      return $value;
    }
  }
} // End class