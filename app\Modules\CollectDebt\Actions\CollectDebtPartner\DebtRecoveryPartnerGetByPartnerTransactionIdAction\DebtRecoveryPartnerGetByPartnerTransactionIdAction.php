<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerGetByPartnerTransactionIdAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerGetByPartnerTransactionIdRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerFindWhereRawAction\SubAction\DebtRecoveryPartnerFindWhereRawSubAction;

class DebtRecoveryPartnerGetByPartnerTransactionIdAction
{
    public function run(DebtRecoveryPartnerGetByPartnerTransactionIdRequest $request): CollectDebtPartner
    {
        $where = sprintf('partner_transaction_id = "%s"', $request->json('data.partner_transaction_id'));
        $where .= sprintf(' AND payment_channel_code = "%s"', $request->json('data.payment_channel_code'));

        $collectDebtPartner = app(DebtRecoveryPartnerFindWhereRawSubAction::class)->run($where, true);
        return $collectDebtPartner;
    }
}
