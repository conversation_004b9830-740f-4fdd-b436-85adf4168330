<?php
namespace App\Modules\CollectDebtGateway\Controllers;

use App\Modules\CollectDebtGateway\Requests\CollectDebtRecoveryGatewayLog\GetAllByFollowRequest;
use App\Modules\CollectDebtGateway\Service\CollectDebtRecoveryGatewayLogService;

class CollectDebtRecoveryGatewayLogController extends Controller
{
    protected CollectDebtRecoveryGatewayLogService $collectDebtLogService;

    public function __construct(
        CollectDebtRecoveryGatewayLogService $collectDebtLogService
    ){
      $this->collectDebtLogService = $collectDebtLogService;
    }

    public function getAllByFollow(GetAllByFollowRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->collectDebtLogService->getAllByFollow($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

}