<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\GetPlanSummaryHandleAfterAccoutingAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;
use Illuminate\Database\Eloquent\Collection;

/**
 * CHỈ XỬ LÝ TYPE=PLAN trong OTHER_DATA
 */
class GetPlanSummaryHandleAfterAccoutingAction
{
  const CO_CHAM_KY = 1;
  const KHONG_CHAM_KY = 2;

  public $returnData = [];

  public function run(string $contractCode='TEST_231227_8B2LZ'): array {
    $groupPlans = CollectDebtSchedule::query()->where('contract_code', $contractCode)->get()->groupBy('master_id');
    throw_if(!$groupPlans, new Exception('Không tìm thấy thông tin lịch thu'));

    $groupPlans->transform(function (Collection  $group, int $planId) {
      $lichThuGoc = $group->where('id', $planId)->first();

      // tất cả các lịch trong group hoàn thành thì mới đc sét là hoàn thành
      $statusCuaTatCaLichTrongGroup = $group->every(function (CollectDebtSchedule $plan) {
        return $plan->isLichThuDaHoanThanh();
      });

      $tongTienGocDaThu = $group->sum(function (CollectDebtSchedule  $plan) {
        if ($plan->isLichThuGoc()) {
          return $plan->success_amount_debit;
        }

        return 0;
      });

      $tongTienPhiDaThu = $group->sum(function (CollectDebtSchedule  $plan) {
        if ($plan->isLichThuPhi()) {
          return $plan->success_amount_debit;
        }

        return 0;
      });

      $tongTienPhiDaGiam = $group->sum(function (CollectDebtSchedule  $plan) {
        if ($plan->isLichThuDuocApDungGiamPhi()) {
          return $plan->getSoTienDuocPhamPhi();
        }

        return 0;
      });

      $tongPhiPhaiThu = $group->sum(function (CollectDebtSchedule  $plan) {
        if ($plan->isLichThuPhi() && !$plan->isLichThuDaHoanThanh()) {
          return $plan->getSoTienConPhaiThanhToan();
        }

        return 0;
      });

      $isChamKy = $group->contains(function (CollectDebtSchedule  $plan) {
        return $plan->isLichThuPhiChamKy();
      });

      $lichThuPhiChamKy = $group->first(function (CollectDebtSchedule  $plan) {
        return $plan->isLichThuPhiChamKy() && $plan->isLichThuPhiChinh();
      });

      $phiQuaHanChuaThu = $group->sum(function (CollectDebtSchedule  $plan) {
        if ( $plan->isLichThuPhiQuaHan() && ($plan->isLichThuMoi() || $plan->isLichThuDangHachToan()) ) {
          return $plan->request_amount_debit;
        }

        return 0;
      });

      $phiChamKyChuaThu = $group->sum(function (CollectDebtSchedule  $plan) {
        if ( $plan->isLichThuPhiChamKy() && ($plan->isLichThuMoi() || $plan->isLichThuDangHachToan()) ) {
          return $plan->request_amount_debit;
        }

        return 0;
      });

      $this->returnData[] = [
        'id'              => $lichThuGoc->id,
        'time_cycle'      => $lichThuGoc->time_start, // thời gian thu lịch gốc
        'time_over_cycle' => $lichThuPhiChamKy ? $lichThuPhiChamKy->time_start : 0, // Thời gian bắt đầu quá kỳ
        'amount'          => $lichThuGoc->request_amount_debit, // Tổng số tiền gốc kỳ cần phải thu
        'fee'             => $tongPhiPhaiThu, // Tổng phí phải thu
        'amount_paid'     => $tongTienGocDaThu, // Số tiền gốc đã thu
        'fee_paid'        => $tongTienPhiDaThu, // Tổng phí đã thu
        'overdue_cycle'   => $isChamKy ? self::CO_CHAM_KY : self::KHONG_CHAM_KY, //  Có quá kỳ TT hay không (1-Có,2-Không)
        'note'            => $lichThuGoc->description,
        'status'          => $statusCuaTatCaLichTrongGroup ? CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH : CollectDebtEnum::SCHEDULE_STT_MOI,
        'data'            => [
          'id'            => $lichThuGoc->id,
          'fee_overdue_cycle_reduction' => $tongTienPhiDaGiam, // Tổng phí đã giảm <Phí quá kỳ đã giảm>,
          'fee_overdue' => $phiQuaHanChuaThu, // Phí quá hạn chưa thu,
          'fee_overdue_cycle' => $phiChamKyChuaThu // Số phí chậm kỳ chưa thu
        ],
      ];

      return $group;
    });

    return $this->returnData;
  }
}