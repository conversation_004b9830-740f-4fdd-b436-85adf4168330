<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiSapDenKyThanhToanAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;

class DigitalNotiSapDenKyThanhToanAction
{
	public array $listContractSapDenKy = [];

	public function run()
	{
		$plans = CollectDebtSchedule::query()
			->whereBetween('time_start', [
				now()->startOfDay()->timestamp,
				now()->addDays(3)->endOfDay()->timestamp
			])
			->where('contract_type', CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY)
			->where('is_settlement', '!=', CollectDebtEnum::SCHEDULE_LA_LICH_TAT_TOAN) // khong phai lich tat toan
			->where('status', '!=', CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH)
			->whereColumn('id', '=', 'master_id') // Lich (C)
			->select(['id', 'contract_code', 'time_start', 'time_end', 'rundate'])
			->get();
		if ($plans->isEmpty()) {
			return;
		}

		$plans->load(['collectDebtSummary:contract_code,other_data']);
		$listContractCode = $plans->pluck('contract_code')->toArray();
		$listContractShare = CollectDebtShare::query()
																				 ->whereIn('contract_code', $listContractCode)
																				 ->get(['contract_code', 'profile_data'])
																				 ->keyBy('contract_code');
																				 
		$plans->each(function (CollectDebtSchedule $plan) use ($listContractShare) {
			$collectDebtSummary = $plan->collectDebtSummary;
			$plansSummaryOtherData = $collectDebtSummary->getSummaryOtherDataItem('PLAN');

			/**
			 * array:11 [▼
						"id" => 51529
						"time_cycle" => 1714237200
						"time_over_cycle" => 1714323600
						"amount" => 3333333
						"fee" => 1666  -> Số phí còn lại phải thanh toán tiếp
						"amount_paid" => 3333333
						"fee_paid" => 1667  -> Số phí đã thanh toán | Muốn tính tổng phí: Thì cộng giá trị này với `fee`
						"overdue_cycle" => 1
						"note" => "Hợp đồng v4"
						"status" => 1
						"data" => array:4 [▶]
					]
			 */

			$soTienDaHoanTra = $collectDebtSummary->total_amount_paid + $collectDebtSummary->total_fee_paid;

			$tongSoTienChuaThanhCacKyTruocVaKySapToiHan = collect($plansSummaryOtherData['data'])->sum(function ($item) use ($plan) {
				$tongPhiBaoGomPhiChuaThanhToanVaDaThanhToan = $item['fee'] + $item['fee_paid'];

				if ($item['id'] <= $plan->id) {
					return ($item['amount'] + $tongPhiBaoGomPhiChuaThanhToanVaDaThanhToan) - ($item['amount_paid'] + $item['fee_paid']);
				}

				return 0;
			});


			// Số tiền HĐ + các loại phí - số tiền đã hoàn trả
			$duNoCuoiKy = $tongSoTienChuaThanhCacKyTruocVaKySapToiHan;

			// Tổng phí chậm kỳ đã sinh ra - Tổng phí chậm kỳ đã thnh toán																													 
			$phiPhatThanhToanChamKy = $collectDebtSummary->fee_overdue_cycle - $collectDebtSummary->fee_overdue_cycle_paid;


			$thanhToanToiThieu = $tongSoTienChuaThanhCacKyTruocVaKySapToiHan;

			$thanhToanTruocNgay = $plan->time_start_as_date->format('d/m/Y');

			$dataSapDenKy = [
				'soTienDaHoanTra' => Helper::makeVndCurrency($soTienDaHoanTra ?? 0),
				'duNoCuoiKy' => Helper::makeVndCurrency($duNoCuoiKy ?? 0),
				'phiPhatChamKy' => Helper::makeVndCurrency($phiPhatThanhToanChamKy ?? 0),
				'thanhToanToiThieu' => Helper::makeVndCurrency($thanhToanToiThieu ?? 0),
				'thanhToanTruocNgay' => $thanhToanTruocNgay,
				'contractCode' => $collectDebtSummary->contract_code,
				'isDungNgayDenKy' => now()->isSameDay($plan->time_start_as_date),
				'soNgayDenKy' => now()->startOfDay()->diffInDays($plan->time_start_as_date)
			];

			if (!$dataSapDenKy['isDungNgayDenKy']) {
				$notifyData = $this->__buildDataSapDenKy($dataSapDenKy, $listContractShare->get($plan->contract_code));
			}else {
				$notifyData = $this->__buildDataDenKyDungNgay($dataSapDenKy, $listContractShare->get($plan->contract_code));
			}

			$digitalNoti = CollectDebtDigitalNoti::query()->forceCreate([
				'contract_code' => $collectDebtSummary->contract_code,
				'type' => 'upcoming_cycle',
				'object_model' => CollectDebtShare::class,
				'object_id' => $collectDebtSummary->id,
				'digital_request' => json_encode($notifyData),
				'status' => CollectDebtDigitalNoti::STT_DA_BUILD_PARAM,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp
			]);

			if (!$digitalNoti) {
				mylog(['Loi khong tao duoc noti qua han cho hd' => $collectDebtSummary->contract_code]);
				return $digitalNoti;
			}

			$this->listContractSapDenKy[] = $plan->contract_code;
			return;
		});

		return $this->listContractSapDenKy;
	}


	/**
	 * array:7 [▼
			"soTienDaHoanTra" => "0"
			"duNoCuoiKy" => "8,002,000"
			"phiPhatChamKy" => "0"
			"thanhToanToiThieu" => "8,002,000"
			"thanhToanTruocNgay" => "30/05/2025"
			"contractCode" => "TEST-250522-7OJCK"
			"isDungNgayDenKy" => true
		]
			*/
	private function __buildDataSapDenKy($dataSapDenKy, CollectDebtShare $collectDebtShare) {
		$title = 'Nhắc đến hạn thanh toán';
		$body = sprintf('Khoản ứng %s của bạn sắp đến hạn thanh toán, vui lòng chuẩn bị trả nợ', $dataSapDenKy['contractCode']);
		$content = view('trichno.noti.sap-den-ky', ['dataSapDenKy' => $dataSapDenKy])->render();
		

		$notifyData = CollectDebtDigitalNoti::buildNotiData(
			$title,
			$body,
			$content,
			'DIGITAL_SAPDENKY',
			$collectDebtShare->getMposMcId(),
			'Hợp đồng sắp đến kỳ thanh toán'
		);

		return $notifyData;
	}

	private function __buildDataDenKyDungNgay($dataSapDenKy, CollectDebtShare $collectDebtShare) {
		$title = 'Hôm nay đến kỳ thanh toán';
		$body = sprintf('Hôm nay là ngày đến kỳ thanh toán khoản ứng %s, vui lòng kiểm tra lại.', $dataSapDenKy['contractCode']);
		$content = view('trichno.noti.den-ky', ['dataSapDenKy' => $dataSapDenKy])->render();
		
		$notifyData = CollectDebtDigitalNoti::buildNotiData(
			$title,
			$body,
			$content,
			'DIGITAL_DENKY',
			$collectDebtShare->getMposMcId(),
			'Hợp đồng đến kỳ thanh toán'
		);

		return $notifyData;
	}
} // End class
