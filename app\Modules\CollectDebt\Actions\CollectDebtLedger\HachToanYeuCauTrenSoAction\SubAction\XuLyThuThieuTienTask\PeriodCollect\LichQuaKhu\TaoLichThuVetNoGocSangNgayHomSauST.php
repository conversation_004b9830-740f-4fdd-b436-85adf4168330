<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\LichQuaKhu;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TaoLichThuVetNoGocSangNgayHomSauST
{
  public function run(CollectDebtSchedule $collectDebtSchedule, float $soTienGocThuVet=0, $tomorrowRundate='') {
    $otherData = json_encode([
      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'request_created_channel' => '',
        ],
        'note' => 'Thu vét nợ gốc sang hôm sau'
      ]
    ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

    $scheduleParam = [
      'profile_id'           => $collectDebtSchedule->profile_id,
      'contract_code'        => $collectDebtSchedule->contract_code,
      'contract_type'        => $collectDebtSchedule->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
      'debit_begin'          => $collectDebtSchedule->debit_begin,
      'debit_end'            => $collectDebtSchedule->debit_end,
      'rundate'              => $tomorrowRundate,
      'time_start'           => $collectDebtSchedule->time_start_as_date->copy()->addDay()->startOfDay()->timestamp,
      'time_end'             => $collectDebtSchedule->time_end_as_date->copy()->addDay()->endOfDay()->timestamp,
      'amount_period_debit'  => $collectDebtSchedule->amount_period_debit,
      'request_amount_debit' => $soTienGocThuVet,
      'success_amount_debit' => 0,
      'other_data'           => $otherData,
      'description'          => $collectDebtSchedule->collectDebtSchedule,
      'is_settlement'        => $collectDebtSchedule->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $collectDebtSchedule->created_by,
      'time_created'         => time(),
      'cycle_number'         => $collectDebtSchedule->cycle_number,
      'master_id'         => $collectDebtSchedule->master_id,
    ];

    return CollectDebtSchedule::forceCreate($scheduleParam);
  }
} // End class