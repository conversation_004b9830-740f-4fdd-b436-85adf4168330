<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction;

use App\Lib\Helper;
use Exception;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\ReplaceNoiDungBySummarySubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\TaoEventMailSapToiHanTatToanSubAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;

class DebtRecoveryHandleSapToiHanAction
{
	private array $__hopDongDaXuLySapToiHanTatToan = [];

	public function run()
	{
		$info = json_encode([
			'Type' => 'MAIL_SAP_TOI_HAN_TAT_TOAN',
			'Time' => now()->toDateTimeString(),
			'LogId' => request('api_request_id')
		], JSON_PRETTY_PRINT);

		@TelegramAlert::sendEmails($info);
		
		$whereRaw = sprintf("DATEDIFF( DATE(FROM_UNIXTIME(contract_time_end)), '%s') IN (5,3,2,1)", now()->format('Y-m-d'));

		$collectDebtGuides = CollectDebtGuide::query()
																				 ->whereRaw($whereRaw)
																				 ->whereDoesntHave('collectDebtEvents', function ($query) {
																					return $query->where('service_care_code', 'MAIL')
																											 ->where('category_care_code', 'NOTIFY_CONTRACT_DUE')
																											 ->whereBetween('time_created', [
																													now()->startOfDay()->timestamp,
																													now()->endOfDay()->timestamp,
																											 ]);
																				 })
																				 ->whereHas('collectDebtSummary', function ($query) {
																					 $query->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
																				 })
																				 ->get();
		
		if ($collectDebtGuides->isEmpty()) {
			throw new Exception('Khong co thong tin HD sap toi han tat toan');
			return;
		}

		$collectDebtGuides =  $collectDebtGuides->load(['collectDebtSummary', 'collectDebtShare']);

		$emailTemplateContentTheoNgay = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => 'NOTIFY_CONTRACT_DUE',
			'customer_service_care_code' => 'MAIL',
		]);

		$emailTemplateContentTheoKy = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => 'NOTIFY_CONTRACT_DUE_PERIOD',
			'customer_service_care_code' => 'MAIL',
		]);
		
		foreach ($collectDebtGuides as $collectDebtGuide) {
			if (@$collectDebtGuide->collectDebtSummary->status_contract == CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN) {
				mylog([
					'HopDongDaTatToan' => 'ok',
					'ContractCode' => $collectDebtGuide->contract_code
				]);

				continue;
			}

			try {
				mylog([
					'-----------------------' => sprintf('%s -----------------', $collectDebtGuide->contract_code),
					'Hop Dong Sap Toi Han' => $collectDebtGuide->contract_code,
				]);
				
				// Tao event doan nay
				$collectDebtEvent = app(TaoEventMailSapToiHanTatToanSubAction::class)->run(
					$collectDebtGuide->collectDebtShare,
					$collectDebtGuide->collectDebtSummary,
				);
				
				// Replace bien vao trong noi dung email
				$emailContent = '';
	
				if ($collectDebtGuide->isChiDanHopDongTrichNgay()) {
					if (empty($emailTemplateContentTheoNgay['content'])) {
						mylog(['[EMPTY MAIL CONTENT NGAY]' => 'khong co thong tin mail template content']);
						continue;
					}

					$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
						$collectDebtEvent,
						$collectDebtGuide->collectDebtSummary,
						$emailTemplateContentTheoNgay
					);
				}
	
				if ($collectDebtGuide->isChiDanHopDongTrichKy()) {
					if (empty($emailTemplateContentTheoKy['content'])) {
						mylog(['[EMPTY MAIL CONTENT KY]' => 'khong co thong tin mail template content']);
						continue;
					}

					$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
						$collectDebtEvent,
						$collectDebtGuide->collectDebtSummary,
						$emailTemplateContentTheoKy
					);
				}
				
				if (empty($emailContent)) {
					mylog(['[LOI KHONG XAC DINH]' => 'EMPTY EMAIL CONTENT']);
					continue;
				}
	
				$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
				$collectDebtEvent->content = $emailContent;
				$savedResult = $collectDebtEvent->save();
	
				if (!$savedResult) {
					mylog(['[LOI SAVED EVENT]' => $collectDebtEvent->id]);
					continue;
				}
	
				$this->__hopDongDaXuLySapToiHanTatToan[] = $collectDebtEvent->only(['id', 'contract_code']);
			}catch(\Throwable $th) {
				mylog(['[LOI VONG LAP]' => Helper::traceError($th)]);
				continue;
			}
		};

		@TelegramAlert::sendEmails("Sap Toi Han Tat Toan: " . json_encode($this->__hopDongDaXuLySapToiHanTatToan, JSON_UNESCAPED_UNICODE));
		return $this->__hopDongDaXuLySapToiHanTatToan;
	}
} // End class