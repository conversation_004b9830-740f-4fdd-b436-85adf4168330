<?php

namespace App\Modules\CollectDebtGateway\Controllers;

use App\Modules\CollectDebtGateway\Requests\PartnerNotifyDebtGateway\ReceiveNotifyRequest;
use App\Modules\CollectDebtGateway\Service\PartnerNotifyCollectDebtGatewayService;

class PartnerNotifyCollectDebtGatewayController extends Controller
{
    protected PartnerNotifyCollectDebtGatewayService $partnerNotifyService;

    public function __construct(PartnerNotifyCollectDebtGatewayService $partnerNotifyService)
    {
        $this->partnerNotifyService = $partnerNotifyService;
    }

    public function receiveNotify(ReceiveNotifyRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->partnerNotifyService->receiveNotify($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }
}
