<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\EnableSettingMiddleware;


$namespace = 'App\Modules\EmailRemind\Controllers';

Route::group(['namespace' => $namespace], function () {
	Route::any('/DebtRecoveryContractEventBuildContent', 'DebtRecoveryContractEventController@buildContent')
			 ->name('DebtRecoveryContractEventBuildContent')
			 ->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_GUI_MAIL_HANG_NGAY'));
			 
	
	Route::any('/DebtRecoveryContractEventCreateSend', 'DebtRecoveryContractEventController@createSend')
			 ->name('DebtRecoveryContractEventCreateSend')
			 ->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_THUC_HIEN_CHAY_EVENT'));

	Route::any('/DebtRecoveryContractEventGetByContract', 'DebtRecoveryContractEventController@getByContract')->name('DebtRecoveryContractEventGetByContract');

	Route::any('/DebtRecoveryContractEventHandleStatusProcess', 'DebtRecoveryContractEventController@handleStatusProcess')
			 ->name('DebtRecoveryContractEventHandleStatusProcess')
			 ->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_THUC_HIEN_CHAY_EVENT'));
});


Route::group(['middleware' => ['checktoken'/*,'checkip'*/], 'namespace' => $namespace], function () {

	// ------------------ debt-recovery-contract-event -----
	Route::post('/DebtRecoveryContractEventCreate', 'DebtRecoveryContractEventController@create')->name('DebtRecoveryContractEventCreate');
	Route::post('/DebtRecoveryContractEventUpdate', 'DebtRecoveryContractEventController@update')->name('DebtRecoveryContractEventUpdate');
	Route::get('/DebtRecoveryContractEventGetById', 'DebtRecoveryContractEventController@getById')->name('DebtRecoveryContractEventGetById');
	Route::post('/DebtRecoveryContractEventSetStatus', 'DebtRecoveryContractEventController@setStatus')->name('DebtRecoveryContractEventSetStatus');
	Route::get('/DebtRecoveryContractEventSearchData', 'DebtRecoveryContractEventController@searchData')->name('DebtRecoveryContractEventSearchData');
        
});
