<?php

namespace App\Modules\CollectDebt\Model;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class CollectDebtRequestAdjustment extends Model
{
  protected $table   = 'debt_recovery_request_adjustment';

  public $timestamps = false;

  protected $guarded = [];

  protected $appends = [];

/* -------------------------------- relationship -------------------------------- */
  public function collectDebtSummary() {
    return $this->belongsTo(CollectDebtSummary::class, 'contract_code', 'contract_code');
  }

  public function collectDebtShare() {
    return $this->belongsTo(CollectDebtShare::class, 'contract_code', 'contract_code');
  }

	public function collectDebtPartner() {
		return $this->belongsTo(CollectDebtPartner::class, 'reference_id', 'id');
	}
/* -------------------------------- statusable -------------------------------- */
  public function isStatusMoiTao(): bool {
    return $this->status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_MOI_TAO;
  }

  public function isStatusDuyet1(): bool {
    return $this->status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_1;
  }

  public function isStatusDuyet2(): bool {
    return $this->status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_2;
  }

  public function isStatusHuy(): bool {
    return $this->status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HUY;
  }

  public function isStatusDangXuLy(): bool {
    return $this->status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DANG_XU_LY;
  }

  public function isStatusDaHoanThanh(): bool {
    return $this->status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HOAN_THANH;
  }

  public function isStatusTrangThaiCuoi(): bool {
    $trangThaiCuoi = [
      CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HUY,
      CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HOAN_THANH
    ];

    return in_array($this->status, $trangThaiCuoi);
  }
  
  public function getRequestAdjustmentOtherData(): array {
    return json_decode($this->other_data, true);
  }

	public function getMaYeuCauTrich(): string {
		$referenceData = json_decode($this->reference_data, true);
		return $referenceData['partner_request_id'];
	}

  public function getPlanHydrateModel(): Collection {
    $otherData = $this->getRequestAdjustmentOtherData();
    $plans = collect($otherData)->where('type', 'PLAN')->first();
    return CollectDebtSchedule::hydrate($plans['data']);
  }

  public function getReferenceDataAsPartner(): CollectDebtPartner {
    $reference = json_decode($this->reference_data, true);
    return new CollectDebtPartner($reference);
  }

  public function isSoTienTrichBangSoTienTaoYcTrichNgay(): bool {
    $collectDebtPartner = $this->getReferenceDataAsPartner();
    return $this->amount_receiver_on_partner == $collectDebtPartner->amount_receiver;
  }

  public function isSoTienTrichNhoHonSoTienTaoYcTrichNgay(): bool {
    $collectDebtPartner = $this->getReferenceDataAsPartner();
    return $this->amount_receiver_on_partner < $collectDebtPartner->amount_receiver;
  }

  public function getSoTienTrichThanhCongThucTe(): float {
    return $this->amount_receiver_on_partner;
  }

  public function getSoTienTrichBiThieu(): float {
    $collectDebtPartner = $this->getReferenceDataAsPartner();
    return $collectDebtPartner->amount_receiver - $this->getSoTienTrichThanhCongThucTe(); 
  }

  public function getRAOtherData(): array {
    return json_decode($this->other_data, true);
  }

  public function putRAOtherData(array $data=[]): string {
    $otherData = $this->getRAOtherData();
    $otherData[] = $data;
    return json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
  }
} // End class
