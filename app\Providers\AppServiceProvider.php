<?php

namespace App\Providers;

use App\Lib\Logs;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('mylog', function () {
            return new Logs();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (env('INFRASTRUCTURE') == 'vps') {
            $logDir = env('LOG_PATH');
            $logFile = $logDir . DIRECTORY_SEPARATOR . env('APP_SERVICE_NAME') . '-' . date('Y-m-d') . '.log';
            if (!file_exists($logFile)) {
                touch($logFile);
            }
        }
    }
}
