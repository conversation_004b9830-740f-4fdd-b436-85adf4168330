<?php

namespace App\Modules\CollectDebtGateway\Requests\MposTestCollectDebtGateway;

use Illuminate\Foundation\Http\FormRequest;

class CancelDebtRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data.request_id' => 'required|integer',
            'data.users_admin_id' => 'required|string|max:255',
            'data.partner_request_id' => 'required|string|max:50',
            'data.partner_transaction_id' => 'required|string|max:50',
            'data.partner_merchant_id' => 'required|string',
            'data.write_note' => 'nullable|integer',
            'data.contract_code' => 'required|string|max:255',
        ];
    }

    public function messages()
    {
        return [
            'data.request_id.required' => 'Trường request_id không để trống',
            'data.request_id.integer' => 'Trường request_id phải đúng định dạng',
            'data.users_admin_id.required' => 'Trường users_admin_id không để trống',
            'data.users_admin_id.string' => 'Trường users_admin_id phải đúng định dạng',
            'data.users_admin_id.max' => 'Trường users_admin_id không được lớn hơn :max',
            'data.partner_request_id.required' => 'Trường partner_request_id không để trống',
            'data.partner_request_id.string' => 'Trường partner_request_id phải đúng định dạng',
            'data.partner_request_id.max' => 'Trường partner_request_id không được lớn hơn :max',
            'data.partner_merchant_id.required' => 'Trường partner_merchant_id không để trống',
            'data.partner_merchant_id.string' => 'Trường partner_merchant_id phải đúng định dạng',
            'data.partner_transaction_id.required' => 'Trường partner_transaction_id không để trống',
            'data.partner_transaction_id.string' => 'Trường partner_transaction_id phải đúng định dạng',
            'data.partner_transaction_id.max' => 'Trường partner_transaction_id không được lớn hơn :max',
            'data.write_note.integer' => 'Trường write_note phải đúng định dạng',
            'data.contract_code.required' => 'Trường contract_code không để trống',
            'data.contract_code.string' => 'Trường contract_code phải đúng định dạng',
            'data.contract_code.max' => 'Trường contract_code không được lớn hơn :max',
        ];

    }
}
