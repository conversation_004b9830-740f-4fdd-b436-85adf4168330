<?php 
namespace App\Lib;

use Exception;
use Illuminate\Support\Str;

class Security {
  const ANTI_SQL = [
    'SELECT',
    'UPDATE',
    'DELETE',
    'INSERT',
    'INSERT INTO',
    'CREATE',
    'CREATE DATABASE',
    'CREATE TABLE',
    'ALTER',
    'ALTER DATABASE',
    'ALTER TABLE',
    'DROP',
    'DROP DATABASE',
    'DROP TABLE',
    'DROP COLUMN',
    'INDEX',
    'DROP INDEX',
    'AND',
    'OR',
    'XOR',
    'EXISTS',
    'WHERE',
    'ORDER BY',
    'LIMIT',
    'OFFSET',
    '1=1',
    '1=2',
    ';',
    '--',
    ';--',
    'JOIN',
    'HAVING'
  ];

  public static function checkSqlCharacter(string $text=''): bool {
    $explodeText = explode(' ', $text);
    if (empty($explodeText)) {
      return false;
    }

    foreach ($explodeText as $item) {
      $requestTextItem = mb_strtoupper(trim($item));

      foreach (self::ANTI_SQL as $antiSql) {
        if (Str::contains($requestTextItem, $antiSql)) {
          throw new Exception(sprintf('Nội dung `%s` của bạn có chứa ký tự `%s` không phù hợp', $text, $item));
        }
      }
    }

    return false;
  }
} // End class