<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\BuildParamDigitalNotiAction\SubAction;

use App\Lib\Helper;
use Exception;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class DigitalNotiBuildParamTrichDuocTienSA
{
	public function run(CollectDebtDigitalNoti $digitalNoti)
	{
		$collectDebtLedger = CollectDebtLedger::query()->find($digitalNoti->object_id, ['id', 'profile_id', 'amount', 'other_data', 'time_accounting']);
		$collectDebtGuide = CollectDebtGuide::query()->firstWhere(['contract_code' => $digitalNoti->contract_code]);
		
		$title = 'Thanh toán tự động thành công';
		$body = sprintf('Hệ thống đã tự động trích nợ khoản vay %s thành công từ tài khoản của bạn.', $collectDebtGuide->contract_code);
		
		$content = view('trichno.noti.thanh-toan-tu-dong', [
			'collectDebtLedger' => $collectDebtLedger,
			'collectDebtGuide' => $collectDebtGuide
		])->render();

		$notifyData = CollectDebtDigitalNoti::buildNotiData(
			$title,
			$body,
			$content,
			'DIGITAL_TRICHNOTHANHCONG',
			$collectDebtGuide->getMposMcId(),
			'Thanh toán tự động thành công'
		);
		
		$isUpdated = $digitalNoti->where(['id' => $digitalNoti->id, 'status' => CollectDebtDigitalNoti::STT_DANG_XU_LY])
							  						 ->update([
															'status' => CollectDebtDigitalNoti::STT_DA_BUILD_PARAM,
															'digital_request' => json_encode($notifyData),
															'time_updated' => now()->timestamp
														]);
		if (!$isUpdated) {
			throw new Exception('Lỗi không build được param noti');
		}

		return $isUpdated;
	}
} // End class
