<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventGetByContractAction;

use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DebtRecoveryContractEventGetByContractAction
{
	public function run(Request $request)
	{
		$fields = [
			'id',
			'category_care_code',
			'service_care_code',
			'description',
			'status',
			'time_start',
			'number',
			'time_expired',
			'time_sented',
			'time_created_content',
			'time_created',
			'time_updated',
			'other_data->summary->contract_code as contract_code',
			'other_data->ledger as ledger_id'
		];

		$contractCode = $request->json('data.filter.contract_code', '');
		$events = CollectDebtContractEvent::query()
																			->where('contract_code', trim($contractCode));
		
		if ( !empty($request->json('data.filter.from_date')) ) {
			$fromDate = Carbon::createFromFormat('d-m-Y', $request->json('data.filter.from_date'))->startOfDay()->timestamp;
			$events = $events->where('time_created', '>=', $fromDate);
		}

		if ( !empty($request->json('data.filter.to_date')) ) {
			$toDate = Carbon::createFromFormat('d-m-Y', $request->json('data.filter.to_date'))->endOfDay()->timestamp;
			$events = $events->where('time_created', '<=', $toDate);
		}

		if ( !empty($request->json('data.filter.status')) ) {
			$events = $events->where('status', $request->json('data.filter.status'));
		}

		if ( !empty($request->json('data.filter.service_care_code')) ) {
			$events = $events->where('service_care_code', trim($request->json('data.filter.service_care_code')));
		}

		$events = $events->orderBy('id', 'DESC')
										->select($fields)
										->paginate(
											$request->json('data.limit', 10),
											$fields, 
											'page', 
											$request->json('data.page', 1) 
										);
		return $events;
	}
} // End class
