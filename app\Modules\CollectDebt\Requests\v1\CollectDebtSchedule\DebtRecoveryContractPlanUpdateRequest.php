<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DebtRecoveryContractPlanUpdateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:1'],
      'data' => ['required', 'array'],
      'data.profile_id' => ['required', 'numeric'],
      'data.contract_code' => ['required', 'string', 'max:50'],
      'data.contract_type' => [
        'required', 
        Rule::in([
          CollectDebtEnum::SCHEDULE_LOAI_HD_THUONG, 
          // CollectDebtEnum::SCHEDULE_LOAI_HD_GIA_HAN, 
          CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY, 
        ])
      ],

      'data.type' => [
        'required', 
        Rule::in([
          CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH, 
          CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU, 
        ])
      ],
      
      'data.isfee' => [
        'required', 
        Rule::in([
          CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI, 
          CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC, 
        ])
      ],

      'data.debit_begin' => [ 'required', 'numeric', 'min:1' ],
      'data.debit_end' => [ 'required', 'numeric', 'min:0' ],
      'data.rundate' => [ 'required', 'numeric', 'date_format:Ymd'],
      'data.time_start' => [ 'required', 'numeric' ],
      'data.time_end' => [ 'required', 'numeric', 'gt:data.time_start' ],
      'data.amount_period_debit' => [ 'required', 'numeric', 'min:0' ],
      'data.request_amount_debit' => [ 'required', 'numeric', 'min:0' ],
      'data.success_amount_debit' => [ 'required', 'numeric', 'min:0' ],
      'data.other_data' => [ 'required', 'json' ],
      'data.profile_data' => [ 'required', 'json' ],
      'data.description' => [ 'nullable', 'string', 'max:255' ],
      'data.updated_by' => [ 'required', 'string','max:255' ],
    ];
  }

  protected function passedValidation()
  {
    $params = $this->all();
    $params['data']['time_updated'] = time();
    $this->merge($params);
  }
} // End class
