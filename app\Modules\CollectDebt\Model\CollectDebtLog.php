<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;

class CollectDebtLog extends Model
{
  protected $table      = 'debt_recovery_logs';
  public    $timestamps = false;
	protected $guarded 		= [];

	public function getPhiChamKyMuonHoan() {
		$requestData = json_decode($this->request_data, true);
		$value = $requestData['data_source']['other_data']['fee_over_cycle_want_refund'] ?? 0;
		return intval($value);
	}

	public function getPhiQuaHanMuonHoan() {
		$requestData = json_decode($this->request_data, true);
		$value = $requestData['data_source']['other_data']['fee_overdue_want_refund'] ?? 0;
		return intval($value);
	}

	public function getSoTienYeuCauHoan() {
		$requestData = json_decode($this->request_data, true);
		$value =  $requestData['amount'] ?? 0;
		return intval($value);
	}
} // End class
