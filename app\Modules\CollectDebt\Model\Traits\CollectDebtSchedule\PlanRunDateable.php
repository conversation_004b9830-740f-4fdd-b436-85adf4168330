<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

use Carbon\Carbon;

trait PlanRunDateable
{
  public function isThoiDiemHachToanLonHonRunDate(Carbon $dateRunRealTime): bool
  {
    return $dateRunRealTime->gt($this->rundate_as_date) && !$dateRunRealTime->isSameDay($this->rundate_as_date);
  }

  public function isThoiDiemHachToanTrungVoiRunDate(Carbon $dateRunRealTime): bool
  {
    return $dateRunRealTime->isSameDay($this->rundate_as_date);
  }

	// dieu kien sinh phi
  public function isKhongPhaiRunDateTuongLai(): bool {
    return now()->isSameDay($this->rundate_as_date) || now()->gt($this->rundate_as_date);
  }

  public function isRunDateTuongLai(): bool {
    $now = now();
    return $now->lt($this->rundate_as_date) && !$now->isSameDay($this->rundate_as_date);
  }

  public function isRunDateLaChuNhat(): bool {
    $runDateCarbon = Carbon::createFromFormat('Ymd', $this->rundate);
    return $runDateCarbon->isSunday();
  }

  public function isRunDateTrongTuan(): bool {
    return !$this->isRunDateLaChuNhat();
  }

	public function isDenHanHoacQuaHanLichThuTnex(): bool {
		return now()->gte($this->rundate_as_date) || now()->isSameDay($this->rundate_as_date);
	}
} // End class