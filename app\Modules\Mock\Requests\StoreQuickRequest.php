<?php

namespace App\Modules\Mock\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\Profile;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\CollectDebtTrichNgayRule;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\PreventDuplicatePaymentMethodRule;
use Carbon\Carbon;

class StoreQuickRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'profile_id' => ['required', 'numeric', 'min:0'],
      'contract_code' => ['required', 'string', 'max:50', 'unique:debt_recovery_contract_guide,contract_code'],
      'contract_cycle' => ['required', 'numeric', 'min:1'],
      'contract_type' => [
        'required',
        'numeric',
        Rule::in([
          CollectDebtEnum::GUIDE_HD_TRICH_NGAY,
          CollectDebtEnum::GUIDE_HD_TRICH_KY,
          CollectDebtEnum::GUIDE_HD_GIA_HAN,
        ]),
      ],
      'contract_intervals' => ['required', 'numeric', 'min:1'],
      'contract_time_start' => ['required', 'numeric'],
      'contract_time_end' => ['required', 'numeric', 'gt:contract_time_start'],
      'amount' => ['required', 'numeric', 'min:1000000'],

      'payment_guide' => ['bail', 'required', 'array'],
      'payment_guide.*.payment_method_code' => ['required', 'string', Rule::in(['MPOS', 'IB_OFF', 'VIRTUALACCOUNT'])],
      'payment_guide.*.payment_channel_code' => ['required', 'string'],
      'payment_guide.*.payment_account_id' => ['required', 'string'],
      'payment_guide.*.other_data' => ['nullable', 'array'],
      'payment_guide.*.other_payment_account_holder_name' => ['nullable', 'string', 'max:255'],
      'payment_guide.*.other_payment_account_branch' => ['nullable', 'string', 'max:255'],
      'payment_guide.*.other_payment_account_bank_code' => ['nullable', 'string', 'max:255'],

      'list_fee' => ['required', 'array'],
      'list_fee.*.type' => [
        'required',
        'numeric',
        Rule::in([
          CollectDebtEnum::GUIDE_LOAI_PHI_HOP_DONG,
          CollectDebtEnum::GUIDE_LOAI_PHI_THAM_DINH_HO_SO,
          CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_UU_DAI_THAM_DINH_HO_SO,
          CollectDebtEnum::GUIDE_LOAI_PHI_GIAI_NGAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_THU_HOI,
          CollectDebtEnum::GUIDE_LOAI_PHI_GIA_HAN,
          CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY,
          CollectDebtEnum::GUIDE_LOAI_UU_DAI_PHI_THAM_GIA,
          CollectDebtEnum::GUIDE_LOAI_PHI_HOAN,
        ])
      ],

      'list_fee.*.percent_fee' => ['nullable', 'numeric', 'min:0'],
      'list_fee.*.flat_fee' => ['nullable', 'numeric', 'min:0'],
      'list_fee.*.fee_max' => ['nullable', 'numeric', 'min:0'],
      'list_fee.*.fee_min' => ['nullable', 'numeric', 'min:0'],

      'other_data' => ['required', 'json'],
      'description' => ['required', 'string', 'max:255'],
      'created_by' => ['bail', 'required', 'string', 'json', new UserInteractiveRule()],
    ];
  }

  protected function prepareForValidation()
  {

    $params = $this->all();

    $params['amount'] = (float) str_replace([',', '.'], '', $params['amount']);
    $params['created_by'] = StandardizedDataFilter::getUserAdminStructCompact([]);

    $params['payment_guide'] = collect($params['payment_guide'])->filter(function ($item) {
      return !empty($item['payment_account_id']);
    })->values()->toArray();

    $timeStart = $params['contract_time_start'];
    $params['contract_time_start'] = Carbon::createFromFormat('d/m/Y', $timeStart)->startOfDay()->timestamp;

    $soNgayVay = $params['contract_cycle'];
    $params['contract_time_end'] = Carbon::createFromFormat('d/m/Y', $timeStart)->addDays($soNgayVay)->endOfDay()->timestamp;

    $params['other_data'] = '{}';
    $params['description'] = 'Hợp đồng v4';
    
    $va = collect($params['payment_guide'])->first(function ($item) {
      return $item['payment_method_code'] == 'VIRTUALACCOUNT';
    });

  
    $this->merge($params);
  }
  protected function passedValidation()
  {
    $params = $this->all();
    
    $params['status'] = CollectDebtEnum::GUIDE_STT_MOI_TAO;

    if ($params['payment_guide']) {
      foreach ($params['payment_guide'] as &$pg) {
        if (!empty($pg['amount_reveiver'])) {
          $pg['amount_reveiver'] = (float) str_replace([',', '.'], '', $pg['amount_reveiver']);
        }
      }
    }
    $params['payment_guide'] = json_encode($params['payment_guide'], JSON_UNESCAPED_UNICODE);
    $params['list_fee'] = json_encode($params['list_fee'], JSON_UNESCAPED_UNICODE);

    $params['time_created'] = time();
    $params['time_approved'] = 0;
    $params['time_canceled'] = 0;
    $params['time_create_calendar'] = 0;


    $this->merge($params);
  }

  public function messages()
  {
    return [
      'contract_time_end.gt' => 'Thời gian kết thúc HĐ phải sau ngày bắt đầu HĐ'
    ];
  }

  public function getMposPaymentAccountId(): string {
    $params = $this->all();
    $paymentGuide = json_decode($params['payment_guide'], true);
    $mposPaymentGuide = collect($paymentGuide)->where('payment_method_code', 'MPOS')->first();
    
    return $mposPaymentGuide['payment_account_id'];
  }
} // End class
