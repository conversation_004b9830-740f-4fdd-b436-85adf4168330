<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction;

use App\Lib\Helper;
use Exception;
use Illuminate\Support\Arr;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction\Task\BuildThongTinLichGhiTrenSoTask;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use DB;

class GhiSoChoYeuCauDaCoSoSubAction
{
  // ĐÃ CÓ SỔ
  public function run(CollectDebtRequest $collectDebtRequest, DebtRecoveryLedgerCreateRequest $request, CollectDebtPartner $collectDebtPartner): CollectDebtLedger
  {
    $collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO;

    $paramInsert = Arr::only($request->json('data'), [
      'profile_id',
      'contract_code',
      'plan_ids',
      'request_id',
      'currency',
      'amount',
      'description',
      'status',
      'time_record',
      'created_by',
      'time_created',
    ]);


    $plansCollection = app(GetLichThuByIdsSubAction::class)->run($collectDebtRequest->plan_ids);

    $paramInsert['plan_ids'] = $plansCollection->implode('id', ',');
    $paramInsert['other_data'] = json_encode([
      [
        'type' => 'REQUEST',
        'time_modified' => time(),
        'data' => StandardizedDataFilter::getRequestCompactAttribute($collectDebtRequest),
        'note' => 'Yêu cầu ghi trên sổ'
      ],

      [
        'type' => 'PLAN',
        'time_modified' => time(),
        'data' =>  Helper::getPlanCompact($plansCollection, true),
        'note' => 'Lịch thu'
      ],

      [
        'type' => 'SUMMARY',
        'time_modified' => time(),
        'data' => [
          'total_amount_debit_success'  => 0, // Tổng tiền thu được ghi vào sổ 
          'total_amount_paid'           => 0, // Số tiền cấn trừ thành công cho lịch
          'amount_paid'                 => 0,  // Số thiền thu thành công cho lịch thu gốc
          'fee_overdue_cycle_paid'      => 0, // Phí chậm kỳ thu được
          'fee_overdue_paid'            => 0, // Phí quá hạn thu được
          'fee_overdue_reduction'       => 0, // giảm phí QH được giảm
          'fee_overdue_cycle_reduction' => 0, // giảm phí CK được giảm
          'fee_overdue'                 => 0, // phí quá hạn
          'fee_overdue_cycle'           => 0, // phí chậm kỳ
          'total_amount_receiver'       => $paramInsert['amount'], // số tiền nhận thực thế, tạm thời để bằng số tiền ghi sổ
          'total_amount_excess_revenue' => 0, // số tiền thu thừa trên sổ
        ],
        'note' => 'Bảng số liệu tổng hợp'
      ],

      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          // Sẽ nhét các option vào đây
        ]
      ]
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

		$paramInsert['time_updated'] = time();
    $collectDebtLedger = CollectDebtLedger::forceCreate($paramInsert);
    throw_if(!$collectDebtLedger, new Exception('Không thể tạo được sổ cho yêu cầu [ĐÃ CÓ SỔ]: ' . $collectDebtRequest->id));

		CollectDebtPartner::where('id', $collectDebtPartner->id)->update([
			'amount_payment' => $collectDebtPartner->amount_receiver
		]);

    // Return bản ghi sổ
    return $collectDebtLedger;
  }
} // End class