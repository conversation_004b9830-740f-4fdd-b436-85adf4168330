<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideQuickStatisticAction;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\GetTongNoGocDaThuCuaHopDongSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideQuickStatisticAction\SubAction\GetYeuCauThuTuDongHomNaySubAction;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class DebtRecoveryContractGuideQuickStatisticAction
{  
  
  public function run(Request $request): array
  {
    $returnData = [
      'is_collect_full_root_debt' => 0,
      'is_exists_auto_request' => 0
    ];

    
    $contractCode = $request->json('data.contract_code');

    $collectDebtShare = CollectDebtShare::where('contract_code', $contractCode)->first();
    $tongTienNoGocDaThu = app(GetTongNoGocDaThuCuaHopDongSubAction::class)->run($contractCode);
    
    if ($tongTienNoGocDaThu >= $collectDebtShare->amount) {
      $returnData['is_collect_full_root_debt'] = 1;
    }

    $collectDebtRequestsToday = app(GetYeuCauThuTuDongHomNaySubAction::class)->run($contractCode);
    

    if ($collectDebtRequestsToday->isNotEmpty()) {
      $isDaVeTrangThaiCuoi = $collectDebtRequestsToday->every(function (CollectDebtRequest $rq) {
        return $rq->isRequestFinalStatus();
      });

      if (!$isDaVeTrangThaiCuoi) {
        $returnData['is_exists_auto_request'] = 1;
      }
    }

    // Chưa dừng job thì coi như không có yc tự động
    $isDungJobHopDong = CollectDebtConfigAuto::isPauseContractJob($contractCode);
    if (!$isDungJobHopDong) {
      $returnData['is_exists_auto_request'] = 0;
    }
    
    return $returnData;
  }
} // End class