<?php

namespace App\Modules\CollectDebt\Requests\v1\ColelctDebtShare;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DebtRecoveryShareUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            'data' => ['required', 'array'],
            'data.id' => ['required', 'integer'],
            'data.contract_code' => ['nullable', 'string', 'max:50', 'unique:debt_recovery_share,contract_code'],
            'data.description' => ['nullable', 'string', 'max:255'],
            'data.other_data' => ['nullable', 'array'],
            'data.contract_data' => ['required', 'array'],
            'data.profile_data' => ['required', 'array'],
            'data.company_data' => ['required', 'array'],
            'data.payment_guide' => ['required', 'array'],
            'data.updated_by' => ['required', 'string'],
        ];
    }
}
