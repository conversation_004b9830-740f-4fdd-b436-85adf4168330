<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime;

use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class IsChuaThuPhiQuaHanHomNayST
{  
  
  public function run(CollectDebtSchedule $lichDangHachToan): bool
  {
    $danhSachLichThu = CollectDebtSchedule::query()
                                          ->where('contract_code', $lichDangHachToan->contract_code)
                                          ->where('rundate', $lichDangHachToan->rundate)
                                          ->get();

    $isCoLichThuGocChinh = $danhSachLichThu->contains(function (CollectDebtSchedule $plan) {
      return $plan->isLichThuGocChinh();
    });

    // <PERSON><PERSON><PERSON> số lượng lịch thu gốc phụ
    $tongSoLuongLichThuGocPhu = $danhSachLichThu->filter(function (CollectDebtSchedule $plan) {
      return $plan->isLichThuGocVet();
    })->count();

    // Điều kiện: Không có lịch thu gốc chính và lịch thu gốc vét <= 1 lịch
    if ($tongSoLuongLichThuGocPhu <= 1 && !$isCoLichThuGocChinh) {
      return true;
    }

    return false;
  }
} // End class
