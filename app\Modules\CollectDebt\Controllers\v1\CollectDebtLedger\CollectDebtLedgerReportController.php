<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\DebtRecoveryLedgerCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoverySendBaoCaoTrichNoTnexAction\DebtRecoverySendBaoCaoTrichNoTnexAction;


class CollectDebtLedgerReportController extends Controller
{
	public function sendReportTrichNoTnex(Request $request)
	{
		try {
			$collectDebtLedger = app(DebtRecoverySendBaoCaoTrichNoTnexAction::class)->initSendBaoCao();
			return $this->successResponse($collectDebtLedger, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
}