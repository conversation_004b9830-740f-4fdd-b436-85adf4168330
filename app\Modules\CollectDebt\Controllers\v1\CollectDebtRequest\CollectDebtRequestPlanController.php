<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestGetByPlanIdRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByPlanIdAction\DebtRecoveryRequestGetByPlanIdAction;


class CollectDebtRequestPlanController extends Controller
{
  public function getByPlanId(DebtRecoveryRequestGetByPlanIdRequest $request)
  {
    try {
      $requests = app(DebtRecoveryRequestGetByPlanIdAction::class)->run($request);
      return $this->successResponse($requests->toArray(), $request);
    } catch (\Throwable $th) {
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }
} // End class
