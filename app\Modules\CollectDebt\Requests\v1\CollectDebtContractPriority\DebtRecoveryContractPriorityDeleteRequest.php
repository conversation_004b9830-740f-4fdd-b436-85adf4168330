<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtContractPriority;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryContractPriorityDeleteRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
			'data.ids' => ['required', 'array'],
			'data.ids.*' => ['required', 'numeric', 'min:1'],
    ];
  }
} // End class
