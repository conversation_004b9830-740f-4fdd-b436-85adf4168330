<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusRecoredAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestSetStatusRecoredRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\DebtRecoveryRequestFindRawQuerySubAction;

class DebtRecoveryRequestSetStatusRecoredAction
{
  public function run(DebtRecoveryRequestSetStatusRecoredRequest $request): CollectDebtRequest
  {
    $whereRaw = sprintf('id = %s', $request->json('data.id'));
    $collectDebtRequest = app(DebtRecoveryRequestFindRawQuerySubAction::class)->run($whereRaw);
    throw_if(!$collectDebtRequest->isReceivedPaymentResult(), new Exception('Yêu cầu thanh toán chưa nhận được kết quả trích nợ từ đối tác thu hồi'));

    if ($collectDebtRequest->isUnRecorded() && $request->json('data.status_recored') == CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO) {
      mylog(['Cap Nhat Thang Thai Ghi So tu 1 len 2' => 'Chua ghi so -> Dang ghi so']);

      $collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO;
      $collectDebtRequest->recored_by = $request->json('data.user_request_id');
      $collectDebtRequest->time_recored = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isRecording() && $request->json('data.status_recored') == CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO) {
      mylog(['Cap Nhat Thang Thai Ghi So tu 2 len 3' => 'Dang ghi so -> Da ghi so']);

      $collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO;
      $collectDebtRequest->recored_by = $request->json('data.user_request_id');
      $collectDebtRequest->time_recored = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isRecording() && $request->json('data.status_recored') == CollectDebtEnum::REQUEST_STT_RC_GHI_SO_LOI) {
      mylog(['Cap Nhat Thang Thai Ghi So tu 2 len 4' => 'Dang ghi so -> Ghi so loi']);

      $collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_GHI_SO_LOI;
      $collectDebtRequest->recored_by = $request->json('data.user_request_id');
      $collectDebtRequest->time_recored = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isRecordedError() && $request->json('data.status_recored') == CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO) {
      mylog(['Cap Nhat Thang Thai Ghi So tu 4 len 1' => 'Ghi so loi -> Chua ghi so']);

      $collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO;
      $collectDebtRequest->recored_by = $request->json('data.user_request_id');
      $collectDebtRequest->time_recored = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }

    $message = sprintf(
      'Luồng cập nhật trạng thái ghi sổ không đúng logic. Trạng thái ghi sổ hiện tại là: "%s", trong khi bạn muốn cập nhật là: "%s"',
      CollectDebtRequest::listingStatusRecorded()[$collectDebtRequest->status_recored],
      CollectDebtRequest::listingStatusRecorded()[$request->json('data.status_recored')],
    );

    throw_if(true, new Exception($message));
  }
} // End class