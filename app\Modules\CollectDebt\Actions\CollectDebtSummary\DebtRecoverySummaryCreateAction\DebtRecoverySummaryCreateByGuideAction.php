<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCreateAction;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoverySummaryCreateByGuideAction
{
  const CO_CHAM_KY_THANH_TOAN = 1;
  const KHONG_CHAM_KY_THANH_TOAN = 2;

  public function run(CollectDebtGuide $collectDebtGuide, Collection $plans): CollectDebtSummary
  {
    $otherDataTypePlan = [
      'type' => 'PLAN',
      'note' => 'Danh sách lịch thu',
      'data' => []
    ];
    foreach ($plans as $plan) {
      $otherDataTypePlan['data'][] = [
        'id'              => $plan->id,
        'time_cycle'      => $plan->time_start, // thời gian thu lịch gốc
        'time_over_cycle' => 0, // Thời gian bắt đầu quá kỳ
        'amount'          => $plan->amount_period_debit, // Tổng số tiền gốc kỳ cần phải thu
        'fee'             => 0, // Tổng phí kỳ phải thu
        'amount_paid'     => $plan->success_amount_debit, // Số tiền gốc đã thu
        'fee_paid'        => 0, // Phí đã thu
        'overdue_cycle'   => self::KHONG_CHAM_KY_THANH_TOAN, //  Có quá kỳ TT hay không (1-Có,2-Không)
        'note'            => $plan->description,
        'status'          => $plan->status,
        'data'            => [
          'id'            => $plan->id,
          'fee_overdue_cycle_reduction' => 0, // Tổng phí quá kỳ đã giảm <Phí quá kỳ đã giảm>
        ],
      ];
    }

    $params = [
      'contract_code'      => $collectDebtGuide->contract_code,
      'contract_type'      => $collectDebtGuide->contract_type,
      'contract_cycle'     => $collectDebtGuide->contract_cycle,
      'contract_intervals' => $collectDebtGuide->contract_intervals,
      'contract_amount'    => $collectDebtGuide->amount,
      'other_data'         => json_encode([
        $otherDataTypePlan
      ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
      'contract_data'      => $collectDebtGuide->contract_data,
      'time_created'       => time()
    ];

    $created =  CollectDebtSummary::firstOrCreate([
      'contract_code' => $collectDebtGuide->contract_code
    ], $params);

    throw_if(!$created, new Exception('Không tạo được bản ghi summary'));

    return CollectDebtSummary::find($created->id);
  }
} // End class