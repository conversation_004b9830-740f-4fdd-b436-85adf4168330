<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

trait PlanXuLyKetQuaTrichNo
{
  public function isThuDuTien(): bool {
    return $this->success_amount_debit > 0 && $this->success_amount_debit == $this->request_amount_debit;
  }

  public function isThuThieuTien(): bool {
    return $this->success_amount_debit < $this->request_amount_debit;
  }

  public function isThuThuaTien(): bool {
    return $this->success_amount_debit > 0 && $this->success_amount_debit > $this->request_amount_debit;
  }

  /**
   * Thời điểm hạch toán hiện tại đã sau time end:
   * -> <PERSON><PERSON> hợp cho việc tính toán chậm kỳ
   * -> <PERSON><PERSON> hợp cho việc tính toán quá hạn
   */
  public function isLichQuaKhuSoVoiHienTai(): bool {
    return now()->gt($this->time_end_as_date) && now()->format('Ymd') != $this->rundate;
  }

  public function isKhongPhaiLichThuQuaKhu(): bool {
    return !$this->isLichQuaKhuSoVoiHienTai();
  }
} // End class