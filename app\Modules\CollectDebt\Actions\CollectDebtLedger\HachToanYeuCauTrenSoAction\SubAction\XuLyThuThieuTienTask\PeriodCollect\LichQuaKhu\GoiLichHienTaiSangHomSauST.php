<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\LichQuaKhu;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Support\Facades\DB;

class GoiLichHienTaiSangHomSauST
{  
  /**
   * Gối lịch số tiền nợ gốc của lịch thu hiện tại vào lịch có rundate là hôm sau
   *
   * @param CollectDebtSchedule $collectDebtSchedule [explicite description]
   * @param CollectDebtSchedule $lichCoRunDateLaHomSau [explicite description]
   * @param float $soTienNoGocConPhaiThu [explicite description]
   *
   * @return void
   */
  public function run(CollectDebtSchedule $lichDangHachToan, CollectDebtSchedule $lichCoRunDateLaHomSau, float $soTienNoGocConPhaiThu)
  {
    $lichCoRunDateLaHomSau->debit_begin += $soTienNoGocConPhaiThu;
    $lichCoRunDateLaHomSau->request_amount_debit += $soTienNoGocConPhaiThu;
    $lichCoRunDateLaHomSau->save();

    // Gối lịch sang hôm sau, kiểm tra lịch hôm sau có yêu cầu chưa
    // Nếu yêu cầu chưa gửi, thì cộng thêm tiền còn phải thu vào trong Request
    $yeuCauThuVaoHomSau = CollectDebtRequest::where('plan_ids', $lichCoRunDateLaHomSau->id)->first();
    if ($yeuCauThuVaoHomSau && $yeuCauThuVaoHomSau->isUnsentPayment()) {
      $yeuCauThuVaoHomSau->amount_request += $soTienNoGocConPhaiThu;
      $yeuCauThuVaoHomSau->save();
    }

    return $lichCoRunDateLaHomSau;
  }
}
