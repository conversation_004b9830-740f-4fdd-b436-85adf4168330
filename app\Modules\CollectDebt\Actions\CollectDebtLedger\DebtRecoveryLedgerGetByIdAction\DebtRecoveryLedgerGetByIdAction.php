<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByIdAction;

use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerGetByIdRequest;

class DebtRecoveryLedgerGetByIdAction
{
  public function run(DebtRecoveryLedgerGetByIdRequest $request)
  {
    if (!empty($request->json('data.id'))) {
      return CollectDebtLedger::query()->where('id', $request->json('data.id'))->get();
    }


    $ledgers = CollectDebtLedger::query()->where('request_id', $request->json('data.request_id'))->get();

    if ($ledgers->isNotEmpty()) {
      
      $ledgers = $ledgers->map(function (CollectDebtLedger $ledger) {
        $ledger->is_late_partner = (int) Str::contains($ledger->description, 'TRICH_MUON');

        return $ledger;
      });
    }

    return $ledgers;
  }
} // End class