<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime;

use App\Lib\TelegramAlert;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TaoLichThuVetNoGocTrongNgayST
{
  public function run(CollectDebtSchedule $lichDangHachToan, float $soTienConPhaiThuCuaLich=0, float $soTienDaThuThanhCong=0)
  {
    $debitBegin = $lichDangHachToan->debit_begin - $soTienDaThuThanhCong;
    $otherData = json_encode([
      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'request_created_channel' => '',
        ],
        'note' => sprintf('Thu vét nợ gốc trong ngày. Lịch đang hạch toán là: %s', $lichDangHachToan->id),
      ]
    ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

    $scheduleParam = [
      'profile_id'           => $lichDangHachToan->profile_id,
      'contract_code'        => $lichDangHachToan->contract_code,
      'contract_type'        => $lichDangHachToan->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
      'debit_begin'          => $debitBegin,
      'debit_end'            => $lichDangHachToan->debit_end,
      'rundate'              => $lichDangHachToan->rundate,
      'time_start'           => $lichDangHachToan->time_start_as_date->copy()->setTime(date('H'), date('i'))->timestamp,
      'time_end'             => $lichDangHachToan->time_end,
      'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
      'request_amount_debit' => $soTienConPhaiThuCuaLich,
      'success_amount_debit' => 0,
      'other_data'           => $otherData,
      'description'          => $lichDangHachToan->collectDebtSchedule,
      'is_settlement'        => $lichDangHachToan->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangHachToan->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangHachToan->cycle_number,
      'master_id'         => $lichDangHachToan->master_id,
    ];

    return CollectDebtSchedule::forceCreate($scheduleParam);
  }
} // End class