<!DOCTYPE html>
<html lang="vi">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Trích nợ V4 - Job Runner</title>
	<style>
		body {
			font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
			margin: 20px;
			background-color: #f5f5f5;
		}

		.container {
			max-width: 90%;
			margin: 0 auto;
			background: white;
			padding: 20px;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		}

		h1 {
			color: #333;
			text-align: center;
			margin-bottom: 30px;
		}

		.status {
			margin: 10px 0;
			padding: 10px;
			border-radius: 4px;
			border-left: 4px solid #007bff;
			background-color: #f8f9fa;
		}

		.status.success {
			border-left-color: #28a745;
			background-color: #d4edda;
		}

		.status.error {
			border-left-color: #dc3545;
			background-color: #f8d7da;
		}

		.status.running {
			border-left-color: #ffc107;
			background-color: #fff3cd;
		}

		.progress {
			margin: 20px 0;
			padding: 15px;
			background-color: #e9ecef;
			border-radius: 4px;
		}

		.progress-bar {
			height: 20px;
			background-color: #007bff;
			border-radius: 4px;
			transition: width 0.3s ease;
		}

		.log {
			margin-top: 20px;
			padding: 15px;
			background-color: #f8f9fa;
			border-radius: 4px;
			max-height: 400px;
			overflow-y: auto;
			font-family: monospace;
			font-size: 12px;
		}
	</style>
	<script>
		const baseUrl = '{{ $baseUrl }}'; // Server-side render
	</script>
</head>

<body>
	<div class="container">
		<h1>📜 Trích Nợ V4</h1>

		<div class="progress">
			<div class="progress-bar" id="progressBar" style="width: 0%"></div>
		</div>

		<div id="statusContainer"></div>

		<div class="log" id="logContainer"></div>
	</div>

	<script>
		let isRunning = false;
		let completedJobs = 0;
		let totalJobs = 0;

		const ENDPOINTS = [
			"DebtRecoveryContractGuideCreatePlan",
			"DebtRecoveryContractPlanCreateRequest",
			"DebtRecoveryRequestSendPayment",
			"CheckRequestViaRedisAction",
			"DebtRecoveryPartnerCheck",
			"DebtRecoveryRequestRecored",
			"CutOffTimeByRedisAction",
			"DebtRecoveryLedgerAccounting",
			"DebtRecoveryLedgerAccountingSummary",
			"DebtRecoveryGuideAutoApproved",
			"DebtRecoveryRequestAdjustmentProcess",
			"DebtRecoveryContractEventBuildContent",
			"DebtRecoveryContractEventCreateSend",
			"DebtRecoverySummaryComplete",
			"DebtRecoveryRequestRecheck",
			"DebtRecoverySummaryCheckLedgerExsist",
			"DebtRecoveryContractCreateRequestViaWallet",
			"DebtRecoveryRequestOpenAndMinusFreeze",
			"DebtRecoveryRequestCanhBaoTrichNgay",
			"DebtRecoveryJobNotifyDebtNow",
			"DebtRecoveryWaitProcessHandle",
			"DebtRecoverySummaryCheckRefund",
			//"DigitalNotiBuildParam",
			//"DigitalNotiSend",
		];

		function log(message, type = 'info', id = null) {
			const logContainer = document.getElementById('logContainer');
			const timestamp = new Date().toLocaleTimeString('vi-VN');
			const logEntry = document.createElement('div');

			let color = '#007bff'; // info
			if (type === 'error') color = '#dc3545';
			else if (type === 'success') color = '#28a745';
			else if (type === 'pending') color = '#fd7e14';

			logEntry.style.color = color;
			logEntry.textContent = `[${timestamp}] ${message}`;

			if (id) {
				logEntry.id = id;
			}

			logContainer.appendChild(logEntry);
			logContainer.scrollTop = logContainer.scrollHeight;
		}

		function updateProgress() {
			const progressBar = document.getElementById('progressBar');
			const percentage = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;
			progressBar.style.width = percentage + '%';
			progressBar.textContent = `${completedJobs}/${totalJobs} (${Math.round(percentage)}%)`;
		}

		function updateStatus(message, type = 'info') {
			const statusContainer = document.getElementById('statusContainer');
			const statusDiv = document.createElement('div');
			statusDiv.className = `status ${type}`;
			statusDiv.textContent = message;
			statusContainer.appendChild(statusDiv);
		}

		async function callUrl(path) {
			if (!isRunning) return;

			const fullUrl = `${baseUrl}/${path}`;
			const pendingLogId = `pending-${path}`;

			log(`🔗 Đang gọi: ${fullUrl}`, 'info');
			log(`⏳ Đang chờ kết quả: ${path}`, 'pending', pendingLogId);

			try {
				const response = await fetch(fullUrl, {
					method: 'GET',
					headers: {
						'Accept': 'application/json',
						'Content-Type': 'application/json'
					}
				});

				// ❌ Hoặc ✅ → remove dòng pending
				const pendingLog = document.getElementById(pendingLogId);
				if (pendingLog) pendingLog.remove();

				if (response.ok) {
					const result = await response.text();
					log(`✅ Thành công: ${path}`, 'success');
					return {
						success: true,
						data: result
					};
				} else {
					log(`❌ Lỗi ${response.status}: ${path}`, 'error');
					return {
						success: false,
						error: `HTTP ${response.status}`
					};
				}
			} catch (error) {
				const pendingLog = document.getElementById(pendingLogId);
				if (pendingLog) pendingLog.remove();

				log(`❌ Lỗi kết nối: ${path} - ${error.message}`, 'error');
				return {
					success: false,
					error: error.message
				};
			}
		}

		async function runAllJobs() {
			if (isRunning) return;

			isRunning = true;
			completedJobs = 0;
			totalJobs = ENDPOINTS.length;

			log('🚀 Bắt đầu chạy tất cả jobs...', 'info');
			updateStatus('Đang chạy jobs...', 'running');
			updateProgress();

			const promises = ENDPOINTS.map(async (endpoint) => {
				if (!isRunning) return;

				const result = await callUrl(endpoint);
				completedJobs++;
				updateProgress();

				if (completedJobs === totalJobs) {
					finishJobs();
				}

				return result;
			});

			await Promise.all(promises);
		}

		function finishJobs() {
			isRunning = false;
			log('🎉 Tất cả jobs đã hoàn tất!', 'success');
			updateStatus('Tất cả jobs đã hoàn tất!', 'success');
		}

		// Khởi động tự động
		window.addEventListener('load', () => {
			log('🚀 Tự động chạy jobs...', 'info');
			updateProgress();
			runAllJobs();
		});
	</script>
</body>

</html>
