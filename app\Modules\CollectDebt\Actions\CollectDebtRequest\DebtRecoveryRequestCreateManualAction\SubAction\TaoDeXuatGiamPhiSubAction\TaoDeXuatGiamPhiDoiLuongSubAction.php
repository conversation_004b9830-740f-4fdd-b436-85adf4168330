<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoDeXuatGiamPhiSubAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtAction;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\GetReduceAndPrepayPlanAction\GetReduceAndPrepayPlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAction\DebtRecoveryRequestActionCreateAction\DebtRecoveryRequestActionCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\GetTongNoGocDaThuCuaHopDongSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\TaoYeuCauTrichTayTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoDeXuatGiamPhiSubAction\Task\TaoYeuCauGiamPhiTask;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class TaoDeXuatGiamPhiDoiLuongSubAction
{
  public $soTienPhiChamKyDuocGiam = 0;
  public $soTienPhiQuaHanDuocGiam = 0;

  public function run(
    CollectDebtShare $collectDebtShare,
    Collection $danhSachToanBoLichThu,
    DebtRecoveryRequestCreateManualRequest $request
  ) {
    $this->__checkDaThuDuGocHayChua($collectDebtShare);
    
    // Tạo đề xuất giảm phí, tuyệt đối không được động chạm vào công nợ
    $collectDebtRequest = app(TaoYeuCauGiamPhiTask::class)->run(
      $collectDebtShare,
      $danhSachToanBoLichThu,
      $request,
    );

		mylog(['Yeu Cau Giam Phi La' => optional($collectDebtRequest)->id]);
		
		if (!$collectDebtRequest) {
			mylog(['[LOI]' => 'loi khong tao duoc yc giam phi']);
			throw new Exception('loi khong tao duoc yc giam phi');
		}
		
    if( $collectDebtRequest ) {
      $paramAction = [
          'type' => CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI,
          'request_id' => $collectDebtRequest->id,
          'action_code' => CollectDebtEnum::REQUEST_ACTION_CODE_CREATED,
          'created_by' => $collectDebtRequest->created_by,
          'description' => $collectDebtRequest->description,
          'other_data' => $request->json('data.attachment') ?? "{}",
          'time_created' => time(),
      ];

      $action = app(DebtRecoveryRequestActionCreateAction::class)->run($paramAction);
			mylog(['Giam Phi Action Record' => optional($action)->id]);
			
			if (!$action) {
				mylog(['[LOI REQUEST ACTION]' => 'Khong tao duoc action']);
				throw new Exception('Khong tao duoc action cho yeu cau giam phi');
			}

			// Bật is_process các lịch của yêu cầu giảm phí thành đang xử lý
			$planIds = $collectDebtRequest->getPlanIds();
			$updatePlanProcessing = CollectDebtSchedule::query()
																								 ->where('contract_code', $collectDebtRequest->contract_code)
																								 ->whereIn('id', $planIds)
																								 ->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
																								 ->update([
																									'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY,
																									'time_updated' => now()->timestamp
																								 ]);
			
			throw_if(
				!$updatePlanProcessing, 
				new Exception('Không thể cập nhật các lịch của yêu cầu giảm phí thành ĐANG XỬ LÝ')
			);
    }

    return $collectDebtRequest;
  }

  private function __checkDaThuDuGocHayChua(CollectDebtShare $collectDebtShare) {
    $collectDebtSummary = CollectDebtSummary::query()
																						->where('contract_code', $collectDebtShare->contract_code)
																						->first();
		mylog([
			'Tong Tien Da Trich Thanh Cong' => $collectDebtSummary->getTongTienTrichThanhCong(),
			'Hop Dong' => $collectDebtSummary->contract_code,
		]);

    $errorMessage = sprintf(
      'Bạn không thể tạo đề nghị giảm phí khi vẫn chưa thu đủ số tiền của HĐ: %s',  
      Helper::priceFormat($collectDebtShare->amount, 'đ'), 
    );

    throw_if(
			!$collectDebtSummary->isTongTienTrichThanhCongLonHonGiaTriHopDong(), 
			new Exception($errorMessage)
		);

		return true;
  }
} // End class