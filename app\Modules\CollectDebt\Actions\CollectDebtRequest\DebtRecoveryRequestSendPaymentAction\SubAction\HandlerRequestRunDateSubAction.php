<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Carbon\Carbon;

class HandlerRequestRunDateSubAction
{
  /**
   * Kiểm tra rundate để có các phương án xử lý (<PERSON>àm theo thứ tự từ trên xuống dưới)
   * 
   * Case 1: Nếu rundate của yêu cầu <= thời gian hiện tại. 
   *  -> Kết luận Thì đây là yêu cầu quá khứ hoặc job ko xử lý
   *  -> Hướng xử lý: Đ<PERSON>h dấu thu tiền bằng 0 và không gửi qua đối tác nữa
   * 
   * Case 2: Nếu yêu cầu cần gửi qua kênh MPOS. Tuy nhiên rundate của yêu cầu lại vào đúng ngày chủ nhật
   *  -> Đánh 
   *  -> Kết luận: MPOS ko trích cho ngày chủ nhật, Nextlend sẽ ko gửi lệnh 
   *  -> Đánh dấu là thu tiền bằng 0, và số tiền của yêu cầu này sẽ đc add sang ngày thứ 2 hôm sau
   * 
   * Case 3: Kiểm tra số tiền yêu cầu trích có lớn đang = 0 hay không. Nếu có thì ngắt ko gửi 
   * sang đối tác
   * 
   * Case 4: Gửi đối tác như bình thường
   *   
   * @param CollectDebtRequest $collectDebtRequest [Bản ghi yêu cầu cần phải đẩy qua kênh đối tác]
   *
   * @return void
   */
  public function run(CollectDebtRequest $collectDebtRequest): CollectDebtRequest
  {
    $replicateRequest = $collectDebtRequest->replicate();
    
    $today = today();
    
    // Case 1: Rundate quá khứ
    $timeExpiredAsCarbon = Carbon::createFromTimestamp($collectDebtRequest->time_expired);
    if ($collectDebtRequest->time_expired < time() && !now()->isSameDay($timeExpiredAsCarbon)) {
      mylog(['Dừng Gửi Lệnh Do Yêu Cầu Bị Hết Hạn Trong Qúa Khứ' => $collectDebtRequest]);

      $collectDebtRequest->status          = CollectDebtEnum::REQUEST_STT_DA_DUYET;
      $collectDebtRequest->status_payment  = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
      $collectDebtRequest->amount_receiver = 0;
      $collectDebtRequest->other_data      = $collectDebtRequest->putOtherData([
        'type'                  => 'RECEIVER',
        'time_modified'         => time(),
        'note'                  => sprintf(
          'Yêu cầu này đã là yêu cầu quá khứ. Expired là: %s, Thời điểm xử lý lệnh là: %s', 
          $collectDebtRequest->time_expired_as_date,
          now()->toDateTimeString()
        ),
        'data' => StandardizedDataFilter::getStandardizedDataFilter('REQUEST', $replicateRequest)
      ]);

      $collectDebtRequest->save();
      
      return $collectDebtRequest;
    }
    
    // Case 3: Yêu cầu thu 0đ --> Đánh dấu kết thúc lệnh
    if ( empty($collectDebtRequest->amount_request) ) {
      mylog(['Dừng Gửi Lệnh Do Yêu Cầu Trích 0đ' => $collectDebtRequest]);

      $collectDebtRequest->status          = CollectDebtEnum::REQUEST_STT_DA_DUYET;
      $collectDebtRequest->status_payment  = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
      $collectDebtRequest->amount_receiver = 0;
      $collectDebtRequest->other_data      = $collectDebtRequest->putOtherData([
        'type'                  => 'RECEIVER',
        'time_modified'         => time(),
        'time_modified_as_date' => now()->toDateTimeString(),
        'note'                  => sprintf('Yêu cầu đã được nextlend tính toán là thu 0đ. Vậy nên hệ thống đánh dấu kết thúc lệnh để kiểm tra lại, phát hiện lúc: %s', now()->toDateTimeString()),
        'data'                  => $replicateRequest->toJson()
      ]);

      $collectDebtRequest->save();
      
      return $collectDebtRequest;
    }

    return $collectDebtRequest;
  }
} // End class