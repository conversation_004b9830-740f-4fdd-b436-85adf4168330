<?php

namespace App\Lib;

use Illuminate\Support\Arr;
use App\Lib\ExtraCustomHelper;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtSetting;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Database\Eloquent\Collection;
use Throwable;


class NotiHelper
{
	public static function getKenhThanhToanNoti(string $paymentMethodCode = ''): string
	{
		if ($paymentMethodCode == 'MPOS') {
			return 'MPOS';
		}

		if ($paymentMethodCode == 'IB_OFF') {
			return 'Chuyển khoản NH';
		}

		if ($paymentMethodCode == 'VIRTUALACCOUNT') {
			return 'Chuyển khoản QR';
		}

		if ($paymentMethodCode == 'WALLET') {
			return 'Thanh toán qua ví Nextlend';
		}

		return 'Khác';
	}
} // End class