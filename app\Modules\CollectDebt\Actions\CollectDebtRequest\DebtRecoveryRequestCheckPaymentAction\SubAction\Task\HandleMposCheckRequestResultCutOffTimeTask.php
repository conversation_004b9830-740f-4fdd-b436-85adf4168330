<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Exception;

class HandleMposCheckRequestResultCutOffTimeTask
{
  public function run(CollectDebtRequest $collectDebtRequest, array $checkDebtResult): CollectDebtRequest
  {
    $request = request();

    if ($checkDebtResult['data']['status'] == 'TIMEOUT') {
      mylog(['Dính Cutoff Time' => 'Lệnh gọi vẫn bị TIMEOUT']);
      return $this->__makeRequestIsFinalStatus($collectDebtRequest, CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA, true);
    }

    if ($checkDebtResult['data']['status'] == 'WAITTING') {
      mylog(['Dính Cutoff Time' => 'Lệnh gọi vẫn bị WAITTING']);
      return $this->__makeRequestIsFinalStatus($collectDebtRequest, CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA, true);
    }

    // Success sẽ có những case sau
    if ($checkDebtResult['data']['error_code'] == '00') {
      switch ($checkDebtResult['data']['status']) {
        case 'SUCCESS':
          mylog(['Dính Cutoff Time' => 'Lệnh gọi SUCCESS']);

          $collectDebtRequest->amount_receiver = $checkDebtResult['data']['amount_debit'];
          $collectDebtRequest->fee = 0;
          $collectDebtRequest->partner_transaction_id = $checkDebtResult['data']['partner_transaction_id'];
          $collectDebtRequest->time_receivered = time();
          $collectDebtRequest->checked_by = $request->json('data.users_admin_id', 'CronJob');
          $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
          $collectDebtRequest->save();
          return $collectDebtRequest;

        case 'CANCEL':
          mylog(['Dính Cutoff Time' => 'Lệnh gọi CANCEL']);
          return $this->__makeRequestIsFinalStatus($collectDebtRequest, CollectDebtEnum::REQUEST_STT_PM_TU_CHOI);

        case 'EXPIRED':
          mylog(['Dính Cutoff Time' => 'Lệnh gọi EXPIRED']);
          return $this->__makeRequestIsFinalStatus($collectDebtRequest, CollectDebtEnum::REQUEST_STT_PM_TU_CHOI);

        case 'PENDING':
          mylog(['Dính Cutoff Time' => 'Lệnh gọi PENDING']);
          return $this->__makeRequestIsFinalStatus($collectDebtRequest, CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA, true);

        default:
          mylog(['Dính Cutoff Time' => 'Lệnh gọi không xác định được là gì: ' . $checkDebtResult['data']['status']]);
          return $this->__makeRequestIsFinalStatus($collectDebtRequest, CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA, true);
      }
    }

    return $collectDebtRequest;
  }

    
  /**
   * Method __makeRequestIsFinalStatus: Đưa y/c về trạng thái cuối khi đã là thời gian cutoff
   * @param $collectDebtRequest CollectDebtRequest [Bản ghi y/c]
   * @param $statusPayment      int [Trạng thái thanh toán]
   * @param $isNeedRecheck      bool [Có phải recheck hay không, các case truy vấn sang MPOS mà ko có thông tin số tiền thì sẽ để là true]
   * @return void
   */
  private function __makeRequestIsFinalStatus(
    CollectDebtRequest $collectDebtRequest, 
    int $statusPayment=CollectDebtEnum::REQUEST_STT_PM_TU_CHOI,
    bool $isNeedRecheck=false
  ): CollectDebtRequest {
    throw_if(empty($statusPayment), new Exception('Thiếu Status Payment'));

    $request = request();

    $collectDebtRequest->amount_receiver = 0;
    $collectDebtRequest->fee = 0;
    $collectDebtRequest->partner_transaction_id = $checkDebtResult['data']['partner_transaction_id'] ?? '';
    $collectDebtRequest->time_receivered = time();
    $collectDebtRequest->checked_by = $request->json('data.users_admin_id', 'CronJob');
    $collectDebtRequest->status_payment = $statusPayment;
    
    if ($isNeedRecheck) {
      $collectDebtRequest->status = CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA;
    }
    $collectDebtRequest->save();
    return $collectDebtRequest;
  }
} // End class
