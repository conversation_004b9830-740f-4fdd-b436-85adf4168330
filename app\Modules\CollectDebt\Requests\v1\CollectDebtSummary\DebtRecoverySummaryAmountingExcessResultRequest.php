<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummaryAmountingExcessResultRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.contract_code' => ['required', 'string', 'max:50'],
      'data.amount_refund_request' => ['required', 'numeric', 'gte:data.amount'], // Số tiền yêu cầu hoàn
      'data.amount' => ['required','numeric', 'min:0'], // Số tiền đã hoàn thành công
      'data.status' => ['required', Rule::in([1, 2])], // 1: Th<PERSON>nh công | 2: Thất bại,
      'data.description' => ['nullable', 'string', 'max:255'],
      'data.order_code' => ['required', 'string', 'max:255']
    ];
  }

  public function isHoanThanhCong(): bool {
    return $this->json('data.status') == 1;
  }

  public function getSoTienYeuCauHoan(): float {
    return $this->json('data.amount_refund_request');
  }

  public function getSoTienHoanThanhCong(): float {
    return $this->json('data.amount');
  }
} // End class
