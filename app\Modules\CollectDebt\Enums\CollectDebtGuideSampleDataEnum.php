<?php

namespace App\Modules\CollectDebt\Enums;

use App\Modules\CollectDebt\Model\CollectDebtGuide;

class CollectDebtGuideSampleDataEnum
{
  public static function makeModel($attribute=[]): CollectDebtGuide {
    $attribute = array_merge([
      'id' => '',
      'profile_id' => '',
      'contract_code' => '',
      'contract_cycle' => '',
      'contract_intervals' => '',
      'contract_time_start' => '',
      'contract_time_end' => '',
      'amount' => '',
      'payment_guide' => '',
      'list_fee' => '',
      'other_data' => '',
      'profile_data' => '',
      'status' => '',
      'created_by' => '',
      'updated_by' => '',
      'approved_by' => '',
      'canceled_by' => '',
      'create_calendar_by' => '',
      'time_created' => '',
      'time_updated' => '',
      'time_approved' => '',
      'time_canceled' => '',
      'time_create_calendar' => '',
      'reason' => '',
      'can' => 'CAN_DO_NOTHING'
    ], $attribute);

    return new CollectDebtGuide($attribute);
  }
}
