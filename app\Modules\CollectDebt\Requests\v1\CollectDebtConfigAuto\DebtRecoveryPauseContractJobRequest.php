<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtConfigAuto;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\HopDongChuaTatToanRule;
use App\Modules\CollectDebt\Rules\CollectDebtRequest\HopDongKhongCoLichQuaKhuRule;

class DebtRecoveryPauseContractJobRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {

    return [
      'data' => ['array'],
      'data.contract_code' => [
        'bail',
        'required', 
        'string', 
        'max:255', 
        new HopDongKhongCoLichQuaKhuRule(),
        new HopDongChuaTatToanRule()
      ],
      'data.created_by' => ['required', 'string', 'max:255'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['created_by'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['created_by']);
    $this->merge($params);
  }

  public function messages()
  {
    return [
      'data.contract_code.exists' => sprintf('Hợp đồng `%s` không tồn tại trong hệ thống', $this->json('data.contract_code'))
    ];
  }
} // End class
