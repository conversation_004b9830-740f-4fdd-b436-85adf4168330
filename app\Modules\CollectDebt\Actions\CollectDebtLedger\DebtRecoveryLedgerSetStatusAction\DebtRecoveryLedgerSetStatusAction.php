<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSetStatusAction;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerSetStatusRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerSetStatusActionRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByIdAction\SubAction\DebtRecoveryLedgerFindWhereRawSubAction;
use Exception;
use Illuminate\Support\Arr;

class DebtRecoveryLedgerSetStatusAction {

    public function run(DebtRecoveryLedgerSetStatusRequest $request) {
        $targetStatus = $request->json('data.status');
        $ledgerId = $request->json('data.id');
        anti_sql($ledgerId);

        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);

        if ($collectDebtLedger->isNoProcess() && $targetStatus == CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN) {
            $param = array_filter([
                'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN,
                'time_updated' => time(),
                'updated_by' => $request->json('data.user_request_id'),
                'description' => $request->json('data.description')
            ]);

            $collectDebtLedger->forceFill($param)->update();
            return $collectDebtLedger->refresh();
        }

        if ($collectDebtLedger->isNoProcess() && $targetStatus == CollectDebtEnum::LEDGER_STT_DA_TU_CHOI) {
            $param = array_filter([
                'status' => CollectDebtEnum::LEDGER_STT_DA_TU_CHOI,
                'time_canceled' => time(),
                'canceled_by' => $request->json('data.user_request_id'),
                'description' => $request->json('data.description')
            ]);

            $collectDebtLedger->forceFill($param)->update();
            return $collectDebtLedger->refresh();
        }

        if ($collectDebtLedger->isAccouting() && $targetStatus == CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN) {
            $param = array_filter([
                'status' => CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN,
                'time_accounting' => time(),
                'accounting_by' => $request->json('data.user_request_id'),
                'description' => $request->json('data.description')
            ]);

            $collectDebtLedger->forceFill($param)->update();
            return $collectDebtLedger->refresh();
        }

        $message = sprintf(
                'Logic cập nhật trạng thái bị sai. Bạn muốn cập nhật trạng thái: %s (%s), trong khi sổ hiện tại đang có trạng thái: %s (%s)',
                $targetStatus,
                CollectDebtLedger::listStatus()[$targetStatus],
                $collectDebtLedger->status,
                CollectDebtLedger::listStatus()[$collectDebtLedger->status],
        );

        throw new Exception($message);
    }

    public function setStatusOther(DebtRecoveryLedgerSetStatusActionRequest $request) {
        $targetStatus = $request->json('data.status_contract');
        if ($targetStatus) {
            return $this->__setStatusContract($targetStatus, $request);
        }
        $targetStatus = $request->json('data.status_excess');
        if ($targetStatus) {
            return $this->__setStatusExcess($targetStatus, $request);
        }
        $targetStatus = $request->json('data.status_report');
        if ($targetStatus) {
            return $this->__setStatusReport($targetStatus, $request);
        }
        $targetStatus = $request->json('data.status_plan');
        if ($targetStatus) {
            return $this->__setStatusPlan($targetStatus, $request);
        }
        $targetStatus = $request->json('data.status_summary');
        if ($targetStatus) {
            return $this->__setStatusSummary($targetStatus, $request);
        }
        $targetStatus = $request->json('data.status_period');
        if ($targetStatus) {
            return $this->__setStatusPeriod($targetStatus, $request);
        }
        $targetStatus = $request->json('data.status_email');
        if ($targetStatus) {
            return $this->__setStatusEmail($targetStatus, $request);
        }
        $targetStatus = $request->json('data.status_sms');
        if ($targetStatus) {
            return $this->__setStatusSms($targetStatus, $request);
        }
        $message = 'Format params chưa chính xác';
        throw new Exception($message);
    }

    protected function __setStatusContract($targetStatus, $request) {
        $listStatus = CollectDebtLedger::listStatusAction();
        $ledgerId = $request->json('data.id');
        anti_sql($ledgerId);
        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);
        $status = $collectDebtLedger->status_contract;
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT || $targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        $inputs = [
            'status_contract' => $targetStatus,
            'time_updated' => time(),
            'time_updated_status_contract'=> time(),
        ];
        if($request->json('data.user_request_id')) {
           $inputs['updated_status_contract_by']  = $request->json('data.user_request_id');
        }
        $param = array_filter($inputs);
        $collectDebtLedger->forceFill($param)->update();
        return $collectDebtLedger->refresh();
    }
    protected function __setStatusExcess($targetStatus, $request) {
        $listStatus = CollectDebtLedger::listStatusAction();
        $ledgerId = $request->json('data.id');
        $user_request_id= $request->json('data.user_request_id');
        anti_sql($ledgerId);
        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);
        $status = $collectDebtLedger->status_excess;
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT || $targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        $inputs = [
            'status_excess' => $targetStatus,
            'time_updated' => time(),
            'time_updated_excess'=> time(),
        ];
        if($user_request_id) {
           $inputs['updated_status_excess_by']  = $user_request_id;
        }
        $param = array_filter($inputs);
        $collectDebtLedger->forceFill($param)->update();
        return $collectDebtLedger->refresh();
    }
    protected function __setStatusReport($targetStatus, $request) {
        $listStatus = CollectDebtLedger::listStatusAction();
        $ledgerId = $request->json('data.id');
        $user_request_id= $request->json('data.user_request_id');
        anti_sql($ledgerId);
        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);
        $status = $collectDebtLedger->status_report;
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT || $targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        $inputs = [
            'status_report' => $targetStatus,
            'time_updated' => time(),
            'time_updated_report'=> time(),
        ];
        if($user_request_id) {
           $inputs['updated_status_report_by']  = $user_request_id;
        }
        $param = array_filter($inputs);
        $collectDebtLedger->forceFill($param)->update();
        return $collectDebtLedger->refresh();
    }
    
    protected function __setStatusPlan($targetStatus, $request) {
        $listStatus = CollectDebtLedger::listStatusAction();
        $ledgerId = $request->json('data.id');
        $user_request_id= $request->json('data.user_request_id');
        anti_sql($ledgerId);
        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);
        $status = $collectDebtLedger->status_plan;
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT || $targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        $inputs = [
            'status_plan' => $targetStatus,
            'time_updated' => time(),
            'time_updated_plan'=> time(),
        ];
        if($user_request_id) {
           $inputs['updated_status_plan_by']  = $user_request_id;
        }
        $param = array_filter($inputs);
        $collectDebtLedger->forceFill($param)->update();
        return $collectDebtLedger->refresh();
    }
    protected function __setStatusSummary($targetStatus, $request) {
        $listStatus = CollectDebtLedger::listStatusAction();
        $ledgerId = $request->json('data.id');
        $user_request_id= $request->json('data.user_request_id');
        anti_sql($ledgerId);
        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);
        $status = $collectDebtLedger->status_summary;
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT || $targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        $inputs = [
            'status_summary' => $targetStatus,
            'time_updated' => time(),
            'time_updated_summary'=> time(),
        ];
        if($user_request_id) {
           $inputs['updated_status_summary_by']  = $user_request_id;
        }
        $param = array_filter($inputs);
        $collectDebtLedger->forceFill($param)->update();
        return $collectDebtLedger->refresh();
    }
    protected function __setStatusPeriod($targetStatus, $request) {
        $listStatus = CollectDebtLedger::listStatusAction();
        $ledgerId = $request->json('data.id');
        $user_request_id= $request->json('data.user_request_id');
        anti_sql($ledgerId);
        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);
        $status = $collectDebtLedger->status_period;
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT || $targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        $inputs = [
            'status_period' => $targetStatus,
            'time_updated' => time(),
            'time_updated_status_period'=> time(),
        ];
        if($user_request_id) {
           $inputs['updated_status_period_by']  = $user_request_id;
        }
        $param = array_filter($inputs);
        $collectDebtLedger->forceFill($param)->update();
        return $collectDebtLedger->refresh();
    }
    protected function __setStatusEmail($targetStatus, $request) {
        $listStatus = CollectDebtLedger::listStatusAction();
        $ledgerId = $request->json('data.id');
        $user_request_id= $request->json('data.user_request_id');
        anti_sql($ledgerId);
        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);
        $status = $collectDebtLedger->status_email;
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT || $targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        $inputs = [
            'status_email' => $targetStatus,
            'time_updated' => time(),
            'time_updated_email'=> time(),
        ];
        if($user_request_id) {
           $inputs['updated_status_email_by']  = $user_request_id;
        }
        $param = array_filter($inputs);
        $collectDebtLedger->forceFill($param)->update();
        return $collectDebtLedger->refresh();
    }
    protected function __setStatusSms($targetStatus, $request) {
        $listStatus = CollectDebtLedger::listStatusAction();
        $ledgerId = $request->json('data.id');
        $user_request_id= $request->json('data.user_request_id');
        anti_sql($ledgerId);
        $whereRaw = sprintf('id = %s', $ledgerId);
        $collectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run($whereRaw, true);
        $status = $collectDebtLedger->status_sms;
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        if ($targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT || $targetStatus == CollectDebtEnum::LEDGER_STT_ACTION_LOI) {
            if ($status != CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT) {
                throw new Exception('Trạng thái hiện tại "' . $listStatus[$status] . '" không được hỗ trợ update sang "' . $listStatus[$targetStatus] . '"');
            }
        }
        
        $inputs = [
            'status_sms' => $targetStatus,
            'time_updated' => time(),
            'time_updated_status_sms'=> time(),
        ];
        if($user_request_id) {
           $inputs['updated_status_sms_by']  = $user_request_id;
        }
        $param = array_filter($inputs);
        $collectDebtLedger->forceFill($param)->update();
        return $collectDebtLedger->refresh();
    }

}

// End class