<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\DebtRecoveryRequestCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task\XuLyPushYeuCauVaoOtherDataTask;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\KiemTraVaDanhDauHuyLenhMposNeuCoTask;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task\DanhDauLichThuLaDangProcessTask;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task\DamBaoYcMposBiHuyPhaiDaHachToanTask;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task\GetCacLichThuCoTheThanhToanTuCongNoTask;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task\TaoYeuCauGhiNhanThuThuaKhongCoLichThuTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST;

class TaoYeuCauKhongCanGuiDoiTacSubAction
{
  public function run(CollectDebtPartner $collectDebtPartner)
  {
		// Kiem tra xem co yc khong qua doi tac nao dang chay khong, neu co thi phai throw loi luon
		$cacYeuCauKhongQuaDoiTac = CollectDebtRequest::query()
																								 ->with('collectDebtLedger')
																								 ->where('contract_code', $collectDebtPartner->contract_code)
																								 ->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT)
																								 ->whereBetween('time_expired', [
																										now()->startOfDay()->timestamp,
																										now()->endOfDay()->timestamp
																								 ])
																								 ->get();
		
		if ($cacYeuCauKhongQuaDoiTac->isNotEmpty()) {
			$isTonTaiYcChuaHachToan = $cacYeuCauKhongQuaDoiTac->contains(function (CollectDebtRequest $re) {
				return !$re->isRecorded() || !$re->collectDebtLedger || (
					$re->collectDebtLedger && $re->collectDebtLedger->status != CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN
				);
			});

			if ($isTonTaiYcChuaHachToan) {
				mylog(['[LOI]' => 'ton tai yc chua hach toan']);
				throw new Exception('Loi ton tai yc chua hach toan');
			}
		}

		$damBaoYeuCauMposBiHuyTruocDaHachToan = app(DamBaoYcMposBiHuyPhaiDaHachToanTask::class)->run($collectDebtPartner->contract_code);
		if (!$damBaoYeuCauMposBiHuyTruocDaHachToan) {
			mylog(['Loi logic yeu cau mpos' => 'ok']);
			throw new Exception('Yeu cau MPOS da huy truoc phai ve trang thai da hach toan thi moi duoc xu ly. HD: ' . $collectDebtPartner->contract_code);
		}

		

    $coreContract = $collectDebtPartner->getCoreContract();
    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $coreContract->contract_code)->first();
    
    throw_if(
      !$collectDebtSummary, 
      new Exception(sprintf('Không tồn tại HĐ: %s trên hệ thống v4. PartnerID: %s', $coreContract->contract_code, $collectDebtPartner->id))
    );

    $soTienKhaDungCoTheMangDiThanhToan = $collectDebtPartner->getAmountBalance();
    
    if ($soTienKhaDungCoTheMangDiThanhToan <= 0) {
			$error = [
        'LogId' => request('api_request_id'),
        'Message' => sprintf('Công nợ: `%s` không có tiền để mang đi thanh toán', $collectDebtPartner->id)
      ];

			mylog($error);

      $errorMessage = parseErr($error);

      TelegramAlert::sendMessagePartner($errorMessage);
      throw new Exception($errorMessage);
    }

    try {

      $plansToMakeRequest = app(GetCacLichThuCoTheThanhToanTuCongNoTask::class)->run(
        $coreContract->contract_code, 
        $soTienKhaDungCoTheMangDiThanhToan,
      );

			/**
			 * Update 14.03.2024, khi trên partner có tiền để thanh toán, nhưng HĐ không có lịch nào nữa thì đi vào quy trình
			 * tạo yêu cầu không có lịch và tính là thu thừa
			 */
      if ($plansToMakeRequest['plans']->isEmpty()) {
				return app(TaoYeuCauGhiNhanThuThuaKhongCoLichThuTask::class)->run($collectDebtPartner, $collectDebtSummary, $coreContract);
      }

      // Check trùng để tránh việc duplicate khi gọi API tạo -> nen where theo ma chung tu
      if ($plansToMakeRequest['plans']->isNotEmpty()) {
        $checkExistsRequest = CollectDebtRequest::query()
					->where([
						'contract_code'        => $coreContract->contract_code,
						'partner_transaction_id'  => $collectDebtPartner->partner_transaction_id,
					])
					->where('status', '!=', CollectDebtEnum::REQUEST_STT_TU_CHOI)
					->first();

        // Da ton tai (tao yeu cau truoc do roi) thi thoi, de cho job khac xu ly
        if ( $checkExistsRequest ) {
					mylog([
						'Yeu cau da duoc tao truoc do roi' => 'Ok',
						'Ma chung tu da tao' => $collectDebtPartner->partner_transaction_id
					]);

          return;
        }

        
        $request = new DebtRecoveryRequestCreateRequest();
        $request->setJson(
          new \Symfony\Component\HttpFoundation\ParameterBag([
            'data' => [
              'type'                        => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
              'profile_id'                  => $coreContract->profile_id ?? '',
              'contract_code'               => $coreContract->contract_code,
              'plan_ids'                    => $plansToMakeRequest['plans']->implode('id', ','), // Tu tinh toan
              'payment_method_code'         => $collectDebtPartner->payment_method_code,
              'payment_channel_code'        => $collectDebtPartner->payment_channel_code,
              'payment_account_id'          => $collectDebtPartner->payment_account_id,
              'payment_account_holder_name' => 'N/A',
              'payment_account_bank_code'   => 'N/A',
              'payment_account_bank_branch' => 'N/A',
              'partner_request_id'          => '',
              'partner_transaction_id'      => $collectDebtPartner->partner_transaction_id,
              'time_begin'                  => today()->copy()->setHours(5)->timestamp,
              'time_expired'                => today()->copy()->setTimeFromTimeString(Helper::getCutOffTime())->timestamp,
              'is_payment'                  => CollectDebtEnum::REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT,
              'version'                     => CollectDebtEnum::REQUEST_VERSION_4,
              'status'                      => CollectDebtEnum::REQUEST_STT_DA_DUYET,
              'status_payment'              => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA,
              'status_recored'              => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO,
              'currency'                    => $collectDebtSummary->getCurrency(),
              'amount_request'              => $plansToMakeRequest['amount_request'], // Tổng số tiền cần thanh toán của các lịch
              'amount_payment'              => $plansToMakeRequest['amount_receiver'],
              'amount_receiver'             => $plansToMakeRequest['amount_receiver'], // update 14.03.24, lấy all số tiền đã nhận đc từ partner
              'fee'                         => $collectDebtPartner->fee,
              'description'                 => $collectDebtPartner->description ?: sprintf('Tạo yêu cầu qua kênh: %s', $collectDebtPartner->payment_method_code),
              'time_created'                => time(),
              'time_approved'               => time(),
              'time_completed'              => time(),
              'created_by'                  => Helper::getCronJobUser(),
              'approved_by'                 => Helper::getCronJobUser(),
              'completed_by'                => Helper::getCronJobUser(),
            ]
          ])
        );

				mylog(['chuan bi tao yeu cau' => 'Ok']);
        $collectDebtRequest = app(DebtRecoveryRequestCreateAction::class)->run($request);
        if ( $collectDebtRequest ) {
					mylog(['da thuc hien tao yeu cau qua kenh: ' => $collectDebtRequest->payment_method_code]);

          $collectDebtPartner->partner_request_id = $collectDebtRequest->partner_request_id;
          $collectDebtPartner->amount_payment    += $collectDebtRequest->amount_receiver;
          
          $partnerLatestOtherData = $collectDebtPartner->getPartnerOtherData();
          $partnerLatestOtherDataString = app(XuLyPushYeuCauVaoOtherDataTask::class)->run($partnerLatestOtherData, $collectDebtRequest);

          $collectDebtPartner->other_data = $partnerLatestOtherDataString;
          $collectDebtPartner->save();

          app(DanhDauLichThuLaDangProcessTask::class)->run($plansToMakeRequest['plans']);
          
          $message = parseErr([
            'Flow' => sprintf('Luồng bị động, ID công nợ: %s', $collectDebtPartner->id),
            'Message' => 'Tạo yêu cầu THÀNH CÔNG',
            'LogID' => request('api_request_id'),
            'RequestID' => $collectDebtRequest->id
          ]);

					mylog(['thong tin yeu cau bi dong' => $message]);
					
          TelegramAlert::sendCreateRequest($message);

          /**
					 * Còn có thể trừ tiếp tiếp vào công nợ cho các lịch khác
					 * Đoạn này cần phải kiểm tra còn lịch mới nào nữa không? 
					 * 		+nếu còn thì ko cập nhật status. 
					 * 		+nếu hết thì đưa về stt cuối
					 */
          if ($collectDebtPartner->canPaidCollectRequest()) {
						$planIds = $plansToMakeRequest['plans']->pluck('id')->toArray();
						$listPlanThanhToanTiep = CollectDebtSchedule::query()->where('contract_code', $coreContract->contract_code)
																																 ->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
																																 ->whereChuaTaoQuaKenhThuNao()
																																 ->whereNotIn('id', $planIds)
																																 ->count();
						
						if ($listPlanThanhToanTiep > 0) {
							$collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_CHUA_XU_LY;
						}
						
						if ($listPlanThanhToanTiep == 0) {
							$collectDebtPartner->time_complated = time();
							$collectDebtPartner->complated_by = Helper::getCronJobUser();

							$collectDebtPartner->time_created_request = time();
							$collectDebtPartner->created_request_by = Helper::getCronJobUser();

							$collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
							$collectDebtPartner->number_perform = 1;
						}
          }

          // Không thể trừ tiếp từ công nợ => Kết thúc công nợ
          if (!$collectDebtPartner->canPaidCollectRequest()) {
						$collectDebtPartner->time_complated = time();
						$collectDebtPartner->complated_by = Helper::getCronJobUser();

						$collectDebtPartner->time_created_request = time();
						$collectDebtPartner->created_request_by = Helper::getCronJobUser();

            $collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
						$collectDebtPartner->number_perform = 1;
          }
        }else {
          $message = parseErr([
            'Flow' => sprintf('Luồng bị động, ID công nợ: %s', $collectDebtPartner->id),
            'Message' => 'Tạo yêu cầu BỊ LỖI',
            'LogID' => request('api_request_id'),
            'Error' => json_encode($collectDebtRequest)
          ]);

          TelegramAlert::sendMessagePartner($message);
        }
      }
    }catch(\Throwable $th) {
			mylog(['Loi xu ly: ' => Helper::traceError($th)]);
      throw $th;
    }

		$savePartnerResult =  $collectDebtPartner->save();

		if ( !$savePartnerResult ) {
			throw new Exception('Save partner bi loi');
		}
    
		$processDanhDauYeuCauMposSeHuy = app(KiemTraVaDanhDauHuyLenhMposNeuCoTask::class)->run(
			$collectDebtPartner->contract_code, 
			$collectDebtRequest
		);
    return $collectDebtPartner->only(['id']);
  }
} // End class