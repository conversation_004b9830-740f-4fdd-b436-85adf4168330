<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DebtRecoveryContractPlanCreateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.schedules' => ['required', 'array'],
      'data.schedules.*.profile_id' => ['required', 'numeric'],
      'data.schedules.*.contract_code' => ['required', 'string', 'max:50'],
      'data.schedules.*.contract_type' => [
        'required', 
        Rule::in([
          CollectDebtEnum::SCHEDULE_LOAI_HD_THUONG, 
          // CollectDebtEnum::SCHEDULE_LOAI_HD_GIA_HAN, 
          CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY, 
        ])
      ],

      'data.schedules.*.type' => [
        'required', 
        Rule::in([
          CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH, 
          CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU, 
        ])
      ],
      
      'data.schedules.*.isfee' => [
        'required', 
        Rule::in([
          CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI, 
          CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC, 
        ])
      ],

      'data.schedules.*.debit_begin' => [ 'required', 'numeric', 'min:1' ],
      'data.schedules.*.debit_end' => [ 'required', 'numeric', 'min:0' ],
      'data.schedules.*.rundate' => [ 'required', 'numeric', 'date_format:Ymd' ],
      'data.schedules.*.time_start' => [ 'required', 'numeric' ],
      'data.schedules.*.time_end' => [ 'required', 'numeric', 'gt:data.schedules.*.time_start' ],
      'data.schedules.*.amount_period_debit' => [ 'required', 'numeric', 'min:0' ],
      'data.schedules.*.request_amount_debit' => [ 'required', 'numeric', 'min:0' ],
      'data.schedules.*.success_amount_debit' => [ 'required', 'numeric', 'min:0' ],
      'data.schedules.*.other_data' => [ 'required', 'json' ],
      'data.schedules.*.profile_data' => [ 'required', 'json' ],
      'data.schedules.*.description' => [ 'nullable', 'string', 'max:255' ],
      'data.schedules.*.created_by' => ['nullable', 'string', 'json', 'max:255'],
      'data.schedules.*.time_created' => [ 'nullable', 'numeric' ],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    foreach ($params['data']['schedules'] as &$collectDebtSchedule) {
      $collectDebtSchedule['status'] = CollectDebtEnum::SCHEDULE_STT_MOI;

      if ( empty($collectDebtSchedule['time_created']) ) {
        $collectDebtSchedule['time_created'] = now()->timestamp;
      }
    }

    $this->merge($params);
  }
} // End class
