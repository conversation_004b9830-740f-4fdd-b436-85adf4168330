<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoverySummaryCreateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data'                              => ['required', 'array'],
      'data.contract_code'                => ['required', 'string', 'max:50'],
      'data.contract_type'                => ['required', 'integer', Rule::in([1, 2])],
      'data.contract_intervals'           => ['required', 'integer'],
      'data.contract_cycle'               => ['required', 'integer'],
      'data.contract_amount'              => ['required', 'numeric'],
      'data.total_amount_paid'            => ['nullable', 'numeric'],
      'data.total_fee_paid'               => ['nullable', 'numeric'],
      'data.fee_overdue_paid'             => ['nullable', 'numeric'],
      'data.fee_overdue_cycle_paid'       => ['nullable', 'numeric'],
      'data.fee_overdue_reduction'        => ['nullable', 'numeric'],
      'data.fee_overdue_cycle_reduction'  => ['nullable', 'numeric'],
      'data.total_amount_refund'          => ['nullable', 'numeric'],
      'data.is_overdue'                   => ['nullable', 'integer', Rule::in([1, 2])],
      'data.is_over_cycle'                => ['nullable', 'integer', Rule::in([1, 2])],
      'data.number_over_cycle'            => ['nullable', 'integer'],
      'data.other_data'                   => ['required', 'json'],
      'data.data_over_cycle'              => ['required', 'json'],
      'data.contract_data'                => ['required', 'json'],
      'data.description'                  => ['nullable', 'string', 'max:255'],
      'data.status'                       => ['nullable', 'integer', Rule::in(array_keys(CollectDebtSummary::listStatus()))],

    ];
  }
} // End class
