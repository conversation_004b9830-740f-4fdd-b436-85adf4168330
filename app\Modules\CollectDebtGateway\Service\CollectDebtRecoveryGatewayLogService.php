<?php
namespace App\Modules\CollectDebtGateway\Service;

use App\Modules\CollectDebtGateway\Repositories\CollectDebtRecoveryGatewayLogRepository;
use Exception;


class CollectDebtRecoveryGatewayLogService
{
    protected CollectDebtRecoveryGatewayLogRepository $collectDebtLogRepo;
    

    public function __construct(CollectDebtRecoveryGatewayLogRepository $collectDebtLogRepo)
    {
        $this->collectDebtLogRepo = $collectDebtLogRepo;
    }

    /**
     * @param $data
     * @return array
     */
    public function getAllByFollow($data)
    {
        $dataResponse = array();
        $fields = '*';
        $input = array();
        $result = array();

        if (!isset($data['fields']) || empty($data['fields'])) {
            $data['fields'] = '*';
        }

        foreach ($data as $key => $value) {
            if ($key == 'fields') {
                $fields = $value;
                continue;
            }
            if (!empty($value)) {
                $input[] = [$this->collectDebtLogRepo->getNameTable() . '.' . $key, '=', $value];
            }
        }

        $result['fields'] = $fields;
        $result['condition'] = $input;

        $dataResponse = $this->collectDebtLogRepo->getAllByFollow($result);

        return $dataResponse;
    }
}