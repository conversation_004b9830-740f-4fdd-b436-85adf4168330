<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtEmail;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\CollectDebtPartnerResourceCollection;
use App\Modules\CollectDebt\Actions\CollectDebtEmail\DebtRecoveryEmailGetByContractAction\DebtRecoveryEmailGetByContractAction;
use App\Modules\CollectDebt\Actions\CollectDebtEmail\DebtRecoveryEmailGetDetailByIdAction\DebtRecoveryEmailGetDetailByIdAction;
use App\Modules\CollectDebt\Actions\CollectDebtEmail\DebtRecoveryEmailGetByContractAction\DebtRecoveryEmailGetByContractOldAction;

class CollectDebtEmailController extends Controller
{
	public function getEmailByContract(Request $request)
	{
		try {
			// $emailPaginate = app(DebtRecoveryEmailGetByContractAction::class)->run($request);
			// $resource = new CollectDebtPartnerResourceCollection($emailPaginate);
			// $response = $resource->toResponse($request)->getData(true);
			$response = app(DebtRecoveryEmailGetByContractOldAction::class)->run($request);
			return $this->successResponse($response, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	} 

	public function getDetailById(Request $request)
	{
		try {
			$collectDebtPartner = app(DebtRecoveryEmailGetDetailByIdAction::class)->run($request->json('data.id', 2390));
			return $this->successResponse($collectDebtPartner, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	} 
} // End class
