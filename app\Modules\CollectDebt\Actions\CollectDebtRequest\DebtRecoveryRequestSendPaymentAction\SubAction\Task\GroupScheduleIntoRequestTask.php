<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Database\Eloquent\Collection;

class GroupScheduleIntoRequestTask
{  
  /**
   * Gom nhóm các lịch vào trong yêu cầu
   * Mỗi bản ghi yêu cầu sẽ có thêm thông tin bản ghi lịch thu (phục vụ cho việc check sau này)
   * 
   * @param Collection $requests [Danh sách bản ghi yêu cầu]
   *
   * @return void
   */
  public function run(Collection $requests): Collection
  {
    $schedules = CollectDebtRequest::loadSchedules($requests)->keyBy('id');
    $requests->transform(function (CollectDebtRequest $request) use ($schedules) {
      $request->schedules = Collection::make();

      $planIds = $request->getPlanIds();
      foreach ($planIds as $planId) {
        if ($schedules->has($planId)) {
          $request->schedules->push($schedules->get($planId));
        }
      }
      return $request;
    });

    return $requests;
  }
} // End class