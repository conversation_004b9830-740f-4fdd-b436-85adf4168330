<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhPartnerSA;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;

class XuLyDieuChinhPartnerSA
{
	public function run(CollectDebtRequestAdjustment $ra): CollectDebtPartner
	{
		$collectDebtPartner = CollectDebtPartner::query()->find($ra->reference_id);

		$collectDebtPartner->other_data = $collectDebtPartner->putOtherData([
			'type' => 'REQUEST_ADJUSTMENT',
			'data' => $ra->only(['id', 'type', 'contract_code', 'reference_id', 'amount_receiver_on_partner', 'amount_before', 'amount_after']),
			'note' => sprintf(
				'Điều chỉnh số tiền từ %s thành %s',
				Helper::priceFormat($collectDebtPartner->amount_receiver),
				Helper::priceFormat($ra->getSoTienTrichThanhCongThucTe())
			),
			'time_modified' => now()->timestamp
		]);

		$collectDebtPartner->amount_receiver  = $ra->getSoTienTrichThanhCongThucTe();
		$collectDebtPartner->amount_payment = $ra->getSoTienTrichThanhCongThucTe();

		$result = $collectDebtPartner->save();

		if (!$result) {
			mylog(['Loi cap nhat dieu chinh partner' => $result]);
			throw new Exception('Loi cap nhat dieu chinh partner');
		}

		return $collectDebtPartner;
	}
} // End class
