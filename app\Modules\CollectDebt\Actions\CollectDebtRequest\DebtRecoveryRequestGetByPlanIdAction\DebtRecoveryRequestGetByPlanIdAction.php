<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByPlanIdAction;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestGetByPlanIdRequest;

class DebtRecoveryRequestGetByPlanIdAction
{
  public function run(DebtRecoveryRequestGetByPlanIdRequest $request)
  {
    $requests = CollectDebtRequest::query()
                                  ->whereRaw("FIND_IN_SET(?, `plan_ids`) > 0", [(string) $request->json('data.plan_id')])
                                  ->select($request->json('data.fields', ['*']))
                                  ->get();
    return $requests;
  }
}
