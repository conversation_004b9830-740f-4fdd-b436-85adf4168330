<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Exception;

class TaoYeuCauGhiNhanThuThuaKhongCoLichThuTask
{
	public function run(CollectDebtPartner $collectDebtPartner, CollectDebtSummary $collectDebtSummary, $coreContract): CollectDebtPartner
	{
		$createYeuCauKhongCoLichThuParams = [
			'type'                        => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
			'profile_id'                  => $coreContract->profile_id ?? '',
			'contract_code'               => $coreContract->contract_code,
			'plan_ids'                    => '', // hđ không có lịch thì để rỗng
			'payment_method_code'         => $collectDebtPartner->payment_method_code,
			'payment_channel_code'        => $collectDebtPartner->payment_channel_code,
			'payment_account_id'          => $collectDebtPartner->payment_account_id,
			'payment_account_holder_name' => 'N/A',
			'payment_account_bank_code'   => 'N/A',
			'payment_account_bank_branch' => 'N/A',
			'partner_request_id'          => '',
			'partner_transaction_id'      => $collectDebtPartner->partner_transaction_id,
			'time_begin'                  => today()->copy()->setHours(5)->timestamp,
			'time_expired'                => today()->copy()->setTimeFromTimeString(Helper::getCutOffTime())->timestamp,
			'is_payment'                  => CollectDebtEnum::REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT,
			'version'                     => CollectDebtEnum::REQUEST_VERSION_4,
			'status'                      => CollectDebtEnum::REQUEST_STT_DA_DUYET,
			'status_payment'              => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA,
			'status_recored'              => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO,
			'currency'                    => $collectDebtSummary->getCurrency(),
			'amount_request'              => 0, // Vì không có lịch, thì số tiền yc = 0
			'amount_payment'              => $collectDebtPartner->getAmountBalance(), // số tiền thanh toán chính là số tiền khả dụng
			'amount_receiver'             => $collectDebtPartner->getAmountBalance(), // update 14.03.24, lấy all số tiền đã nhận đc từ partner
			'fee'                         => $collectDebtPartner->fee,
			'description'                 => $collectDebtPartner->description ?: sprintf('Tạo yêu cầu thanh toán không có lịch qua kênh: %s', $collectDebtPartner->payment_method_code),
			'time_created'                => time(),
			'time_approved'               => time(),
			'time_completed'              => time(),
			'created_by'                  => Helper::getCronJobUser(),
			'approved_by'                 => Helper::getCronJobUser(),
			'completed_by'                => Helper::getCronJobUser(),
			'other_data'	=> json_encode([
				[
					'type' => 'EXCESS_REQUEST_WITH_NO_PLAN',
					'time_modified' => time(),
					'data' => [],
					'note' => 'Tạo yêu cầu thanh toán thu thừa không có lịch thu do HĐ đã tất toán và gạch hết lịch'
				]
			], JSON_UNESCAPED_UNICODE),
			'plan_data' => json_encode([])
		];

		mylog(['param tao yc khong co lich thu' => $createYeuCauKhongCoLichThuParams]);

		$collectDebtRequest = CollectDebtRequest::query()->forceCreate($createYeuCauKhongCoLichThuParams);
		throw_if(!$collectDebtRequest, new Exception('Lỗi không tạo được yêu cầu thanh toán không có lịch thu'));

		$collectDebtRequest->partner_request_id = sprintf('NL%s%s', date('ymd'), $collectDebtRequest->id);
		$result = $collectDebtRequest->save();
		if (!$result) {
			mylog(['[LOI]' => 'loi tao yc khong co lich thu (thu thua)']);
			throw new Exception('loi tao yc khong co lich thu (thu thua)');
		}

		// Cập nhật lại partner
		$collectDebtPartner->partner_request_id = $collectDebtRequest->partner_request_id;
		$collectDebtPartner->amount_payment += $collectDebtPartner->getAmountBalance();
		$collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
		$result = $collectDebtPartner->save();

		if (!$result) {
			mylog(['[LOI]' => 'loi khong cap nhat ma yeu cau ve partner']);
			throw new Exception('loi khong cap nhat ma yeu cau ve partner');
		}

		return $collectDebtPartner;
	}
} // End class