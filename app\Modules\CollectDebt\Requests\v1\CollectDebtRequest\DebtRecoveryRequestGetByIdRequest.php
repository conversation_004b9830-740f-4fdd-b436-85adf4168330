<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryRequestGetByIdRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'integer'],
      'data.user_request_id' => ['nullable', 'string', 'max:255'],
      'data.with_debt_log' => ['present', 'boolean']
    ];
  }
} // End class
