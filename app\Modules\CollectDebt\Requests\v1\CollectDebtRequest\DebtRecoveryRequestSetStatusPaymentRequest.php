<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class DebtRecoveryRequestSetStatusPaymentRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'integer'],
      'data.status_payment' => [
        'required', 
        Rule::in([
          CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI,
          CollectDebtEnum::REQUEST_STT_PM_DANG_GUI,
          CollectDebtEnum::REQUEST_STT_PM_TU_CHOI,
          CollectDebtEnum::REQUEST_STT_PM_GUI_LOI,
          CollectDebtEnum::REQUEST_STT_PM_DA_GUI,
          CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA
        ])
      ],
      'data.user_request_id' => ['required', 'numeric', 'min:1'],
      'data.description' => ['required', 'string', 'max:255'],
    ];
  }
} // End class
