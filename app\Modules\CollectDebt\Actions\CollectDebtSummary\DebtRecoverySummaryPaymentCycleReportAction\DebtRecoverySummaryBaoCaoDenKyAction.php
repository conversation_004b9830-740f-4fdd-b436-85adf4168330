<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPaymentCycleReportAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPaymentCycleReportRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeUserLienQuanSA;
use Carbon\Carbon;

class DebtRecoverySummaryBaoCaoDenKyAction
{
	public array $returnData = [];

	/**
	 * Mindset: 
	 *  	+ <PERSON><PERSON> thu có thể kéo dài, vậy nên cần SELECT lịch max của các từng kỳ để lấy được lịch master_id
	 * 		+ <PERSON><PERSON> khi có lịch master_id thì truy vân whereIn đơn giản, 
	 * 		+ <PERSON>u<PERSON>i cùng lấy thêm dữ liệu từ summary để ghép data và trả về cho người dùng
	 *
	 * @param DebtRecoverySummaryPaymentCycleReportRequest $request
	 * @return void
	 */
	public function run(DebtRecoverySummaryPaymentCycleReportRequest $request)
	{
		$plans = CollectDebtSchedule::query()
															  ->with(['collectDebtSummary.collectDebtShare'])
																->where('type', CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH)
																->where('isfee', CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC)
																->where('contract_type', CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY);

		// Where contract code
		if (!empty($request->json('data.filter.contract_code'))) {
			$plans = $plans->where('contract_code', trim($request->json('data.filter.contract_code')));
		}

		// profile id
		$filter = $request->json('data.filter');
		if ( !empty($filter['profile_ids']) ) {
			$plans->whereIn('profile_id', $filter['profile_ids']);
		}

		// bắt đã thanh toán hay chưa (1: đã thanh toán | 2: chưa thanh toán)
		$isPaid = $request->json('data.filter.is_paid', 0);

		if (!empty($isPaid)) {
			// Da thanh toan
			if ($isPaid == 1) {
				$plans->where('success_amount_debit', '>', 0);
			}


			// Chua thanh toan
			if ($isPaid == 2) {
				$plans->where('success_amount_debit', '=', 0);
			}
		}

		if (!empty($filter['time_start_from'])) {
			$startFrom = $filter['time_start_from'];
			$startFrom = Carbon::createFromFormat('d-m-Y', $filter['time_start_from'])->startOfDay()->timestamp;
			$plans->where('time_start', '>=', $startFrom);
		}

		if (!empty($filter['time_start_to'])) {
			$startTo = $filter['time_start_to'];
			$startTo = Carbon::createFromFormat('d-m-Y', $filter['time_start_to'])->endOfDay()->timestamp;
			$plans->where('time_start', '<=', $startTo);
		}

		// Bộ lọc kỳ có ngày T+1 là chủ nhật
		if (!empty($filter['is_sunday'])) {
			// Là chủ nhật
			if ($filter['is_sunday'] == 1) {
				$plans->whereRaw("DAYOFWEEK(STR_TO_DATE(rundate, '%Y%m%d')) = 1");
			}

			// không phải là chủ nhật
			if ($filter['is_sunday'] == 2) {
				$plans->whereRaw("DAYOFWEEK(STR_TO_DATE(rundate, '%Y%m%d')) != 1");
			}
		}

		// Bộ lọc kỳ cuối cùng (tất toán) hay không?
		if (!empty($filter['is_last_cycle'])) {
			// Là kỳ cuối cùng
			if ($filter['is_last_cycle'] == 1) {
				$plans->where('is_settlement', CollectDebtEnum::SCHEDULE_LA_LICH_TAT_TOAN);
			}

			// không phải kỳ cuối cùng
			if ($filter['is_last_cycle'] == 2) {
				$plans->where('is_settlement', '!=', CollectDebtEnum::SCHEDULE_LA_LICH_TAT_TOAN);
			}
		}

		if (!$request->isViewAllContract()) {
			$plans->whereIn('contract_code', $request->json('data.list_contract_code_can_access'));
		}

		if ($request->isEmptyContract()) {
			$plans->where('id', '=', -1);
		}

		$plansResults = $plans->select([
			'id',
			'contract_code',
			'time_start',
			'master_id',
			'request_amount_debit',
			'success_amount_debit',
			'time_start',
			'time_end',
			'rundate',
			'profile_id',
			'status',
			'time_updated',
			'is_settlement'
		])
		->paginate(
			$request->json('data.limit', 10), 
			['*'], 
			'page',
			$request->json('data.page', 1)
		);


		$collection = $plansResults->getCollection();
		
		$collection = $collection->map(function (CollectDebtSchedule $plan) {
			mylog([
				'Dang xu ly cho HD' => $plan->contract_code,
				'ID Lich Dang Xu Ly' => $plan->id,
			]);

			$collectDebtSummary = $plan->collectDebtSummary;

			$profileDataArray = $collectDebtSummary->collectDebtShare->getProfileDataAsArray();
				
				$merchantInfo = [
					'id' => $profileDataArray['merchant']['id'],
					'fullname' => $profileDataArray['merchant']['fullname'],
					'email' => $profileDataArray['merchant']['email'],
					'mobile' => $profileDataArray['merchant']['mobile'],
					'address' => $profileDataArray['merchant']['address'],
				];
	
				$contractInfo = [
					'contract_code' => $collectDebtSummary->contract_code,
					'amount' => $collectDebtSummary->contract_amount,
					'contract_cycle' => $collectDebtSummary->contract_cycle,
					'contract_intervals' => $collectDebtSummary->contract_intervals,
					'time_start_end' => sprintf('%s - %s', $collectDebtSummary->collectDebtShare->time_start_as_vn_date, $collectDebtSummary->collectDebtShare->time_end_as_vn_date)
				];
				
				$summaryPlanOtherData = $collectDebtSummary->getSummaryOtherDataItem('PLAN');
				if (empty($summaryPlanOtherData)) {
					mylog(['Loi HD' => $collectDebtSummary->contract_code]);
					mylog(['other data' => @$collectDebtSummary->getSummaryOtherData()]);
				}
				$planOtherDataKeyById = collect($summaryPlanOtherData['data'])->keyBy('id')->toArray();

				if (isset($planOtherDataKeyById[$plan->id]['amount']) && isset($planOtherDataKeyById[$plan->id]['amount_paid'])) {
					$amount = $planOtherDataKeyById[$plan->id]['amount'] ?? 0; 
					$amountPaid = $planOtherDataKeyById[$plan->id]['amount_paid'] ?? 0;
				}else {
					$amount = $plan->request_amount_debit; 
					$amountPaid = $plan->success_amount_debit;
				}

				$paymentInfo = [
					'payment_date' => $plan->time_start_as_date->format('d/m/Y'),
					'total_amount_must_payment' => $amount,
					'total_amount_paid' => $amountPaid,
					'totla_amount_continous_payment' => $amount - $amountPaid
				];
				
	
				$userAction = app(ThongKeUserLienQuanSA::class)->run($collectDebtSummary);
				
				unset($plan->request_amount_debit);
				unset($plan->success_amount_debit);
				unset($plan->contract_code);

				$plan->merchant_info = $merchantInfo;
				$plan->contract_info = $contractInfo;
				$plan->payment_info = $paymentInfo;
				$plan->user_action = $userAction;
				$plan->profile_info = [
					'id' => $plan->profile_id
				];
				
				$plan->is_rundate_sunday = $plan->rundate_as_date->isSunday() ? 1 : 0;
				unset($plan->profile_id);
				unset($plan->collectDebtSummary);
				return $plan;
		});

		$plansResults->setCollection($collection);
		
		return $plansResults;
	}
} // End class
