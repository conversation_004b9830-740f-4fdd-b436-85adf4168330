@extends('layouts.master')
@section('title', '<PERSON><PERSON><PERSON> lập số tiền trích thành công')

@section('page_name', '<PERSON><PERSON><PERSON> lập tiền trích HĐ')
@section('content')
<style type="text/css">
    #btn-gialap {
      bottom: 0;
      left: 10%;
    }
</style>
<!-- Main content -->
<div class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body" id="myTable">
            @if ($requests->isNotEmpty())

            <table class="table table-hover table-bordered">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Mã y/c</th>
                  <th>Bắt đầu</th>
                  <th>Kết thúc</th>
                  <th>Kênh trích</th>
                  <th>YC trích</th>
                  <th>G<PERSON>lập trích thành công</th>
                </tr>
              </thead>

              <tbody>
                @foreach ($requests as $ct => $rqs)
                  <tr>
                    <th colspan="7"><code>{{ $ct }}</code></th>
                  </tr>

                  @foreach ($rqs as $rq)
                  <tr>
                    <td>{{ $rq->id }}</td>
                    <td><em>{{ $rq->partner_request_id }}</em></td>
                    <td>{{ $rq->time_begin_as_date }}</td>
                    <td>{{ $rq->time_expired_as_date }}</td>
                    <td>{{ $rq->payment_channel_code }}</td>
                    <td>{{ \App\Lib\Helper::makeVndCurrency($rq->amount_request) }}</td>
                    <td>
                      <input type="text" 
                            class="form-control fake-amount" 
                            data-id="{{ $rq->id }}" 
                            data-contract="{{ $ct }}"
                            @if ($rq->fakeAmount))
                              value="{{ \App\Lib\Helper::makeVndCurrency($rq->fakeAmount->fake_amount) }}" 
                            @else
                              value="" 
                            @endif
                            oninput="formatNumberCurrency(event)" 
                      />
                    </td>
                  </tr>
                  @endforeach
                @endforeach 
              </tbody>
            </table>

            <div class="position-relative">
              <button type="button" id="btn-gialap" class="btn btn-primary btn-block position-fixed" onclick="return onFakeRequest(this)">Giả lập</button>
            </div>
            @endif
          </div>
        </div>
      </div>
    </div>
    <!-- /.row -->
  </div><!-- /.container-fluid -->
</div>

@endsection

@push('js_bot')
<script>
  function onFakeRequest(element) {
    let params = [];
    $(element).parents('#myTable').find('.fake-amount').each(function (key, item) {
      params.push({
        contract_code: item.getAttribute('data-contract'),
        request_id: item.getAttribute('data-id'),
        fake_amount: item.value
      });
    });

    let url = "{{ config('app.url') . '/mock/request/store' }}";
    console.log(params);
    $.post(url, {
      params: params
    }).then(res => {
       if (res.success) {
        alert('giả lập thành công');
        return location.reload();
       }
    })
  }
</script>
@endpush 