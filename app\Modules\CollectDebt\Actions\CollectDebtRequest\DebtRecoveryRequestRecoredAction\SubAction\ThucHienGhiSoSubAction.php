<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecoredAction\SubAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\DebtRecoveryLedgerCreateAction;

class ThucHienGhiSoSubAction
{
  public function run(CollectDebtRequest $collectDebtRequest, Request $request): CollectDebtRequest
  {
    mylog(['Thực hiện ghi sổ cho yêu cầu có ID là:' => $collectDebtRequest->id]);
    $params = [
      'data' => [
        'profile_id'    => $collectDebtRequest->profile_id,
        'contract_code' => $collectDebtRequest->contract_code,
        'plan_ids'      => $collectDebtRequest->plan_ids,
        'request_id'    => $collectDebtRequest->id,
        'currency'      => $collectDebtRequest->currency,
        'amount'        => $collectDebtRequest->amount_receiver,
        'description'   => $collectDebtRequest->description ?? '',
        'time_record'   => time(),
        'created_by'    => request()->json('data.created_by', Helper::getCronJobUser()),
        'time_created'  => time()
      ]
    ];

    $debtRecoveryLedgerCreateRequest = new DebtRecoveryLedgerCreateRequest();
    $debtRecoveryLedgerCreateRequest->setJson(new ParameterBag($params));

    $ledger = app(DebtRecoveryLedgerCreateAction::class)->run($debtRecoveryLedgerCreateRequest);
    throw_if(!$ledger, new Exception('Lỗi ghi sổ'));

    // Ghi sổ thành công
    $collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO;

    if ($collectDebtRequest->isApproved()) {
      $collectDebtRequest->status = CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH;
    }

    $collectDebtRequest->time_recored           = time();
    $collectDebtRequest->time_completed         = time();
    $collectDebtRequest->recored_by             = $request->json('data.recored_by', Helper::getCronJobUser());
    $collectDebtRequest->completed_by           = $request->json('data.completed_by', Helper::getCronJobUser());
    $collectDebtRequest->completed_recheck_by   = $request->json('data.completed_recheck_by', Helper::getCronJobUser());
    $collectDebtRequest->save();
    return $collectDebtRequest;
  }
} // End class