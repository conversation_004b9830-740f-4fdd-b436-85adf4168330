<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\XuLyCutOffTimeTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\XuLyInsideTimeTask;

class XuLyThuThieuLamLaiLichTask	
{  
  public $metaData = [];

  public function run(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong, CollectDebtRequest $yeuCauDangDuocHachToan)
  {
		if (
			$yeuCauDangDuocHachToan->isYeuCauTrichLichThuQuaKhu() || 
			$yeuCauDangDuocHachToan->isYeuCauKeoDaiTimeExpired()  || 
			$yeuCauDangDuocHachToan->isYeuCauCutOff()
		) {
      return app(XuLyCutOffTimeTask::class)->run($lichDangHachToan, $soTienTrichThanhCong);
		}

		if ($yeuCauDangDuocHachToan->isThoiDiemHachToanYeuCauTrungVoiHienTai()) {
			$cutOffTime = Carbon::parse(Helper::getCutOffTime());
			$currentTime = Carbon::parse(now()->format('H:i:s'));

			if ($currentTime->lessThan($cutOffTime)) {
				return app(XuLyInsideTimeTask::class)->run($lichDangHachToan, $soTienTrichThanhCong);
			}else {
				return app(XuLyCutOffTimeTask::class)->run($lichDangHachToan, $soTienTrichThanhCong);
			}
		}

		// khong ky vong xuong day
		if ( Helper::isInsideTime() ) {
			return app(XuLyInsideTimeTask::class)->run($lichDangHachToan, $soTienTrichThanhCong);
		} 

		if ( Helper::isCutOffTime() ) {
			return app(XuLyCutOffTimeTask::class)->run($lichDangHachToan, $soTienTrichThanhCong);
		} 
  } // End method
} // End class