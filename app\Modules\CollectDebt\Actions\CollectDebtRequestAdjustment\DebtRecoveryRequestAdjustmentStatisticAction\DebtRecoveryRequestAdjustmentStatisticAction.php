<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentStatisticAction;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;


class DebtRecoveryRequestAdjustmentStatisticAction
{
  public function run(Request $request)
  {
    $trangThaiDaDuyet = [
      CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_1,
      CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_2,
      CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HOAN_THANH,
    ];


    $query = sprintf(
      "
        SUM(amount_receiver_on_partner) as tong_tien_yc,  
        SUM(CASE WHEN status IN (%s) THEN amount_receiver_on_partner ELSE 0 END) as tong_tien_da_duyet
      ",
      implode(',', $trangThaiDaDuyet)
    );

    $statistic = CollectDebtRequestAdjustment::query();
    if (!empty($request->json('data.contract_code'))) {
      $statistic = $statistic->where('contract_code', $request->json('data.contract_code'));
    }

    $statistic = $statistic->selectRaw($query)->first();

    return [
      [
        'label' => 'tong_tien_yc',
        'name' => 'YC',
        'value' => $statistic->tong_tien_yc,
      ],

      [
        'label' => 'da_duyet',
        'name' => 'Duyệt',
        'value' => $statistic->tong_tien_da_duyet
      ],
    ];
  }
} // End class
