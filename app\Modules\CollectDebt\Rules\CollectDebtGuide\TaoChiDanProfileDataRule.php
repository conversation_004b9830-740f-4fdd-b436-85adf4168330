<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtGuide;

use Illuminate\Contracts\Validation\Rule;

class TaoChiDanProfileDataRule implements Rule
{
    private $__errorMessage; 
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
      $userInterative = json_decode($value, true);
      if ( empty($userInterative['id']) ) {
        $this->__errorMessage = 'Profile Data thiếu trường `id`'; 
        return false;
      }

      if ( empty($userInterative['merchant']) ) {
        $this->__errorMessage = 'Profile Data thiếu trường `username`'; 
        return false;
      }

      return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
      return $this->__errorMessage;
    }
}
