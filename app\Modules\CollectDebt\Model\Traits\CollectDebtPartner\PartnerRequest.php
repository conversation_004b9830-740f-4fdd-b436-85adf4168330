<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtPartner;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;

trait PartnerRequest
{
  public function isTonTaiYeuCau(): bool
  {
    return $this->request_exists == CollectDebtEnum::PARTNER_REQUEST_DA_CO_YC_THANH_TOAN;
  }

  public function isKhongTonTaiYeuCau(): bool
  {
    return $this->request_exists == CollectDebtEnum::PARTNER_REQUEST_CHUA_CO_YC_THANH_TOAN;
  }
} // End class