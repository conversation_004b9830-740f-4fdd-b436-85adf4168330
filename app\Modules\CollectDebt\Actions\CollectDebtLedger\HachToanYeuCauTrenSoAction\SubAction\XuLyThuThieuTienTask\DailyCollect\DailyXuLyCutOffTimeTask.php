<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\DailyCollect;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\CongTienQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\IsCoLichThuPhiQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\Task\SwapLichThuNeuTrichTuongLaiST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime\IsChuaThuPhiQuaHanHomNayST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffGetLichThuCoRundateLaHomSauST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffTaoLichThuVetPhiSangNgayHomSau;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffTaoLichThuPhiQuaHanSangHomSauST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffTaoLichThuVetNoGocSangNgayHomSauST;

class DailyXuLyCutOffTimeTask
{
  public array $metaData = [];

  public function run(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong = 0)
  {
    // Thu gốc
    if ($lichDangHachToan->isLichThuGoc()) {
      return $this->xuLyCutOffThuGoc($lichDangHachToan, $soTienTrichThanhCong);
    }

    // Thu phí (Thanh toán phí)
    if ($lichDangHachToan->isLichThuPhi()) {
      return $this->xuLyCutOffThuPhi($lichDangHachToan, $soTienTrichThanhCong);
    }
  }

  public function xuLyCutOffThuGoc(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong = 0)
  {
    $soTienConPhaiThuTiep = $lichDangHachToan->request_amount_debit - $soTienTrichThanhCong;
    /** ------------- Không phải lệnh tất toán ------------- */
    if ($lichDangHachToan->isKhongPhaiLichTatToan()) {
      /**
       * 1. Kiểm tra hôm sau có lịch nào không (hôm sau có bản ghi lịch rundate nào không)
       *    1a. Nếu không -> thì tạo lịch thi vét nợ gốc có rundate là hôm sau
       *    1b. Nếu có    -> cộng thêm số tiền còn phải thu vào debit_begin, request_amount_debit vào lịch hôm sau
       * 2. Thực hiện sinh lịch thu phí chậm kỳ nếu lịch đang hạch toán là lịch chính
       */
      $lichCoRunDateLaHomSau = app(CutOffGetLichThuCoRundateLaHomSauST::class)->run($lichDangHachToan);
      

      // Tạo lịch thu nốt số tiền còn phải thu tiếp sang hôm sau
      if (!$lichCoRunDateLaHomSau) {
        $lichThuNoGocHomSau = app(CutOffTaoLichThuVetNoGocSangNgayHomSauST::class)->run(
          $lichDangHachToan,
          $soTienTrichThanhCong,
          $soTienConPhaiThuTiep
        );
      }

      // Cộng số tiền nợ gốc sang lịch thu kỳ tiếp theo
      if ($lichCoRunDateLaHomSau) {
        $updateGoiLichHomSau = CollectDebtSchedule::query()
                            ->where('id', $lichCoRunDateLaHomSau->id)
                            ->update([
                              'debit_begin' => DB::raw("debit_begin + " .  $soTienConPhaiThuTiep),
                              'request_amount_debit' => DB::raw("request_amount_debit + " .  $soTienConPhaiThuTiep),
                            ]);

				if (!$updateGoiLichHomSau) {
					mylog(['[LOI]' => 'loi update goi lich sang hom sau']);
					throw new Exception('loi update goi lich sang hom sau');
				}
      }
    } // End-if kiem tra lenht tat toan

    /** ------------- Là lệnh tất toán ------------- */
    if ($lichDangHachToan->isLichTatToan()) {
      $isCoLichThuPhiQuaHan = app(IsCoLichThuPhiQuaHanTask::class)->run($lichDangHachToan->contract_code, true);
      /**
       * 1. Move số tiền nợ gốc còn phải thu tiếp sang ngày hôm sau
       * 2. Nếu là lịch thu chính thì cần sinh phí quá hạn
       */
      $lichThuNoGocHomSau = app(CutOffTaoLichThuVetNoGocSangNgayHomSauST::class)->run(
        $lichDangHachToan,
        $soTienTrichThanhCong,
        $soTienConPhaiThuTiep
      );

      throw_if(!$lichThuNoGocHomSau, new Exception('Tat toan - khong the tao lich thu no goc sang ngay hom sau'));

      $isChuaThuPhiQuaHan = false;
      
      if ($lichDangHachToan->isLichThuGocVet()) {
        $isChuaThuPhiQuaHan = app(IsChuaThuPhiQuaHanHomNayST::class)->run($lichDangHachToan);
      }

      if ($lichDangHachToan->isLichThuGocChinh() || $isChuaThuPhiQuaHan) {
				if ( $lichDangHachToan->isKhongPhaiRunDateTuongLai() ) {
					mylog([
						'lich thu phi' => 'rundate khong phai tuong lai',
						'thuc hien sinh phi qua han' => 'ok'
					]);

					$phiQuaHan = $lichDangHachToan->getSoTienPhiTheoLoaiPhi(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN, $soTienConPhaiThuTiep);
					
					// Hôm nay chưa sinh phí quá hạn thì move phí QH mới sinh sang hôm sau
					if ( !$isCoLichThuPhiQuaHan ) {
						$lichThuPhiQuaHanHomSau = app(CutOffTaoLichThuPhiQuaHanSangHomSauST::class)->run(
							$lichDangHachToan,
							$phiQuaHan
						);
					}
					
					// Đang có lịch thu phí quá hạn, thì cộng số tiền quá hạn hiện tài vào và lấy sét lại thời gian chạy bằng đúng của lịch thu phí quá hạn đó
					if ( $isCoLichThuPhiQuaHan ) {
						$lichThuPhiQuaHanDaCongGopTien = app(CongTienQuaHanTask::class)->run(
							$isCoLichThuPhiQuaHan,
							$isCoLichThuPhiQuaHan->time_start,
							$isCoLichThuPhiQuaHan->time_end,
							$isCoLichThuPhiQuaHan->rundate,
							$phiQuaHan
						);
					}

					$this->metaData[] = ['label' => CollectDebtEnum::METADATA_PHI_QUA_HAN, 'value' => $phiQuaHan];
				}

				if ( $lichDangHachToan->isRunDateTuongLai() ) {
					mylog([
						'lich thu phi' => 'rundate LA TUONG LAI',
						'khong duoc sinh phi qua han' => 'YES'
					]);

          $swap = app(SwapLichThuNeuTrichTuongLaiST::class)->run($lichDangHachToan, $lichThuNoGocHomSau, true);
					mylog(['swap' => $swap]);
        }
      }
    }

   
    return $this->metaData;
  } // End method xuLyCutOffThuGoc

  public function xuLyCutOffThuPhi(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong = 0)
  {
    
    $soTienPhiConPhaiThuTiep = $lichDangHachToan->request_amount_debit - $soTienTrichThanhCong;

    if ($soTienPhiConPhaiThuTiep > 0) {
    
      // Mặc định đã là thu phí QH rồi
      $isCoLichThuPhiQuaHan = app(IsCoLichThuPhiQuaHanTask::class)->run($lichDangHachToan->contract_code, true);
    
      if ( !$isCoLichThuPhiQuaHan ) {
        $lichThuPhiDaMoveSangHomSau = app(CutOffTaoLichThuVetPhiSangNgayHomSau::class)->run(
          $lichDangHachToan,
          $soTienPhiConPhaiThuTiep
        );
      }

      if ( $isCoLichThuPhiQuaHan ) {
        $lichThuPhiQuaHanDaCongGopTien = app(CongTienQuaHanTask::class)->run(
          $isCoLichThuPhiQuaHan,
          $isCoLichThuPhiQuaHan->time_start,
          $isCoLichThuPhiQuaHan->time_end,
          $isCoLichThuPhiQuaHan->rundate,
          $soTienPhiConPhaiThuTiep
        );
      }
    }

    return $this->metaData;
  }
} // End class