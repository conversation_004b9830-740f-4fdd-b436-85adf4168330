# api_generate.md

## 📌 Mục tiêu
Viết 1 API phục vụ cho nghiệp vụ thu hồi nợ theo đúng domain hiện tại.

## ⚙️ Cấu trúc hệ thống
- Dùng Echo Framework
- Flow chuẩn: `Route → Controller → Request Validate → Service → Repository`
- C<PERSON> logging context (`ctx.<PERSON>gger().Infof(...)`)
- Model GORM đã có sẵn, ví dụ: `DebtTransaction`, `LoanContract`

## 🏗️ Phong cách viết code
- Tách rõ các tầng
- Code rõ ràng, maintainable
- Không viết mẫu — hãy dùng tên struct, biến, function đúng với context

## ✳️ Format mong muốn
- Route
- Request struct (validate)
- Controller function
- Service function
- Repository function (nếu có)
- Response mẫu

## 📥 Input:
Viết API xử lý việc **khách hàng thanh toán khoản nợ một phần (partial payment)**.  
- T<PERSON><PERSON>ền vào: contract_id, amount, method (tiền mặt/chuyển khoản)
- <PERSON><PERSON><PERSON> cầu:
  - Validate `contract_id` tồn tại
  - Kiểm tra trạng thái khoản vay
  - Cập nhật bảng `DebtTransaction`
  - Gọi service ghi nhận giao dịch
  - Trả về trạng thái thanh toán

## ✅ Output
Trả về JSON có `status`, `transaction_id`, `remaining_debt`
