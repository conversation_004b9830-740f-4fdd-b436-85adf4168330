<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\Task;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\DebtRecoveryContractGuideCreatePlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\Task\DongBoTaoLichThuPhiQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime\TaoLichThuVetNoGocTrongNgayST;

class DongBoTaoLichThuGocTask
{
  public $noGocDaThuThanhCong = 0;

  public $soTienNoGocKyTruoc = 0;

  public function run( CollectDebtGuide $collectDebtGuide, array $guideOtherData=[] ) {

    $periodBuild = app(DebtRecoveryContractGuideCreatePlanAction::class)->handleCreatePeriodSchedule($collectDebtGuide);
    $inserted = CollectDebtSchedule::insert($periodBuild);

    $plans = CollectDebtSchedule::where('contract_code', $collectDebtGuide->contract_code)->get();    
    
    $planTatToan = $plans->first(function (CollectDebtSchedule $plan) {
      return $plan->isLichTatToan();
    });

    $plans->map(function (CollectDebtSchedule $plan) use ($guideOtherData, $collectDebtGuide, $planTatToan) {
      $cycle = $guideOtherData['overdue_cycle'][$plan->time_start_as_date->format('d-m-Y')];
      $plan->success_amount_debit = $cycle['amount_debit_minus'];
      $plan->status = CollectDebtEnum::SCHEDULE_STT_MOI;

      if (!$plan->isKhongPhaiRunDateTuongLai() && $plan->isKhongPhaiLichTatToan()) {
        $plan->status = CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH;
      }

      // phí chậm kỳ
      if (!empty($cycle['fee_deferred_cycle'])) {
        $lichThuPhiChamKy = app(DongBoTaoLichThuPhiChamKyTask::class)->run($plan, $collectDebtGuide, $cycle['fee_deferred_cycle']);
      }

      // phí quá hạn
      if (!empty($cycle['fee_deferred'])) {
        $lichThuPhiQuaHan = app(DongBoTaoLichThuPhiQuaHanTask::class)->run($plan, $collectDebtGuide, $cycle['fee_deferred']);
      }

      $soTienConPhaiThuTiep = $plan->getSoTienConPhaiThanhToan();

      if ($soTienConPhaiThuTiep > 0) {
        // trong hạn -> tạo lịch thu vét
        if (!$collectDebtGuide->isTodayDenHanHoacQuaHanHopDong() && $plan->isKhongPhaiRunDateTuongLai()) {
          $lichThuVet = app(TaoLichThuVetNoGocTrongNgayST::class)->run(
            $plan,
            $soTienConPhaiThuTiep,
            $plan->success_amount_debit
          );
        }

        // quá hạn
        if ($collectDebtGuide->isTodayDenHanHoacQuaHanHopDong()) {
          $planTatToan->request_amount_debit += $soTienConPhaiThuTiep;
          $planTatToan->debit_begin += $soTienConPhaiThuTiep;;
          $planTatToan->save();
        }
      }

      $plan->save();
      return $plan; 
    });
  }
}
