<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class GetHopDongDangHachToanST
{
  public function run(): array
  {
    $contractCode = CollectDebtSchedule::where('status', CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN)
                                       ->select(['contract_code'])
                                       ->get()
                                       ->pluck('contract_code')
                                       ->toArray();
    return $contractCode;
  }
}
