<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;

class GetHopDongDangCoCongNoCanXuLyST {

  public array $contractCodes = [];

  public function run() 
  {
    $partners = CollectDebtPartner::query()
                                  ->where('status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
                                  ->where('status_refund', CollectDebtEnum::PARTNER_STT_RF_CHUA_HOAN)
                                  ->whereNotNull('other_data')
                                  ->where(function ($query) {
                                    $query->where(function ($q) {
                                      $q->where('payment_method_code', 'MPOS')
                                        ->has('collectDebtRequest');
                                    })->orWhere(function ($q) {
                                      $q->whereIn('payment_method_code', ['IB_OFF', 'VIRTUALACCOUNT'])
                                        ->whereAvailableBalance();  
                                    });
                                  })
                                  ->select(['id', 'other_data'])
                                  ->get();
                                
    if ($partners->isEmpty()) {
      return [];
    }

    $partners->each(function (CollectDebtPartner $partner) {
      $coreContract = $partner->getCoreContract();
      $this->contractCodes[] = $coreContract->contract_code;
      return $partner;
    });

    return array_filter($this->contractCodes);
  }
} // End class