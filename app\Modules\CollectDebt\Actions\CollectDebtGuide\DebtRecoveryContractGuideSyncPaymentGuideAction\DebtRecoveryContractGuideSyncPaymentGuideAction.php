<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideSyncPaymentGuideAction;

use App\Lib\NextlendCore;
use Exception;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoveryContractGuideSyncPaymentGuideAction
{
	public function run(Request $request)
	{
		$contractCode = trim($request->json('data.contract_code', ''));
		throw_if(empty($contractCode), new Exception('Mã HĐ là bắt buộc'));

		$collectDebtGuide = CollectDebtGuide::query()->where('contract_code', $contractCode)->first();
		throw_if(!$collectDebtGuide, new Exception('Chỉ dẫn HĐ không tồn tại'));

		

		$paymentMethodContractVer1 = $this->__getPaymentMethodContractVer1($contractCode);
		throw_if(
			empty($paymentMethodContractVer1['payment_guide']),
			new Exception('Khong co thong tin payment guide')
		);

		$paymentGuides = collect($paymentMethodContractVer1['payment_guide'])->keyBy('payment_method_code')->toArray();
		$collectDebtGuidePaymentMethods = json_decode($collectDebtGuide->payment_guide, true);

		$cl = collect($collectDebtGuidePaymentMethods)->keyBy('payment_method_code')->toArray();

		foreach ($paymentGuides as $paymentMethodCode => $it) {
			if (empty($cl[$paymentMethodCode])) {
				$cl[$paymentMethodCode] = $it;
			}
		}

		foreach ($collectDebtGuidePaymentMethods as &$pmItem) {
			if ( !empty($paymentGuides[$pmItem['payment_method_code']]) ) {
				$pmItem = $paymentGuides[$pmItem['payment_method_code']];
			}
		}

		$collectDebtGuidePaymentMethodsAsString = array_values($cl);
		CollectDebtGuide::query()->where('contract_code', $contractCode)->update(['payment_guide' => $collectDebtGuidePaymentMethodsAsString]);
		CollectDebtShare::query()->where('contract_code', $contractCode)->update(['payment_guide' => $collectDebtGuidePaymentMethodsAsString]);


		$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();
		
		$summaryOtherData = $collectDebtSummary->getSummaryOtherData();
		$paymentMethodIndex = $collectDebtSummary->getSummaryOtherDataIndex('PAYMENT_METHOD');
		$paymentMethodItems = $collectDebtSummary->getSummaryOtherDataItem('PAYMENT_METHOD');

		/**
		 * 0 => array:8 [
				"payment_method_code" => "MPOS"
				"payment_channel_code" => "MPOS"
				"payment_account_id" => "********"
				"payment_account_holder_name" => ""
				"payment_account_bank_branch" => ""
				"payment_account_bank_code" => ""
				"other_data" => array:4 [
					"payment_account_name" => "TEST VISA HAI"
					"payment_account_number" => "********"
					"payment_account_branch" => "NA"
					"payment_account_bank_code" => "NA"
				]
				"closed" => "NO"
			]
		 */
		
		$collect = collect($paymentMethodItems['data'])->keyBy('payment_method_code')->toArray();
		
		foreach ($paymentGuides as $paymentMethodCode => $itemV1) {
			// v1 đã nằm trong
			if ( !empty($collect[$paymentMethodCode]) ) {
				$collect[$paymentMethodCode] = [
					'payment_method_code' => $itemV1['payment_method_code'],
					'payment_channel_code' => $itemV1['payment_channel_code'],
					'payment_account_id' => $itemV1['payment_account_id'],
					'payment_account_holder_name' => $itemV1['payment_account_holder_name'] ?? '',
					'payment_account_bank_branch' => $itemV1['payment_account_bank_branch'] ?? '',
					'payment_account_bank_code' => $itemV1['payment_account_bank_code'] ?? '',
					'other_data' =>  $itemV1['other_data'],
					'closed' => $collect[$paymentMethodCode]['closed']
				];
			}

			// v1 chưa nằm trong
			if ( empty($collect[$paymentMethodCode]) ) {
				$collect[$paymentMethodCode] = [
					'payment_method_code' => $itemV1['payment_method_code'],
					'payment_channel_code' => $itemV1['payment_channel_code'],
					'payment_account_id' => $itemV1['payment_account_id'],
					'payment_account_holder_name' => $itemV1['payment_account_holder_name'] ?? '',
					'payment_account_bank_branch' => $itemV1['payment_account_bank_branch'] ?? '',
					'payment_account_bank_code' => $itemV1['payment_account_bank_code'] ?? '',
					'other_data' =>  $itemV1['other_data'],
					'closed' => $collectDebtSummary->isHopDongDaTatToan() ? 'YES' : 'NO'
				];
			}
		}

		$paymentMethodItems['data'] = array_values($collect);

		$summaryOtherData[$paymentMethodIndex] = $paymentMethodItems;
		CollectDebtSummary::query()->where('contract_code', $contractCode)->update([
			'other_data' => json_encode($summaryOtherData)
		]);

		return $collectDebtGuide;
	}

	private function __getPaymentMethodContractVer1(string $contractCode = '')
	{
		$nextlendCore = app(NextlendCore::class)->callRequest([
			'contract_code' => $contractCode
		], 'ContractV4_getPaymentGuide', 'GET');

		$result = $nextlendCore->decryptData(true);
		return $result;
	}
} // End class
