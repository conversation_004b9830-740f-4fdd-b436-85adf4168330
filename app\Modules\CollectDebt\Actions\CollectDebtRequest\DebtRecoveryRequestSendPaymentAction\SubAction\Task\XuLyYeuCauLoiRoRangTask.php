<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task;

use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Utils\CommonVar;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class XuLyYeuCauLoiRoRangTask
{
  /**
   * Bối cảnh: YC thực hiện gửi và bị lỗi rõ ràng -> đánh dấu time_sended và sẽ gọi lại sau 20p
   * B1: Gửi lại qua MPOS
   * B2: Trả về kết quả là trùng
   *
   * @param CollectDebtRequest $collectDebtRequest
   * @return CollectDebtRequest
   */
  public function run(CollectDebtRequest $collectDebtRequest, $sendDebtResult): CollectDebtRequest
  {
    $collectDebtRequest->time_sended = time();
    $collectDebtRequest->other_data = $collectDebtRequest->putOtherData([
      'type' => 'SENDER',
      'time_modified' => now()->timestamp,
      'data' => $sendDebtResult,
      'note' => 'Gửi lệnh bị lỗi RÕ RÀNG'
    ]);
    $collectDebtRequest->save();
    return $collectDebtRequest;
  }
} // End class