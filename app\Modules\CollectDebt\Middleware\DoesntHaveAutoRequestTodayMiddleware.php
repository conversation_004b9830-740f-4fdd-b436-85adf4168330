<?php

namespace App\Modules\CollectDebt\Middleware;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Closure;
use Exception;

class DoesntHaveAutoRequestTodayMiddleware
{
  /**
   * Handle an incoming request.
   *
   * @param  \Illuminate\Http\Request  $request
   * @param  \Closure  $next
   * @return mixed
   */

  public function handle($request, Closure $next)
  {
    $lichTuDongHomNay = CollectDebtRequest::where('contract_code', $request->json('data.contract_code'))
                                          ->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
                                          ->where('time_begin', '<=', time())
                                          ->where('time_expired', '>=', time())
                                          ->where('status', CollectDebtEnum::REQUEST_STT_MOI_TAO)
                                          ->get();
    
    $errorMessage = sprintf('<PERSON>ợ<PERSON> đồng `%s` vẫn đang có lịch thu tự động hôm nay. H<PERSON><PERSON> đảm bảo bạn đã dừng job thu tự động và đã hủy các yêu cầu trích tự động', $request->json('data.contract_code'));
    throw_if($lichTuDongHomNay->count() != 0, new Exception($errorMessage));
    
    return $next($request);
  }
} // End class
