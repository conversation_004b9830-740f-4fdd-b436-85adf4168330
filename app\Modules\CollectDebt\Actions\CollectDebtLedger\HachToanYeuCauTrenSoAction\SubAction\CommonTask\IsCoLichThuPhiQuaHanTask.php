<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class IsCoLichThuPhiQuaHanTask
{
  public function run(string $contractCode='', $isCheckLichQuaHanChuaTaoQuaKenhThuNao=false)
  {
    $planThuPhiQuaHans = CollectDebtSchedule::where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
                                            ->where('isfee', CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI);

		// chua tao qua kenh thu nao, chua xu ly
    if ($isCheckLichQuaHanChuaTaoQuaKenhThuNao) {
      $planThuPhiQuaHans = $planThuPhiQuaHans->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY);
    }

    $planThuPhiQuaHans = $planThuPhiQuaHans->where('contract_code', $contractCode)
                                           ->get();
    
    if ($planThuPhiQuaHans->isEmpty()) {
      return false;
    }

    return $planThuPhiQuaHans->first(function (CollectDebtSchedule $plan) {
      return $plan->isLichThuPhiQuaHan();
    });
  }
} // End class