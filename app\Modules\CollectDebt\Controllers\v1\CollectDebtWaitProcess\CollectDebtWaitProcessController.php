<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtWaitProcess;

use Exception;
use App\Lib\Helper;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\CollectDebtWaitProcess\DebtRecoveryWaitProcessHandleAction;
use App\Modules\CollectDebt\Actions\CollectDebtWaitProcess\DebtRecoveryWaitProcessHandleImproveAction;

class CollectDebtWaitProcessController extends Controller
{
	public function processHandle(Request $request)
	{
		try {
			$waitProcessIds = app(DebtRecoveryWaitProcessHandleImproveAction::class)->initWaitProcess();
			return $this->successResponse($waitProcessIds, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
