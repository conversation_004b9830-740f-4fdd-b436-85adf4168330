<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\SubAction;

use App\Lib\NextlendCore;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DanhDauHopDongVer1LaDaTatToanSubAction
{
  public function run(CollectDebtSummary $collectDebtSummary) {
		mylog(['TAT_TOAN_HD' => $collectDebtSummary->contract_code]);
		
    $payload = [
      'contract_code' => $collectDebtSummary->contract_code,
      'total_amount_paid' => $collectDebtSummary->total_amount_paid, // tổng gốc đã thanh toán
      'total_fee_paid' => $collectDebtSummary->total_fee_paid, // tổng phí đã thanh toán
      'total_amount_refund' => $collectDebtSummary->total_amount_refund, // tổng hoàn
      'total_amount_receiver' => $collectDebtSummary->total_amount_receiver, // tổng nhận
      'fee_deferred' => $collectDebtSummary->fee_overdue, // phí quá hạn
      'fee_cycle_deferred' => $collectDebtSummary->fee_overdue_cycle, // phí ck
      'discount_fee_cycle_deferred' => $collectDebtSummary->fee_overdue_cycle_reduction, // giảm phí ck
      'discount_fee_deferred' => $collectDebtSummary->fee_overdue_reduction, // giảm phí qh
      'time_settlement' => time(),
      'time_accounting' => $collectDebtSummary->time_accounting,
      'time_complated' => $collectDebtSummary->time_complated,
      'time_overdued' => $collectDebtSummary->time_overdued, // thời gian quá hạn
    ];

    $nextlendCore = app(NextlendCore::class)->callRequest($payload, 'ContractV4_settlement', 'post');
    $decryptData = $nextlendCore->decryptData();
    return $decryptData;
  }
}