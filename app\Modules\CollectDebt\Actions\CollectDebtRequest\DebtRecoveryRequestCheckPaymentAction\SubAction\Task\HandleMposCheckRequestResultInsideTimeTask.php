<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class HandleMposCheckRequestResultInsideTimeTask
{  
  /**
   * Trạng thái cuối: SUCCESS, CANCEL, EXPIRED
   * 
   *
   * @param CollectDebtRequest $collectDebtRequest [explicite description]
   * @param array $checkDebtResult [explicite description]
   *
   * @return CollectDebtRequest
   */
  public function run(CollectDebtRequest $collectDebtRequest, array $checkDebtResult): CollectDebtRequest
  {
    $request = request();
    
    if ($checkDebtResult['data']['status'] == 'TIMEOUT') {
      // Chờ
    }

    if ($checkDebtResult['data']['status'] == 'WAITTING') {
      // Chờ
    }

    // Success sẽ có những case sau
    if ($checkDebtResult['data']['error_code'] == '00') {
      switch ($checkDebtResult['data']['status']) {
        /**
         * Case 1: Thành công 
          Update trạng thái partner của yêu cầu trích: Đã nhận yêu cầu
          Update trạng thái yêu cầu trích: Đã gửi 
          Kết thúc luồng, chuyển yêu cầu khác 

         */
        case 'SUCCESS':
          $collectDebtRequest->amount_receiver = $checkDebtResult['data']['amount_debit'];
          $collectDebtRequest->fee = 0;
          $collectDebtRequest->partner_transaction_id = $checkDebtResult['data']['partner_transaction_id'];
          $collectDebtRequest->time_receivered = time();
          $collectDebtRequest->checked_by = $request->json('data.users_admin_id', 'CronJob');
          $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;

          break;
        
       
        case 'CANCEL':
          $collectDebtRequest->checked_by = $request->json('data.users_admin_id', 'CronJob');
          $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_TU_CHOI;
          break;

        case 'EXPIRED':
          $collectDebtRequest->checked_by = $request->json('data.users_admin_id', 'CronJob');
          $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_TU_CHOI;
          break;

        case 'PENDING':
          // Chờ
          break;

        default:
          // Không tác động => chờ
          break;
      }
    }

    $collectDebtRequest->save();
    return $collectDebtRequest;
  }
} // End class
