<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task;

use App\Lib\ApiCall;
use App\Utils\CommonVar;
use Exception;

class GetHopDongNhanTienTuNguonThuThuaTask
{
	public function run(string $contractCode)
	{
		$payload = [
			'module' => CommonVar::API_CASH_IN_MODULE,
			'path' => '/CashinRequestGetKhoanUngNhanTien',
			'params' => [
				'contract_code' => $contractCode
			],
			'method' => 'GET'
		];

		$r = (new ApiCall())->callFunctionApi($payload, true);

		if (isset($r['data']['listMaHopDongNhanTienCashIn'])) {
			return $r;
		}

		throw new Exception('Không có thông tin HĐ nhận tiền');
	}
} // End class