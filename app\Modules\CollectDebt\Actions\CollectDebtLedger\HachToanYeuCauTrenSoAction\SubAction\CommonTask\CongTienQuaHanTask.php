<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;
use Mo<PERSON>y\CountValidator\Exact;

class CongTienQuaHanTask
{
  public function run(
    CollectDebtSchedule $lichThuPhiQuaHanDaCo, 
    int $timeStart, 
    int $timeEnd, 
    int $runDate, 
    float $soTien, // có thể là phí quá hạn đc sét hoặc số tiền trích thành công
    $type='PLUS'
  )
  {
    $lichThuPhi = CollectDebtSchedule::find($lichThuPhiQuaHanDaCo->id);

    if ($type == 'PLUS') {
      $lichThuPhi->request_amount_debit += $soTien;
      $lichThuPhi->debit_begin += $soTien;
      $lichThuPhi->type = CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH;
    }else {
      $lichThuPhi->request_amount_debit -= $soTien;
      $lichThuPhi->debit_begin -= $soTien;
    }
    
    $lichThuPhi->time_start = $timeStart;
    $lichThuPhi->time_end = $timeEnd;
    $lichThuPhi->rundate = $runDate;
    $lichThuPhi->status = CollectDebtEnum::SCHEDULE_STT_MOI;
    
    $lichThuPhi->other_data = $lichThuPhi->resetKenhThu();
    
		$result = $lichThuPhi->save();
		
		if (!$result) {
			mylog(['[LOI]' => 'cong tien thu phi qua han err']);
			throw new Exception('cong tien thu phi qua han err');
		}

    return $lichThuPhi;
  }
} // End class