<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtConfig;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetAmountCanRefundAction\DebtRecoverySummaryGetAmountCanRefundAction;

class ConfigShowOffController extends Controller
{
	public function index(Request $request)
	{
    $contractCode = $request->json('data.contract_code');


    $buttonState = [
      'DUNG_JOB_TU_DONG' => 'ON',
      'TAO_YC_TAT_TOAN_HD' => 'OFF',
      'HOAN_TIEN' => 'ON',
      'NHAN_CHUYEN_KHOAN_NGOAI' => 'ON',
      'TAO_YC_TRICH_TAY' => 'OFF',
      'TIEP_TUC_JOB_TU_DONG' => 'OFF',
      'DANH_DAU_LA_TAT_TOAN' => 'OFF',
      'HUY_YC_TRICH_TU_DONG' => 'OFF',
			'HOAN_PHI_DA_THU' => 'OFF'
    ];

    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();
    
    if ($collectDebtSummary->isHopDongDaTatToan()) {
      $buttonState['DUNG_JOB_TU_DONG'] = 'OFF';
      $buttonState['TAO_YC_TAT_TOAN_HD'] = 'OFF';
      $buttonState['HOAN_TIEN'] = 'OFF';
      $buttonState['TIEP_TUC_JOB_TU_DONG'] = 'OFF';
      $buttonState['TAO_YC_TRICH_TAY'] = 'OFF';

			$tongPhiCoTheHoan = $collectDebtSummary->getTongPhiCoTheHoan();
			// phi co the hoan > 0, va dang khong co yeu cau hoan nao khac
			if ($tongPhiCoTheHoan > 0 && $collectDebtSummary->amount_refunding == 0) {
				$buttonState['HOAN_PHI_DA_THU'] = sprintf('Hoàn phí: %s', Helper::priceFormat($tongPhiCoTheHoan));
			}
    }

  /* ---------------------- Kiểm tra xem số tiền có thể hoàn, nếu > 0 thì show nút, ngược lại thì ẩn nút ------------ */
    $tongTienCoTheHoan = app(DebtRecoverySummaryGetAmountCanRefundAction::class)->run(['contract_code' => $contractCode]);
    if ($tongTienCoTheHoan['total_amount_can_refund'] > 0) {
      $buttonState['HOAN_TIEN'] = sprintf('Hoàn thu thừa (%s)', Helper::priceFormat($tongTienCoTheHoan['total_amount_can_refund'], ' VNĐ'));
    }

    if ($tongTienCoTheHoan['total_amount_can_refund'] <= 0 || $collectDebtSummary->amount_refunding > 0) {
      $buttonState['HOAN_TIEN'] = 'OFF';
    }

  /* ---------------------- Bắt các nút khác  ---------------------- */
    if (!$collectDebtSummary->isHopDongDaTatToan()) {
      // Kiểm tra đã bấm nút dừng job hay chưa, nếu bấm rồi thì ẩn nó đi
      $configAuto = CollectDebtConfigAuto::isPauseContractJob($contractCode);
      
       /* ---------------- Nút dừng job -------------- */
      if ($configAuto) {
        $buttonState['DUNG_JOB_TU_DONG'] = 'OFF';

        // Nếu đã thu hết gốc mà vẫn còn lịch thu phí, thì show nút Tạo yc tất toán HĐ
        $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();

        // Xử lý nút tạo yc giảm phí
        $isDuDieuKienHienThiNut = $this->isHienThiNutTaoYcGiamPhi(
          $collectDebtSummary->contract_code,
          $configAuto, 
         	$collectDebtSummary->isTongTienTrichThanhCongLonHonGiaTriHopDong()
        );

        if (!$isDuDieuKienHienThiNut) {
          $buttonState['TAO_YC_TAT_TOAN_HD'] = 'OFF';
        }

        if ($isDuDieuKienHienThiNut) {
          $buttonState['TAO_YC_TAT_TOAN_HD'] = 'ON';
        }
      }

      if (!$configAuto) {
        $buttonState['DUNG_JOB_TU_DONG'] = 'ON';
       
      }

      // nút dừng yc tự động đã đc bấm -> đang dừng job tự động
      if ($buttonState['DUNG_JOB_TU_DONG'] == 'OFF') {
        $buttonState['TAO_YC_TRICH_TAY'] = 'ON';
        $buttonState['TIEP_TUC_JOB_TU_DONG'] = 'ON';
        $buttonState['HUY_YC_TRICH_TU_DONG'] = 'ON';

        // Kiểm tra xem toàn bộ yêu cầu trích tự động về trạng thái cuối hay chưa. Về hết rồi thì ẩn
        if ($this->isToanBoYeuCauTuDongDaVeTrangThaiCuoi($contractCode)) {
          $buttonState['HUY_YC_TRICH_TU_DONG'] = 'OFF';
        }
      }

      // nút dừng yc tự động chưa đc bấm -> chưa dừng job tụ động
      if ($buttonState['DUNG_JOB_TU_DONG'] == 'ON') {
        $buttonState['TAO_YC_TRICH_TAY'] = 'OFF';
        $buttonState['TIEP_TUC_JOB_TU_DONG'] = 'OFF';
        $buttonState['HUY_YC_TRICH_TU_DONG'] = 'OFF';
      }
    }

    return $this->successResponse($buttonState, $request);
	}

  public function isToanBoYeuCauTuDongDaVeTrangThaiCuoi(string $contractCode='') {
    $collectDebtRequests = CollectDebtRequest::query()->where('contract_code', $contractCode)
                            ->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT)
														->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
                            ->get();
    
    return $collectDebtRequests->every(function (CollectDebtRequest $rq) {
      return $rq->isRequestFinalStatus();
    });
  }

  /**
   * Điều kiện hiển thị nút: Dừng job, đã thu hết gốc, còn phí, chưa có yêu cầu giảm phí
   *
   * Điều kiển ẩn nút: có YC giảm phí mới tạo || Đã duyệt 1 || Đã duyệt 2
   * 
   * @param string $contractCode
   * @return boolean: true: hiển thị | false: ẩn
   */
  public function isHienThiNutTaoYcGiamPhi(string $contractCode, bool $isDungJob, bool $isDuDieuKienLamTatToan): bool {
    $collectDebtRequests = CollectDebtRequest::query()
          ->where('contract_code', $contractCode)
          ->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
          ->where('amount_request', 0)
          ->where('amount_receiver', 0)
          ->get();
    
    $listYeuCauDeXuatGiamPhi = $collectDebtRequests->filter(function (CollectDebtRequest $rq) {
      return $rq->isTrichTayGiamPhi();
    });

    $isDapUngDieuKienAnNut = $listYeuCauDeXuatGiamPhi->contains(function (CollectDebtRequest $rq) {
      return $rq->isNew() || $rq->isTrichTayGiamPhiVaChuaDuyetBuoc1() || $rq->isTrichTayGiamPhiVaChuaDuyetBuoc2() || $rq->isDaDuyetGiamPhiBuoc2();
    });

   
    if ($isDapUngDieuKienAnNut) {
      return false;
    }
    
    $isChuaCoYeuCauGiamPhiMoiTao = $listYeuCauDeXuatGiamPhi->isEmpty() || $listYeuCauDeXuatGiamPhi->every(function (CollectDebtRequest $rq) {
      return !$rq->isNew() && $rq->isRequestFinalStatus();
    });

    if ($isDungJob && $isDuDieuKienLamTatToan && $isChuaCoYeuCauGiamPhiMoiTao) {
      return true;
    }

    return false;
  }
} // End class
