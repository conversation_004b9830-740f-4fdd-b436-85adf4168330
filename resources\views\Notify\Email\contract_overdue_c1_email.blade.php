<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Email quá hạn Cấp 1</title>
    @include('Notify.Email.inc.style')
</head>
<body>
    @php($merchant = $data['merchant'])
    @php($nextlendCompany = $data['company'])

    <div class="mail-content" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">
        <div class="content">
            <p>
                Kính gửi: <PERSON>u<PERSON> khách
                <strong>{{ sprintf('%s - %s', $merchant['fullname'], $merchant['business_representative'])}}</strong>
            </p>
        
            <p>
                <b>{{ $nextlendCompany['company_fullname'] }}</b>
                ({{ $nextlendCompany['company_subname'] }}) xin gửi lời chào trân trọng và cảm ơn Quý khách
                hàng đã quan tâm đến dịch vụ “Hỗ trợ Ứng vốn Kinh doanh”, “Đặc biệt” dành cho đơn vị đang là đối tác của hệ sinh thái “Next360”:
            </p>
            
            <ul style="list-style-type: none; margin-left: 25px;">
                <li>Chủ thể: {{ $merchant['fullname'] }}</li>
                <li>ĐKKD số: {{ $merchant['certificate_id'] }}</li>
                <li>Đại diện: {{ $merchant['business_representative'] }} - Chức vụ: Đại diện đơn vị</li>
                <li>
                    Số CMND/CCCD: {{ $merchant['passport' ]}} - 
                    Ngày cấp: {{ $merchant['issue_date']}} - 
                    Nơi cấp: {{ $merchant['issued_by']}}
                </li>
                <li>Địa chỉ: {{ $merchant->getAddress() }}</li>
                <li>
                    Phụ lục Hợp đồng số: <strong>{{ $data['contract']['code'] }}</strong>
                </li>

                <li>Số vốn ứng: {{ $data['contract']['amount'] }} VNĐ</li>

                <li>
                    Thời hạn: 
                    {{ $data['contract']['cycle'] }} ngày, kể từ ngày
                    {{ $data['contract']['time_start'] }} đến ngày
                    {{ $data['contract']['time_end'] }}
                </li>

                <li>Sau đây gọi chung là <strong>“Quý khách”</strong>.</li>
            </ul>

            <p>
                Tuy nhiên, Quý khách chưa thực hiện hoàn trả vốn ứng đầy đủ theo thỏa thuận. Theo đó, cho đến nay, tình trạng hoàn trả khoản ứng vốn như sau:
            </p>

            @include('Notify.Email.inc.qua_han_table')
            
            <p>
                <em>
                    * Số ngày quá hạn: số ngày thể hiện trong thông báo này chỉ tính đến ngày gửi thông báo, số ngày quá hạn thực tế được chốt tại ngày Quý khách hoàn trả vốn ứng.
                </em>
            </p>

            <p>
                Thông báo này là một phần không tách rời của Phụ lục và Hợp đồng đã ký giữa Vi Mô và Quý khách.
            </p>

            <p>
                Để đảm bảo lợi ích của mình, Quý khách vui lòng hoàn trả số tiền ứng vốn và số phí dịch vụ quá hạn ngay khi nhận được Thông báo này.
            </p>
            @include('Notify.Email.inc.hinh_thuc_thanh_toan_va')
            
            <p>
                Quý khách chưa hoàn tất nghĩa vụ thanh toán, các dịch vụ của Quý khách sẽ bị khóa tạm thời. Khi đó, Công ty Vi Mô sẽ xem xét áp dụng các điều khoản và quy định pháp luật để thu hồi số tiền trên. Nếu Quý khách đã thanh toán, xin vui lòng bỏ qua thông báo này.
            </p>

            <p>
                Để biết thêm chi tiết hoặc cần hỗ trợ, Quý khách vui lòng liên hệ Phòng Dịch vụ và Chăm sóc Khách hàng của {{ $nextlendCompany['company_subname'] }}:
                Điện thoại: <b> {{ $nextlendCompany->getPhoneNumber() }}</b>.

                Email:
                <a href="mailto:{{ $nextlendCompany->getEmail() }}" style="text-decoration: none;">
                    {{ $nextlendCompany->getEmail() }}
                </a>
            </p>

            <p>
                {{ $nextlendCompany['company_subname'] }} xin trân trọng cảm ơn Quý khách đã quan tâm, sử dụng các sản phẩm, dịch vụ của chúng tôi và rất mong nhận được sự hợp tác của Quý khách.
            </p>

            <p>Trân trọng!</p>

            @include('Notify.Email.inc.mail_footer')
        </div>
    </div>
</body>
</html>