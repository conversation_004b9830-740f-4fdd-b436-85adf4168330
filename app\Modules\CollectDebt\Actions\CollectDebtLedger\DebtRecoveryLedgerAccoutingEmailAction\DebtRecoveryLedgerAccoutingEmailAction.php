<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccoutingEmailAction;

use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerSetStatusActionRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSetStatusAction\DebtRecoveryLedgerSetStatusAction;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareGetContractCodeAction\DebtRecoveryShareGetContractCodeAction;
use App\Modules\CollectDebt\Requests\v1\ColelctDebtShare\DebtRecoveryShareGetByContractCodeRequest;
use App\Traits\SetUpDataEmail;
use Illuminate\Support\Facades\Mail;
use Exception;

class DebtRecoveryLedgerAccoutingEmailAction {
    /*
     * Lấy các thằng nào mà có trạng thái email = 1 và đã làm xong summayry = 3
     */

    public $category_care_code;
    public $service_care_code = 'MAIL';
    protected $__sapToiHanArr = [
        1, 2, 3, 5
    ];

    public function run($request) {
        $ledgersWithTheirSchedules = CollectDebtLedger::where(['status_email' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT, 'status_summary' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT])->first();
        if (!$ledgersWithTheirSchedules) {
            $this->__ReturnError('Không có thông tin sổ cần gửi mail');
        }
        $process = $this->__process($ledgersWithTheirSchedules);
        if (!$process) {
            $this->__ReturnError('PROCESS ERROR');
        }
        $dataSummary = $this->__getDataSummary($ledgersWithTheirSchedules);
        if (!$dataSummary) {
            $this->__ReturnError('DATA SUMMARY ERROR');
        }

        $dataShare = $this->__getDataShare($ledgersWithTheirSchedules);
        if (!$dataShare) {
            $this->__ReturnError('DATA SHARE EMPTY');
        }
        $isMail = $this->_isSendMail($ledgersWithTheirSchedules, $dataSummary, $dataShare);
        if (!$isMail) {
            $this->__ReturnError('DATA SUMMARY ERROR2');
        }
        $create = $this->__createEvent($ledgersWithTheirSchedules, $dataShare, $dataSummary);
        if ($create) {
            $this->__complete($ledgersWithTheirSchedules);
        } else {
            $this->__fail($ledgersWithTheirSchedules);
        }
        return $create;
    }

    protected function __setDataEvent($value) {
        $data = [
//            'contract'=> [],
//            'profile'=> [],
//            'list_fee'=> [],
//            'company'=> [],
//            'payment'=> [],
//            'overdue'=> [],
//            'overdue_period'=> [],
        ];
        if ($value) {
            if (isset($value['company_data']) && $value['company_data']) {
                $data['company'] = json_decode($value['company_data'], true);
            }
            if (isset($value['contract_data']) && $value['contract_data']) {
                $data['contract'] = json_decode($value['contract_data'], true);
            }
            if (isset($value['profile_data']) && $value['profile_data']) {
                $data['profile'] = json_decode($value['profile_data'], true);
            }
            if (isset($value['payment_guide']) && $value['payment_guide']) {
                $data['payment'] = json_decode($value['payment_guide'], true);
            }
            if (isset($value['list_fee']) && $value['list_fee']) {
                $data['list_fee'] = json_decode($value['list_fee'], true);
            }
        }
        return $data;
    }

    protected function __createEvent($ledgersWithTheirSchedules, $dataShare, $dataSummary) {
        $other_data = json_decode($ledgersWithTheirSchedules->other_data, true);
        $plan = [];

        $rq = new \App\Modules\EmailRemind\Request\CollectDebtContractEvent\DebtRecoveryContractEventCreateRequest();
        $inputs = [
            'category_care_code' => $this->category_care_code,
            'service_care_code' => $this->service_care_code,
            'data' => $this->__setDataEvent($dataShare),
            'description' => 'Tạo Event',
            'other_data' => [
                'summary' => $dataSummary,
            ],
            'time_start' => time(),
            'contract_code' => $dataSummary->contract_code,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $create = (new \App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateAction\DebtRecoveryContractEventCreateAction())->run($inputs);
        if (isset($create['id']) && $create['id']) {
            return true;
        }
        return false;
    }

    protected function __ReturnError($code) {
        throw new Exception($code);
    }

    protected function __getDataSummary($ledgersWithTheirSchedules) {
        return \App\Modules\CollectDebt\Model\CollectDebtSummary::where(['contract_code' => $ledgersWithTheirSchedules->contract_code])->first();
    }

    protected function _isSendMail($ledgersWithTheirSchedules, $dataSummary, $dataShare) {
        $other_data = $ledgersWithTheirSchedules->getOtherDataItem('SUMMARY');
        $summary = $other_data['data'];
        if ($summary) {
            if (isset($summary['fee_overdue']) && $summary['fee_overdue'] > 0) {
                $days = $dataSummary->number_day_overdue;
                if ($days >= 1 && $days < 5) {
                    $this->category_care_code = 'NOTIFY_CONTRACT_DUE1';
                } else if ($days >= 5 && $days <= 10) {
                    $this->category_care_code = 'NOTIFY_CONTRACT_DUE2';
                } else if ($days >= 11) {
                    $this->category_care_code = 'NOTIFY_CONTRACT_DUE3';
                }
                return true;
            }
            if (isset($summary['fee_overdue_cycle']) && $summary['fee_overdue_cycle'] > 0) {
                $this->category_care_code = 'CONTRACT_OVERDUE_CYCLE';
                return true;
            }
            // if ($this->__SAP_TOI_HAN($dataSummary, $dataShare)) {
            //     $this->category_care_code = 'NOTIFY_CONTRACT_DUE';
            //     return true;
            // }
        }
//        $this->category_care_code = 'CONTRACT_OVERDUE_CYCLE';

        if (!$this->category_care_code) {
            $this->__noHandle($ledgersWithTheirSchedules);
            $this->__ReturnError('KHÔNG CẦN GỬI MAIL');
        }

        return false;
    }

    /*
     * Loại hợp đồng
     * kỳ thì theo lịch
     * theo ngày thì tìm lịch cuối
     * contract_type
     */

    protected function __SAP_TOI_HAN($dataSummary, $dataShare) {
//        const GUIDE_HD_TRICH_NGAY = 1;
//  const GUIDE_HD_GIA_HAN = 2;
//  const GUIDE_HD_TRICH_KY = 3;
        $contract = json_decode($dataShare['contract_data'], true);
        if ($contract) {
            if ($dataSummary->contract_type == CollectDebtEnum::GUIDE_HD_TRICH_KY) {
                $other_data = json_decode($dataSummary->other_data, true);
                if ($other_data) {
                    if ($contract['time_end'] > time()) {
                        $timeEnd = $contract['time_end'];
                        $origin = date_create(date('Y-m-d', $timeEnd));
                        $target = date_create(date('Y-m-d'));
                        $interval = date_diff($origin, $target);
                        $days = $interval->format('%a');
                        if (in_array($days, $this->__sapToiHanArr)) {
                            return true;
                        }
                    }



                    foreach ($other_data as $key => $value) {
                        if (isset($value['type']) && $value['type'] == 'PLAN') {
                            foreach ($value['data'] as $key1 => $value1) {

                                if ($value1['time_cycle'] > time()) {
                                    $timeEnd = $value1['time_cycle'];
                                    $origin = date_create(date('Y-m-d', $timeEnd));
                                    $target = date_create(date('Y-m-d'));
                                    $interval = date_diff($origin, $target);
                                    $days = $interval->format('%a');
                                    if (in_array($days, $this->__sapToiHanArr)) {
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                $timeEnd = $contract['time_end'];
                $origin = date_create(date('Y-m-d', $timeEnd));
                $target = date_create(date('Y-m-d'));
                $interval = date_diff($origin, $target);
                $days = $interval->format('%a');
                if (in_array($days, $this->__sapToiHanArr)) {
                    return true;
                }
            }
        }
        return false;
    }

    protected function __getDataShare($ledgersWithTheirSchedules) {
        $inputs = [
            'contract_code' => $ledgersWithTheirSchedules->contract_code
        ];
        return app(DebtRecoveryShareGetContractCodeAction::class)->run($inputs);
    }

    protected function __process($ledgersWithTheirSchedules) {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_email' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

    protected function __complete($ledgersWithTheirSchedules) {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_email' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

    protected function __fail($ledgersWithTheirSchedules) {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_email' => CollectDebtEnum::LEDGER_STT_ACTION_LOI,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

    protected function __noHandle($ledgersWithTheirSchedules) {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_email' => CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

}
