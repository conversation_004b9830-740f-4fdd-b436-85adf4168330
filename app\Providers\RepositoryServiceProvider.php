<?php

namespace App\Providers;

use App\Modules\CollectDebtGateway\Repositories\ICollectDebtGateway;
use App\Modules\CollectDebtGateway\Repositories\MposCollectDebtGateway;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
  /**
   * Register any application services.
   *
   * @return void
   */
  public function register()
  {
    $this->app->bind(ICollectDebtGateway::class, MposCollectDebtGateway::class);
  }

  /**
   * Bootstrap any application services.
   *
   * @return void
   */
  public function boot()
  {
    
  }
} // End class
