<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

use Carbon\Carbon;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

trait PlanScope
{
  public function scopeWhereRunDateTruocHoacBangThoiDiemHienTai($query, int $dateRunAsTimestamp = 0)
  {
    return $query->whereRaw(" UNIX_TIMESTAMP(DATE_FORMAT(rundate, '%Y-%m-%d')) <= ?", [$dateRunAsTimestamp]);
  }

  public function scopeWhereDateRunIsCurrentDate($query, Carbon $dateRun)
  {
    return $query->whereRaw(" DATE(DATE_FORMAT(rundate, '%Y-%m-%d')) = ?", [$dateRun->toDateString()]);
  }

  public function scopeWhereNotFinalStatus($query)
  {
    return $query->whereNotIn('status', [CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH, CollectDebtEnum::SCHEDULE_STT_DA_HUY]);
  }

  public function scopeWhereChuaTaoQuaKenhThuNao($query)
  {
    return $query->whereRaw("LENGTH(JSON_UNQUOTE(JSON_EXTRACT(other_data, '$[*].data.request_created_channel'))) = 4");
  }
} // End class