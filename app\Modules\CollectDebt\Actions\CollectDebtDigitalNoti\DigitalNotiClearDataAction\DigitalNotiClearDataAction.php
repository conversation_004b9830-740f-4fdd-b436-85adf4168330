<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiClearDataAction;

use App\Modules\CollectDebt\Model\CollectDebtDigitalTmp;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;


class DigitalNotiClearDataAction
{
	public $timeKeepNoti = 3 * 24 * 60 * 60;

	public array $listContractSapDenHan = [];

	public function run()
	{
		CollectDebtDigitalTmp::query()->delete();


		$digitalNoti = CollectDebtDigitalNoti::query()->where('status', CollectDebtDigitalNoti::STT_DA_GUI_NOTI)
																								  ->orWhere('status', CollectDebtDigitalNoti::STT_GUI_NOTI_THAT_BAI)
																								  ->first();
		if ($digitalNoti) {
			CollectDebtDigitalNoti::query()->where('time_created', '<', $digitalNoti->time_created - $this->timeKeepNoti)->delete();
		}
	}
} // End class
