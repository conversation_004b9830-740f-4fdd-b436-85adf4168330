<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction;

use Exception;
use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Utils\CommonVar;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\GetTongNoGocDaThuCuaHopDongSubAction;

class DebtRecoveryContractGuideCheckPartnerBalanceAction
{
  public function run(DebtRecoveryContractGuideStatisticManualRequest $request)
  {
    $collectDebtShare = CollectDebtShare::query()
      ->where('contract_code', $request->json('data.contract_code'))
      ->select(['id', 'payment_guide', 'profile_data', 'contract_data'])
      ->first();

    throw_if(!$collectDebtShare, new Exception('Không tìm thấy thông tin chỉ dẫn'));


   
    $partnerBalance =  [
      [
        'label' => 'MPOS',
        'name' => 'Số dư tài khoản MPOS',
        'value' => $this->getSoDuTaiKhoanMPOS($collectDebtShare)
      ],

      [
        'label' => 'VIRTUALACCOUNT',
        'name' => 'Số dư tài khoản VIRTUAL ACCOUNT',
        'value' => $this->getSoDuTaiKhoanVA($collectDebtShare)
      ],
    ];

    
    return [
      'contract' => $collectDebtShare,
      'parner_balance' => $partnerBalance
    ];
  }

  public function getSoDuTaiKhoanMPOS(CollectDebtShare $collectDebtShare): float
  {
		if (Helper::canFakeAmountMpos($collectDebtShare->getMposMerchantId())) {
			return *********;
		}
		
    $payload = [
      'module' => CommonVar::API_REQUEST_DEBT_MODULE,
      'path' => '/partner-collect-debt-gateway/check-balance',
      'params' => [
        'partner_request_id' => '',
        'partner_merchant_id' => $collectDebtShare->getMposMerchantId(),
        'request_id' => '',
        'users_admin_id' => 'quantri',
        'payment_channel' => 'MPOS',
      ],
      'method' => 'GET'
    ];

    mylog(['Thong tin check so du' => $payload]);
    
    $checkBalanceMposResult = (new ApiCall())->callFunctionApi($payload, true);
    
    mylog(['Ket Qua Check So Du' => $checkBalanceMposResult]);

    if (empty($checkBalanceMposResult['data']['amount'])) {
      return 0;
    }

    $amount = (float) $checkBalanceMposResult['data']['amount'];

    if ($amount <= 0) {
      return 0;
    }

    return $amount;
  }

  public function getSoDuTaiKhoanVA(CollectDebtShare $collectDebtShare): float
  {

    $virtualAccountChuaXuLy = CollectDebtPartner::query()
      ->where('payment_account_id', $collectDebtShare->getVirtualAccountId())
      ->where('status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
      ->get();

    if ($virtualAccountChuaXuLy->isEmpty()) {
      return 0;
    }

    $tongSoDuKhaDung = 0;

    foreach ($virtualAccountChuaXuLy as $collectDebtPartner) {
      $tongSoDuKhaDung += $collectDebtPartner->getAmountBalance();
    }

    return $tongSoDuKhaDung;
  }
} // End classs