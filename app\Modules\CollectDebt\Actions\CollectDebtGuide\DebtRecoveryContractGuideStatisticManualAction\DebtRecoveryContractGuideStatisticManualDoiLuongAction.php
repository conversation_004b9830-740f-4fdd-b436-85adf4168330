<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction;

use App\Lib\Helper;
use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\GetReduceAndPrepayPlanAction\GetReduceAndPrepayPlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction\GetNoTheoLoaiPhaiThuHomNaySA;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction\KiemTraLenhTrichNgayVaRaCanhBaoSA;

class DebtRecoveryContractGuideStatisticManualDoiLuongAction
{

  public function run(DebtRecoveryContractGuideStatisticManualRequest $request)
  {
    $contractCode = $request->json('data.contract_code');
		mylog(['Form trich tay HD' => $contractCode]);

    $collectDebtShare = CollectDebtShare::where('contract_code', $contractCode)
                                        ->first();

    throw_if(!$collectDebtShare, new Exception('Không tìm thấy thông tin chỉ dẫn'));

    $reduceAndPrepayPlan = app(GetReduceAndPrepayPlanAction::class)->run($contractCode);
    $collectDebtShareArray = $collectDebtShare->toArray();
    $collectDebtShareArray['time_start_as_vn_date'] = $collectDebtShare->time_start_as_date->format('d/m/Y');
    $collectDebtShareArray['time_end_as_vn_date'] =  $collectDebtShare->time_end_as_date->format('d/m/Y');
    
    $tongNoGocHomNayPhaiThu = app(GetNoTheoLoaiPhaiThuHomNaySA::class)->run($contractCode, CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC);
    $tongPhiHomNayConPhaiThu = app(GetNoTheoLoaiPhaiThuHomNaySA::class)->run($contractCode, CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI);

    $returnData = [
      // hợp đồng
      'contract'                      => $collectDebtShareArray,

      // tổng phí quá hạn
      'reduce_over_due_fee'           => $reduceAndPrepayPlan['reduce_over_due_fee'],

      // phí quá hạn đã thu
      'reduce_over_due_fee_collected' => $reduceAndPrepayPlan['reduce_over_due_fee_collected'],

      // lịch thu phí chậm kỳ
      'reduce_over_period_plans'      => $reduceAndPrepayPlan['reduce_over_period_plans'],

      // tổng nợ gốc
      'total_root_debt'               => $tongNoGocHomNayPhaiThu,

      // tổng phí
      'total_fee_debt'                => $tongPhiHomNayConPhaiThu,

      // tổng nợ
      'total_debt'                    => $tongNoGocHomNayPhaiThu + $tongPhiHomNayConPhaiThu,
    ]; 

		if ($request->json('data.form_type', '') == 'GIAM_PHI') {
			mylog(['[FORM GIAM PHI]' => 'ok']);
			
			$returnData['warning_message'] = '';

			$returnData['warning_message'] = app(KiemTraLenhTrichNgayVaRaCanhBaoSA::class)->run($contractCode);

			// Update 06.06.2024
			// luong moi, so tien phi = so tien con phai thu tiep cua HD
			$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $collectDebtShare->contract_code)->first();
			throw_if(!$collectDebtSummary, new Exception('HD khong ton tai'));

			$plans = CollectDebtSchedule::query()->where('contract_code', $collectDebtShare->contract_code)
																					 ->where('status', '!=', CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH)
																					 ->get();

			mylog(['Cac lich phai thu la' => @$plans->pluck('id')->toArray()]);
			
			$tongTienConPhaiThu = 0; // So tien nay se dung de giam phi

			$tongTienGocPhaiThuTrenLich = 0;

			if ($plans->isNotEmpty()) {
				$tongTienConPhaiThu = $plans->sum(function (CollectDebtSchedule $p) {
					return $p->getSoTienConPhaiThanhToan();
				});
	
	
				$tongTienGocPhaiThuTrenLich = $plans->sum(function (CollectDebtSchedule $p) {
					return $p->isLichThuGoc();
				});
			}

			mylog([
				'Tong Tien Phai Thu Tren Lich - Se dung de giam phi' => $tongTienConPhaiThu,
				'Tong Goc Con Phai Thu' => $tongTienGocPhaiThuTrenLich
			]);
			
			// Nếu vẫn còn nợ gốc thì lấy thêm điều kiện: 
			// Tổng Số tiền phí đã thu >= Số tiền nợ gốc còn phải thu 
			if ($tongTienGocPhaiThuTrenLich > 0) {
				$tongPhiDaThu = $collectDebtSummary->total_fee_paid;
				mylog(['Tong Phi Da Thu Tren Tong Hop' => $tongPhiDaThu]);

				$expression = $tongPhiDaThu >= $tongTienGocPhaiThuTrenLich;

				mylog(['Expression tongPhiDaThu >= tongTienGocPhaiThuTrenLich' => $expression]);

				if (!$expression) {
					$returnData['warning_message'] .= sprintf(
						'<br><p>Hệ thống phát hiện vẫn còn tiền gốc phải thu (%s). Hãy đảm bảo tổng phí đã thu phải >= số tiền gốc còn phải thu</p> <br> Hệ thống sẽ tạm <b>TỪ CHỐI</b> thao tác tất toán giảm phí của bạn!!!'
					, Helper::priceFormat($tongTienGocPhaiThuTrenLich));
				}
			}

			$returnData['total_fee_debt'] = $tongTienConPhaiThu;
		} // End check giam phi
    
    return $returnData;
  }
} // End class