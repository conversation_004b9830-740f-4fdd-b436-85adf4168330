<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction;

use DB;
use Arr;
use Exception;
use App\Lib\Helper;
use App\Lib\NextlendCore;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\SubAction\XuLyTuChoiHoanPhiSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\SubAction\XuLyTuChoiHoanThuThuaSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\SubAction\XuLyTuHoanPhiThanhCongSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\SubAction\XuLyTuHoanThuThuaThanhCongSubAction;

class DebtRecoverySummaryCheckRefundAction
{
	private array $__exceptIds = [];

	public function initCheckRefund(Request $request) {
		$returnData = [];

		for ($i = 0; $i <= 20; $i++) {
			mylog(['--------------------------------------' => sprintf('%s ------------------------------', $i)]);

			try {
				$result = $this->run();
				if ($result == 'EMPTY') {
					$returnData[] = 'khong co thong tin HOAN TIEN can kiem tra';
					break;
				}

				if ($result && optional($result)->id) {
					$returnData[] = $result->only(['id', 'order_code']);
				}
			} catch (\Throwable $th) {
				mylog(['[LOI XU LY]' => Helper::traceError($th)]);
				$returnData[] = Helper::traceError($th);
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $returnData;
	}

	public function run()
	{
		$collectDebtLog = CollectDebtLog::query()
																		->whereIn('service_code', [
																			CollectDebtEnum::RL_DICH_VU_HOAN_PHI,
																			CollectDebtEnum::RL_DICH_VU_HOAN_THU_THUA,
																		])
																		->where('status', CollectDebtEnum::RL_STT_MOI_TAO);

		if (!empty($this->__exceptIds)) {
			$collectDebtLog = $collectDebtLog->whereNotIn('id', $this->__exceptIds);
		}

		$collectDebtLog = $collectDebtLog->first();

		if (!$collectDebtLog) {
			mylog(['EMPTY' => 'khong co thong tin ban ghi log']);
			return 'EMPTY';
		}

		// goi kiem tra truoc
		$nextlendCore = app(NextlendCore::class)->callRequest([
			'order_code' => $collectDebtLog->order_code,
		], 'RequestRefund_getByOrderCode', 'GET');

		$hoanTienRecord = $nextlendCore->decryptData(true);
		
		if (empty($hoanTienRecord['id'])) {
			mylog(['[LOI CHECK REFUND]' => 'khong co ban ghi hoan tien']);
			throw new Exception('khong co ban ghi hoan tien');
		}

		mylog([
			'[Ban ghi hoan tien]' => Arr::only($hoanTienRecord, ['id', 'order_code', 'service_code', 'amount', 'amount_accept', 'status'])
		]);

		$this->__exceptIds[] = $collectDebtLog->id;

		// khong thuoc cac trang thai cuoi, thi ko xu ly
		if (
			!in_array($hoanTienRecord['status'], [
				CollectDebtEnum::RL_REFUND_SYSTEM_DA_HUY,
				CollectDebtEnum::RL_REFUND_SYSTEM_DA_TU_CHOI,
				CollectDebtEnum::RL_REFUND_SYSTEM_DA_HOAN_TIEN_CHO_MC,
			])
		) {
			mylog(['Khong thuoc cac trang thai cuoi, ko xu ly' => 'ok']);
			throw new Exception(sprintf('YC hoan: `%s` khong thuoc trang thai cuoi', $collectDebtLog->order_code));
		}

		// Update len dang xu ly
		$updateLenDangXuLy = CollectDebtLog::query()
																			 ->where('id', $collectDebtLog->id)
																			 ->where('status', CollectDebtEnum::RL_STT_MOI_TAO)
																			 ->update(['status' => CollectDebtEnum::RL_STT_DANG_XU_LY]);
		if (!$updateLenDangXuLy) {
			mylog(['LOI UPDATE' => 'ko the update thanh DANG XU LY']);
			throw new Exception('ko the update thanh DANG XU LY');
		}

		$collectDebtLog = CollectDebtLog::query()->find($collectDebtLog->id);

		if ($collectDebtLog->status != CollectDebtEnum::RL_STT_DANG_XU_LY) {
			mylog(['LOI STATUS' => 'ban ghi dang ko o trang thai DANG XU LY']);
			throw new Exception('ban ghi dang ko o trang thai DANG XU LY');
		}

		$collectDebtSummary = CollectDebtSummary::query()->find($collectDebtLog->reference_id);
		mylog(['Summary xu ly' => $collectDebtSummary]);
		
		DB::beginTransaction();
		try {
			// Da bi huy hoac da bi tu choi
			if (	$hoanTienRecord['status'] == CollectDebtEnum::RL_REFUND_SYSTEM_DA_HUY  || 
					  $hoanTienRecord['status'] == CollectDebtEnum::RL_REFUND_SYSTEM_DA_TU_CHOI
			) {
				mylog(['YC hoan tien da bi huy hoac tu choi']);

				// tu choi hoan phi
				if ($collectDebtLog->service_code == CollectDebtEnum::RL_DICH_VU_HOAN_PHI) {
					$ketQuaXuLy = app(XuLyTuChoiHoanPhiSubAction::class)->run($collectDebtLog, $collectDebtSummary);
				}
				
				// tu choi thu thua
				if ($collectDebtLog->service_code == CollectDebtEnum::RL_DICH_VU_HOAN_THU_THUA) {
					$ketQuaXuLy = app(XuLyTuChoiHoanThuThuaSubAction::class)->run($collectDebtLog, $collectDebtSummary);
				}

				// update ve da xu ly that bai
				$collectDebtLog->status = CollectDebtEnum::RL_STT_DA_XU_LY_THAT_BAI;
				$collectDebtLog->description = $hoanTienRecord['description'];
				$collectDebtLog->other_data = json_encode($hoanTienRecord);
				$r = $collectDebtLog->save();

				if (!$r) {
					mylog(['Loi cap nhat ve da xu ly that bai' => $r]);
					throw new Exception('Loi cap nhat ve da xu ly that bai');
				}

				DB::commit();
				return $collectDebtLog;
			}

			// Da tao yc hoan tien cho MC
			if ($hoanTienRecord['status'] == CollectDebtEnum::RL_REFUND_SYSTEM_DA_HOAN_TIEN_CHO_MC) {
				mylog(['Da tao yc hoan tien cho MC' => 'ok']);

				if ($collectDebtLog->service_code == CollectDebtEnum::RL_DICH_VU_HOAN_PHI) {
					$ketQuaXuLy = app(XuLyTuHoanPhiThanhCongSubAction::class)->run($collectDebtLog, $collectDebtSummary, $hoanTienRecord['amount_accept']);
				}
		
				if ($collectDebtLog->service_code == CollectDebtEnum::RL_DICH_VU_HOAN_THU_THUA) {
					$ketQuaXuLy = app(XuLyTuHoanThuThuaThanhCongSubAction::class)->run($collectDebtLog, $collectDebtSummary, $hoanTienRecord['amount_accept']);
				}

				// update ve da xu ly thanh cong
				$collectDebtLog->status = CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG;
				$collectDebtLog->description = $hoanTienRecord['description'];
				$collectDebtLog->other_data = json_encode($hoanTienRecord);
				$r = $collectDebtLog->save();

				if (!$r) {
					mylog(['Loi cap nhat ve da xu ly thanh cong' => $r]);
					throw new Exception('Loi cap nhat ve da xu ly thanh cong');
				}

				DB::commit();
				return $collectDebtLog;
			}

			mylog(['Trang thai khac' => $hoanTienRecord['status']]);
			throw new Exception('Ban ghi hoan tien chua ve trang thai cuoi');
		}catch(\Throwable $th) {
			mylog(['Err' => Helper::traceError($th)]);
			DB::rollBack();

			$updateVeMoiTao = CollectDebtLog::query()->where('id', $collectDebtLog->id)
																							 ->where('status', CollectDebtEnum::RL_STT_DANG_XU_LY)
																							 ->update(['status' => CollectDebtEnum::RL_STT_MOI_TAO]);

			mylog(['cap nhat ve moi tao' => $updateVeMoiTao]);
			throw $th;
		}

		return $collectDebtLog;
	}
} // End class