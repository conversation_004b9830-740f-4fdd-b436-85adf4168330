<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Model\NextlendCompany;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\Task\DigitalNotiChamKyTask;

class EmailRemindOverdueCycleSubAction
{

  public function run($collectEvent)
  {
    $dataResponse = '';

    $buildParams = $this->buildParams($collectEvent);
    if (!empty($buildParams)) {
      $params = [
        'customer_category_care_code' => $collectEvent->category_care_code,
        'customer_service_care_code' => $collectEvent->service_care_code,
      ];
      $template = app(GetTemplateMailSubAction::class)->run($params);
      if (!empty($template)) {
        $mailContent = $template['content'];
        $mailContent = str_replace('%5', '[', $mailContent);
        $mailContent = str_replace('%5D', ']', $mailContent);
        foreach ($buildParams as $keyword => $value) {
          $mailContent = str_replace($keyword, $value, $mailContent);
        }
        $dataResponse = $mailContent;
      }
    }

    return $dataResponse;
  }

  protected function buildParams($collectEvent)
  {
    $otherData = json_decode($collectEvent->other_data, true);
    $data = json_decode($collectEvent->data, true);
    $merchant = isset($data['profile']) && isset($data['profile']['merchant']) ? $data['profile']['merchant'] : [];
    $contract = isset($data['contract']) ? $data['contract'] : [];
    $company = isset($data['company']) ? $data['company'] : [];
    $payment = isset($data['payment']) ? $data['payment'] : [];
    $summary = isset($otherData['summary']) ? $otherData['summary'] : [];
    $plans = isset($summary['other_data']) ? json_decode($summary['other_data'], true) : [];
    if (empty($merchant) && empty($contract) && empty($company) && empty($summary)) {
      return '';
    }
    $company = new NextlendCompany($company);
    $paymentVa = collect($payment)->where('other_data', '!=', '')->where('payment_method_code', 'VIRTUALACCOUNT')->map(function ($items) {
      return isset($items['other_data']) ? $items['other_data'] : '';
    })->first();
    $planOverdue = collect($plans)->where('type', 'PLAN')->map(function ($items) {
      return collect($items['data'])->filter(function ($period) {
        return isset($period['time_over_cycle']) && $period['time_over_cycle'] != 0;
      })->last();
    });
    if (empty($planOverdue->first())) {
      return '';
    }
    $feeOverdueCycle = $planOverdue->map(function ($fee) {
      return isset($fee['data']) && isset($fee['data']['fee_overdue_cycle']) ? $fee['data']['fee_overdue_cycle'] : 0;
    })->first();
    $planOverdue = $planOverdue->first();

    $businessRepresentative = isset($merchant['business_representative']) ? $merchant['business_representative'] : '';

    $timeCycle = $planOverdue['time_cycle'];
    $origin = date_create(date('Y-m-d', $timeCycle));
    $target = date_create(date('Y-m-d'));
    $interval = date_diff($origin, $target);
    $numberOverCycle = $interval->format('%a');

    $params = [
      '[hop_dong_khach_hang]' => isset($merchant['fullname']) ? $merchant['fullname'] : '',
      '[hop_dong_nguoi_dai_dien]' => $businessRepresentative,
      '[hop_dong_ma]' => isset($contract['contract_code']) ? $contract['contract_code'] : '',
      '[hop_dong_so_tien_ung]' => isset($contract['amount']) ? Helper::makeVndCurrency($contract['amount']) : '',
      '[hop_dong_so_ngay_vay]' => isset($contract['cycle']) ? $contract['cycle'] : '',
      '[hop_dong_tu_ngay]' => isset($contract['time_start']) && !empty($contract['time_start']) ? date('d/m/Y', $contract['time_start']) : '',
      '[hop_dong_den_ngay]' => isset($contract['time_end']) && !empty($contract['time_end']) ? Carbon::createFromTimestamp($contract['time_end'], 'UTC')->format('d/m/Y') : '',
      '[hop_dong_hinh_thuc_trich_no]' => isset($contract['intervals']) ? $contract['intervals'] : '',
      '[hop_dong_ngay_chu_ky_trich_no]' => isset($planOverdue['time_cycle']) && !empty($planOverdue['time_cycle']) ? date('d/m/Y', $planOverdue['time_cycle']) : '',
      '[hop_dong_so_tien_hoan]' => isset($summary['total_amount_receiver']) ? Helper::makeVndCurrency($summary['total_amount_receiver']) : '',
      '[hop_dong_so_tien_chua_hoan_ky]' => isset($planOverdue['amount']) && isset($planOverdue['amount_paid']) ? Helper::makeVndCurrency($planOverdue['amount'] - $planOverdue['amount_paid']) : '',
      '[hop_dong_so_ngay_qua_han]' => $numberOverCycle,
      '[hop_dong_phi_cham_ky]' => isset($feeOverdueCycle) ? Helper::makeVndCurrency($feeOverdueCycle) : '',
      '[hop_dong_stk_cong_ty]' => isset($company['company_bank_account_1']) ? $company['company_bank_account_1'] : '',
      '[hop_dong_ten_ngan_hang_cong_ty]' => isset($company['company_bank_name_1']) ? $company['company_bank_name_1'] : '',
      '[hop_dong_ten_chu_tk_cong_ty]' => isset($company['company_bank_holder_1']) ? $company['company_bank_holder_1'] : '',
      '[hop_dong_nd_chuyen_khoan]' => sprintf('NAP TIEN TK MC MA %s - %s', $company->getNoiDungChuyenKhoanMaNapTien(), $company->getCuPhapFullName($merchant)),
      '[hop_dong_ten_ngan_hang_va_cong_ty]' => isset($paymentVa['payment_account_bank_code']) ? $paymentVa['payment_account_bank_code'] : '',
      '[hop_dong_stk_va_cong_ty]' => isset($paymentVa['payment_account_number']) ? $paymentVa['payment_account_number'] : '',
      '[hop_dong_ten_chu_tk_va_cong_ty]' => isset($paymentVa['payment_account_name']) ? $paymentVa['payment_account_name'] : '',
      '[hop_dong_ma_qr_va]' => isset($paymentVa['qrImage']) ? $paymentVa['qrImage'] : '',
      '[hop_dong_sdt_cong_ty]' => $company->getPhoneNumber(),
      '[hop_dong_email_cong_ty]' => $company->getEmail(),
      '[hop_dong_dia_chi_cong_ty]' => $company->getAddress(),
      '[hop_dong_website_1_cong_ty]' => isset($company['company_url_1']) ? $company['company_url_1'] : '',
      '[hop_dong_website_2_cong_ty]' => isset($company['company_url_2']) ? $company['company_url_2'] : '',
      '[hop_dong_ten_cong_ty]' => isset($company['company_fullname']) ? $company['company_fullname'] : '',
      '[hop_dong_ten_ngan_cong_ty]' => isset($company['company_subname']) ? $company['company_subname'] : '',
    ];

		
    return $params;
  }
} // End class
