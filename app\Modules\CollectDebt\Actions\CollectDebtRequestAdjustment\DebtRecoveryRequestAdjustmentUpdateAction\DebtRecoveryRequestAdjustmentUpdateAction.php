<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentUpdateAction;

use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentUpdateRequest;
use Exception;

class DebtRecoveryRequestAdjustmentUpdateAction
{
  public function run(DebtRecoveryRequestAdjustmentUpdateRequest $request): CollectDebtRequestAdjustment
  {
    $collectDebtRequestAdjustment = CollectDebtRequestAdjustment::query()->find($request->json('data.id'));
    throw_if(!$collectDebtRequestAdjustment, new Exception('Yêu cầu điều chỉnh không tồn tại'));

    $params = $request->json('data');
    $collectDebtRequestAdjustment->forceFill($params)->update();
    return $collectDebtRequestAdjustment->refresh();
  }
}
