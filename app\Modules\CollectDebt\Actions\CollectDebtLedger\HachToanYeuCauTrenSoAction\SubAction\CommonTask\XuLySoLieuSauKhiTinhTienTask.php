<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummarySetByAccountingRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySetByAccounting\DebtRecoverySummarySetByAccounting;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class XuLySoLieuSauKhiTinhTienTask
{
  public $metaData = [];

  public function run(CollectDebtSchedule $plan, float $soTienMangDiThanhToan)
  {
    if ($plan->isLichThuGoc()) {
      $this->metaData[] = ['label' => CollectDebtEnum::METADATA_THU_GOC, 'value' => $soTienMangDiThanhToan];
    }  

    if ($plan->isLichThuPhiChamKy()) {
      $this->metaData[] = ['label' => CollectDebtEnum::METADATA_THANH_TOAN_PHI_CK, 'value' => $soTienMangDiThanhToan];
    }

    if ($plan->isLichThuPhiQuaHan()) {
      $this->metaData[] = ['label' => CollectDebtEnum::METADATA_THANH_TOAN_PHI_QH, 'value' => $soTienMangDiThanhToan];
    }

    $this->metaData[] = ['label' => CollectDebtEnum::METADATA_CAN_TRU_TIEN_VAO_LICH, 'value' => $soTienMangDiThanhToan];

    return $this->metaData;
  }
}
