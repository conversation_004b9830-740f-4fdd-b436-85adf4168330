<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class GetTongNoGocConPhaiThuCuaHopDongSubAction
{
  public function run(string $contractCode = ''): float
  {
    $tongNoGoc = CollectDebtSchedule::where('contract_code', $contractCode)
                                    ->where('isfee', 0)
                                    ->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
                                    ->sum('request_amount_debit');
    return $tongNoGoc;
  }
} // End class
