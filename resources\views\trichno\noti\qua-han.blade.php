<body style="margin: 0;">
	<style>
		.container { font-size: 14px; line-height: 1.8; }
		.row { display: block; margin-bottom: 10px; justify-content: space-between;  }
		.label { width: 115px; color: gray; display: inline-block; }
		.value { font-weight: 500; text-align: right;}
		.badge { display: inline-block; padding: 2px 8px; font-size: 12px; border-radius: 12px; color: white; font-weight: 500; line-height: 1; }
	  .badge-red { background-color: #e53935; }
	</style>

	<div class="container">
		<div class="row">
			<span class="label">Số tiền vay:</span>
			<span class="value">{{ \App\Lib\Helper::numberFormat($collectDebtSummary->contract_amount) }}</span>
		</div>

		<div class="row">
			<span class="label">Thời gian:</span>
			<span class="value">{{ $collectDebtSummary->getTimeEndAsDate()->format('d/m/Y') }}</span>
		</div>

		<div class="row">
			<span class="label">Mã hợp đồng:</span>
			<span class="value">{{ $collectDebtSummary->contract_code }}</span>
		</div>

		<div class="row">
			<span class="label">Thời hạn:</span>
			<span class="value">Quá hạn {{ $collectDebtSummary->number_day_overdue }} ngày</span>
		</div>

		<div class="row">
			<span class="label">Nợ gốc:</span>
			<span class="value">{{ $noGocConPhaiTra }}</span>
		</div>

		<div class="row">
			<span class="label">Phí phát sinh:</span>
			<span class="value">{{ $phiConPhaiTra }}</span>
		</div>

		<div class="row">
			<span class="label">Tình trạng:</span>
			<span class="value badge badge-red">Quá hạn</span>
		</div>
	</div>
</body>