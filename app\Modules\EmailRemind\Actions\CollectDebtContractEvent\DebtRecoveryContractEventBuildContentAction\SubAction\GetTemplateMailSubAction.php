<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction;

use App\Lib\ApiCall;
use App\Utils\CommonVar;

class GetTemplateMailSubAction
{
    public function run($request)
    {
        $params = [
            'customer_category_care_code' => $request['customer_category_care_code'],
            'customer_service_care_code' => $request['customer_service_care_code'],
        ];


        $payload = [
            'module' => CommonVar::API_CUSTOMER_MODULE,
            'path' => '/template/getByServiceAndCat',
            'params' => $params,
            'method' => 'GET'
        ];

        $result = (new ApiCall())->callFunctionApi($payload, false);

        return $result;
    }
}
