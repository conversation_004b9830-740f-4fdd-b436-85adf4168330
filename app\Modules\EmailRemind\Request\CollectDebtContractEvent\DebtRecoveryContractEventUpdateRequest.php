<?php

namespace App\Modules\EmailRemind\Request\CollectDebtContractEvent;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\EmailRemind\Rules\CollectDebtContractEvent\ValidateCustomerCategoryCareCode;
use App\Modules\EmailRemind\Rules\CollectDebtContractEvent\ValidateCustomerServiceCareCode;
use Illuminate\Validation\Rule;

class DebtRecoveryContractEventUpdateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {

    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'integer'],
      'data.category_care_code' => ['required', 'string', 'max:50', new ValidateCustomerCategoryCareCode()],
      'data.service_care_code' => ['required', 'string', 'max:50', new ValidateCustomerServiceCareCode()],
      'data.data' => ['required', 'array'],
      'data.description' => ['nullable', 'string', 'max:255'],
      'data.other_data' => ['nullable', 'array'],
      'data.time_start' => ['nullable', 'integer'],
      'data.number' => ['nullable', 'integer'],
      'data.time_expired' => ['nullable', 'integer'],
    ];
  }
} // End class
