<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\BuildParamDigitalNotiAction\SubAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;
use App\Modules\CollectDebt\Model\CollectDebtGuide;

class DigitalNotiBuildParamTatToanSA
{
	public function run(CollectDebtDigitalNoti $digitalNoti)
	{
		$collectDebtSummary = CollectDebtSummary::query()->find($digitalNoti->object_id);
		$collectDebtGuide = CollectDebtGuide::query()->firstWhere(['contract_code' => $digitalNoti->contract_code]);

		$title = sprintf('Khoản ứng %s đã được tất toán', $collectDebtSummary->contract_code);
		$body = sprintf('Bạn đã tất toán khoản vay ứng vốn thành công.%sCảm ơn bạn đã sử dụng dịch vụ.', PHP_EOL);
		$content = view('trichno.noti.tat-toan', [
			'collectDebtGuide' => $collectDebtGuide,
			'collectDebtSummary' => $collectDebtSummary
		])->render();
		
		$notifyData = CollectDebtDigitalNoti::buildNotiData(
			$title,
			$body,
			$content,
			'DIGITAL_TATTOANKHOANUNG',
			$collectDebtGuide->getMposMcId(),
			'Hợp đồng đã tất toán'
		);

		$isUpdated = $digitalNoti->where(['id' => $digitalNoti->id, 'status' => CollectDebtDigitalNoti::STT_DANG_XU_LY])
							  						 ->update([
															'status' => CollectDebtDigitalNoti::STT_DA_BUILD_PARAM,
															'digital_request' => json_encode($notifyData),
															'time_updated' => now()->timestamp
														]);
		if (!$isUpdated) {
			throw new Exception('Lỗi không build được param noti');
		}

		return $isUpdated;
	}
} // End class
