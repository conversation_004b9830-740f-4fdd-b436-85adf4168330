<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoDeXuatGiamPhiSubAction\Task;

use App\Lib\Helper;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\FillOtherTask;
use App\Modules\CollectDebt\Model\CollectDebtAction;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;

class TaoYeuCauGiamPhiTask {
  public function run(
    CollectDebtShare $collectDebtShare, 
    Collection $danhSachLichThuPhiDuocGiamTru,
    DebtRecoveryRequestCreateManualRequest $request,
    string $paymentMethodCode='IB_OFF'
  ) {
    $paymentGuide = $collectDebtShare->getPaymentGuideItem($paymentMethodCode);
    $fillOther = app(FillOtherTask::class)->run($request, $danhSachLichThuPhiDuocGiamTru);

    $param = [
      'type'                             => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
      'profile_id'                       => $collectDebtShare->profile_id,
      'contract_code'                    => $collectDebtShare->contract_code,
      'plan_ids'                         => $danhSachLichThuPhiDuocGiamTru->implode('id', ','), // Danh sách ID lịch ghi nhận giảm phí
      'payment_method_code'              => $paymentGuide['payment_method_code'],
      'payment_channel_code'             => $paymentGuide['payment_channel_code'],
      'payment_account_id'               => $paymentGuide['payment_account_id'],
      'partner_transaction_id'           => '', 
      'currency'                         => $collectDebtShare->getCurrencyShared(),
      'amount_request'                   => 0,
      'amount_receiver'                  => 0,
      'time_begin'                       => now()->timestamp,
      'time_expired'                     => now()->setTime(22, 0)->timestamp,
      'is_payment'                       => CollectDebtEnum::REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT,
      'description'                      => $request->json('data.description'),
      'other_data'                       => $fillOther,
      'plan_data'                        => Helper::getPlanCompact($danhSachLichThuPhiDuocGiamTru),
      'version'                          => CollectDebtEnum::REQUEST_VERSION_4,
      'created_by'                       => $request->json('data.created_by'),
      'time_created'                     => time(),
      'create_from'                      => CollectDebtEnum::REQUEST_LOAI_TRICH_TAY,
      'status_payment'                   => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA,
    ];

    $collectDebtRequest = CollectDebtRequest::forceCreate($param);
    $collectDebtRequest->partner_request_id = sprintf('NL%s%s', date('ymd'), $collectDebtRequest->id);
    $collectDebtRequest->save();
  
    return $collectDebtRequest;
  }
} // End class 