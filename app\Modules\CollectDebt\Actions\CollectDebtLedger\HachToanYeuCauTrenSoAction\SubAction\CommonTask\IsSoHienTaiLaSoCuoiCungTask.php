<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class IsSoHienTaiLaSoCuoiCungTask
{
	public function run(int $planId, CollectDebtLedger $ledger): bool
	{
		$collectDebtLedgers = CollectDebtLedger::query()
			->where('id', '!=', $ledger->id)
			->where('contract_code', $ledger->contract_code)
			->where('status', '!=', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
			->get();

		// khong co so nao -> so hien tai la so cuoi
		if ($collectDebtLedgers->isEmpty()) {
			return true;
		}

		// co so chua hoac dang xu ly: Kiem tra xem lich hien tai co nam tren cac so do hay khong
		foreach ($collectDebtLedgers as $lg) {

			// So bao muon thi se khong tinh
			if ($lg->isSoDoiTacBaoMuon()) {
				continue;
			}

			$planIdsHachToanTrenSo = $lg->getPlanIds();
			if (in_array($planId, $planIdsHachToanTrenSo)) {
				return false;
			}
		}

		return true;
	}
}
