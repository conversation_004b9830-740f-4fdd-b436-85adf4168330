<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebtGateway\Repositories\Connections\NextLendServiceConnection;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\DebtRecoveryPartnerCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;

class CheckRequestPaymentBatchAction
{
	private const CHUNK_LIMIT = 30;
	private const POOL_CONCURRENCY = 5;
	private const HTTP_TIMEOUT = 10;

	public NextLendServiceConnection $nextlendServiceCon;

	public function __construct(NextlendServiceConnection $nextlendServiceCon)
	{
		$this->nextlendServiceCon = $nextlendServiceCon;
	}

	public function run()
	{
		$client = $this->createHttpClient();
		$whereRaw = $this->buildWhereCondition();

		CollectDebtRequest::query()
			->with('collectDebtPartner')
			->whereRaw($whereRaw)
			->chunkById(self::CHUNK_LIMIT, function (Collection $collectDebtRequests) use ($client) {
				$this->processRequestsBatch($client, $collectDebtRequests);
			});
	}

	private function createHttpClient(): Client
	{
		return new Client([
			'base_uri' => $this->nextlendServiceCon->_API_URL,
			'timeout' => self::HTTP_TIMEOUT,
			'verify' => false
		]);
	}

	private function buildWhereCondition(): string
	{
		return sprintf(
			"payment_method_code = 'MPOS' AND (status = %s OR status = %s)
                                    AND status_payment = %s
                                   ",
			CollectDebtEnum::REQUEST_STT_MOI_TAO,
			CollectDebtEnum::REQUEST_STT_DA_DUYET,
			CollectDebtEnum::REQUEST_STT_PM_DA_GUI,
			env('THOI_GIAN_CHECK_LAI_YEU_CAU_TRICH', 30) * 60,
			time()
		);
	}

	private function processRequestsBatch(Client $client, Collection $collectDebtRequests): void
	{
		// Generator
		$requests = function () use ($collectDebtRequests) {
			foreach ($collectDebtRequests as $rq) {
				$input = app(CheckRequestViaMposSubAction::class)->buildParamsDirect($rq);
				yield $rq => $this->nextlendServiceCon->buildHttpRequest($input, 'checkDebt');
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, CollectDebtRequest $item) {
				$body = (string)$response->getBody();
				$result = json_decode($body, true);

				// Log request/response
				$this->logRequestResponse($item, $result);

				$this->handleCheckResult($item, $result);
			},
			'rejected' => function (RequestException $reason, CollectDebtRequest $item) {
				// Log failed request
				$this->logRequestResponse($item, ['error' => $reason->getMessage()]);

				echo "✗ #$item->partner_request_id failed: " . $reason->getMessage() . "\n";
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function handleCheckResult(CollectDebtRequest $collectDebtRequest, $checkDebtResult = [])
	{
		// có partner
		if ($collectDebtRequest->collectDebtPartner) {
			$this->updateTimeChecked($collectDebtRequest);
			return;
		}

		// không có kết quả check
		if (empty($checkDebtResult['RespCode']) || empty($checkDebtResult['data'])) {
			$this->updateTimeChecked($collectDebtRequest);
			return;
		}

		// Có response code
		$nextlendServiceData = json_decode($checkDebtResult['data'], true);
		$mposResultData = json_decode($nextlendServiceData['data'], true);

		// mpos trả kết quả check bị lỗi
		if (empty($mposResultData['status'])) {
			$this->updateTimeChecked($collectDebtRequest);
			return;
		}

		// Check có thông tin
		$mposDebtStatus = $mposResultData['data']['debtStatus'];

		switch ($mposDebtStatus) {
			case 'PENDING':
				$this->updateTimeChecked($collectDebtRequest);
				return;

			case 'APPROVED':
				$amountSuccess = $mposResultData['data']['debtRecoveryAmount'];
				$this->createPartnerRecord($collectDebtRequest, $amountSuccess, $mposResultData);
				break;

			case 'CANCEL':
			case 'EXPIRED':
				$amountSuccess = 0;
				$this->createPartnerRecord($collectDebtRequest, $amountSuccess, $mposResultData);
				break;

			case 'TIMEOUT':
				// sẽ call lại
				return;

			default:
				$this->updateTimeChecked($collectDebtRequest);
				return;
		}
	}

	private function updateTimeChecked(CollectDebtRequest $collectDebtRequest): void
	{
		$collectDebtRequest->forceFill(['time_checked' => time()])->update();
	}

	private function createPartnerRecord(CollectDebtRequest $collectDebtRequest, int $amountSuccess, array $mposResultData): void
	{
		try {
			$params = [
				'data' => [
					'payment_channel_code' => 'MPOS',
					'payment_method_code' => 'MPOS',
					'payment_account_id' => $collectDebtRequest->payment_account_id,
					'partner_request_id' => $collectDebtRequest->partner_request_id,
					'partner_transaction_id' => $collectDebtRequest->partner_transaction_id,
					'amount_payment' => $collectDebtRequest->amount_payment,
					'amount_receiver' => $amountSuccess,
					'fee' => 0,
					'request_exists' => 1,
					'response' => json_encode($mposResultData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
					'description' => 'Tạo bản ghi tiền về từ check payment tự động',
					'created_by' => Helper::getCronJobUser(),
					'time_created' => time(),
					'time_updated' => time(),
					'updated_by' => Helper::getCronJobUser(),
					'time_complated' => time(),
					'complated_by' => Helper::getCronJobUser(),
					'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
				]
			];

			$request = new DebtRecoveryPartnerCreateRequest();
			$request->setJson(new ParameterBag($params));

			app(DebtRecoveryPartnerCreateAction::class)->run($request);

		} catch (\Throwable $th) {
			// Log lỗi nhưng không throw để không ảnh hưởng đến việc update time_checked
			echo "✗ Error creating partner record for #{$collectDebtRequest->partner_request_id}: " . $th->getMessage() . "\n";
			$this->updateTimeChecked($collectDebtRequest);
		}
	}

	private function logRequestResponse(CollectDebtRequest $collectDebtRequest, array $responseData): void
	{
		try {
			$requestData = app(CheckRequestViaMposSubAction::class)->buildParamsDirect($collectDebtRequest);

			Log::info("[checkDebt--->$collectDebtRequest->partner_request_id]", [
				'req--->' => $requestData,
				'res--->' => $responseData,
			]);
		} catch (\Throwable $th) {
			// Không throw lỗi để không ảnh hưởng đến flow chính
			echo "✗ Error logging for #{$collectDebtRequest->partner_request_id}: " . $th->getMessage() . "\n";
		}
	}
}  // End class