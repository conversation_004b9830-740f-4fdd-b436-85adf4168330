<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayMposSubAction;

use App\Lib\TelegramAlert;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayMposSubAction\TatToanMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayMposSubAction\TrichMotPhanMposSubAction;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class TaoYeuCauTrichTayMposSubAction
{
  public function run(
    CollectDebtShare $collectDebtShare,
    Collection $danhSachToanBoLichThu,
    DebtRecoveryRequestCreateManualRequest $request
  ) {
    return app(TrichMotPhanMposSubAction::class)->run(
      $collectDebtShare,
      $danhSachToanBoLichThu,
      $request
    );
  } // End method
} // End class