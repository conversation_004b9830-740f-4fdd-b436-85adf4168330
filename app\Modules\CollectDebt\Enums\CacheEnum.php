<?php

namespace App\Modules\CollectDebt\Enums;

use Illuminate\Support\Facades\Redis;

class CacheEnum
{
	const LIST_HOP_DONG_DUNG_JOB = 'LIST_HOP_DONG_DUNG_JOB';
	const LIST_CAU_HINH_FIRE_JOB = 'LIST_CAU_HINH_FIRE_JOB';

	const NLV4_REDIS_LIST_LENH_TRICH_CAN_CHECK = 'NLV4_REDIS_LIST_LENH_TRICH_CAN_CHECK';
	const NLV4_REDIS_LIST_CUTOFF = 'NLV4_REDIS_LIST_CUTOFF';

	const NLV4_REDIS_TRICH_NGAY_REQUEST = 'NLV4_REDIS_TRICH_NGAY_REQUEST';

/*--------------------------- Logic redis cutofff ---------------------------*/
	public static function putToListCutOff(string $partnerRequestId, int $timeExpired)
	{
		$key = CacheEnum::NLV4_REDIS_LIST_CUTOFF;

		// Nhét các lệnh trích vào với score chính là timeExpired
		Redis::zadd($key, [$partnerRequestId => $timeExpired]);

		// Set expired đến cuối ngày
		$expiredAt = now()->endOfDay()->timestamp;

		$ttl = $expiredAt - time();

		if ($ttl > 0) {
			Redis::expire($key, $ttl);
		}
	}

	public static function removeFromListCutOff(string $partnerRequestId)
	{
		$key = CacheEnum::NLV4_REDIS_LIST_CUTOFF;
		Redis::zrem($key, $partnerRequestId);
	}
/*--------------------------- Logic redis check lệnh trích tự động ---------------------------*/
	public static function putRequestToCheck($requestId)
	{
		$key = CacheEnum::NLV4_REDIS_LIST_LENH_TRICH_CAN_CHECK;

		$nextCheckTime = now()->addMinutes(30)->timestamp;

		Redis::zadd($key, [$requestId => $nextCheckTime]);

		// Set expired đến 23h15 hôm nay
		$expiredAt = now()->setTime(23, 15)->timestamp;

		$ttl = $expiredAt - time();

		if ($ttl > 0) {
			Redis::expire($key, $ttl);
		}
	}

	public static function removeCheckedRequest($requestId)
	{
		$key = CacheEnum::NLV4_REDIS_LIST_LENH_TRICH_CAN_CHECK;
		Redis::zrem($key, $requestId);
	}
/*--------------------------- Logic redis cảnh báo trích ngay ---------------------------*/
	public static function putToListTrichNgay(string $partnerRequestId, int $timeExpired)
	{
		$key = CacheEnum::NLV4_REDIS_TRICH_NGAY_REQUEST;

		// Nhét các lệnh trích vào với score chính là timeExpired
		Redis::zadd($key, [$partnerRequestId => $timeExpired]);

		// Set expired đến cuối ngày
		$expiredAt = $timeExpired + 2*3600; // list này tồn tại trong 2h của time_expired

		$ttl = $expiredAt - time();

		if ($ttl > 0) {
			Redis::expire($key, $ttl);
		}
	}

	public static function removeFromListTrichNgay(string $partnerRequestId)
	{
		$key = CacheEnum::NLV4_REDIS_TRICH_NGAY_REQUEST;
		Redis::zrem($key, $partnerRequestId);
	}
} // End class