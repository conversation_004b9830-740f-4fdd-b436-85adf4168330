<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanCreateRqRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\DebtRecoveryContractCreateRequestViaWalletAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\TaoYeuCauTuDongMposTuLichThuDoiLuongSubAction;

class CollectDebtScheduleRequestController extends Controller
{
	public function createRequest(DebtRecoveryContractPlanCreateRqRequest $request)
	{
		try {
			$yeuCauDaTao = app(TaoYeuCauTuDongMposTuLichThuDoiLuongSubAction::class)->initTaoLenhTuDong();
			return $this->successResponse($yeuCauDaTao, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}


	public function createRequestViaWallet(Request $request)
	{
		try {
			$collectDentSummary = app(DebtRecoveryContractCreateRequestViaWalletAction::class)->run($request);
			return $this->successResponse(['collect_debt_summary_id' => $collectDentSummary->id], $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
