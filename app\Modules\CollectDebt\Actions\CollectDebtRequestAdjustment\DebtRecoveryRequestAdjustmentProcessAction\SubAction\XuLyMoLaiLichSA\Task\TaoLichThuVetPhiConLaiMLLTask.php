<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use Exception;

class TaoLichThuVetPhiConLaiMLLTask
{
  public function run(CollectDebtSchedule $lichDangMoLai, float $soPhiConPhaiThuTiep = 0, CollectDebtShare $collectDebtShare)
  {
    // Trong này other_data phải thể hiện rõ, số tiền phí cần thu tiếp là phí nào
    if ($lichDangMoLai->isLichThuPhiChamKy()) {
      $paramThuPhi = [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_CHAM_KY,
          'fee_config' => $lichDangMoLai->getCauHinhPhiShared(CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY, $collectDebtShare),
          'request_created_channel' => '',
        ],
        'note' => 'Thu tiếp phí chậm kỳ - MLL'
      ];
    }

    if ($lichDangMoLai->isLichThuPhiQuaHan()) {
      $paramThuPhi = [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_QUA_HAN,
          'fee_config' => $lichDangMoLai->getCauHinhPhiShared(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN, $collectDebtShare),
          'request_created_channel' => '',
        ],
        'note' => 'Thu tiếp phí quá hạn - MLL'
      ];
    }

    $otherData = json_encode([
      $paramThuPhi,
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

    if ($soPhiConPhaiThuTiep > 0) {
      $scheduleParam = [
        'profile_id'           => $lichDangMoLai->profile_id,
        'contract_code'        => $lichDangMoLai->contract_code,
        'contract_type'        => $lichDangMoLai->contract_type,
        'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
        'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
        'debit_begin'          => $soPhiConPhaiThuTiep,
        'debit_end'            => 0,
        'rundate'              => date('Ymd'),
        'time_start'           => now()->timestamp,
        'time_end'             => now()->endOfDay()->timestamp,
        'amount_period_debit'  => $lichDangMoLai->amount_period_debit,
        'request_amount_debit' => $soPhiConPhaiThuTiep,
        'success_amount_debit' => 0,
        'other_data'           => $otherData,
        'description'          => $lichDangMoLai->description,
        'is_settlement'        => $lichDangMoLai->is_settlement,
        'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
        'created_by'           => $lichDangMoLai->created_by,
        'time_created'         => time(),
        'cycle_number'         => $lichDangMoLai->cycle_number,
        'master_id'            => $lichDangMoLai->master_id,
      ];

      $lichThuVetPhi = CollectDebtSchedule::forceCreate($scheduleParam);

			if (!$lichThuVetPhi) {
				mylog(['Loi tao lich thu vet phi con lai' => $lichThuVetPhi]);
				throw new Exception('Loi tao lich thu vet phi con lai');
			}

			return $lichThuVetPhi;
    }
  }
} // End class