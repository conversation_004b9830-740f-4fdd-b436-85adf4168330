<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction;


use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\BuildParamTaoYeuCauTuDongTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\ThucHienTaoLenhTrichTuDongTask;
use App\Modules\CollectDebt\Enums\CacheEnum;

class TaoYeuCauTuDongMposTuLichThuDoiLuongSubAction
{
	private array $__listCodeCanNe = [];

	private array $__yeuCauDaTao = [];

	public function initTaoLenhTuDong()
	{
		// Lấy ra các HĐ cần né
		$this->getListHopDongCanNe();

		for ($i = 1; $i <= 30; $i++) {
			try {
				$result = $this->run();

				// Truy van ma he thong tra ra "EMPTY", thi break luon
				if ($result == 'EMPTY') {
					$this->__yeuCauDaTao[] = 'EMPTY';
					break;
				}

				if ($result && $result->id) {
					$yc = [
						'request_id' => $result->id,
						'contract_code' => $result->contract_code,
						'plan_ids' => $result->plan_ids
					];

					$this->__yeuCauDaTao[] = $yc;
				}else {	
					dump('N/A');
				}
				
			} catch (\Throwable $th) {
				mylog(['Loi xu ly tao lich tu dong' => Helper::traceError($th)]);
				
				@TelegramAlert::sendCreateRequest('Loi xu ly tao lich tu dong: ' . Helper::traceError($th));
				continue;
			} finally {
				//sleep(1);
			}
		}

		return $this->__yeuCauDaTao;
	}

	public function run()
	{
		// 1. Chỉ lấy ra các lịch thu chưa xử lý
		$plan = CollectDebtPlan::query()
													->whereDoesntHave('collectDebtProcessing')
													->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
													->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
													->where('rundate', '<=', now()->format('Ymd'));

		if (!empty($this->__listCodeCanNe)) {
			$plan = $plan->whereNotIn('contract_code', $this->__listCodeCanNe);
		}

		$plan = $plan->orderByRaw('rundate ASC, cycle_number ASC, type ASC, is_settlement ASC')
		->select(["id", "contract_code", "rundate"])
		->first();

		if (!$plan) {
			return 'EMPTY';
		}

	
		mylog(['lich thu dang xu ly la' => $plan]);

		// 2. Lịch thu mà dính mảng né thì bắn lỗi
		if (in_array($plan->contract_code, $this->__listCodeCanNe)) {
			$msg = sprintf('[TaoYeuCauTuDong][%s] Vi phạm mảng né HĐ dữ liệu', $plan->contract_code);
			Log::info($msg);
			throw new Exception('Loi HD da co lich thu xu ly');
		}

		// 3. Update lịch thu của HĐ hiện tại và các lịch thu cùng rundate thành `ĐANG XỬ LÝ`
		$updatedPlanRow = CollectDebtSchedule::query()
			->where('contract_code', $plan->contract_code)
			->where('rundate', $plan->rundate)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->update([
				'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY,
				'time_updated' => now()->timestamp
			]);

		if (!$updatedPlanRow) {
			mylog(['update current row' => $updatedPlanRow]);
			throw new Exception('update current row');
		}

		// Loại trừ hợp đồng này để không select vào nữa
		$this->__listCodeCanNe[] = $plan->contract_code;

		$listLichThuRefresh = CollectDebtSchedule::query()
																						 ->join('debt_recovery_share', 'debt_recovery_share.contract_code', '=', 'debt_recovery_contract_plan.contract_code')
																						 ->where('debt_recovery_contract_plan.contract_code', $plan->contract_code)
																						 ->where('debt_recovery_contract_plan.rundate', $plan->rundate)
																						 ->where('debt_recovery_contract_plan.is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
																						 ->where('debt_recovery_contract_plan.status', CollectDebtEnum::SCHEDULE_STT_MOI)
																						 ->select([
																								'debt_recovery_contract_plan.*',
																								// Share
																								'debt_recovery_share.partner_code', 
																								'debt_recovery_share.payment_guide'
																							])
																						 ->get();

																	 
		$isToanBoLichLaDangXuLy = $listLichThuRefresh->every(function (CollectDebtSchedule $p) {
			return $p->is_process == CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY;
		});

		// Tất cả các lịch thu của HĐ mà có cùng rundate phải có is_process là ĐANG XỬ LÝ thì mới cho tiếp tục
		throw_if(!$isToanBoLichLaDangXuLy, new Exception('Toan bo lich chua ve dang xu ly'));

		$listLichThuTaoYeuCau = $listLichThuRefresh;
		$listLichThuTaoYeuCau = app(PlanSortableCollectionByRule::class)->sortCollection($listLichThuTaoYeuCau);
		
		// 5. Start transaction
		DB::beginTransaction();
		try {
			
			$buildParamTaoYeuCau = app(BuildParamTaoYeuCauTuDongTask::class)->run($listLichThuTaoYeuCau);
				
			$collectDebtRequest = app(ThucHienTaoLenhTrichTuDongTask::class)->run($buildParamTaoYeuCau, $listLichThuTaoYeuCau);
			
			$collectDebtProcessing = CollectDebtProcessing::query()->forceCreate([
				'contract_code' => $collectDebtRequest->contract_code,
				'created_at' => now(),
				'updated_at' => now()
			]);

			if (!$collectDebtProcessing) {
				throw new Exception('Loi khong tao duoc ban ghi processing...');
			}
			
			$this->__listCodeCanNe[] = $collectDebtRequest->contract_code;

			DB::commit();

			CacheEnum::putToListCutOff($collectDebtRequest->partner_request_id, $collectDebtRequest->time_expired);
			return $collectDebtRequest;
		} catch (\Throwable $th) {
			DB::rollBack();
			mylog(['loi tao yc' => Helper::traceError($th)]);

			// Update toàn bộ lịch thu cần tạo yc về is_process: CHƯA XỬ LÝ
			$updated = CollectDebtSchedule::query()
																		->where('contract_code', $plan->contract_code)
																		->whereIn('id', $listLichThuTaoYeuCau->pluck('id')->toArray())
																		->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
																		->update([
																			'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
																		]);
			
			if (!$updated) {
				$this->__listCodeCanNe[] = $plan->contract_code;
				throw new Exception('Loi rollback trang thai, can sua tay');
			}															
		}
	}

	public function getListHopDongCanNe(): array
	{
		$listHopDongDungJob = CollectDebtConfigAuto::getHopDongDungJobCache();
		$this->__listCodeCanNe = $listHopDongDungJob;
		return $this->__listCodeCanNe;
	}
} // End class
