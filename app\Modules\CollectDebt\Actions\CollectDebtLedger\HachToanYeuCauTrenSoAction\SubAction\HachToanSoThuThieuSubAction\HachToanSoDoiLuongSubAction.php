<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\HachToanSoThuThieuSubAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\CapNhatSoLieuVaoSoTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\IsSoHienTaiLaSoCuoiCungTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\XuLySoLieuSauKhiTinhTienTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\EmitEventSauKhiLamLaiLichTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\TinhTienSoTienDaThuVaoLichTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\IsToanBoYeuCauDaVeTrangThaiCuoiTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\CapNhatYeuCauTrenSoLaDaHoanThanhTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\XuLyLenhTatToanGiamPhiTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\XuLyThuThieuLamLaiLichTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\DailyCollect\DailyXuLyThuThieuLamLaiLichTask;

class HachToanSoDoiLuongSubAction
{
  public float $soTienHachToan = 0;

  public array $thuThua = [];

  public array $summaryLedgerData = [];

  public array $lichIds = [];

  public bool $isCacYeuCauDaVeTrangThaiCuoi = false;

  public bool $isSoCuoiCung = false;

  public array $canTru = [];

  public function run(CollectDebtLedger $ledger)
  {
    $planIds = $ledger->getPlanIds();

		mylog([
			'So duoc hach toan, id' => $ledger->id,
			'Yeu cau duoc hach toan: ' => $ledger->request_id,
			'Plan-Ids' => $planIds
		]);

    foreach ($planIds as $p) {
      $this->lichIds[$p] = 'ok';
    }
    
    $this->soTienHachToan = $ledger->amount;

		$yeuCauDangDuocHachToan = $ledger->collectDebtRequest;

		/* ----------- La trich tay giam phi ----------- */
		if ($yeuCauDangDuocHachToan->isTrichTayGiamPhi()) {
			$yeuCauGiamPhiMetaData = app(XuLyLenhTatToanGiamPhiTask::class)->run($yeuCauDangDuocHachToan, $ledger->schedules);
			
			// Xu ly meta data
			if (!empty($yeuCauGiamPhiMetaData['meta_data'])) {
				$this->summaryLedgerData = array_merge($this->summaryLedgerData, $yeuCauGiamPhiMetaData['meta_data']);
			}

			// Gach lich luon
			if (!empty($yeuCauGiamPhiMetaData['request_reduce_fee'])) {
				$ledger->schedules->map(function (CollectDebtSchedule $plan) {
					$otherData = $plan->getPlanOtherData();
					$otherData[] = [
						'type' => 'REDUCE_FEE',
						'time_modified' => time(),
						'data' => [
							'total_reduce_fee' => $plan->getSoTienConPhaiThanhToan(),
						],
						'note' => 'giảm phí'
					];

					
					$updatedYeuCauGiamPhi = CollectDebtSchedule::where('id', $plan->id)
																											->update([
																												'other_data' => json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
																											]);
					if (!$updatedYeuCauGiamPhi) {
						mylog(['[LOI YC GIAM PHI]' => 'Loi khong cap nhat duoc other_data']);
						throw new Exception('Loi khong cap nhat duoc other_data yc giam phi');
					}

					$this->summaryLedgerData[] = ['label' => CollectDebtEnum::METADATA_LICH_THUC_HIEN_GACH, 'value' => $plan->id];
					
					return $plan;
				});
			}
		}

		// khong phai trich tay giam phi
		if (!$yeuCauDangDuocHachToan->isTrichTayGiamPhi()) {
			$ledger->schedules->map(function (CollectDebtSchedule $plan) use ($ledger, $yeuCauDangDuocHachToan) {
				unset($this->lichIds[$plan->id]);
			/* ----------- Xử lý cái khác ----------- */
				mylog([
					'Id yeu cau' => $yeuCauDangDuocHachToan->id,
					'Ma yeu cau' => $yeuCauDangDuocHachToan->partner_request_id
				]);

				$this->isCacYeuCauDaVeTrangThaiCuoi = app(IsToanBoYeuCauDaVeTrangThaiCuoiTask::class)->run($plan);
				$this->isSoCuoiCung = app(IsSoHienTaiLaSoCuoiCungTask::class)->run($plan->id, $ledger);
			
				$soTienPhaiThanhToanTrenLich = $plan->getSoTienConPhaiThanhToan();

				mylog([
					'ID lich dang xu ly: ' => $plan->id,
					'Is Cac yeu cau da ve trang thai cuoi: ' => $this->isCacYeuCauDaVeTrangThaiCuoi,
					'Is So Cuoi Cung' => $this->isSoCuoiCung,
					'So tien con phai thanh toan tren lich' => $soTienPhaiThanhToanTrenLich
				]);

				/**
				 * B1: Lịch đã đc gạch trước đó rồi. => cần return để ko xử lý lịch đó nữa
				 * B2: Nếu như ko còn lịch trên sổ mà vẫn dư tiền thì đánh dấu thu thừa luôn => ghi nhận thu thừa
				 */
				if ($soTienPhaiThanhToanTrenLich == 0) {
					if ($this->isKhongConLichThuTrenSo() && $this->soTienHachToan > 0) {
						$this->thuThua[] = [
							'amount_excess' => $this->soTienHachToan,
							'payment_method_code' => $yeuCauDangDuocHachToan->payment_method_code,
							'line' => 137,
						];
					}

					return $plan;
				}

				
				if ($this->isCacYeuCauDaVeTrangThaiCuoi && $this->isSoCuoiCung) {

					$this->summaryLedgerData[] = ['label' => CollectDebtEnum::METADATA_LICH_THUC_HIEN_GACH, 'value' => $plan->id];
					
					/**
					 * Case đối tác báo trích tiền thành công nhưng muộn. Ví dụ:
					 * 1. lúc 22h là cutoff, NL sẽ đánh dấu là yêu cầu thu 0đ
					 * 2. quá trình sau đó vẫn hạch toán như bình thường
					 * 3. lúc 23h, MPOS báo đã thu được tiền -> số tiền đó vẫn ghi sổ, hạch toán. Khi
					 * hạch toán phải được cho vào quy trình thu thừa
					 */
					if ($plan->isTrangThaiCuoi() && $this->isKhongConLichThuTrenSo()) {
						$this->thuThua[] = [
							'amount_excess' => $this->soTienHachToan,
							'payment_method_code' => $yeuCauDangDuocHachToan->payment_method_code,
							'line' => 93,
							'plan' => $plan->id,
						];

						return $plan;
					}

					// Thực hiện cộng tiền success_amount_debit
					if ($soTienPhaiThanhToanTrenLich > 0) {
						$soTienMangDiThanhToan = min($soTienPhaiThanhToanTrenLich, $this->soTienHachToan);
						
						$result = app(XuLySoLieuSauKhiTinhTienTask::class)->run($plan, $soTienMangDiThanhToan);
							
						$this->summaryLedgerData = array_merge($this->summaryLedgerData, $result);

						$planSauKhiTinhTien = app(TinhTienSoTienDaThuVaoLichTask::class)->run(
							$plan->id, 
							$soTienMangDiThanhToan,
							$yeuCauDangDuocHachToan
						);

						$this->soTienHachToan -= $soTienMangDiThanhToan;

						$this->canTru[] = [
							'plan_id' => $plan->id,
							'amount_offset_debt' => $soTienMangDiThanhToan
						];

						$soTienConPhaiThanhToanSauTinhTien = $planSauKhiTinhTien->getSoTienConPhaiThanhToan();
					
					
						// Sổ cuối rồi mà vẫn chưa thu hết => sinh phí
						if ($soTienConPhaiThanhToanSauTinhTien > 0 && !$plan->isTrangThaiCuoi()) {
							if ($plan->isHopDongTrichKy()) {
								$result = app(XuLyThuThieuLamLaiLichTask::class)->run(
									$planSauKhiTinhTien, 
									$planSauKhiTinhTien->success_amount_debit,
									$yeuCauDangDuocHachToan
								);
							}
							
							if ($plan->isHopDongTrichNgay()) {
								$result = app(DailyXuLyThuThieuLamLaiLichTask::class)->run(
									$planSauKhiTinhTien, 
									$planSauKhiTinhTien->success_amount_debit,
									$yeuCauDangDuocHachToan
								);
							}

							$this->summaryLedgerData = array_merge($this->summaryLedgerData, $result);
						} 

						
						// Tịnh tiến xong rồi đã THU ĐỦ TIỀN hoặc thừa tiền
						if ($soTienConPhaiThanhToanSauTinhTien == 0) {
							// Gạch và kill luôn lịch hiện tại
							$this->summaryLedgerData[] = ['label' => CollectDebtEnum::METADATA_LICH_THUC_HIEN_GACH, 'value' => $plan->id];

							if ($this->soTienHachToan > 0 && $this->isKhongConLichThuTrenSo()) {
								$this->thuThua[] = [
									'amount_excess' => $this->soTienHachToan,
									'payment_method_code' => $yeuCauDangDuocHachToan->payment_method_code,
									'line' => 146,
								];
							}
						}

					}
				} else {
					/**
					 * Các yêu cầu chưa về trạng thái cuối cùng
					 * Lúc này chỉ tịnh tiến số tiền đã trích của lịch mà thôi
					 **/
					if ($soTienPhaiThanhToanTrenLich > 0) {
						$soTienMangDiThanhToan = min($soTienPhaiThanhToanTrenLich, $this->soTienHachToan);
						
						$result = app(XuLySoLieuSauKhiTinhTienTask::class)->run($plan, $soTienMangDiThanhToan);

						$this->summaryLedgerData = array_merge($this->summaryLedgerData, $result);

						$planSauKhiTinhTien = app(TinhTienSoTienDaThuVaoLichTask::class)->run(
							$plan->id, 
							$soTienMangDiThanhToan,
							$yeuCauDangDuocHachToan
						);

						$this->soTienHachToan -= $soTienMangDiThanhToan;

						$this->canTru[] = [
							'plan_id' => $plan->id,
							'amount_offset_debt' => $soTienMangDiThanhToan
						];

						// Xử lý thông tin sau khi tịnh tiến, nếu đã cấn trừ hết tiền mà số tiền vẫn còn và không còn lịch trên sổ -> tính la thu thừa
						if ($planSauKhiTinhTien->getSoTienConPhaiThanhToan() == 0) {
							// Gạch và kill luôn lịch hiện tại
							$this->summaryLedgerData[] = ['label' => CollectDebtEnum::METADATA_LICH_THUC_HIEN_GACH, 'value' => $plan->id];

							if ($this->soTienHachToan > 0 && $this->isKhongConLichThuTrenSo()) {
								$this->thuThua[] = [
									'amount_excess' => $this->soTienHachToan,
									'payment_method_code' => $yeuCauDangDuocHachToan->payment_method_code,
									'line' => 190,
								];
							}
						}
					}
				}
			}); // End Loop plan
		}

    $thuThuaOtherData = [];

    if (!empty($this->thuThua)) {
      $thuThuaOtherData = [
        'type' => 'EXCESS',
        'time_modified' => time(),
        'data' => $this->thuThua,
        'note' => 'Ghi nhận số tiền thu thừa'
      ];
    }

    // Phát sự kiện bằng cách đánh dấu status
    $emitEventParamUpdate = app(EmitEventSauKhiLamLaiLichTask::class)->run(
      $this->isCacYeuCauDaVeTrangThaiCuoi,
      $this->isSoCuoiCung,
      $this->summaryLedgerData
    );

    // Cập nhât số liệu vào sổ & đánh dấu là làm lại lịch thành công
    $capNhatSoLieuVaoSo = app(CapNhatSoLieuVaoSoTask::class)->run(
      $ledger, 
      $this->summaryLedgerData,
      $emitEventParamUpdate,
      $thuThuaOtherData,
      $this->canTru
    );

    // Cập nhật yêu cầu là đã hoàn thành <do đã hạch toán yêu cầu trên sổ xong>
    $capNhatYeuCauTrenSo = app(CapNhatYeuCauTrenSoLaDaHoanThanhTask::class)->run(
			$ledger, 
			$this->summaryLedgerData
		);

    return $ledger;
  }


  public function isKhongConLichThuTrenSo(): bool {
    return empty($this->lichIds);
  }
} // End class