<?php

namespace App\Modules\CollectDebtGateway\Inter;

interface MposCollectDebtGatewayInterface
{
    /**
     * @param array $data
     * @return array
     */
    public function sendCollectDebtCommand(array $data);

    /**
     * @param array $data
     * @return array
     */
    public function cancelCollectDebtCommand(array $data);

    /**
     * @param array $data
     * @return array
     */
    public function checkCollectDebtCommandOnPartner(array $data);

    /**
     * @param array $data
     * @return array
     */
    public function checkBalanceMerchant(array $data);

    /**
     * @param array $data
     * @return array
     */
    public function notifyContractFinish(array $data);

     /**
     * @param array $data
     * @return array
     */
    public function reCheckCollectDebtCommandOnPartner(array $data);
}
