<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\DebtRecoveryLedgerAccountingSummaryV2Action;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\SubAction\GetLedgerNeedToAccountingSummarySA;

class HachToanTongHopAction
{
	private array $__exceptLedgerIds = [];

	public function run()
	{
		for ($i = 1; $i <= 30; $i++) {
			try {
				$collectDebtLedger = app(GetLedgerNeedToAccountingSummarySA::class)->run($this->__exceptLedgerIds);

				if ( !$collectDebtLedger ) {
					mylog(['[EMPTY]' => 'khong co thong tin so lam tong hop']);
					break;
				}

				mylog(['ID so thuc hien hach toan' => @$collectDebtLedger->only(['id', 'contract_code'])]);

				$collectDebtLedger = app(DebtRecoveryLedgerAccountingSummaryV2Action::class)->run($collectDebtLedger);
				
			} catch (\Throwable $th) {
				mylog([
					'Loi Hach Toan Cho So' => Helper::traceError($th),
					'ID So' => $collectDebtLedger->id
				]);

				continue;
			}
		}

		// return cac so ids da xu ly
		return $this->__exceptLedgerIds;
	}
} // End class
