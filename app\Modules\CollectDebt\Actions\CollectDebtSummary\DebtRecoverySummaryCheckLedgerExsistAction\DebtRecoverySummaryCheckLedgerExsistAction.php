<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareGetContractCodeAction\DebtRecoveryShareGetContractCodeAction;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DebtRecoverySummaryCheckLedgerExsistAction
{
	const KY_THU_BAT_DAU_TU_KY = 1;

	public function run(Request $request)
	{
		$collectDebtSummary = CollectDebtSummary::query();

		// Bấm đồng bộ tay
		if (!empty($request->json('data.contract_code'))) {
			$collectDebtSummary = $collectDebtSummary->where('contract_code', $request->json('data.contract_code'))
																							 ->limit(1)
																							 ->get();
		}

		// job đồng bộ
		if (empty($request->json('data.contract_code'))) {
			$collectDebtSummary =  $collectDebtSummary->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
																								->where(function ($query) {
																									$query->whereJsonDoesntContain('other_data', [
																										[ 'type' => 'CHECK_DAILY' ]
																									])->orWhereJsonContains('other_data', [
																										[
																											'type' => 'CHECK_DAILY',
																											'rundate' => now()->subDay()->format('Ymd')
																										]
																									]);
																								})
																							  ->limit(1)
																								->get();
		}
			

		throw_if($collectDebtSummary->isEmpty(), new Exception("Không tìm thấy bản ghi cần đồng bộ"));

		$collectDebtSummary = $collectDebtSummary->first();
		$otherData = $collectDebtSummary->getSummaryOtherData();

		$checkDailyTypeSummary = $collectDebtSummary->getSummaryOtherDataItem('CHECK_DAILY');

		// có key CHECK_DAILY
		if (!empty($checkDailyTypeSummary)) {
			$checkDailyTypeIndex = $collectDebtSummary->getSummaryOtherDataIndex('CHECK_DAILY');

			$otherData[$checkDailyTypeIndex] = [
				'type' => 'CHECK_DAILY',
				'data' => [],
				'time_modified' => time(),
				'note' => 'Kiểm tra đồng bộ hằng ngày',
				'rundate' => date('Ymd')
			];
		}

		// chưa đồng bộ
		if (empty($checkDailyTypeSummary)) {
			$otherData[] = [
				'type' => 'CHECK_DAILY',
				'data' => [],
				'time_modified' => time(),
				'note' => 'Kiểm tra đồng bộ hằng ngày',
				'rundate' => date('Ymd')
			];
		}

		$collectDebtSummary->update(['other_data' => json_encode($otherData, JSON_UNESCAPED_UNICODE)]);
		$collectDebtSummary = $collectDebtSummary->refresh();

		// Tạo event
		$dataShare = $this->__getDataShare($collectDebtSummary);
		$summaryDataSync = $this->__caculateTime($collectDebtSummary);
		$summaryMerge = array_merge($collectDebtSummary->toArray(), $summaryDataSync);
		$this->__createEvent($dataShare, $summaryMerge);

		return [];
	}

	protected function __getDataShare($collectDebtSummary)
	{
		$inputs = [
			'contract_code' => $collectDebtSummary->contract_code
		];
		return app(DebtRecoveryShareGetContractCodeAction::class)->run($inputs);
	}


	protected function __setDataEvent($dataShare)
	{
		$data = [];

		if ($dataShare) {
			if (isset($dataShare['company_data']) && $dataShare['company_data']) {
				$data['company'] = json_decode($dataShare['company_data'], true);
			}
			if (isset($dataShare['contract_data']) && $dataShare['contract_data']) {
				$data['contract'] = json_decode($dataShare['contract_data'], true);
			}
			if (isset($dataShare['profile_data']) && $dataShare['profile_data']) {
				$data['profile'] = json_decode($dataShare['profile_data'], true);
			}
			if (isset($dataShare['payment_guide']) && $dataShare['payment_guide']) {
				$data['payment'] = json_decode($dataShare['payment_guide'], true);
			}
			if (isset($dataShare['list_fee']) && $dataShare['list_fee']) {
				$data['list_fee'] = json_decode($dataShare['list_fee'], true);
			}
		}

		return $data;
	}

	protected function __createEvent(array $dataShare, array $dataSummary)
	{

		$inputs = [
			'category_care_code' => 'CONTRACT',
			'service_care_code' => 'SYNC',
			'data' => $this->__setDataEvent($dataShare),
			'description' => 'Tạo Event Đồng bộ hàng ngày',
			'other_data' => [
				'summary' => $dataSummary,
			],
			'time_start' => time(),
            'contract_code' => $dataSummary['contract_code'],
		];
		$create = (new \App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateAction\DebtRecoveryContractEventCreateAction())->run($inputs);
		if (isset($create['id']) && $create['id']) {
			return true;
		}
		return false;
	}

	/**
	 * Tính toán kỳ tiếp theo là kỳ số mấy
	 *
	 * @param [type] $dataSummary
	 * @return void
	 */
	public function __caculateTime(CollectDebtSummary $collectDebtSummary): array
	{
		$contractData = $collectDebtSummary->getContractData();
		$timeEndAsDate = Carbon::createFromTimestamp($contractData['time_end']);

		if ($collectDebtSummary->isHopDongDaTatToan() || $collectDebtSummary->isDaQuaHanHopDong()) {
			return [
				'number_day_status_debt' => 0,
				'next_payment_period' => 0,
			];
		}

		
		// HĐ trích ngày
		if ($collectDebtSummary->isHopDongSummaryTrichNgay()) {
			$chenhLechGiuaNgayTatToanVaNgayHienTai = $timeEndAsDate->diffInDays(now());

			$returnData = [
				'number_day_status_debt' => $chenhLechGiuaNgayTatToanVaNgayHienTai,
				'next_payment_period' => 0, // HĐ trích ngày thì truyền cái giá trị kỳ thứ mấy = 0;
			];
		}

		
		// HĐ trích kỳ
		if ($collectDebtSummary->isHopDongSummaryTrichKy()) {
			$plans = CollectDebtSchedule::query()
																	->where('contract_code', $collectDebtSummary->contract_code)
																	->where('type', CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH)
																	->where('isfee', CollectDebtEnum::METADATA_THU_GOC)
																	->get();

			$rangeDate = [];
			foreach ($plans as $index => $p) {
				$rangeDate[] = [
					'start' => $p->time_start_as_date,
					'end' => $plans->get($index+1) ? $plans->get($index+1)->time_start_as_date->endOfDay() : now()->addDay()->endOfDay(),
					'cycle_number' => $p['cycle_number']
				];
			}

			$kyTiepTheo = 1;

			foreach ($rangeDate as $range) {
				// Vào if, thì sẽ lấy đc kỳ đang thu của thời điểm hiện tại
				if (now()->gt($range['start']) && now()->lt($range['end'])) {
					$kyTiepTheo = self::KY_THU_BAT_DAU_TU_KY +  $range['cycle_number'] + 1;
					break;
				}
			}

			
			$planTiepTheo = $plans->first(function (CollectDebtSchedule $plan) use ($kyTiepTheo) {
				return $plan->cycle_number == $kyTiepTheo-1;
			});

			$chenhLechNgayHienTaiVaKyTiepTheo = $planTiepTheo->time_start_as_date->endOfDay()->diffInDays(now());

			if ($planTiepTheo->rundate_as_date->isSameDay(now())) {
				$chenhLechNgayHienTaiVaKyTiepTheo = 1;
			}

			$returnData = [
				'number_day_status_debt' => $chenhLechNgayHienTaiVaKyTiepTheo,
				'next_payment_period' => $kyTiepTheo,
			];
		}

		
		return $returnData;
	}
} // End class
