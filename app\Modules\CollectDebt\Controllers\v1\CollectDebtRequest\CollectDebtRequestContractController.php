<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\CollectDebtRequestResourceCollection;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestForContractAction\StatisticRequestContractAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestReduceFeeAction\DebtRecoveryRequestReduceFeeAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSearchDataAction\DebtRecoveryRequestSearchDataAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestReduceFeeAction\DebtRecoveryRequestReduceFeeSearchDataAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetByContractCodeAction\DebtRecoveryContractGuideGetByContractCodeAction;


class CollectDebtRequestContractController extends Controller
{
	public function getByContract(Request $request)
	{
		try {
			$collectDebtRequests = app(DebtRecoveryRequestSearchDataAction::class)->run($request);
			$collectDebtRequestResource = new CollectDebtRequestResourceCollection($collectDebtRequests);
			$response = $collectDebtRequestResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function statisticContract(Request $request)
	{
		try {
			$statistic = app(StatisticRequestContractAction::class)->run($request->json('data.contract_code'));
			return $this->successResponse($statistic, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryRequestReduceFee(Request $request)
	{
		try {
			$collectDebtRequests = app(DebtRecoveryRequestReduceFeeAction::class)->run($request);
			return $this->successResponse([
				'rows' => $collectDebtRequests->count(),
				'data' => $collectDebtRequests->toArray()
			], $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryRequestReduceFeeSearchData(Request $request)
	{
		try {
			$collectDebtRequests = app(DebtRecoveryRequestReduceFeeSearchDataAction::class)->run($request);
			$collectDebtRequestResource = new CollectDebtRequestResourceCollection($collectDebtRequests);
			$response = $collectDebtRequestResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
