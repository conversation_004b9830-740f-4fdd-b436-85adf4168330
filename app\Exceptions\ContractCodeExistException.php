<?php

namespace App\Exceptions;

use Exception;
use App\Traits\ApiResponser;

class ContractCodeExistException extends Exception
{
	use ApiResponser;

	public $errors;
	public $errorCode;
	public $message;

	public function __construct(array $errors, $errorCode = 422, $message = '')
	{
		parent::__construct($message, $errorCode);
		$this->errors = $errors;
		$this->errorCode = $errorCode;
		$this->message = $message;
	}

	public function render()
	{
		return $this->errorResponse(409, $this->message, $this->errors);
	}
}
