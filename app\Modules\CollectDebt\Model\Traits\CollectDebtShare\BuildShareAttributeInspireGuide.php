<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtShare;

use Carbon\Carbon;

trait BuildShareAttributeInspireGuide
{
  public $contractData = null;

  public function getShareContractData() {
    if (!empty($this->contractData)) {
      return $this->contractData;
    }

    $this->contractData = json_decode($this->contract_data, true);
    return $this->contractData;
  }

/* ------------------------ Bắt đầu tại đây ------------------------ */
  public function getProfileIdAttribute()
  {
    $profileDataAsArray = $this->getProfileDataAsArray();
    return $profileDataAsArray['id'];
  }


  public function getContractTypeAttribute()
  {
    $contractData = $this->getShareContractData();
    return $contractData['type'];
  }

  public function getContractCycleAttribute()
  {
    $contractData = $this->getShareContractData();
    return $contractData['cycle'];
  }

  public function getContractIntervalsAttribute()
  {
    $contractData = $this->getShareContractData();
    return $contractData['intervals'];
  }

  public function getContractTimeStartAttribute()
  {
    $contractData = $this->getShareContractData();
    return Carbon::createFromFormat('d-m-Y H:i:s',$contractData['time_start_as_date'])->timestamp;
  }

  public function getContractTimeEndAttribute()
  {
    $contractData = $this->getShareContractData();
    return Carbon::createFromFormat('d-m-Y H:i:s',$contractData['time_end_as_date'])->timestamp;
  }

  public function getAmountAttribute()
  {
    $contractData = $this->getShareContractData();
    return $contractData['amount'];
  }

  public function getStatusAttribute()
  {
    return 5;
  }

  public function getTimeStartAsDateAttribute()
  {
    $contractData = $this->getShareContractData();
    return Carbon::createFromFormat('d-m-Y H:i:s',$contractData['time_start_as_date']);
  }

  public function getTimeStartAsVnDateAttribute()
  {
    $contractData = $this->getShareContractData();
    return Carbon::createFromFormat('d-m-Y H:i:s',$contractData['time_start_as_date'])->format('d/m/Y');
  }

  public function getTimeEndAsDateAttribute()
  {
    $contractData = $this->getShareContractData();
    return Carbon::createFromFormat('d-m-Y H:i:s',$contractData['time_end_as_date']);
  }

  public function getTimeEndAsVnDateAttribute()
  {
    $contractData = $this->getShareContractData();
    return Carbon::createFromFormat('d-m-Y H:i:s',$contractData['time_end_as_date'])->format('d/m/Y');
  }
} // End class