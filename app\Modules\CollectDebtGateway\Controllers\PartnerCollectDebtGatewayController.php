<?php
namespace App\Modules\CollectDebtGateway\Controllers;

use App\Modules\CollectDebtGateway\Requests\PartnerDebtGateway\CancelDebtRequest;
use App\Modules\CollectDebtGateway\Requests\PartnerDebtGateway\CheckBalanceRequest;
use App\Modules\CollectDebtGateway\Requests\PartnerDebtGateway\CheckDebtRequest;
use App\Modules\CollectDebtGateway\Requests\PartnerDebtGateway\NotifyContractFinishRequest;
use App\Modules\CollectDebtGateway\Requests\PartnerDebtGateway\SendDebtRequest;
use App\Modules\CollectDebtGateway\Service\PartnerCollectDebtGatewayService;


class PartnerCollectDebtGatewayController extends Controller
{
    protected PartnerCollectDebtGatewayService $partnerService;

    public function __construct(PartnerCollectDebtGatewayService $partnerService)
    {
        $this->partnerService = $partnerService;
    }

    public function sendDebt(SendDebtRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->partnerService->sendDebt($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

    public function checkDebt(CheckDebtRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->partnerService->checkDebt($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

    public function checkBalance(CheckBalanceRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->partnerService->checkBalance($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

    public function cancelDebt(CancelDebtRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->partnerService->cancelDebt($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

    public function notifyContractFinish(NotifyContractFinishRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->partnerService->notifyContractFinish($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

    public function reCheckDebt(CheckDebtRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->partnerService->reCheckDebt($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }
}