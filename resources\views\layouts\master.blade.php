<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>@yield('title', 'NextLend - Test Trích Nợ')</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="{{ config('app.url') . '/css/all.min.css' }}">
  <!-- Theme style -->
  <link rel="stylesheet" href="{{ config('app.url') . '/css/adminlte.min.css' }}">
  <link rel="stylesheet" href="{{ config('app.url') . '/css/style.css' }}?v={{time()}}">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.10.0/css/bootstrap-datepicker.min.css" integrity="sha512-34s5cpvaNG3BknEWSuOncX28vz97bRI59UnVtEEpFX536A7BtZSJHsDyFoCl8S7Dt2TPzcrCEoHBGeM4SUBDBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>

<body class="layout-top-nav layout-navbar-fixed">
  <div class="wrapper">
    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light navbar-fixed">
      <!-- Left navbar links -->
      <ul class="navbar-nav">
        <li class="nav-item d-none d-sm-inline-block">
          @php($url = config('app.url') . '/mock/create-guide')
              <a href="{{ $url }}" 
                  class="nav-link  @if(request()->fullUrl() == $url) active font-weight-bold @endif">Tạo chỉ dẫn thu hồi</a>
        </li>

        <li class="nav-item d-none d-sm-inline-block">
          @php($url = config('app.url') . '/mock/congno')
              <a href="{{ $url }}" 
                class="nav-link @if(request()->fullUrl() == $url) active font-weight-bold @endif">Giả lập công nợ</a>
        </li>
      </ul>
    </nav>
    <!-- /.navbar -->
    
    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
      <!-- Content Header (Page header) -->
      <div class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-12">
              <h1 class="m-0">@yield('page_name', 'Tạo chỉ dẫn thu hồi nhanh')</h1>
            </div><!-- /.col -->
          </div><!-- /.row -->
        </div><!-- /.container-fluid -->
      </div>
      <!-- /.content-header -->
  
      @yield('content')
      <!-- /.content -->
    </div>
    <!-- /.content-wrapper -->
  </div>

  <!-- jQuery -->
  <script src="{{ config('app.url') . '/js/jquery.min.js' }}"></script>
  <!-- Bootstrap 4 -->
  <script src="{{ config('app.url') . '/js/bootstrap.bundle.min.js' }}"></script>
  <!-- AdminLTE App -->
  <script src="{{ config('app.url') . '/js/adminlte.min.js' }}"></script>
  <!-- AdminLTE for demo purposes -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.10.0/js/bootstrap-datepicker.min.js" integrity="sha512-LsnSViqQyaXpD4mBBdRYeP6sRwJiJveh2ZIbW41EBrNmKxgr/LFZIiWT6yr+nycvhvauz8c2nYMhrP80YhG7Cw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script>
    $(document).ready(function () {
      $('.datepicker').datepicker();
    })
  </script>
  @stack('js_bot')
<script>
  function formatNumberCurrency(event) {
      const amountInput = event.target;
      const value = amountInput.value;

      // Loại bỏ tất cả các ký tự không phải là chữ số và dấu phẩy
      const cleanedValue = value.replace(/[^0-9,]/g, "");

      // Loại bỏ dấu phẩy trong chuỗi trừ dấu phẩy đầu tiên (nếu có)
      const normalizedValue = cleanedValue.replace(/,(?!$)/g, "");

      // Định dạng số tiền với dấu phẩy
      const formattedValue = normalizedValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

      amountInput.value = formattedValue;
  }
</script>
</body>

</html>