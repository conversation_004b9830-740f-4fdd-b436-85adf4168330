<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;

class Merchant extends Model
{
  /**
   * [▼
      "id" => 418
      "cid" => ""
      "sync" => ""
      "email" => "<EMAIL>"
      "phone" => ""
      "mobile" => "84912345678"
      "source" => 1
      "status" => 2
      "address" => "Phuc DOng, Long Bien, Ha Noi"
      "city_id" => 11871
      "sale_id" => ""
      "address1" => ""
      "address2" => ""
      "checksum" => "968f70f0a2d21d3dfcf8eb7017c2ce95"
      "fullname" => "Test MC Huyen"
      "latitude" => ""
      "passport" => "0181818187173333"
      "amount_to" => ""
      "issued_by" => "Ha Noi Viet Nam"
      "longitude" => ""
      "detail_fee" => ""
      "issue_date" => "01-08-2023"
      "loan_money" => 10000000
      "partner_id" => 1
      "profile_id" => 2307288832380494
      "sale_email" => ""
      "short_name" => "MC HUYEN"
      "amount_from" => ""
      "description" => ""
      "district_id" => 11937
      "status_care" => 1
      "time_locked" => ""
      "wait_active" => 2
      "belong_group" => 1
      "detail_trans" => ""
      "evaluate_max" => ""
      "evaluate_min" => ""
      "time_actived" => ""
      "time_created" => "1692583824"
      "time_deleted" => ""
      "time_updated" => "1696473057"
      "status_action" => 1
      "time_handover" => ""
      "time_unlocked" => ""
      "advance_number" => 1
      "certificate_id" => "01063934633422"
      "converted_data" => ""
      "number_updated" => ""
      "operation_year" => "2018"
      "sale_region_id" => ""
      "sync_mynextpay" => 1
      "users_admin_id" => 4
      "average_revenue" => ""
      "avg_month_count" => 12
      "avg_trans_value" => 12
      "condition_valid" => 2
      "avg_trans_number" => 123213
      "belong_group_new" => ""
      "business_address" => ""
      "business_type_id" => 1
      "evaluate_to_time" => ""
      "evaluation_month" => ""
      "business_areas_id" => 4
      "time_set_profiled" => ""
      "evaluate_from_time" => ""
      "locked_admin_user_id" => ""
      "partner_payment_code" => ""
      "time_partner_actived" => "1643907599"
      "actived_admin_user_id" => ""
      "created_admin_user_id" => ""
      "deleted_user_admin_id" => ""
      "partner_merchant_code" => "50324747"
      "status_create_profile" => ""
      "updated_admin_user_id" => ""
      "contract_user_admin_id" => ""
      "unlocked_admin_user_id" => ""
      "business_representative" => "Huyen Trần Tên Doanh Nghiệp"
      "contract_past_number_day" => ""
      "time_updated_status_action" => ""
      "transaction_count_per_month" => ""
    ]
   */
  protected $guarded = [];  

  public function getAddress(): string {
    if (!empty($this->address)) {
      return $this->address;
    }

    if (!empty($this->address1)) {
      return $this->address1;
    }

    if (!empty($this->address2)) {
      return $this->address2;
    }

    return '';
  }

  public function getBorrowerName(): string {
    return $this->borrower['fullname'] ?? 'N/A';
  }
} // End class
