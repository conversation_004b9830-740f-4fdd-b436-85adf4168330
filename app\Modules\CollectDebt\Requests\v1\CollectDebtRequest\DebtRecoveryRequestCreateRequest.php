<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Rules\CollectDebtRequest\ExplodeItemIsIntegerRule;

class DebtRecoveryRequestCreateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.type'                        => ['required', Rule::in([CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO])],
      'data.profile_id'                  => ['required', 'numeric', 'min:1'],
      'data.contract_code'               => [
        'required', 
        'string', 
        'max:50',
        Rule::exists('debt_recovery_contract_guide', 'contract_code')
      ],
      'data.plan_ids'                    => ['required', 'string', 'max:255', new ExplodeItemIsIntegerRule()],
      'data.payment_method_code'         => ['required', 'string', 'max:50'],
      'data.payment_channel_code'        => ['required', 'string', 'max:50'],
      'data.payment_account_id'          => ['required', 'string', 'max:50'],
      'data.payment_account_holder_name' => ['required', 'string', 'max:255'],
      'data.payment_account_bank_code'   => ['required', 'string', 'max:50'],
      'data.payment_account_bank_branch' => ['required', 'string', 'max:255'],
      'data.partner_request_id'          => ['nullable', 'string', 'max:50', 'unique:debt_recovery_request,partner_request_id'],
      'data.partner_transaction_id'      => ['nullable', 'string', 'max:50'],
      'data.time_begin'                  => ['required', 'numeric', 'min:0'],
      'data.time_expired'                => ['required', 'numeric', 'gt:data.time_begin'],
      'data.is_payment'                  => [
        'required', 
        'integer',
        Rule::in([
          CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT, 
          CollectDebtEnum::REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT, 
        ])
      ],
      'data.version'                     => ['required'],
      'data.status'                      => [
        'required',
        Rule::in(array_keys(CollectDebtRequest::listingStatus()))
      ],

      'data.status_payment' => [
        'required',
        Rule::in(array_keys(CollectDebtRequest::listingStatusPayment()))
      ],

      'data.status_recored' => [
        'required',
        Rule::in(array_keys(CollectDebtRequest::listingStatusRecorded()))
      ],

      'data.currency'                    => ['required', 'string', 'max:3', Rule::in(['VND', 'USD'])],
      'data.amount_request'              => ['required', 'numeric', 'min:1'],
      'data.amount_payment'              => ['required', 'numeric', 'min:0'],
      'data.amount_receiver'             => ['required', 'numeric', 'min:0'],
      'data.fee'                         => ['required', 'numeric', 'min:0'],
      'data.description'                 => ['required', 'string', 'max:255'],
      'data.created_by'                  => ['required'],
      'data.time_created'                => ['required'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['time_created'] = time();
    $params['data']['version'] = 3;

    $this->merge($params);
  }
} // End class
