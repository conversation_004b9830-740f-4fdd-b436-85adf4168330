<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtGuide;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;

trait GuideStatusable
{
  public function isGuideMoiTao(): bool
  {
    return $this->status == CollectDebtEnum::GUIDE_STT_MOI_TAO;
  }

  public function isGuideDaDuyet(): bool
  {
    return $this->status == CollectDebtEnum::GUIDE_STT_DA_DUYET;
  }

  public function isGuideDangXuLy(): bool
  {
    return $this->status == CollectDebtEnum::GUIDE_STT_DANG_TAO_LICH;
  }

  public function isGuideDaTuChoi(): bool
  {
    return $this->status == CollectDebtEnum::GUIDE_STT_DA_TU_CHOI;
  }

  public function isGuideDaTaoLichThanhCong(): bool
  {
    return $this->status == CollectDebtEnum::GUIDE_STT_DA_TAO_LICH_THANH_CONG;
  }

  public function canCapNhatChiDan(): bool {
    return $this->isGuideMoiTao() || $this->isGuideDaDuyet() || $this->isGuideDangXuLy();
  }

  public function listingStatus(): array
  {
    return [
      CollectDebtEnum::GUIDE_STT_MOI_TAO => 'Chưa duyệt',
      CollectDebtEnum::GUIDE_STT_DA_DUYET => 'Đã duyệt',
      CollectDebtEnum::GUIDE_STT_DANG_TAO_LICH => 'Đang tạo lịch',
      CollectDebtEnum::GUIDE_STT_DA_TU_CHOI => 'Đã từ chối',
      CollectDebtEnum::GUIDE_STT_DA_TAO_LICH_THANH_CONG => 'Đã tạo lịch',
    ];
  }

  public function getStatusText(): string
  {
    $statusText = $this->listingStatus()[$this->status] ?? 'Không xác định';
    return mb_strtoupper($statusText);
  }
} // End class