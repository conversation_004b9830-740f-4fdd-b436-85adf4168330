<?php

namespace App\Modules\CollectDebt\Model;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class CollectDebtSetting extends Model
{
  const RUNNING = 1;
  const STOPED = 2;

  const TYPE_ON_OFF = 2;
  const TYPE_VALUE = 1;

  protected $table = 'debt_recovery_setting';
  public $timestamps = false;

  protected $appends = ['daily_run_as_array'];

  public function getDailyRunAsArrayAttribute() {
    return explode(',', $this->daily_run);
  }

  public static function isEnableSetting(string $jobName=''): bool {
    $setting = self::where('code', $jobName)->first();
    return self::checkEnableSetting($setting);
  }

  public static function isEnableGopLichCungRunDate(): bool {
    $setting = self::where('code', 'GOP_LICH_CUNG_RUNDATE_CUA_HD')->first();
    return self::checkEnableSetting($setting);
  }

  public static function findSetting(string $code=''): self {
    $setting = self::where('code', $code)->first();
    return $setting;
  }

  
  public static function checkEnableSetting($collectDebtSetting): bool {
    if (!$collectDebtSetting) {
      return false;
    }

    if ( config('app.env') == 'local' ) {
      return true;
    }

    if ($collectDebtSetting->status == self::STOPED) {
      return false;
    }

    $shortNameToday = now()->format('D');
    if (!in_array($shortNameToday, $collectDebtSetting->daily_run_as_array)) {
      return false;
    }

    $flag = true;

    if (!empty($collectDebtSetting->time_start)) {
      $timeStart = today()->setTimeFromTimeString($collectDebtSetting->time_start);
      $flag = now()->gte($timeStart);
    }

    if (!$flag) {
      return false;
    }

    if (!empty($collectDebtSetting->time_end)) {
      $timeEnd = today()->setTimeFromTimeString($collectDebtSetting->time_end);
      $flag = now()->lte($timeEnd);
    }
  
    return $flag;
  }

	public static function isEnableRuleCanTruGocTruocPhiSau(): bool {
		$setting = self::where('code', 'RULES_CAN_TRU_TRICH_NO')->first();
		if (!$setting || $setting->value == 'GOC_TRUOC_PHI_SAU') {
			return true;
		}
		
		return false;
	}
} // End class
