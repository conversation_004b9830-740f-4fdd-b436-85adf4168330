<?php

namespace App\Modules\CollectDebt\Observers;

use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCreateAction\DebtRecoverySummaryCreateByGuideAction;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class CollectDebtRequestObserver
{
  public function saved(CollectDebtRequest $collectDebtRequest)
  {
    // <PERSON>hi hủy yêu cầu phải reset kênh thu trên lịch
    if ($collectDebtRequest->isCanceled()) {
      $planIds = $collectDebtRequest->getPlanIds();
      $plans = CollectDebtSchedule::whereIn('id', $planIds)->get();
      $plans->map(function (CollectDebtSchedule $plan) {
        $plan->other_data = $plan->resetKenhThu();
        $plan->save();
        return $plan;
      });
    }
  }
} // End class
