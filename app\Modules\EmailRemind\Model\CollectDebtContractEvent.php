<?php

namespace App\Modules\EmailRemind\Model;

use Exception;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

class CollectDebtContractEvent extends Model
{
	protected $table 	 = 'debt_recovery_contract_event';
	public $timestamps = false;
	protected $guarded = [];

	public function getEventData(): array {
		return json_decode($this->data, true);
	}

	public function getEventOtherData(): array {
		return json_decode($this->other_data, true);
	}

	public function getEmailKhachVay(): array {
		$eventData = $this->getEventData();
		$merchant = $eventData['profile']['merchant'] ?? [];

		if (empty($merchant)) {
			mylog(['Khong lay duoc thong tin merchant trong event' => $eventData]);
			throw new Exception('Khong lay duoc thong tin merchant trong event');
		} 

		$listTo = [
			[
				'name' => $merchant['fullname'],
				'identifier_account' => $merchant['email'],
				'identifier_data' => ''
			]
		];

		mylog(['Khach nhan mail la' => $listTo]);

		return $listTo;
	}

	public function getNguoiDaiDien(string $key='business_representative'): string {
		$eventData = $this->getEventData();
		$merchant = $eventData['profile']['merchant'] ?? [];

		if (empty($merchant)) {
			mylog(['Khong lay duoc thong tin merchant trong event' => $eventData]);
			throw new Exception('Khong lay duoc thong tin merchant trong event');
		} 

		return Str::of($merchant[$key])->upper()->__toString();
	}

	public function getMposMcId() {
		$data = json_decode($this->data, true);
		return $data['profile']['merchant']['partner_merchant_code'];
	}
} // End class