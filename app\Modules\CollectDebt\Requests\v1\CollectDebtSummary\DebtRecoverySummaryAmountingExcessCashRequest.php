<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummaryAmountingExcessCashRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.action' => ['required', 'string', Rule::in(['REFUND', 'DEBT'])],
      'data.contract_code' => ['required', 'string', 'max:50'],
      'data.id' => ['nullable', 'numeric'],
      'data.amount' => ['required', 'integer', 'min:1'],
      'data.user_request_id' => ['required', 'string', 'max:255'],
      'data.description' => ['nullable', 'string', 'max:255'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['admin_user_id'] = $params['data']['user_request_id']['id'];
    $params['data']['user_request_id'] = StandardizedDataFilter::getUserAdminStructCompact($params['data']['user_request_id']);
    
    $this->merge($params);
  }

  public function isRefundAction(): bool
  {
    return $this->json('data.action') == 'REFUND';
  }
} // End class
