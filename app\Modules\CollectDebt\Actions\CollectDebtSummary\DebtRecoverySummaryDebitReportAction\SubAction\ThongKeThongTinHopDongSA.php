<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;

class ThongKeThongTinHopDongSA
{
	/**
	 * Tr<PERSON> về các thông tin của HĐ
	 * 		Mã hđ
	 * 		Nguồn MC
	 * 		MC name
	 * 		MC email
	 *
	 * @param CollectDebtSummary $collectDebtSummary
	 * @return array
	 * array:3 [
	 */
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$profileData = $collectDebtSummary->collectDebtShare->getProfileDataAsArray();
		
		return [
			'contract_code' => $collectDebtSummary->contract_code,
			'partner' => 'MPOS',
			'merchant_id'		=> $profileData['merchant']['id'],
			'merchant_name' => $profileData['merchant']['fullname'],
			'merchant_email' => $profileData['merchant']['email'],
		];
	}
}
