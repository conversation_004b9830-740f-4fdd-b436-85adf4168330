<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerGetByContractCodeAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoveryPartnerGetByContractCodeAction
{
  public function run(Request $request)
  {
    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $request->json('data.contract_code'))->first();

    $contractCode = $request->json('data.contract_code');
    $partners = CollectDebtPartner::query()
                                  ->where('contract_code', $contractCode);
    
    $paymentMethodCode = $request->json('data.filter.payment_method_code');
    if ( !empty($paymentMethodCode) ) {
      $partners = $partners->where('payment_method_code', $paymentMethodCode);
    }

    $partnerRequestTransactionId = $request->json('data.filter.partner_request_transaction_id');
    
    if ( !empty($partnerRequestTransactionId) ) {
      $partners = $partners->where(function ($query) use ($partnerRequestTransactionId) {
        return $query->where('partner_request_id', $partnerRequestTransactionId)
                     ->orWhere('partner_transaction_id', $partnerRequestTransactionId);
      });
    }

    $status = $request->json('data.filter.status');

    if ( !empty($status) ) {
      $partners = $partners->where('status', $status);
    }

    $paymentAccountId = $request->json('data.filter.payment_account_id');

    if ( !empty($paymentAccountId) ) {
      $partners = $partners->where('payment_account_id', $paymentAccountId);
    }

		$fromDate = $request->json('data.filter.from_date');
		if (!empty($fromDate)) {
			$fromDateTimestamp = Carbon::createFromFormat('d-m-Y', $fromDate)->startOfDay()->timestamp;
			$partners = $partners->where('time_created', '>=', $fromDateTimestamp);
		}

		$toDate = $request->json('data.filter.to_date');
		if (!empty($toDate)) {
			$toDateTimestamp = Carbon::createFromFormat('d-m-Y', $toDate)->endOfDay()->timestamp;
			$partners = $partners->where('time_created', '<=', $toDateTimestamp);
		}

		$isCongNoBaoMuon = $request->json('data.filter.is_cong_no_bao_muon', false);
		if (!empty($isCongNoBaoMuon)) {
			$partners = $partners->where('description', 'LIKE', 'TRICH_MUON%');
		}

    $partners = $partners->orderByRaw($request->json('sortBy', 'id DESC'))
                        ->select($request->json('data.fields', ['*']))
                        ->paginate(
                        $request->json('data.limit', 10),
                        ['*'],
                        'page',
                        $request->json('data.page', 1)
                      );

    $partnersCollection = $partners->getCollection()->map(function (CollectDebtPartner $partner) use ($collectDebtSummary) {
      $partner->currency = $collectDebtSummary->getCurrency();
      return $partner;
    });

    $partners->setCollection($partnersCollection);

    return $partners;
  }
}
