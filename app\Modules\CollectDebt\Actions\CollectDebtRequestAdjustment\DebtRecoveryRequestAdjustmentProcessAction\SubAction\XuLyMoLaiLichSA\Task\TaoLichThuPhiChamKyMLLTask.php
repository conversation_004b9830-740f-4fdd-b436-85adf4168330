<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use Exception;

class TaoLichThuPhiChamKyMLLTask
{
  public function run(CollectDebtSchedule $lichDangMoLai, float $phiChamKy = 0, CollectDebtShare $collectDebtShare): CollectDebtSchedule
  {
    $scheduleParam = [
      'profile_id'           => $lichDangMoLai->profile_id,
      'contract_code'        => $lichDangMoLai->contract_code,
      'contract_type'        => $lichDangMoLai->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
      'debit_begin'          => $phiChamKy,
      'debit_end'            => 0,
      'rundate'              => now()->format('Ymd'),
      'time_start'           => now()->timestamp,
      'time_end'             => now()->endOfDay()->timestamp,
      'amount_period_debit'  => $lichDangMoLai->amount_period_debit,
      'request_amount_debit' => $phiChamKy,
      'success_amount_debit' => 0,
      'other_data'           => json_encode([
        [
          'type' => 'OTHER',
          'time_modified' => time(),
          'data' => [
            'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_CHAM_KY,
            'fee_config' => $lichDangMoLai->getCauHinhPhiShared(CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY, $collectDebtShare),
            'request_created_channel' => '',
          ],
          'note' => 'Sinh phí chậm kỳ'
        ],
      ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_UNICODE),
      'description'          => $lichDangMoLai->description,
      'is_settlement'        => $lichDangMoLai->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangMoLai->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangMoLai->cycle_number,
      'master_id'            => $lichDangMoLai->master_id,
    ];

    $lichThuPhiChamKy = CollectDebtSchedule::forceCreate($scheduleParam);
		
		if (!$lichThuPhiChamKy) {
			mylog(['Loi tao lich thu phi ck' => $lichThuPhiChamKy]);
			throw new Exception('Loi tao lich thu phi cham ky');
		}
		
		return $lichThuPhiChamKy;
  }
}
