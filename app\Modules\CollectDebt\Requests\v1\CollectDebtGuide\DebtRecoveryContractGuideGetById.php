<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtGuide;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryContractGuideGetById extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:0'],
      'user_request_id' => ['nullable']
    ];
  }
} // End class
