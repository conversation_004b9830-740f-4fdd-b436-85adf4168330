<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtLedger;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Rules\CollectDebtRequest\ExplodeItemIsIntegerRule;

class DebtRecoveryLedgerCreateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    $ledgerListStatus = CollectDebtLedger::listStatus();
    
    return [
      'data'               => ['required', 'array'],

      'data.profile_id'    => [
        'required', 
        'numeric', 
      ],

      'data.contract_code' => [
        'required', 
        'string', 
        'max:50',
      ],

      'data.plan_ids' => ['required', 'string', 'max:300', new ExplodeItemIsIntegerRule()],

      'data.request_id' => [
        'required', 
        'numeric', 
        'min:1',
      ],

      'data.currency'       => ['required', 'string', 'max:3'],
      'data.amount'        => ['required', 'numeric', 'min:0'],
      'data.description'   => ['nullable', 'string', 'max:255'],
      'data.status'        => ['required', Rule::in(array_keys($ledgerListStatus))],
      'data.time_record'   => ['required', 'numeric'],
      'data.created_by'    => ['required', 'string', 'max:255'],
      'data.time_created'  => ['required'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['time_created'] = time();
    
    if ( empty($params['data']['time_record']) ) {
      $params['data']['time_record'] = time();
    }

    $this->merge($params);
  }

  public function messages() {
    return [
      'data.profile_id.exists' => 'ProfileID không tồn tại trong hệ'
    ];
  }
} // End class
