<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\SubAction;

use DB;
use Exception;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class XuLyTuHoanPhiThanhCongSubAction
{
	public function run(CollectDebtLog $collectDebtLog, CollectDebtSummary $collectDebtSummary, $soTienDaThucHienHoan=0)
	{
		$soTienYeuCauHoan = $collectDebtLog->getSoTienYeuCauHoan();
		$phiCkMuonHoan = $collectDebtLog->getPhiChamKyMuonHoan();
		$phiQhMuonHoan = $collectDebtLog->getPhiQuaHanMuonHoan();

		mylog([
			'soTienYeuCauHoan' => $soTienYeuCauHoan,
			'phiCkMuonHoan' => $phiCkMuonHoan,
			'phiQhMuonHoan' => $phiQhMuonHoan,
		]);

		$otherData = $collectDebtSummary->getSummaryOtherData();
		$otherData[] = [
			'type' => 'REFUND',
			'data' => [
				'soTienDaThucHienHoan' => $soTienDaThucHienHoan
			],
			'time_modified' => now()->timestamp,
			'note' => 'Hoàn phí thành công'
		];

		// Hoàn tiền 100%
		if ($soTienDaThucHienHoan == $soTienYeuCauHoan) {
			$r = $collectDebtSummary->update([
				'amount_refunding' => DB::raw('amount_refunding - ' . $soTienYeuCauHoan),
				'total_amount_refund' => DB::raw('total_amount_refund + ' . $soTienDaThucHienHoan),
				'fee_overdue_cycle_refund' => DB::raw('fee_overdue_cycle_refund + ' . $phiCkMuonHoan),
				'fee_overdue_refund' => DB::raw('fee_overdue_refund + ' . $phiQhMuonHoan),
				'other_data' => json_encode($otherData),
				'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO
			]);

			if (!$r) {
				mylog([
					'LOI CAP NHAT REFUNDING' => 'ok',
					'CASE' => 'Hoan tien 100%'
				]);
				throw new Exception('LOI CAP NHAT REFUNDINF');
			}

			return $collectDebtSummary;
		}

		if ($soTienDaThucHienHoan < $soTienYeuCauHoan) {
			$soTienCanTru = $soTienDaThucHienHoan;

			$phiChamKyHoanThucTe = 0;
			$phiQuaHanHoanThucTe = 0;

			if ($phiCkMuonHoan > 0 && $soTienCanTru > 0) {
				$phiChamKyHoanThucTe = min($phiCkMuonHoan, $soTienCanTru);
				$soTienCanTru = $soTienCanTru - $phiChamKyHoanThucTe;
			}

			if ($phiQhMuonHoan > 0 && $soTienCanTru > 0) {
				$phiQuaHanHoanThucTe = min($phiQhMuonHoan, $soTienCanTru);
			}


			$r = $collectDebtSummary->update([
				'amount_refunding' => DB::raw('amount_refunding - ' . $soTienYeuCauHoan),
				'total_amount_refund' => DB::raw('total_amount_refund + ' . $soTienDaThucHienHoan),
				'fee_overdue_cycle_refund' => DB::raw('fee_overdue_cycle_refund + ' . $phiChamKyHoanThucTe),
				'fee_overdue_refund' => DB::raw('fee_overdue_refund + ' . $phiQuaHanHoanThucTe),
				'other_data' => json_encode($otherData),
				'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO
			]);

			if (!$r) {
				mylog([
					'LOI CAP NHAT REFUNDIING' => 'ok',
					'CASE' => 'HOAN 1 PHAN'
				]);

				throw new Exception('LOI CAP NHAT REFUNDIING');
			}

			return $collectDebtSummary;
		}

		mylog(['LOI LOGIC HOAN TIEN' => 'LOI']);
		throw new Exception('LOI LOGIC HOAN TIEN');
	}
} // End class
