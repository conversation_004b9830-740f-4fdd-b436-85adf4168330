<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Carbon\Carbon;

class ThongKeThoiGianSA
{
	/**
	 * Tr<PERSON> về các thông tin:
	 * 		Số ngày ứng: 
	 * 		Ng<PERSON>y bắt đầu (Từ)
	 * 		Ng<PERSON>y kết thúc (Đến)
	 * 		Loại (trích ngày or trích kỳ)
	 * 		Tần suất (n ngày / lần)
	 *
	 * @param CollectDebtSummary $collectDebtSummary
	 * @return array
	 * array:3 [
	 */
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$contractData = $collectDebtSummary->getContractData();
		
		$returnData = [
			[
				'name' => 'Ngày ứng',
				'value' => $contractData['cycle']
			],

			[
				'name' => 'Từ',
				'value' => Carbon::createFromFormat('d-m-Y H:i:s', $contractData['time_start_as_date'])->format('d/m/Y')
			],

			[
				'name' => 'Đến',
				'value' => Carbon::createFromFormat('d-m-Y H:i:s', $contractData['time_end_as_date'])->format('d/m/Y')
			],

			[
				'name' => sprintf('Trích %s', $collectDebtSummary->isHopDongSummaryTrichKy() ? 'kỳ' : 'ngày'),
				'value' => sprintf('%s ngày/1 lần', $contractData['intervals'])
			],
		];

		if ($collectDebtSummary->isHopDongDaTatToan()) {
			$returnData[] = [
				'name' => 'Tất toán',
				'value' => Carbon::createFromTimestamp($collectDebtSummary->time_settlement)->format('d/m/Y')
			];
		}

		return $returnData;
	}
}
