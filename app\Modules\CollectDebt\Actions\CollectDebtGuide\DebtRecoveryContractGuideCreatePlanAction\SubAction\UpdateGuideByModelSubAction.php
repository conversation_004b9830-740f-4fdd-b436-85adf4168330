<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtGuide;

class UpdateGuideByModelSubAction
{
  public function run(CollectDebtGuide $collectDebtGuide, array $params = []): CollectDebtGuide
  {
    $updateResult = $collectDebtGuide->update($params);
    return $collectDebtGuide->refresh();
  }
} // End class
