<?php

namespace App\Modules\CollectDebtGateway\Repositories;

use App\Modules\CollectDebtGateway\Inter\CollectDebtRecoveryGatewayLogInterface;
use App\Modules\CollectDebtGateway\Model\CollectDebtRecoveryGatewayLog;
use Illuminate\Support\Facades\DB;

class CollectDebtRecoveryGatewayLogRepository extends EloquentRepository implements CollectDebtRecoveryGatewayLogInterface
{

    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return \App\Modules\CollectDebtGateway\Model\CollectDebtRecoveryGatewayLog::class;
    }

    /**
     * @return string|null
     */
    public function getNameTable()
    {
        return $this->_model->table;
    }

    /**
     * @param $inputs
     * @return array|bool
     */
    public function createGetId($inputs)
    {
        $id = $this->_model->insertGetId($inputs);
        if ($id && $id > 0) {
            return ['id' => $id];
        }
        return false;
    }

    /**
     * @param $id
     * @param $inputs
     * @return array|bool
     */
    public function updateGetId($id, $inputs)
    {
        $result = $this->_model->where('id', $id)->update($inputs);
        if ($result && $result > 0) {
            return ['id' => $id];
        }

        return false;
    }

    /**
     * @param $data
     * @return array
     */
    public function getByPartnerRequestId($data)
    {
        if (empty($data['fields'])) {
            $data['fields'] = '*';
        }
        $result = $this->_model->where('partner_request_id', $data['partner_request_id'])->selectRaw($data['fields'])->get();
        if ($result->isEmpty()) {
            return [];
        }
        $result = $result->first()->toArray();

        return $result;
    }


    /**
     * @param $data
     * @return array
     */
    public function getByRequestId($data)
    {
        if (empty($data['fields'])) {
            $data['fields'] = '*';
        }
        $result = $this->_model->where('request_id', $data['request_id'])->selectRaw($data['fields'])->get();
        if ($result->isEmpty()) {
            return [];
        }
        $result = $result->first()->toArray();

        return $result;
    }

    /**
     * @param $data
     * @return array
     */
    public function getAllByFollow($data)
    {
        if (empty($data['fields'])) {
            $data['fields'] = '*';
        }

        $result = $this->_model->where($data['condition'])->selectRaw($data['fields'])->get();
        if ($result->isEmpty()) {
            return [];
        }
        $result = $result->toArray();

        return $result;
    }
}
