<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

use Illuminate\Support\Arr;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtLateFee;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Carbon\Carbon;
use Exception;

trait PlanPhanLoaiHopDong
{
	public function getTypeOthers(): array {
		$typeOthers = collect($this->getPlanOtherData())->where('type', 'OTHER')
																								->transform(function ($item) {
																									$item['data']['request_created_channel'] = '';
																									return $item;
																								})
																								->toArray();
		return $typeOthers;
	}

  public function isHopDongTrichNgay(): bool {
    return $this->contract_type == CollectDebtEnum::GUIDE_HD_TRICH_NGAY;
  }

  public function isHopDongTrichKy(): bool {
    return $this->contract_type == CollectDebtEnum::GUIDE_HD_TRICH_KY;
  }

  public function getSoTienPhiTheoLoaiPhi(int $loaiPhi, float $soTienMuonSinhPhi): float {
		$feeConfig = $this->getCauHinhPhiTheoLoai($loaiPhi);
		
		$flatFee = 0;
		if (!empty( $feeConfig['flat_fee']) ) {
			$flatFee = (float) $feeConfig['flat_fee'];
		}

		$totalFee = ( $soTienMuonSinhPhi * $feeConfig['percent_fee'] / 100 ) + $flatFee;
		
		// Áp dụng cận trên & cận dưới của cấu hình phí
		if ( !empty($feeConfig['fee_min']) && $totalFee < $feeConfig['fee_min'] ) {
			return $feeConfig['fee_min'];
		}
		
		if ( !empty($feeConfig['fee_max']) && $totalFee > $feeConfig['fee_max'] ) {
			return $feeConfig['fee_max'];
		}

		$totalFee = round($totalFee, 0, PHP_ROUND_HALF_UP);

		if ($loaiPhi == CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN && $this->isKhongPhaiRunDateTuongLai()) {
			$phanTramPhiPhatTraCham = env('PHAN_TRAM_PHI_PHAT_TRA_CHAM', 3);
			$contractCode = $this->contract_code;
			$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();
			
			if (!$collectDebtSummary) {
				return $totalFee;
			}

			// Sinh phí phạt lần đầu, kiểm tra ngày áp dụng phí phạt
			if ( empty($collectDebtSummary->number_day_overdue) ) {
				try {
					$loaiPhiPhatTraCham = $this->getCauHinhPhiTheoLoai(CollectDebtEnum::GUIDE_LOAI_PHI_PHAT_TRA_CHAM);
				
					if (!empty($loaiPhiPhatTraCham['percent_fee'])) {
						$phanTramPhiPhatTraCham = $loaiPhiPhatTraCham['percent_fee'];
					}
				}catch(\Throwable $th) { 
					// bo qua, giu nguyen 3% phi phat tra cham
					$phanTramPhiPhatTraCham = env('PHAN_TRAM_PHI_PHAT_TRA_CHAM', 3);
				}

				// Nếu hợp đồng tnex thì chỉ sinh phí chậm kỳ thôi
				if ($collectDebtSummary->partner_code == 'TNEX') {
					return $totalFee;
				}

				// Sét điều kiện áp dụng phí phạt: Không phải HĐ Tnex
				$phiPhatTraCham = $soTienMuonSinhPhi * $phanTramPhiPhatTraCham / 100;
				$phiPhatTraCham = round($phiPhatTraCham, 0, PHP_ROUND_HALF_UP);
				
				$collectDebtLateFee = CollectDebtLateFee::query()->insert([
					'type' => CollectDebtEnum::TYPE_PHI_PHAT_TRA_CHAM,
					'contract_code' => $contractCode,
					'percent_late_fee' => $phanTramPhiPhatTraCham,
					'money_late_fee' => 0,
					'plan_id' => $this->id,
					'total_amount_debt' => $soTienMuonSinhPhi,
					'total_fee' => $phiPhatTraCham,
					'is_ignore_overdue_fee' => 0,
					'created_at' => now(),
					'updated_at' => now()
				], [
					'type' => CollectDebtEnum::TYPE_PHI_QUA_HAN_CHONG_PHI,
					'contract_code' => $contractCode,
					'percent_late_fee' => $feeConfig['percent_fee'],
					'money_late_fee' => $flatFee,
					'plan_id' => $this->id,
					'total_amount_debt' => $soTienMuonSinhPhi,
					'total_fee' => $totalFee,
					'is_ignore_overdue_fee' => 1,
					'created_at' => now(),
					'updated_at' => now()
				]);

				if (!$collectDebtLateFee) {
					throw new Exception('Lỗi không sinh được phí phạt trả chậm và phí quá hạn: ' . $contractCode); 
				}

				$totalFee = $totalFee + $phiPhatTraCham;
			}
		}
		
		return $totalFee;
	} 

  public function getCauHinhPhiTheoLoai(int $feeType=0): array {
		$collectDebtShare = CollectDebtShare::where('contract_code', $this->contract_code)->first();
		$listFee = json_decode($collectDebtShare->list_fee, true);
		
		$feeConfig = Arr::first($listFee, function ($feeItem) use ($feeType)  {
			return $feeItem['type'] == $feeType;
		});	

		if ($feeConfig) {
			$feeConfig['name'] = $this->getTenLoaiPhi($feeConfig['type']);
			return $feeConfig;
		}

		return $listFee;
	}

	public function getSoTienPhiShared(int $loaiPhi, float $soTienMuonSinhPhi, CollectDebtShare $collectDebtShare): float {
		$feeConfig = $this->getCauHinhPhiShared($loaiPhi, $collectDebtShare);
		
		$flatFee = 0;
		if (!empty( $feeConfig['flat_fee']) ) {
			$flatFee = (float) $feeConfig['flat_fee'];
		}

		$totalFee = ( $soTienMuonSinhPhi * $feeConfig['percent_fee'] / 100 ) + $flatFee;
		
		// Áp dụng cận trên & cận dưới của cấu hình phí
		if ( !empty($feeConfig['fee_min']) && $totalFee < $feeConfig['fee_min'] ) {
			return $feeConfig['fee_min'];
		}
		
		if ( !empty($feeConfig['fee_max']) && $totalFee > $feeConfig['fee_max'] ) {
			return $feeConfig['fee_max'];
		}

		if ($loaiPhi == CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN) {
			$soNgayQuaHan = now()->diffInDays($collectDebtShare->time_end_as_date);
			
			if ($soNgayQuaHan > 0) {
				$totalFee = $totalFee * $soNgayQuaHan;
			}
		}
		
		return round($totalFee, 0, PHP_ROUND_HALF_UP);
	} 

	public function getCauHinhPhiShared(int $feeType=0, CollectDebtShare $collectDebtShare): array {
		$listFee = json_decode($collectDebtShare->list_fee, true);
		
		$feeConfig = Arr::first($listFee, function ($feeItem) use ($feeType)  {
			return $feeItem['type'] == $feeType;
		});	

		if ($feeConfig) {
			$feeConfig['name'] = $this->getTenLoaiPhi($feeConfig['type']);
			return $feeConfig;
		}

		return $listFee;
	}

  public function getTenLoaiPhi($type): string {
		$configFee =  [
			CollectDebtEnum::GUIDE_LOAI_PHI_HOP_DONG               => 'Phí hợp đồng',
			CollectDebtEnum::GUIDE_LOAI_PHI_THAM_DINH_HO_SO        => 'Phí thẩm định hồ sơ',
			CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN                => 'Phí quá hạn',
			CollectDebtEnum::GUIDE_LOAI_PHI_UU_DAI_THAM_DINH_HO_SO => 'Ưu đãi thẩm định hồ sơ',
			CollectDebtEnum::GUIDE_LOAI_PHI_GIAI_NGAN              => 'Phí giải ngân',
			CollectDebtEnum::GUIDE_LOAI_PHI_THU_HOI                => 'Phí thu hồi',
			CollectDebtEnum::GUIDE_LOAI_PHI_GIA_HAN                => 'Phí gia hạn',
			CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY                => 'Phí chậm kỳ',
			CollectDebtEnum::GUIDE_LOAI_UU_DAI_PHI_THAM_GIA        => 'Ưu đãi phí tham gia',
			CollectDebtEnum::GUIDE_LOAI_PHI_HOAN                   => 'Phí hoàn',
		];

		return $configFee[$type] ?? 'Không xác định';
	}
} // End class