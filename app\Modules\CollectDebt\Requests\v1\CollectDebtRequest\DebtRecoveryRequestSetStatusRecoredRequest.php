<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class DebtRecoveryRequestSetStatusRecoredRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'integer'],
      'data.status_recored' => [
        'required', 
        Rule::in([
          CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO,
          CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO,
          CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO,
          CollectDebtEnum::REQUEST_STT_RC_GHI_SO_LOI
        ])
      ],
      'data.user_request_id' => ['required', 'numeric', 'min:1'],
      'data.description' => ['required', 'string', 'max:255'],
    ];
  }
} // End class
