<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\QrImageTask;

use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Exception;

class UpdateQrCodeImageTask
{

	public function run(CollectDebtGuide $collectDebtGuide, string $qrCodeImage = '')
	{

		throw_if(
			empty($qrCodeImage),
			new Exception('Mã HĐ hoặc ảnh QR Code đã bị để trống')
		);

		$this->handleGuide($collectDebtGuide, $qrCodeImage);

		if ($collectDebtGuide->collectDebtShare) {
			$this->handleQrImageShared($collectDebtGuide->collectDebtShare, $qrCodeImage);
		}
		
		return $collectDebtGuide->contract_code;
	} 

	public function handleGuide(CollectDebtGuide $collectDebtGuide, $qrCodeImage)
	{
		$paymentGuide = json_decode($collectDebtGuide->payment_guide, true);

		$paymentGuide = collect($paymentGuide)->transform(function ($pg) {
			if ($pg['payment_method_code'] == 'VIRTUALACCOUNT') {
				$pg['other_data']['qrImage'] = '';
				$pg['other_data']['is_sync'] = '1';
			}

			return $pg;
		})->values()->toArray();

		return $collectDebtGuide->update(['payment_guide' => json_encode($paymentGuide)]);
	}

	public function handleQrImageShared(CollectDebtShare $collectDebtShare, $qrCodeImage)
	{
		$paymentGuide = json_decode($collectDebtShare->payment_guide, true);

		$paymentGuide = collect($paymentGuide)->transform(function ($pg) use ($qrCodeImage) {
			if ($pg['payment_method_code'] == 'VIRTUALACCOUNT') {
				$pg['other_data']['qrImage'] = $qrCodeImage;
			}

			return $pg;
		})->values()->toArray();

		return $collectDebtShare->update(['payment_guide' => json_encode($paymentGuide)]);
	}
	
} // End class