<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\QrImageTask\UpdateQrCodeImageTask;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\QrImageTask\GetQrImageFromCodeTask;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class KhoiTaoBangTongHopSubAction
{
  const CO_CHAM_KY_THANH_TOAN = 1;
  const KHONG_CHAM_KY_THANH_TOAN = 2;

  // Lúc này vừa mới tạo lịch xong nên vẫn có thể dùng chỉ dẫn được $collectDebtGuide
  public function run(CollectDebtGuide $collectDebtGuide, Collection $plans): CollectDebtSummary
  {
    $otherDataTypePlan = [
      'type' => 'PLAN',
      'note' => 'Danh sách lịch thu',
      'data' => [],
      'time_modified' => time()
    ];
    foreach ($plans as $plan) {
      $otherDataTypePlan['data'][] = [
        'id'              => $plan->id,
        'time_cycle'      => $plan->time_start, // thời gian thu lịch gốc
        'time_over_cycle' => 0, // Thời gian bắt đầu quá kỳ
        'amount'          => $plan->amount_period_debit, // Tổng số tiền gốc kỳ cần phải thu
        'fee'             => 0, // Tổng phí kỳ phải thu
        'amount_paid'     => $plan->success_amount_debit, // Số tiền gốc đã thu
        'fee_paid'        => 0, // Phí đã thu
        'overdue_cycle'   => self::KHONG_CHAM_KY_THANH_TOAN, //  Có quá kỳ TT hay không (1-Có,2-Không)
        'note'            => $plan->description,
        'status'          => $plan->status,
        'data'            => [
          'id'            => $plan->id,
          'fee_overdue_cycle_reduction' => 0, // Tổng phí quá kỳ đã giảm <Phí quá kỳ đã giảm>
        ],
      ];
    }

    $paymentMethods = $this->buildPaymentMethod($collectDebtGuide);
    $params = [
      'contract_code'      => $collectDebtGuide->contract_code,
      'contract_type'      => $collectDebtGuide->contract_type,
      'contract_cycle'     => $collectDebtGuide->contract_cycle,
      'contract_intervals' => $collectDebtGuide->contract_intervals,
      'contract_amount'    => $collectDebtGuide->amount,
      'contract_data'      => $collectDebtGuide->contract_data,
			'is_request_sync'		 => CollectDebtEnum::SUMMARY_CO_DONG_BO,
      'other_data'         => json_encode([
        $otherDataTypePlan,
        $paymentMethods,
      ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
      'time_created'       => time(),
			'partner_code' => $collectDebtGuide->partner_code,
			'profile_id' => $collectDebtGuide->profile_id,
    ];

    $created =  CollectDebtSummary::firstOrCreate([
      'contract_code' => $collectDebtGuide->contract_code
    ], $params);

    throw_if(!$created, new Exception('Không tạo được bản ghi summary'));

		$collectDebtGuide->load(['collectDebtShare']);
		$qrImageString = app(GetQrImageFromCodeTask::class)->run($collectDebtGuide->getQrCodeAttr());
		app(UpdateQrCodeImageTask::class)->run($collectDebtGuide, $qrImageString);

    return CollectDebtSummary::find($created->id);
  }

  public function buildPaymentMethod(CollectDebtGuide $collectDebtGuide) {
    $paymentMethods = [
      'type' => 'PAYMENT_METHOD',
      'note' => 'Phương thức thanh toán',
      'data' => [],
      'time_modified' => time()
    ];
    
    $paymentGuides = json_decode($collectDebtGuide->payment_guide, true);
    foreach ($paymentGuides as $pg) {
      $paymentMethods['data'][] = [
        'payment_method_code' => $pg['payment_method_code'],
        'payment_channel_code' => $pg['payment_channel_code'],
        'payment_account_id' => $pg['payment_account_id'],
        'payment_account_holder_name' => $pg['payment_account_holder_name'] ?? '',
        'payment_account_bank_branch' => $pg['payment_account_bank_branch'] ?? '',
        'payment_account_bank_code' => $pg['payment_account_bank_code'] ?? '',
        'other_data' => $pg['other_data'] ?? [],
        'closed' => 'NO',
      ];
    }

    return $paymentMethods;
  }
} // End class
