<?php

namespace App\Modules\CollectDebt\Enums;

class CollectDebtEnum
{
/* ------------------ guide -------------*/
  const GUIDE_STT_MOI_TAO = 1;
  const GUIDE_STT_DA_DUYET = 2;
  const GUIDE_STT_DANG_TAO_LICH = 3;
  const GUIDE_STT_DA_TU_CHOI = 4;
  const GUIDE_STT_DA_TAO_LICH_THANH_CONG = 5;
  const GUIDE_STT_DA_THU_HET_TIEN = 6;

  // Phí
  const GUIDE_LOAI_PHI_HOP_DONG               = 1;
  const GUIDE_LOAI_PHI_THAM_DINH_HO_SO        = 2;
  const GUIDE_LOAI_PHI_QUA_HAN                = 3;
  const GUIDE_LOAI_PHI_UU_DAI_THAM_DINH_HO_SO = 4;
  const GUIDE_LOAI_PHI_GIAI_NGAN              = 5;
  const GUIDE_LOAI_PHI_THU_HOI                = 6;
  const GUIDE_LOAI_PHI_GIA_HAN                = 7;
  const GUIDE_LOAI_PHI_CHAM_KY                = 8;
  const GUIDE_LOAI_UU_DAI_PHI_THAM_GIA        = 9;
  const GUIDE_LOAI_PHI_HOAN                   = 10;
  const GUIDE_LOAI_PHI_PHAT_TRA_CHAM          = 11;

  const GUIDE_HD_TRICH_NGAY = 1;
  const GUIDE_HD_GIA_HAN = 2;
  const GUIDE_HD_TRICH_KY = 3;

/* ------------------ schedule -------------*/
  const SCHEDULE_LOAI_HD_THUONG = 1;
  const SCHEDULE_LOAI_HD_GIA_HAN = 2;
  const SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY = 3;
  const SCHEDULE_LOAI_HD_KHOAN_UNG_TRICH_NGAY = 1;

  const SCHEDULE_LOAI_LICH_THU_CHINH = 1;
  const SCHEDULE_LOAI_LICH_THU_PHU = 2;

  const SCHEDULE_LA_LICH_THU_PHI = 1;
  const SCHEDULE_LA_LICH_THU_KHONG_CO_PHI = 0;
  const SCHEDULE_LA_LICH_THU_NO_GOC = 0;

  const SCHEDULE_STT_MOI = 1;
  const SCHEDULE_STT_DANG_HACH_TOAN = 2;
  const SCHEDULE_STT_DA_HOAN_THANH = 3;
  const SCHEDULE_STT_DA_HUY = 4;
  const SCHEDULE_STT_DANG_XU_LY = 5;

  const SCHEDULE_LISTING_ORDER_BY = 'is_settlement ASC, time_start ASC, isfee ASC, id ASC';
  const SCHEDULE_LEDGER_UU_TIEN_CAN_TRU_ORDER_BY = 'time_start ASC';

  const SCHEDULE_LA_LICH_TAT_TOAN = 1;
  
  const INFOCODE_SINH_PHI_CHAM_KY = 'SINH_PHI_CHAM_KY';
  const INFOCODE_SINH_PHI_QUA_HAN = 'SINH_PHI_QUA_HAN';
  const INFOCODE_THANH_TOAN_PHI_CHAM_KY = 'THANH_TOAN_PHI_CHAM_KY';
  const INFOCODE_THANH_TOAN_PHI_QUA_HAN = 'THANH_TOAN_PHI_QUA_HAN';

	const SCHEDULE_PROCESS_CHUA_XU_LY = 1;
	const SCHEDULE_PROCESS_DANG_XU_LY = 2;
	const SCHEDULE_PROCESS_DA_XU_LY = 3;
/* ------------------ request -------------*/
  const REQUEST_TYPE_THANH_TOAN_TRICH_NO = 1;

  const REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT = 1;
  const REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT = 0;

  const REQUEST_VERSION_4 = 4;


  // Trạng thái
  const REQUEST_STT_MOI_TAO = 1;
  const REQUEST_STT_DA_DUYET = 2;
  const REQUEST_STT_DA_DUYET_GIAM_PHI_CAP_1 = 21; // Status appends do có quy trình duyệt tay cấp 1
  const REQUEST_STT_DA_DUYET_GIAM_PHI_CAP_2 = 22; // Status appends do có quy trình duyệt tay cấp 2
  

  const REQUEST_STT_DA_HOAN_THANH = 3;
  const REQUEST_STT_TU_CHOI = 4;
  const REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA = 5;

  // Trạng thái thanh toán
  const REQUEST_STT_PM_CHUA_GUI = 1;
  const REQUEST_STT_PM_DANG_GUI = 2;
  const REQUEST_STT_PM_TU_CHOI = 3;
  const REQUEST_STT_PM_GUI_LOI = 4;
  const REQUEST_STT_PM_DA_GUI = 5;
  const REQUEST_STT_PM_DA_NHAN_KET_QUA = 6;

  // Trạng thái ghi sổ
  const REQUEST_STT_RC_CHUA_GHI_SO = 1;
  const REQUEST_STT_RC_DANG_GHI_SO = 2;
  const REQUEST_STT_RC_DA_GHI_SO = 3;
  const REQUEST_STT_RC_GHI_SO_LOI = 4;

  // Trạng thái hành đông - action
  const REQUEST_STT_AC_MOI = 1;
  const REQUEST_STT_AC_DANG_DUYET = 2;
  const REQUEST_STT_AC_TU_CHOI = 3;
  const REQUEST_STT_AC_DA_DUYET = 4;

    // Loại trích tự động, trích tay
  const REQUEST_LOAI_TRICH_TU_DONG = 1;
  const REQUEST_LOAI_TRICH_TAY = 2;

	const REQUEST_CHUA_CUT_OFF = 1;
	const REQUEST_DANG_CUT_OFF = 2;
	const REQUEST_DA_XL_CUT_OFF = 3;
	
  // Request CAN <duoc phep lam nhung gi>
  const CAN_VIEW_DETAIL_REQUEST = 'CAN_VIEW_DETAIL_REQUEST';
  const CAN_APPROVE_MANUAL_REQUEST = 'CAN_APPROVE_MANUAL_REQUEST';
  const CAN_APPROVE_REDUCE_FEE_STEP_1 = 'CAN_APPROVE_REDUCE_FEE_STEP_1';
  const CAN_APPROVE_REDUCE_FEE_STEP_2 = 'CAN_APPROVE_REDUCE_FEE_STEP_2';
  const CAN_CANCEL_MANUAL_REQUEST = 'CAN_CANCEL_MANUAL_REQUEST';
  const CAN_CANCEL_AUTO_REQUEST = 'CAN_CANCEL_AUTO_REQUEST';
  const CAN_CHECK_PARTNER = 'CAN_CHECK_PARTNER';
  const CAN_VIEW_REQUEST_ON_LEDGER = 'CAN_VIEW_REQUEST_ON_LEDGER';
  const CAN_CANCEL_LENH_RUT_TIEN_NHANH = 'CAN_CANCEL_LENH_RUT_TIEN_NHANH';
  const CAN_CREATE_ADJUSTMENT_REQUEST = 'CAN_CREATE_ADJUSTMENT_REQUEST';
  const CAN_RE_CANCEL = 'CAN_RE_CANCEL'; // hủy lại lệnh đã hủy
/* ------------------ partner -------------*/
  const PARTNER_REQUEST_DA_CO_YC_THANH_TOAN = 1;
  const PARTNER_REQUEST_CHUA_CO_YC_THANH_TOAN = 2;

  const PARTNER_STT_CHUA_XU_LY = 1;
  const PARTNER_STT_DANG_XU_LY = 2;
  const PARTNER_STT_DA_XU_LY = 3;
  const PARTNER_STT_XU_LY_LOI = 4;
  const PARTNER_STT_CHO_DUYET = 5;
  const PARTNER_STT_DA_TU_CHOI = 6;

  const PARTNER_STT_RF_CHUA_HOAN = 1;
  const PARTNER_STT_RF_DANG_XU_LY = 2;
  const PARTNER_STT_RF_DA_HOAN_TIEN = 3;
  const PARTNER_STT_RF_XU_LY_LOI = 4;

/* ------------------ ledger <ghi sổ> -------------*/
  const LEDGER_STT_KHONG_PHAI_XU_LY = 5;
  const LEDGER_STT_CHUA_XU_LY = 1;
  const LEDGER_STT_DANG_HACH_TOAN = 2;
  const LEDGER_STT_DA_HACH_TOAN = 3;
  const LEDGER_STT_DA_TU_CHOI = 4;


  // Status action
  const LEDGER_STT_ACTION_KHONG_XU_LY = 5;
  const LEDGER_STT_ACTION_CHUA_CAP_NHAT = 1;
  const LEDGER_STT_ACTION_DANG_CAP_NHAT = 2;
  const LEDGER_STT_ACTION_DA_CAP_NHAT = 3;
  const LEDGER_STT_ACTION_LOI = 4;
/* ------------------ Summary -------------*/
  //dang sai nhé status <> 3 compless thôi
  const SUMMARY_STT_DANG_CAP_NHAP = 1;
  const SUMMARY_STT_DANG_THUC_HIEN = 2;
  const SUMMARY_STT_DA_HOAN_THANH = 3;
  const SUMMARY_STT_DA_TU_CHOI = 4;

  
  const SUMMARY_CO_CHAM_KY = 1;
  const SUMMARY_KHONG_QUA_KY = 2;

  const SUMMARY_CO_QUA_HAN = 1;
  const SUMMARY_KHONG_QUA_HAN = 2;

  const SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN = 2;
  const SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN = 1;
  const SUMMARY_STATUS_CONTRACT_DANG_TAM_DUNG = 3;

  const SUMMARY_KHONG_GUI_MAIL_CHAM_KY = 0;
  const SUMMARY_CO_GUI_MAIL_CHAM_KY = 1;
  const SUMMARY_DANG_GUI_MAIL_CHAM_KY = 2;

  const SUMMARY_KHONG_GUI_MAIL_QUA_HAN = 0;
  const SUMMARY_CO_GUI_MAIL_QUA_HAN = 1;
  const SUMMARY_DANG_GUI_MAIL_QUA_HAN = 2;

	const SUMMARY_KHONG_DONG_BO = 0;
	const SUMMARY_CO_DONG_BO = 1;
	const SUMMARY_DANG_DONG_BO = 2;

	const SUMMARY_KHONG_GUI_NOTI_QUA_HAN = 0;
  const SUMMARY_CO_GUI_NOTI_QUA_HAN = 1;
  const SUMMARY_DANG_GUI_NOTI_QUA_HAN = 2;
	
/* ------------ METADAYA FOR SUMMARY -------- */
  const METADATA_SO_TIEN_GHI_SO = 'METADATA_SO_TIEN_GHI_SO';
  const METADATA_THU_GOC = 'METADATA_THU_GOC';
  const METADATA_PHI_CHAM_KY = 'METADATA_PHI_CHAM_KY';
  const METADATA_PHI_QUA_HAN = 'METADATA_PHI_QUA_HAN';
  const METADATA_THANH_TOAN_PHI_CK = 'METADATA_THANH_TOAN_PHI_CK';
  const METADATA_THANH_TOAN_PHI_QH = 'METADATA_THANH_TOAN_PHI_QH';
  const METADATA_CAN_TRU_TIEN_VAO_LICH = 'METADATA_CAN_TRU_TIEN_VAO_LICH';
  const METADATA_GIAM_PHI_CHAM_KY = 'METADATA_GIAM_PHI_CHAM_KY';
  const METADATA_GIAM_PHI_QUA_HAN = 'METADATA_GIAM_PHI_QUA_HAN';

  const METADATA_LICH_THUC_HIEN_GACH = 'METADATA_LICH_THUC_HIEN_GACH';

/* ------------ LEDGER_PLAN_DATA -------- */
  const LEDGER_PLAN_DATA_PHI_DA_THU = 'LEDGER_PLAN_DATA_PHI_DA_THU';
  const LEDGER_PLAN_DATA_NO_GOC_DA_THU = 'LEDGER_PLAN_DATA_NO_GOC_DA_THU';
  const LEDGER_PLAN_DATA_PHI_CHAM_KY = 'LEDGER_PLAN_DATA_PHI_CHAM_KY';
  const LEDGER_PLAN_DATA_PHI_QUA_HAN = 'LEDGER_PLAN_DATA_PHI_QUA_HAN';
  const LEDGER_PLAN_DATA_GIAM_PHI_CHAM_KY = 'LEDGER_PLAN_DATA_GIAM_PHI_CHAM_KY';
  const LEDGER_PLAN_DATA_GIAM_PHI_QUA_HAN = 'LEDGER_PLAN_DATA_GIAM_PHI_QUA_HAN';

    // Debt request action code
    const REQUEST_ACTION_CODE_CREATED  = 'CREATED';
    const REQUEST_ACTION_CODE_UPDATED  = 'UPDATED';
    const REQUEST_ACTION_CODE_CANCELED  = 'CANCELED';
    const REQUEST_ACTION_CODE_APPROVE1  = 'APPROVE1';
    const REQUEST_ACTION_CODE_APPROVE2  = 'APPROVE2';
    const REQUEST_ACTION_CODE_APPROVE3  = 'APPROVE3';
    const REQUEST_ACTION_CODE_APPROVE4  = 'APPROVE4';
    const REQUEST_ACTION_CODE_APPROVE5  = 'APPROVE5';
    const REQUEST_ACTION_CODE_COMMENT  = 'COMMENT';
    const REQUEST_ACTION_CODE_COMPLETED   = 'COMPLETED';

    //Debt request Action type

    const REQUEST_ACTION_TYPE_GIAM_PHI = 1;
    const REQUEST_ACTION_TYPE_HOAN_TIEN = 2;

  // Request Adjustment - Yêu cầu điều chỉnh
  const REQUEST_ADJUSTMENT_STT_MOI_TAO = 1;
  const REQUEST_ADJUSTMENT_STT_DUYET_1 = 2;
  const REQUEST_ADJUSTMENT_STT_DUYET_2 = 3;
  const REQUEST_ADJUSTMENT_STT_HUY = 4;
  const REQUEST_ADJUSTMENT_STT_DANG_XU_LY = 5;
  const REQUEST_ADJUSTMENT_STT_HOAN_THANH = 6;

  const REQUEST_ADJUSTMENT_TYPE_DIEU_CHINH_DOI_TAC = 1;
  const REQUEST_ADJUSTMENT_TYPE_DIEU_KHAC = 2;

  const RA_STT_CAN_XEM_CHI_TIET = 'RA_STT_CAN_XEM_CHI_TIET';
  const RA_STT_CAN_DUYET_1 = 'RA_STT_CAN_DUYET_1';
  const RA_STT_CAN_DUYET_2 = 'RA_STT_CAN_DUYET_2';
  const RA_STT_CAN_HUY = 'RA_STT_CAN_HUY';

	// ContractPriorfy
	const CONTRACT_PRIORITY_STT_MOI_TAO = 1;
	const CONTRACT_PRIORITY_STT_DANG_XU_LY = 2;
	const CONTRACT_PRIORITY_STT_DA_XU_LY = 3;
	const CONTRACT_PRIORITY_STT_DA_HUY = 4;


/* ----------------- dừng job trích tự động ------------------- */
	const SO_PHUT_DUNG_JOB_TRICH_TU_DONG = 20;

/* --------------- WAIT PROCESS ------------------- */
	const WAIT_PROCESS_STT_CHUA_XU_LY = 1;
	const WAIT_PROCESS_STT_DANG_XU_LY = 2;
	const WAIT_PROCESS_STT_XU_LY_LOI = 3;
	const WAIT_PROCESS_STT_DA_XU_LY = 4;
	const WAIT_PROCESS_STT_DUOC_HUY = 5;

/* --------------- EVENT ------------------- */
	const EVENT_STT_MOI_TAO = 1;
	const EVENT_STT_DANG_TAO_NOI_DUNG = 2;
	const EVENT_STT_DA_TAO_NOI_DUNG_MAIL = 3;
	const EVENT_STT_DANG_GUI = 4;
	const EVENT_STT_DA_TAO_YC_THANH_CONG = 5;
	const EVENT_STT_DA_TU_CHOI = 7;

/* --------------- RECOVERY LOG ------------------- */
	const RL_DICH_VU_HOAN_PHI = 'REFUND_FEE';
	const RL_DICH_VU_HOAN_THU_THUA = 'REFUND_EXCESS';
	const RL_DICH_VU_TRU_TIEN_VAO_VI = 'DEDUCT_WALLET';
	const RL_DICH_VU_KIEM_TRA_TRICH_NGAY = 'CHECK_DEBT_NOW';

	const RL_STT_MOI_TAO = 1;
	const RL_STT_DANG_XU_LY = 2;
	const RL_STT_DA_XU_LY_THANH_CONG = 3;
	const RL_STT_DA_XU_LY_THAT_BAI = 4;


	const RL_REFUND_SYSTEM_DA_HOAN_TIEN_CHO_MC = 4;
	const RL_REFUND_SYSTEM_DA_HUY = 5;
	const RL_REFUND_SYSTEM_DA_TU_CHOI = 6;

/* --------------- PHÍ PHẠT ------------------- */
	const TYPE_PHI_PHAT_TRA_CHAM = 1;
	const TYPE_PHI_QUA_HAN_CHONG_PHI = 2;
} // End class