@php($companySubName = $nextlendCompany->getSubName())
<p><strong>H<PERSON><PERSON> thức hỗ trợ thanh toán hoàn trả:</strong></p>

<div>
    <p class="ml-5">
        <span>
            1. Hoàn tiền đã ứng bằng giao dịch thanh toán thẻ trên MPOS.VN sẽ được cấn trừ khoản
            tiền đã ứng theo chu kỳ ngày: {{ $companySubName }} sẽ trích số tiền tương ứng dựa trên khoản
            thanh toán theo ngày từ giao dịch khách hàng của Quý khách thanh toán quẹt thẻ qua
            ứng dụng mPOS của {{ $companySubName }}.
        </span>

        <strong>
            Phí giao dịch thanh toán thẻ theo quy định Hợp đồng thanh toán thẻ.
        </strong>
    </p>

    <div class="mt-2 ml-5">
        2. Hoàn tiền đã ứng bằng hình thức chuyển khoản về tài khoản nhận thanh toán của {{ $companySubName }}:
        {{ $companySubName }} sẽ cấn trừ vào giờ hành chính của ngày làm việc (không bao gồm ngày chủ nhật, nghĩ lễ).
        <strong>Phí chuyển khoản theo quy định Ngân hàng.</strong>

        <div style="margin-left: 30px;">
          <p>&#8226; Số tài khoản nhận tiền: {{ $nextlendCompany['company_bank_account_1'] }}</p>
          <p>&#8226; Ngân hàng: {{ $nextlendCompany['company_bank_name_1'] }}</p>
          <p>&#8226; Tên chủ tài khoản: {{ $nextlendCompany['company_bank_holder_1'] }}</p>
          <p>&#8226; Nội dung chuyển khoản: Nap tien TK MC ma {{ $nextlendCompany->getNoiDungChuyenKhoanMaNapTien() }} -
                “{{ $merchant['business_representative'] }}”</p>
        </div>
    </div>

    @if ( !empty($data['va']['payment_account_id']) )
        <div class="ml-5">
            <p>
                3. Hoàn tiền đã ứng bằng việc Quét QR: Mã QR thanh toán được dành riêng cho khoản ứng
                của đơn vị. Ngay sau khi Quý khách quét QR thanh toán thành công sẽ được cấn trừ
                “Ngay lập tức” vào khoản ứng cần hoàn trả của Quý khách.
                <strong>Phí giao dịch là 0 đồng.</strong>
            </p>


            <div class="center">
            <img src="{!! $data['va']['other_data']['qrImage'] !!}" alt="QR Image" >

            <span class="d-block" style="text-align: center;">Ngân hàng: {{ $data['va']['other_data']['payment_account_bank_code'] }}</span>
            <span class="d-block my-1" style="text-align: center;">Số tài khoản: {{ $data['va']['payment_account_id'] }}</span>
            <span class="d-block" style="text-align: center;">Chủ tài khoản:
                <strong>{{ $data['va']['other_data']['payment_account_name'] }}</strong>
            </span>
            </div>
        </div>
    @endif
  </div>
