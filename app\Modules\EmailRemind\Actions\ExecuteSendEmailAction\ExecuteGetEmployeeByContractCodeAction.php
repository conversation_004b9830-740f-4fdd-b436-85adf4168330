<?php

namespace App\Modules\EmailRemind\Actions\ExecuteSendEmailAction;

use App\Lib\NextlendCore;

class ExecuteGetEmployeeByContractCodeAction
{
  public function run(string $contract_code = '') {

    $body = [
      'contract_code' => $contract_code,
    ];

    $sendMail = app(NextlendCore::class)->callRequest($body, 'ContractV4_getEmailByContractCode', 'GET');

    return $sendMail->decryptData(true);
  }
} // End class