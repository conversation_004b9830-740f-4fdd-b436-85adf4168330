<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\CongTienQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\IsCoLichThuPhiQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\FeeHandler\XuLyThanhToanPhiFH;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\FeeHandler\XuLySinhPhiQuaHanFH;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime\IsChuaThuPhiQuaHanHomNayST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffGetLichThuCoRundateLaHomSauST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffTaoLichThuVetPhiSangNgayHomSau;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffTaoLichThuPhiChamKySangHomSauST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffTaoLichThuPhiQuaHanSangHomSauST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime\CutOffTaoLichThuVetNoGocSangNgayHomSauST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\Task\SwapLichThuNeuTrichTuongLaiST;

class XuLyCutOffTimeTask
{
  public array $metaData = [];

  /** ------------------- 
   * [CUTOFF-TIME]
   * Đoạn này cần check hôm sau có lịch thu tiếp hay không, nếu không thì tính là quá hạn
   *  Có lịch => chậm kỳ
   *  Không có lịch => Kiểm tra lệnh hiện tại là tất toán chưa?
   *                    + Nếu chưa => trong kỳ 
   *                    + Rồi => Qúa hạn
   *  ------------------- */
  public function run(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong = 0)
  {
    // Thu gốc
    if ($lichDangHachToan->isLichThuGoc()) {
      return $this->xuLyCutOffThuGoc($lichDangHachToan, $soTienTrichThanhCong);
    }

    // Thu phí (Thanh toán phí)
    if ($lichDangHachToan->isLichThuPhi()) {
      return $this->xuLyCutOffThuPhi($lichDangHachToan, $soTienTrichThanhCong);
    }
  }

  public function xuLyCutOffThuGoc(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong = 0)
  {
    $soTienConPhaiThuTiep = $lichDangHachToan->request_amount_debit - $soTienTrichThanhCong;

    /** ------------- Không phải lệnh tất toán ------------- */
    if (!$lichDangHachToan->isLichTatToan()) {
      /**
       * 1. Kiểm tra hôm sau có lịch nào không (hôm sau có bản ghi lịch rundate nào không)
       *    1a. Nếu không -> thì tạo lịch thi vét nợ gốc có rundate là hôm sau
       *    1b. Nếu có    -> cộng thêm số tiền còn phải thu vào debit_begin, request_amount_debit vào lịch hôm sau
       * 2. Thực hiện sinh lịch thu phí chậm kỳ nếu lịch đang hạch toán là lịch chính
       */
      $lichCoRunDateLaHomSau = app(CutOffGetLichThuCoRundateLaHomSauST::class)->run($lichDangHachToan);

      // Tạo lịch thu nốt số tiền còn phải thu tiếp sang hôm sau
      if (!$lichCoRunDateLaHomSau || ($lichCoRunDateLaHomSau && $lichCoRunDateLaHomSau->isKhongPhaiLichTatToan())) {
        $lichCoRunDateLaHomSau = app(CutOffTaoLichThuVetNoGocSangNgayHomSauST::class)->run(
          $lichDangHachToan,
          $soTienTrichThanhCong,
          $soTienConPhaiThuTiep
        );
      }

      // Cộng số tiền nợ gốc sang lịch thu kỳ tiếp theo
      if ($lichCoRunDateLaHomSau && $lichCoRunDateLaHomSau->isLichTatToan()) {
        $lichCoRunDateLaHomSau->debit_begin += $soTienConPhaiThuTiep;
        $lichCoRunDateLaHomSau->request_amount_debit += $soTienConPhaiThuTiep;
        $lichCoRunDateLaHomSau->save();
      }

      if ($lichDangHachToan->isLichThuGocChinh()) {
				if ($lichDangHachToan->isKhongPhaiRunDateTuongLai()) {
					mylog([
						'lich dang hach toan' => 'KHONG PHAI lich thu tuong lai',
						'thuc hien sinh phi ck' => 'CK'
					]);

					$phiChamKy = $lichDangHachToan->getSoTienPhiTheoLoaiPhi(CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY, $soTienConPhaiThuTiep);

					$lichThuPhiChamKySangHomSau = app(CutOffTaoLichThuPhiChamKySangHomSauST::class)->run($lichDangHachToan, $phiChamKy, $lichCoRunDateLaHomSau);
	
					$this->metaData[] = ['label' => CollectDebtEnum::METADATA_PHI_CHAM_KY, 'value' => $phiChamKy];
				}
				
				if ($lichDangHachToan->isRunDateTuongLai()) {
					mylog(['lich dang hach toan la' => 'LICH THU TUONG LAI']);
					$swap = app(SwapLichThuNeuTrichTuongLaiST::class)->run($lichDangHachToan, $lichCoRunDateLaHomSau, true);
					mylog(['swap trong han' => $swap]);
				}
      }
    } // End-if kiem tra lenht tat toan

    /** ------------- Là lệnh tất toán ------------- */
    if ($lichDangHachToan->isLichTatToan()) {
      $isCoLichThuPhiQuaHan = app(IsCoLichThuPhiQuaHanTask::class)->run($lichDangHachToan->contract_code, true);

      /**
       * 1. Move số tiền nợ gốc còn phải thu tiếp sang ngày hôm sau
       * 2. Nếu là lịch thu chính thì cần sinh phí quá hạn
       */
      $lichThuNoGocHomSau = app(CutOffTaoLichThuVetNoGocSangNgayHomSauST::class)->run(
        $lichDangHachToan,
        $soTienTrichThanhCong,
        $soTienConPhaiThuTiep
      );

      throw_if(!$lichThuNoGocHomSau, new Exception('Tat toan - khong the tao lich thu no goc sang ngay hom sau'));

      $isChuaThuPhiQuaHan = false;

      if ($lichDangHachToan->isLichThuGocVet()) {
        $isChuaThuPhiQuaHan = app(IsChuaThuPhiQuaHanHomNayST::class)->run($lichDangHachToan);
      }

      if ($lichDangHachToan->isLichThuGocChinh() || $isChuaThuPhiQuaHan) {
				if ( $lichDangHachToan->isKhongPhaiRunDateTuongLai() ) {
					mylog([
						'lich thu phi' => 'rundate khong phai tuong lai',
						'thuc hien sinh phi qua han' => 'ok'
					]);

					$phiQuaHan = $lichDangHachToan->getSoTienPhiTheoLoaiPhi(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN, $soTienConPhaiThuTiep);

					if (!$isCoLichThuPhiQuaHan) {
						$lichThuPhiQuaHanHomSau = app(CutOffTaoLichThuPhiQuaHanSangHomSauST::class)->run(
							$lichDangHachToan,
							$phiQuaHan
						);
					}

					if ($isCoLichThuPhiQuaHan) {
						$lichThuPhiQuaHanDaCongGopTien = app(CongTienQuaHanTask::class)->run(
							$isCoLichThuPhiQuaHan,
							$isCoLichThuPhiQuaHan->time_start,
							$isCoLichThuPhiQuaHan->time_end,
							$isCoLichThuPhiQuaHan->rundate,
							$phiQuaHan
						);
					}


					$this->metaData[] = ['label' => CollectDebtEnum::METADATA_PHI_QUA_HAN, 'value' => $phiQuaHan];
				}

				if ( $lichDangHachToan->isRunDateTuongLai() ) {
					mylog([
						'lich thu phi' => 'rundate LA TUONG LAI',
						'khong duoc sinh phi qua han' => 'YES'
					]);

          $swap = app(SwapLichThuNeuTrichTuongLaiST::class)->run($lichDangHachToan, $lichThuNoGocHomSau, true);
					mylog(['swap' => $swap]);
        }
      }
    }

    return $this->metaData;
  } // End method xuLyCutOffThuGoc

  public function xuLyCutOffThuPhi(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong = 0)
  {
    $xuLyThanhToanPhi = app(XuLyThanhToanPhiFH::class)->run($lichDangHachToan, $soTienTrichThanhCong);

    $soTienPhiConPhaiThuTiep = $lichDangHachToan->request_amount_debit - $soTienTrichThanhCong;

    if ($soTienPhiConPhaiThuTiep > 0) {
      if ($lichDangHachToan->isLichThuPhiChamKy()) {
        $lichThuPhiDaMoveSangHomSau = app(CutOffTaoLichThuVetPhiSangNgayHomSau::class)->run(
          $lichDangHachToan,
          $soTienPhiConPhaiThuTiep
        );
      }

      if ($lichDangHachToan->isLichThuPhiQuaHan()) {
        $isCoLichThuPhiQuaHan = app(IsCoLichThuPhiQuaHanTask::class)->run($lichDangHachToan->contract_code, true);

        if (!$isCoLichThuPhiQuaHan) {
          $lichThuPhiDaMoveSangHomSau = app(CutOffTaoLichThuVetPhiSangNgayHomSau::class)->run(
            $lichDangHachToan,
            $soTienPhiConPhaiThuTiep
          );
        }

        if ($isCoLichThuPhiQuaHan) {
          $lichThuPhiQuaHanDaCongGopTien = app(CongTienQuaHanTask::class)->run(
            $isCoLichThuPhiQuaHan,
            $isCoLichThuPhiQuaHan->time_start,
          	$isCoLichThuPhiQuaHan->time_end,
          	$isCoLichThuPhiQuaHan->rundate,
            $soTienPhiConPhaiThuTiep
          );
        }
      }
    }


    return $this->metaData;
  }
} // End class