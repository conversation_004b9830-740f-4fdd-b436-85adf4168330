<?php

use Illuminate\Support\Facades\Route;

$namespace = 'App\Modules\CollectDebtGateway\Controllers';
Route::group(['middleware' => ['checktoken'/*,'checkip'*/], 'namespace' => $namespace], function () {

    // Route::prefix('/mpos-collect-debt-gateway')->group(function (){
    //     Route::post('/send-debt',['uses' => 'MposCollectDebtGatewayController@sendDebt'])->name('mpos.collect.debt.gateway.send.debt');
    //     Route::post('/cancel-debt',['uses' => 'MposCollectDebtGatewayController@cancelDebt'])->name('mpos.collect.debt.gateway.cancel.debt');
    //     Route::post('/check-debt',['uses' => 'MposCollectDebtGatewayController@checkDebt'])->name('mpos.collect.debt.gateway.check.debt');
    //     Route::post('/check-balance',['uses' => 'MposCollectDebtGatewayController@checkBalance'])->name('mpos.collect.debt.gateway.check.balance');

    // });

    Route::prefix('/partner-collect-debt-gateway')->group(function () {
        Route::post('/send-debt', ['uses' => 'PartnerCollectDebtGatewayController@sendDebt'])->name('partner.collect.debt.gateway.send.debt');
        Route::post('/check-debt', ['uses' => 'PartnerCollectDebtGatewayController@checkDebt'])->name('partner.collect.debt.gateway.check.debt');
        Route::get('/check-balance', ['uses' => 'PartnerCollectDebtGatewayController@checkBalance'])->name('partner.collect.debt.gateway.check.balance');
        Route::post('/cancel-debt', ['uses' => 'PartnerCollectDebtGatewayController@cancelDebt'])->name('partner.collect.debt.gateway.cancel.debt');
        Route::post('/notify-contract-finish', ['uses' => 'PartnerCollectDebtGatewayController@notifyContractFinish'])->name('partner.collect.debt.gateway.notify.contract.finish');
        Route::post('/re-check-debt', ['uses' => 'PartnerCollectDebtGatewayController@reCheckDebt'])->name('partner.collect.debt.gateway.re.check.debt');
    });

    Route::prefix('/partner-notify-collect-debt-gateway')->group(function () {
        Route::post('/receive-notify', ['uses' => 'PartnerNotifyCollectDebtGatewayController@receiveNotify'])->name('partner.notify.collect.debt.gateway.receive.notify');
    });


    Route::prefix('/collect-debt-log')->group(function () {
        Route::get('/getAllByFollow', ['uses' => 'CollectDebtRecoveryGatewayLogController@getAllByFollow'])->name('collect.debt.log.get.all.by.follow');
    });
});
