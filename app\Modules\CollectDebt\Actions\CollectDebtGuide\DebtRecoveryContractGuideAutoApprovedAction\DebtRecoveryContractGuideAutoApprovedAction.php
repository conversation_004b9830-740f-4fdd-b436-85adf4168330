<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\DongBoHopDongVer1SubAction;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareCreateAction\DebtRecoveryShareCreateAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use Exception;

class DebtRecoveryContractGuideAutoApprovedAction
{
	public function run($request)
	{
		$dataResponse = [];

		$collectDebtGuides = CollectDebtGuide::where('status', CollectDebtEnum::GUIDE_STT_MOI_TAO)
																				 ->get();

		throw_if($collectDebtGuides->isEmpty(), new Exception("Không tìm thấy bản ghi mới tạo"));

		foreach ($collectDebtGuides as $collectDebtGuide) {
			$collectDebtShare = $this->createDataShare($collectDebtGuide->toArray());
			if (empty($collectDebtShare)) {
				continue;
			}
			$dataResponse['id'][] = $collectDebtShare['id'];
			$collectDebtGuide->status = CollectDebtEnum::GUIDE_STT_DA_DUYET;
			$collectDebtGuide->save();
		}

		return $dataResponse;
	}

	protected function createDataShare($data)
	{
		$dataResponse = [];

		$params = [];
		$params['contract_code'] = $data['contract_code'];
		$params['description'] = $data['description'];

		if (Helper::jsonValidator($data['other_data'])) {
			$params['other_data'] = json_decode($data['other_data'], true);
			$otherData = $params['other_data'];
			foreach ($otherData as $key => $other) {
				if ($key == 'company' && isset($other['type']) && $other['type'] == 'OTHER') {
					if (isset($other['data']) && !empty($other['data'])) {
						$params['company_data'] = $other['data'];
						break;
					}
				}
			}
		} else {
			$params['other_data'] = $data['other_data'];
		}

		if (Helper::jsonValidator($data['contract_data'])) {
			$params['contract_data'] = json_decode($data['contract_data'], true);
		} else {
			$params['contract_data'] = $data['contract_data'];
		}

		if (Helper::jsonValidator($data['profile_data'])) {
			$params['profile_data'] = json_decode($data['profile_data'], true);
		} else {
			$params['profile_data'] = $data['profile_data'];
		}

		if (Helper::jsonValidator($data['payment_guide'])) {
			$params['payment_guide'] = json_decode($data['payment_guide'], true);
		} else {
			$params['payment_guide'] = $data['payment_guide'];
		}

		$params['list_fee'] = $data['list_fee'];
		$params['created_by'] = $data['created_by'];

		$params['partner_code'] = $data['partner_code'];
		
		$result = app(DebtRecoveryShareCreateAction::class)->run($params);
		if (isset($result['id']) && !empty($result['id'])) {
			$dataResponse['id'] = $result['id'];
		}


		return $dataResponse;
	}
}
