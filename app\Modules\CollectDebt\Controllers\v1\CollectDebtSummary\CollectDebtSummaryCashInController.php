<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary;

use DB;
use App\Lib\Helper;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummarySyncHandleExcessRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryBuildFormRepaymentRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryMinusAmountRepaymentRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySyncHandleExcessAction\DebtRecoverySummarySyncHandleExcessAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryBuildFormRepaymentAction\DebtRecoverySummaryBuildFormRepaymentAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentAction\DebtRecoverySummaryPlusAmountRepaymentAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryMinusAmountRepaymentAction\DebtRecoverySummaryMinusAmountRepaymentAction;

// Làm việc với hệ thống CashIn
class CollectDebtSummaryCashInController extends Controller
{
	public function DebtRecoverySummaryBuildFormRepayment(DebtRecoverySummaryBuildFormRepaymentRequest $request)
	{
		try {
			$buildFormRepaymentResult = app(DebtRecoverySummaryBuildFormRepaymentAction::class)->run($request);
			return $this->successResponse($buildFormRepaymentResult, $request);
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoverySummaryPlusAmountRepayment(DebtRecoverySummaryPlusAmountRepaymentRequest $request)
	{
		DB::beginTransaction();
		try {
			$collectDebtSummary = app(DebtRecoverySummaryPlusAmountRepaymentAction::class)->run($request);
			DB::commit();
			return $this->successResponse($collectDebtSummary->toArray(), $request);
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	// yc nạp hoặc phiếu thu bị hủy, giảm repayment amount
	public function DebtRecoverySummaryMinusAmountRepayment(DebtRecoverySummaryMinusAmountRepaymentRequest $request)
	{
		DB::beginTransaction();
		try {
			$collectDebtSummary = app(DebtRecoverySummaryMinusAmountRepaymentAction::class)->run($request);
			DB::commit();
			return $this->successResponse([
				'id' => $collectDebtSummary->id
			], $request, 200, __('Hủy chuyển ngân thành công'));
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoverySummarySyncHandleExcess(DebtRecoverySummarySyncHandleExcessRequest $request)
	{
		try {
			$collectDebtSummary = app(DebtRecoverySummarySyncHandleExcessAction::class)->run($request);
			return $this->successResponse(['id' => $collectDebtSummary->id], $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
