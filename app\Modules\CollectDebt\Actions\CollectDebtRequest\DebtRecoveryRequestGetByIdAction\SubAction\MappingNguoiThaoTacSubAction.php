<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByIdAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtRequest;

class MappingNguoiThaoTacSubAction
{
	public function run(CollectDebtRequest $collectDebtRequest): CollectDebtRequest
	{
		$requestOtherData = $collectDebtRequest->getRequestOtherData();
		
		$created_by = json_decode($collectDebtRequest->created_by, true);
		$approved_by = json_decode($collectDebtRequest->approved_by, true);
		$sended_by = json_decode($collectDebtRequest->sended_by, true);
		$canceled_by = json_decode($collectDebtRequest->canceled_by, true);
		$checked_by = json_decode($collectDebtRequest->checked_by, true);

		foreach ($requestOtherData as &$item) {
			switch ($item['type']) {
				case 'CREATED':
					$item['user'] = $created_by['username'] ?? 'cronjob';
					break;
					
				case 'APPROVED':
					$item['user'] = $approved_by['username'] ?? 'cronjob';
					break;

				case 'SENDER':
					$item['user'] = $sended_by['username'] ?? 'cronjob';
					break;

				case 'CANCEL':
					$item['user'] = $canceled_by['username'] ?? 'cronjob';
					break;

				case 'CHECK_PAYMENT':
					$item['user'] = $checked_by['username'] ?? 'cronjob';
					break;

				case 'APPROVED_REDUCE_1':
					$item['user'] = $approved_by['username'] ?? 'cronjob';
					break;

				case 'APPROVED_REDUCE_2':
					$p = $collectDebtRequest->isNguoiDuyet2();

          if (is_string($p)) {
            $p = json_decode($p, true);
          }

					$item['user'] = $p['username'] ?? 'cronjob';
					break;

				case 'RECORED':
					$item['user'] = 'cronjob';
					break;

				case 'ACCOUNTING':
					$item['user'] = 'cronjob';
					break;

				case 'CUTOFF':
					$item['user'] = 'cronjob';
					break;

				case 'OTHER':
					$item['user'] = 'cronjob';
					break;

				case 'CREATE_REDUCE':
					$item['user'] = $created_by['username'] ?? 'cronjob';
					break;

				case 'CREATE_DEBT_NOW':
					$item['user'] = $created_by['username'] ?? 'cronjob';
					break;

				case 'CREATE_DEBT_MANUAL':
					$item['user'] = $created_by['username'] ?? 'cronjob';
					break;

				case 'CREATE_DEBT_DOCUMENT':
					$item['user'] = $created_by['username'] ?? 'cronjob';
					break;

				case 'REQUEST_ADJUSTMENT':
					$item['user'] = 'cronjob';
					break;

				case 'RECEIVER':
					$item['user'] = 'cronjob';
					break;

				case 'RE_CHECK_PAYMENT':
					$item['user'] = 'cronjob';
					break;

				case 'EXCESS_REQUEST_WITH_NO_PLAN':
					$item['user'] = 'cronjob';
					break;

				case 'OPEN_AND_MINUS_FREEZE':
					$item['user'] = 'cronjob';
					break;

				case 'RECHECK_DEBT_NOW_REQUEST':
					$item['user'] = 'cronjob';
					break;

				default:
					$item['user'] = 'cronjob';
					break;
			}
		}

		$collectDebtRequest->other_data = json_encode($requestOtherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
		return $collectDebtRequest;
	}
} // End class
