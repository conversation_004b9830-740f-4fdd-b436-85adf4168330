<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use Exception;

class TaoLichThuPhiQuaHanMLLTask
{
  public function run(CollectDebtSchedule $lichDangMoLai, float $phiQuaHan = 0, CollectDebtShare $collectDebtShare): CollectDebtSchedule
  {
    $scheduleParam = [
      'profile_id'           => $lichDangMoLai->profile_id,
      'contract_code'        => $lichDangMoLai->contract_code,
      'contract_type'        => $lichDangMoLai->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
      'debit_begin'          => $phiQuaHan,
      'debit_end'            => 0,
      'rundate'              => date('Ymd'),
      'time_start'           => now()->timestamp,
      'time_end'             => now()->endOfDay()->timestamp,
      'amount_period_debit'  => $lichDangMoLai->amount_period_debit,
      'request_amount_debit' => $phiQuaHan,
      'success_amount_debit' => 0,
      'other_data'           => json_encode([
        [
          'type' => 'OTHER',
          'time_modified' => time(),
          'data' => [
            'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_QUA_HAN,
            'fee_config' => $lichDangMoLai->getCauHinhPhiShared(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN, $collectDebtShare),
            'request_created_channel' => '',
          ],
          'note' => 'Sinh phí quá hạn'
        ]
      ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_UNICODE),
      'description'          => $lichDangMoLai->description,
      'is_settlement'        => $lichDangMoLai->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangMoLai->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangMoLai->cycle_number,
      'master_id'         => $lichDangMoLai->master_id,
    ];

    $lichThuPhiQuaHan = CollectDebtSchedule::forceCreate($scheduleParam);

		if (!$lichThuPhiQuaHan) {
			mylog(['loi tao lich thu phi qua han' => $lichThuPhiQuaHan]);
			throw new Exception('loi tao lich thu phi qua han');
		}
		
		return $lichThuPhiQuaHan;
  }
}
