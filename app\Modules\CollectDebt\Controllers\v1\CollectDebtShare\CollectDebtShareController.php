<?php
namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtShare;

use App\Lib\Helper;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareCreateAction\DebtRecoveryShareCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareGetContractCodeAction\DebtRecoveryShareGetContractCodeAction;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareUpdateAction\DebtRecoveryShareUpdateAction;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\ColelctDebtShare\DebtRecoveryShareCreateRequest;
use App\Modules\CollectDebt\Requests\v1\ColelctDebtShare\DebtRecoveryShareGetByContractCodeRequest;
use App\Modules\CollectDebt\Requests\v1\ColelctDebtShare\DebtRecoveryShareUpdateRequest;

class CollectDebtShareController extends Controller
{
    public function create(DebtRecoveryShareCreateRequest $request)
	{
		try {
            $data = $request->json('data');
			$collectDebtRequest = app(DebtRecoveryShareCreateAction::class)->run($data);
			return $this->successResponse($collectDebtRequest, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

    public function update(DebtRecoveryShareUpdateRequest $request)
	{
		try {
			$data = $request->json('data');
			$collectDebtRequest = app(DebtRecoveryShareUpdateAction::class)->run($data);
			return $this->successResponse($collectDebtRequest, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

    public function getByContractCode(DebtRecoveryShareGetByContractCodeRequest $request)
	{
		try {
			$data = $request->json('data');
			$collectDebtRequest = app(DebtRecoveryShareGetContractCodeAction::class)->run($data);
			return $this->successResponse($collectDebtRequest, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
}