<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtRequest;

use Carbon\Carbon;

trait RequestExpiredable
{
  public function isYeuCauConHan(): bool {
    return $this->time_expired > time();
  }

  public function isYeuCauDaHetHan(): bool {
    return $this->time_expired <= time();
  }

	public function isYeuCauTrichLichThuQuaKhu(): bool {
		$timeExpiredAsDate = Carbon::createFromTimestamp($this->time_expired);
		return empty($this->partner_transaction_id) && $this->amount_receiver == 0 && now()->gt($timeExpiredAsDate);
	}

	public function isYeuCauKeoDaiTimeExpired(): bool {
		$otherData = $this->getRequestOtherData();
		return collect($otherData)->contains(function ($item) {
			return $item['type'] == 'SET_LONG_TIME_EXPIRED';
		});
	}
} // End class
