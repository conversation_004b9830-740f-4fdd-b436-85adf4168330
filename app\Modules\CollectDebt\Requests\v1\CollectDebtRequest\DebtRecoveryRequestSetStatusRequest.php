<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\CollectDebtTrichNgayRule;

class DebtRecoveryRequestSetStatusRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:1'],
      'data.status' => [
        'required', 
        'string', 
        Rule::in([
          CollectDebtEnum::REQUEST_STT_MOI_TAO,
          CollectDebtEnum::REQUEST_STT_DA_DUYET,
          CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
          CollectDebtEnum::REQUEST_STT_TU_CHOI,
          CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA,
        ])
      ],
      'data.user_request_id' => ['required', 'string', 'max:255'],
      'data.description' => ['nullable', 'string', 'max:255'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['user_request_id'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['user_request_id']);
    $this->merge($params);
  }
} // End class
