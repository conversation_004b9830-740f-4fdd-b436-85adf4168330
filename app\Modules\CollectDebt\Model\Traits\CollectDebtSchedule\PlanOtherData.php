<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

use Illuminate\Support\Arr;

trait PlanOtherData
{
  public function getPlanOtherData(): array
  {
    if (empty($this->other_data)) {
      return [];
    }
    return json_decode($this->other_data, true);
  }

  public function putPlanOtherData($data = []): string
  {
    $otherData = $this->getPlanOtherData();
    $otherData[] = $data;
    return json_encode($otherData, JSON_UNESCAPED_UNICODE);
  }

  public function setOtherData(string $key, $values): string
  {
    $otherData = $this->getPlanOtherData();
    
    if (empty($otherData[$key])) {
      $otherData[$key] = $values;
    } else {
      $explode = explode(',', $otherData[$key]);
      $explode[] = $values;
      $otherData[$key] = implode(',', $explode);
    }

    return json_encode($otherData, JSON_UNESCAPED_UNICODE);
  }

  public function setTaoLichQuaKenhNao(string $paymentMethodCode='MPOS') {
    $otherData = $this->getPlanOtherData();
    
    $otherData = collect($otherData)->transform(function ($item) use ($paymentMethodCode) {
      if ($item['type'] == 'OTHER') {
        $explode = explode(',', $item['data']['request_created_channel']);
        $explode[] = $paymentMethodCode;
        $explode = array_values(array_filter($explode));

        $item['data']['request_created_channel'] = implode(',', $explode);
      }

      return $item;
    })->toJson(JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

    return $otherData;
  }

  public function getRequestByPaymentMethodCodeOtherData(string $paymentMethodCode) {
    $otherData = $this->getPlanOtherData();
    
    $requestByPaymentMethodCode = collect($otherData)->filter(function ($item) {
      return $item['type'] == 'REQUEST';
    })->first(function ($item) use ($paymentMethodCode) {
      return $item['data']['payment_method_code'] == $paymentMethodCode;
    });

    return $requestByPaymentMethodCode;
  }


  public function getOtherDataTypeIndex(string $type): int {
		$otherData = $this->getPlanOtherData();
		return collect($otherData)->search(function ($item) use ($type) {
			return $item['type'] == $type;
		});
	}

  public function resetKenhThu() {
    $index = $this->getOtherDataTypeIndex('OTHER');

    $otherData = $this->getPlanOtherData();
    $typeOther = collect($otherData)->where('type', 'OTHER')->first();
    $typeOther['data']['request_created_channel'] = '';
    $otherData[$index] = $typeOther;
    
    return json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
  }
} // End class