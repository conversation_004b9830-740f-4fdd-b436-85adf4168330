<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\SubAction;

use App\Modules\EmailRemind\Actions\ExecuteSendEmailAction\ExecuteGetEmployeeByContractCodeAction;
use App\Modules\EmailRemind\Actions\ExecuteSendEmailAction\ExecuteSendEmailAction;
use Exception;

class SendMailDueDateSubAction
{
    public function run($collectEvent)
    {
        $dataResponse = [];

        $input = [
            'time_updated' => time(),
            'status' => config('collect_debt_email_remind_config.debt_contract_event_status.sending_request'),
        ];
        $collectEvent->update($input);
        $collectEvent = $collectEvent->refresh();
        throw_if(!$collectEvent, new Exception('Không thể cập nhật được bản ghi'));

        $sendMail = $this->__sendMail($collectEvent);
        if (empty($sendMail)) {
            $input = [
                'time_updated' => time(),
                'description' => sprintf('Lỗi không gửi được mail sang hệ thống chăm sóc - %s',request('api_request_id')),
                'status' => config('collect_debt_email_remind_config.debt_contract_event_status.failed'),
            ];
            $collectEvent->update($input);
            return $dataResponse;
        }

        $input = [
            'time_updated' => time(),
            'time_sented' => time(),
            'description' => 'Đã gửi mail sang hệ thống chăm sóc',
            'status' => config('collect_debt_email_remind_config.debt_contract_event_status.created_request'),
        ];
        $collectEvent->update($input);

        return $dataResponse;
    }

    protected function __sendMail($event)
    {
        $dataResponse = [];
        if (empty($event->content)) {
            return $dataResponse;
        }
        $data = json_decode($event->data, true);
        $merchant = isset($data['profile']) && isset($data['profile']['merchant']) ? $data['profile']['merchant'] : [];
        $contract = isset($data['contract']) ? $data['contract'] : [];
        if (empty($merchant) && empty($contract)) {
            return $dataResponse;
        }

        $arrCc = [];

        $resEmploy = app(ExecuteGetEmployeeByContractCodeAction::class)->run($contract['contract_code']);
        if (!empty($resEmploy) && is_array($resEmploy)) {
            foreach ($resEmploy as $key => $employee) {
                if (in_array($key, config('collect_debt_email_remind_config.mail.code_sender'))) {
                    if (!empty($employee) && is_array($employee)) {
                        foreach ($employee as $e) {
                            array_push($arrCc, [
                                'name' => '',
                                'identifier_account' => $e,
                                'identifier_data' => ''
                            ]);
                        }
                    }
                }
            }
        }

				mylog(['Thong tin can CC la' => $arrCc]);

				if (empty($arrCc)) {
					mylog(['khong co thong tin cc' => 'yes']);
					return $dataResponse;
				}

        if(!empty($arrCc) && is_array($arrCc)) {
            $arrCc = array_unique($arrCc, SORT_REGULAR);
        }

        $listTo = [
            [
                'name' => $merchant['fullname'],
                'identifier_account' => $merchant['email'],
                'identifier_data' => ''
            ]
        ];

        $subject = sprintf('NextLend Thông Báo đến `%s` Tình Trạng Hoàn Tiền Ứng Vốn Kinh Doanh Ngày %s', strtoupper($merchant['business_representative']), date('d-m-Y'));

        $sendMailResult = app(ExecuteSendEmailAction::class)->run(
            $subject,
            $event->content,
            config('collect_debt_email_remind_config.mail.NHAC_NO_THANH_TOAN.sender'),
            $listTo,
            $arrCc,
            $event->category_care_code,
            sprintf('%s|STH-%s', $contract['contract_code'], time())
        );

        if (!empty($sendMailResult) && isset($sendMailResult['id']) && !empty($sendMailResult['id'])) {
            $dataResponse['id'] = $sendMailResult['id'];
        }

        return $dataResponse;
    }
}
