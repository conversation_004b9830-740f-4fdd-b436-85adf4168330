<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\HopDongChuaTatToanRule;

class DebtRecoveryRequestCancelAutoTypeRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.request_id' => ['present', 'array'],
      'data.request_id.*' => ['numeric'],
      'data.canceled_by' => ['required','string', 'max:255'],
      'data.time_canceled' => ['required'],
      'data.reason' => ['required', 'string', 'max:255'],
      'data.contract_code' => [
        'required', 
        'string', 
        'max:50', 
        new HopDongChuaTatToanRule()
      ]
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['time_canceled'] = time();
    $params['data']['canceled_by'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['canceled_by']);
    $this->merge($params);
  }
} // End class
