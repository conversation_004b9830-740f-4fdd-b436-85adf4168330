<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoverySummaryTurnOnContractMustSyncAt23hAction
{
	public function run()
	{
		/**
		 * [Update 01.08.2024]
		 * Job này sẽ call lúc 23h và lúc 1h, để đảm bảo không bị lọt bản ghi do biến dayCheck (rundate) theo luồng cũ 
		 * và nặng (do select nhiều)
		 * 
		 * Chỗ này chỉ cần update summary cho các bản ghi chưa tất toán là xong
		 */
		$updateSummary = CollectDebtSummary::query()
			->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
			->update([
				'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO,
				'time_updated' => now()->timestamp
			]);


		mylog(['So luong ban ghi da dong bo la' => $updateSummary]);
		return ['so_luong_hop_dong_bat_dong_bo' => $updateSummary];
	}
} // End class
