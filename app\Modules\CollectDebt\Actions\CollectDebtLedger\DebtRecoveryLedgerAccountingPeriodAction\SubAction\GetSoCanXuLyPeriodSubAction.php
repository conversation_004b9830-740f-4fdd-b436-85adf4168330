<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingPeriodAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class GetSoCanXuLyPeriodSubAction
{
  public function run()
  {
    $ledgers = CollectDebtLedger::where('status_period', CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY)
                                ->orderBy('id', 'DESC')
                                ->limit(3)
                                ->get();

    return $ledgers;
  }
}
