<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction;

use DB;
use Exception;
use App\Lib\Logs;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\DebtNowEnum;
use App\Modules\CollectDebt\Model\CollectDebtNotifyDebtNow;
use App\Modules\EmailRemind\Actions\ExecuteSendEmailAction\ExecuteSendEmailAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotification\PushNotificationAction\PushNotificationAction;

class SendNotifyViaChannelsAction
{
	public array $returnData = [];
	public array $exceptIds = [];
	public array $logData = [];

	public function run()
	{
		for ($i = 1; $i <= 10; $i++) {
			try {
				$debtNow = $this->handle();

				if ($debtNow == 'EMPTY') {
					$this->returnData[] = $debtNow;
					break;
				}

				if (optional($debtNow)->id) {
					$this->returnData[] = $debtNow->id;
				}
			} catch (\Throwable $th) {
				@TelegramAlert::sendMessage(Helper::traceError($th));
				continue;
			}
		}

		return $this->returnData;
	}

	public function handle()
	{
		$this->logData = [];

		$debtNow = CollectDebtNotifyDebtNow::query()
			->where('status', DebtNowEnum::STT_MOI_TAO)
			->where('push_notify_count', '<', 5);

		if (!empty($this->exceptIds)) {
			$debtNow = $debtNow->whereNotIn('id', $this->exceptIds);
		}

		$debtNow = $debtNow->first();

		if (!$debtNow) {
			return 'EMPTY';
		}

		$key = sprintf('[BanGhiDebtNow_%s]', $debtNow->id);
		$this->logData[$key][] = 'Bản ghi debt now đang xử lý: ' . $debtNow->id;
		$this->logData[$key][] = ['api_request_id' => request('api_request_id')];

		$this->exceptIds[] = $debtNow->id;

		$updateProcessing = CollectDebtNotifyDebtNow::query()->where([
			'id' => $debtNow->id,
			'status' => DebtNowEnum::STT_MOI_TAO
		])->update([
			'status' => DebtNowEnum::STT_DANG_XU_LY,
			'updated_at' => now(),
			'push_notify_count' => DB::raw('push_notify_count+1')
		]);

		if (!$updateProcessing) {
			$this->logData[$key][] = 'Lỗi không cập nhật được thành đang xử lý';
			throw new Exception('Lỗi không cập nhật được thành đang xử lý');
		}

		$debtNow->refresh();

		if ($debtNow->push_notify_count == 1) {
			// Update lên đang xử lý => xử lý tạo bản ghi tele
			$pushNotify = app(PushNotificationAction::class)->run(
				$debtNow,
				DebtNowEnum::TYPE_PUSH_NOTIFY,
				$debtNow->subject,
				[]
			);

			$this->logData[$key][] = 'Đã push notify thành công';
		}

		DB::beginTransaction();
		try {
			// Thực hiện gửi mail
			$sendMailResult = app(ExecuteSendEmailAction::class)->run(
				$debtNow->subject,
				$debtNow->buildMailContent(),
				(array)$debtNow->sender,
				$debtNow->buildMailTo(),
				$debtNow->buildMailCC(),
				'WARNING_DEBT_NOW',
				'CanhBaoTrichNgay_' . $debtNow->id,
			);

			// Thành công
			if (!empty($sendMailResult['id'])) {
				$this->logData[$key][] = 'Gửi mail thành công với id mail là: ' . $sendMailResult['id'];

				$updateProcessed = CollectDebtNotifyDebtNow::query()->where([
					'id' => $debtNow->id,
					'status' => DebtNowEnum::STT_DANG_XU_LY
				])->update([
					'status' => DebtNowEnum::STT_DA_XU_LY,
					'updated_at' => now()
				]);

				if (!$updateProcessed) {
					$this->logData[$key][] = 'Lỗi không cập nhật đc về đã xử lý';
				}
			} else {
				$this->logData[$key][] = 'Gửi mail thất bại';

				// Thất bại, đưa về trạng thái mới tạo
				$updateNew = CollectDebtNotifyDebtNow::query()->where([
					'id' => $debtNow->id,
					'status' => DebtNowEnum::STT_DANG_XU_LY
				])->update(['status' => DebtNowEnum::STT_MOI_TAO]);

				if (!$updateNew) {
					$this->logData[$key][] = 'Lỗi không cập nhật đc về mới tạo';
				}
			}

			DB::commit();
		} catch (\Throwable $th) {
			$this->logData[$key][] = 'Lỗi: ' . Helper::traceError($th);

			$updateNew = CollectDebtNotifyDebtNow::query()->where([
				'id' => $debtNow->id,
				'status' => DebtNowEnum::STT_DANG_XU_LY
			])->update(['status' => DebtNowEnum::STT_MOI_TAO]);
			DB::rollBack();
		} finally {
			$r = (new Logs())->writeFileLog($this->logData);
			return $debtNow;
		}
	}
} // End class
