<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtNotifyDebtNow;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CollectDebtNotifyDebtNowRequest extends FormRequest
{
    public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {

    return [
      'data.contract_code' => ['required', 'string', 'max:50'],
      'data.to' => ['required', 'string'],
      'data.cc' => ['required', 'string'],
      'data.sender' => ['required', 'string'],
      'data.subject' => ['required', 'string'],
      'data.description' => ['nullable', 'string', 'max:300'],
      'data.other_data' => ['nullable'],
    ];
  }

}