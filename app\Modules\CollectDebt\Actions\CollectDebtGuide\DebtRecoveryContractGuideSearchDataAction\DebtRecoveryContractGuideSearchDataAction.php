<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideSearchDataAction;

use App\Modules\CollectDebt\Model\CollectDebtGuide;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DebtRecoveryContractGuideSearchDataAction
{
  public function run(Request $request)
  {
    $collectDebtGuilds = CollectDebtGuide::query()
																				 ->with('collectDebtSummary:id,contract_code,status_contract');
    
    if (!empty($request->json('data.filter.contract_code'))) {
      $collectDebtGuilds->where('contract_code', $request->json('data.filter.contract_code'));
    }

		if (!empty($request->json('data.filter.contract_type'))) {
      $collectDebtGuilds->where('contract_type', $request->json('data.filter.contract_type'));
    }


    if (!empty($request->json('data.filter.status'))) {
      $collectDebtGuilds->where('status', $request->json('data.filter.status'));
    }

    if (!empty($request->json('data.filter.status_settled'))) {
      $collectDebtGuilds->whereHas('collectDebtSummary', function ($query) use ($request) {
				return $query->where('status_contract', $request->json('data.filter.status_settled'));
			});
    }

    if (!empty($request->json('data.filter.profile_ids'))) {
      $collectDebtGuilds->whereIn('profile_id', $request->json('data.filter.profile_ids'));
    }

    $createFrom = $request->json('data.filter.create_from');
    
    if (!empty($createFrom)) {
      $collectDebtGuilds->where('time_created', '>=', Carbon::createFromFormat('d-m-Y', $createFrom)->startOfDay()->timestamp);
    }

    $createTo = $request->json('data.filter.create_to');
    if (!empty($createTo)) {
      $collectDebtGuilds->where('time_created', '<=', Carbon::createFromFormat('d-m-Y', $createTo)->endOfDay()->timestamp);
    }

		$partnerCode = $request->json('data.filter.partner_code', '');
		if (!empty($partnerCode)) {
			$collectDebtGuilds->where('partner_code', trim(strtoupper($partnerCode)));
		}

    $collectDebtGuilds = $collectDebtGuilds->latest('id')
                                           ->paginate( $request->json('data.limit', 10), ['*'], 'page', $request->json('data.page', 1) );
    return $collectDebtGuilds;
  }
} // End class
