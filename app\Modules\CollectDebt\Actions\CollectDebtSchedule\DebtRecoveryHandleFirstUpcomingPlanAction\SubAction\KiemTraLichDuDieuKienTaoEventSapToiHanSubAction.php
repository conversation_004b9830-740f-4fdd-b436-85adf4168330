<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use Illuminate\Database\Eloquent\Collection;

class KiemTraLichDuDieuKienTaoEventSapToiHanSubAction
{
	/**
	 * 1. Chỉ thông báo sắp tới hạn khi HĐ chưa có sổ hoặc toàn bộ sổ đang nằm trong DB phải về
	 * 		trạng thái là đã hạch toán tổng hợp
	 * 
	 * 2. Không có lịch hoặc toàn bộ lịch trước lịch hiện tại phải về trạng thái cuối
	 *
	 * @param CollectDebtSchedule $plan
	 * @return boolean
	 */
	public function run(CollectDebtSchedule $plan): bool
	{
		$collectDebtLedgers = CollectDebtLedger::query()
																					 ->where('contract_code', $plan->contract_code)
																					 ->select(['id', 'status_summary'])
																					 ->get();
			
		if ($plan->contract_type == CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_TRICH_NGAY) {
			$listLichThuCungHopDong = CollectDebtSchedule::query()
																								 ->where('contract_code', $plan->contract_code)
																								 ->where('id', '<', $plan->id)
																								 ->where('rundate', '<', date('Ymd'))
																								 ->select(['id', 'contract_code', 'status', 'time_start', 'time_end', 'rundate'])
																								 ->get();
			
		}

		if ($plan->contract_type == CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY) {
			$listLichThuCungHopDong = CollectDebtSchedule::query()
																								 ->where('contract_code', $plan->contract_code)
																								 ->where('id', '<', $plan->id)
																								 ->whereColumn('id', '=', 'master_id')
																								 ->select(['id', 'contract_code', 'status', 'time_start', 'time_end', 'rundate'])
																								 ->get();
		}
		

		$isToanBoLichVeTrangThaiCuoi = false;

		if ($listLichThuCungHopDong->isEmpty()) {
			$isToanBoLichVeTrangThaiCuoi = true;
		}else {
			$isToanBoLichVeTrangThaiCuoi = $listLichThuCungHopDong->every(function (CollectDebtSchedule $p) {
				return $p->status == CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH;
			});
		}


		$isToanBoSoDaHachToan = false;
		
		if ($collectDebtLedgers->isEmpty()) {
			$isToanBoSoDaHachToan = true;
		}else {
			$isToanBoSoDaHachToan = $collectDebtLedgers->every(function (CollectDebtLedger $ledger) {
				return $ledger->status_summary == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT;
			});
		}

		

		return $isToanBoLichVeTrangThaiCuoi && $isToanBoSoDaHachToan;
	}
} // End class