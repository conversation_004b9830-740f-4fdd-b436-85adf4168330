<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\FeeHandler;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class XuLySinhMovePhiSangHomSauFH
{
  public function run(CollectDebtSchedule $lichDangHachToan, float $soPhiConPhaiThuTiep = 0, $lichCoRunDateLaHomSau=null) {
    // Trong này other_data phải thể hiện rõ, số tiền phí cần thu tiếp là phí nào
    if ($lichDangHachToan->isLichThuPhiChamKy()) {
      $paramThuPhi = [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_CHAM_KY,
          'fee_config' => $lichDangHachToan->getCauHinhPhiTheoLoai(CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY),
          'request_created_channel' => '',
        ],
        'note' => 'Thu tiếp phí chậm kỳ'
      ];
    }

    if ($lichDangHachToan->isLichThuPhiQuaHan()) {
      $paramThuPhi = [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_QUA_HAN,
          'fee_config' => $lichDangHachToan->getCauHinhPhiTheoLoai(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN),
          'request_created_channel' => '',
        ],
        'note' => 'Thu tiếp phí quá hạn'
      ];
    }
    
    $otherData = json_encode([
      $paramThuPhi,
    ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

    $feeScheduleParam = [
      'profile_id'           => $lichDangHachToan->profile_id,
      'contract_code'        => $lichDangHachToan->contract_code,
      'contract_type'        => $lichDangHachToan->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
      'debit_begin'          => $soPhiConPhaiThuTiep,
      'debit_end'            => 0,
      'rundate'              => $lichDangHachToan->rundate_as_date->copy()->addDay()->format('Ymd'),
      'time_start'           => $lichDangHachToan->time_start_as_date->copy()->addDay()->startOfDay()->timestamp,
      'time_end'             => $lichDangHachToan->time_end_as_date->copy()->addDay()->endOfDay()->timestamp,
      'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
      'request_amount_debit' => $soPhiConPhaiThuTiep,
      'success_amount_debit' => 0,
      'other_data'           => $otherData,
      'description'          => $lichDangHachToan->collectDebtSchedule,
      'is_settlement'        => $lichDangHachToan->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangHachToan->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangHachToan->cycle_number,
      'master_id'         => $lichDangHachToan->master_id,
    ];

    // if ($lichCoRunDateLaHomSau && $lichCoRunDateLaHomSau->isLichTatToan()) {
    //   $feeScheduleParam['cycle_number'] = $lichCoRunDateLaHomSau->cycle_number;
    // }

    $feeSchedule = CollectDebtSchedule::forceCreate($feeScheduleParam);
    return $feeSchedule;
  }
} // End class