<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class CutOffGetLichThuCoRundateLaHomSauST
{
  public function run(CollectDebtSchedule $lichDangHachToan)
  {
    $runDateHomSau = $lichDangHachToan->rundate_as_date->copy()->addDay()->format('Ymd');

    $lichThuCoRunDateHomSay = CollectDebtSchedule::where('contract_code', $lichDangHachToan->contract_code)
      ->where('rundate', $runDateHomSau)
      ->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
      ->first();

    return $lichThuCoRunDateHomSay;
  }
} // End class