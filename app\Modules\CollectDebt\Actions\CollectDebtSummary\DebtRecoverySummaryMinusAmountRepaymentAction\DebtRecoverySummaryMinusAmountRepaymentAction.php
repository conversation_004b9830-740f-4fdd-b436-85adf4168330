<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryMinusAmountRepaymentAction;

use DB;
use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryMinusAmountRepaymentRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentAction\SubAction\TaoYeuCauNapTienSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetAmountCanRefundAction\DebtRecoverySummaryGetAmountCanRefundAction;

class DebtRecoverySummaryMinusAmountRepaymentAction
{
	public function run(DebtRecoverySummaryMinusAmountRepaymentRequest $request)
	{
		$collectDebtSummary = CollectDebtSummary::query()
			->where('contract_code', $request->json('data.contract_code'))
			->lock('FOR UPDATE SKIP LOCKED')
			->first();

		throw_if(!$collectDebtSummary, new Exception('HĐ không tồn tại trong hệ thống'));

		throw_if(
			$collectDebtSummary->total_amount_repayment_debt < $request->getSoTienYeuCauNapBiHuy(),
			new Exception('Số tiền chuyển ngân không đủ để hủy tiền của yc nạp. Cần check lại ngay')
		);

		$updateAffectedRow = CollectDebtSummary::query()
																					 ->where('id', $collectDebtSummary->id)
																					 ->update([
																						'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO,
																						'total_amount_repayment_debt' => DB::raw('total_amount_repayment_debt - ' . $request->getSoTienYeuCauNapBiHuy())
																					]);
		
		throw_if($updateAffectedRow == 0, new Exception('Lỗi câu lệnh SQL không thể giảm tiền chuyển ngân'));
		return $collectDebtSummary;	
	}
} // End class