<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task\GetThoiDiemLichInfoMLLTask;
use Exception;

class TaoLichThuVetNoGocMLLTask
{
  public function run(CollectDebtSchedule $lichDangMoLai, float $soTienConPhaiThuCuaLich = 0, float $soTienDaThuThanhCong = 0)
  {
    $debitBegin = $lichDangMoLai->debit_begin - $soTienDaThuThanhCong;
    $thoiDiemLichInfo = app(GetThoiDiemLichInfoMLLTask::class)->run($lichDangMoLai);

    $scheduleParam = [
      'profile_id'           => $lichDangMoLai->profile_id,
      'contract_code'        => $lichDangMoLai->contract_code,
      'contract_type'        => $lichDangMoLai->contract_type,
      'type'                 => $thoiDiemLichInfo['type'],
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
      'debit_begin'          => $debitBegin,
      'debit_end'            => $lichDangMoLai->debit_end,
      'rundate'              => $thoiDiemLichInfo['rundate'],
      'time_start'           => $thoiDiemLichInfo['time_start'],
      'time_end'             => $thoiDiemLichInfo['time_end'],
      'amount_period_debit'  => $lichDangMoLai->amount_period_debit,
      'request_amount_debit' => $soTienConPhaiThuCuaLich,
      'success_amount_debit' => 0,
      'other_data'           => json_encode([
        [
          'type' => 'OTHER',
          'time_modified' => time(),
          'data' => [
            'request_created_channel' => '',
          ],
          'note' => 'Thu vét nợ gốc',
        ]
      ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
      'description'          => $lichDangMoLai->description,
      'is_settlement'        => $lichDangMoLai->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangMoLai->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangMoLai->cycle_number,
      'master_id'            => $lichDangMoLai->master_id,
    ];

    $lichThuVetNoGoc =  CollectDebtSchedule::forceCreate($scheduleParam);

		if (!$lichThuVetNoGoc) {
			mylog(['Loi khong tao duoc lich thu vet no goc' => $lichThuVetNoGoc]);
			throw new Exception('Loi khong tao duoc lich thu vet no goc');
		}

		return $lichThuVetNoGoc;
  }
} // End class