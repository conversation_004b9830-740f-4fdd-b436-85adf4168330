<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\SubAction;

use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingAction\SubAction\XuLyThuThieuTienTask\GroupRequestIntoScheduleTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSetStatusAction\DebtRecoveryLedgerSetStatusAction;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerSetStatusActionRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummarySetByAccountingRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySetByAccounting\DebtRecoverySummarySetByAccounting;
use Exception;

class HandlerAccountingSummarySA {

    use \App\Traits\ApiResponser;

    public function run($ledgersWithTheirSchedules) {
        $request = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_summary' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
        ];
        $request->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($request);
        if (get_class($update) == CollectDebtLedger::class) {
            /*
             * Tính ra dữ liệu rồi gọi vào phần tịnh tiến
             * Tổng tiền thu được
             * Tổng phí thu được 
             * Có giảm phí hay ko 
             * Tổng phí QH đã giảm 
             * Tổng phí QK đã giảm
             */
            $checkInfo = false;
            $other_data = json_decode($ledgersWithTheirSchedules->other_data, true);
            if ($other_data) {
                foreach ($other_data as $key => $value) {
                    if (isset($value['type']) && $value['type'] == 'SUMMARY' && isset($value['data']) && $value['data']) {
                        $info = $value['data'];
                        $contractCode = $ledgersWithTheirSchedules->contract_code;
                        $totalAmount = $info['total_amount_paid'];
                        $amountPaid = $info['amount_paid'];
                        $feeOverdueCycle = $info['fee_overdue_cycle'];
                        $feeOverdue = $info['fee_overdue'];
                        $feeOverdueReduction = $info['fee_overdue_reduction'];
                        $feeOverdueCycleReduction = $info['fee_overdue_cycle_reduction'];
                        $feeOuduePaid = $info['fee_overdue_paid'];
                        $feeCyclePaid = $info['fee_overdue_cycle_paid'];
                        $data_over_cycle = [];
                        $data_giam_phi_qua_ky= [];
                        if(isset($info['data_over_cycle'])) {
                            $data_over_cycle = $info['data_over_cycle'];
                        }
                        if(isset($info['data_giam_phi_qua_ky'])) {
                            $data_giam_phi_qua_ky = $info['data_giam_phi_qua_ky'];
                        }
                        $checkInfo = true;
                        
                        break;
                    }
                }
            }
            $totalFeePaid = ($feeOuduePaid + $feeCyclePaid);
            if (!$checkInfo) {
                $message = 'Thông tin không đầy đủ không thể sử lý';
                throw new Exception($message);
            }


            $requestData = new DebtRecoverySummarySetByAccountingRequest();
            $inputs['data'] = [
                'contract_code' => $contractCode,
                'total_amount_paid' => $amountPaid, // so tien thanh toan cho nợ gốc
//                'amount_paid' => $amountPaid,
                'total_fee_paid' => $totalFeePaid,// so phi đã trả
                'fee_overdue_paid' => $feeOuduePaid,
                'fee_overdue_cycle_paid' => $feeCyclePaid,
                'fee_overdue_reduction' => $feeOverdueReduction,
                'fee_overdue_cycle_reduction' => $feeOverdueCycleReduction,
                'fee_overdue_cycle' => $feeOverdueCycle,
                'fee_overdue' => $feeOverdue,
                'data_over_cycle'=> $data_over_cycle,
                'data_giam_phi_qua_ky'=> $data_giam_phi_qua_ky,
            ];
            $requestData->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
            $update = (new DebtRecoverySummarySetByAccounting())->run($requestData);
            if (get_class($update) == \App\Modules\CollectDebt\Model\CollectDebtSummary::class) {
                return $update;
            } else {
                $message = 'Lỗi DebtRecoverySummarySetByAccounting run';
                throw new Exception($message);
            }
        } else {
            $message = 'Lỗi DebtRecoveryLedgerSetStatusAction setStatusOther';
            throw new Exception($message);
        }
    }

}

// End class