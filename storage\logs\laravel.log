[2025-08-12 09:39:58] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120939_DFEp6z"}} 
[2025-08-12 09:39:58] local.INFO: [checkDebt--->NL25080858397] {"req--->":{"nextlend_request_id":58397,"partner_code":"MPOS","merchantId":"50722906","lendingRequestId":"NL25080858397","debitAmount":9593983.0,"requestTime":"20250812093958","lendingId":"MPOS-2506121009382-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25080858386] {"req--->":{"nextlend_request_id":58386,"partner_code":"MPOS","merchantId":"50728581","lendingRequestId":"NL25080858386","debitAmount":6500000.0,"requestTime":"20250812093959","lendingId":"MPOS-2507071643966-L5","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25080858389] {"req--->":{"nextlend_request_id":58389,"partner_code":"MPOS","merchantId":"50731215","lendingRequestId":"NL25080858389","debitAmount":7663380.0,"requestTime":"20250812093959","lendingId":"MPOS-2506181345825-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25080858392] {"req--->":{"nextlend_request_id":58392,"partner_code":"MPOS","merchantId":"50728581","lendingRequestId":"NL25080858392","debitAmount":7770976.0,"requestTime":"20250812093959","lendingId":"MPOS-2506191343550-L4","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50728581,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25080858392\\\",\\\"mposDebtId\\\":141481954,\\\"debtAmount\\\":7770976},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"18b2ae9841fd0a4f8b6aabb5d5eab1e5","Description":"success"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25080858391] {"req--->":{"nextlend_request_id":58391,"partner_code":"MPOS","merchantId":"50728581","lendingRequestId":"NL25080858391","debitAmount":26000000.0,"requestTime":"20250812093959","lendingId":"MPOS-2507071652172-L6","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50728581,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25080858391\\\",\\\"mposDebtId\\\":141483781,\\\"debtAmount\\\":26000000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"9703ef4023918613cf420612ad2d2a97","Description":"success"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158549] {"req--->":{"nextlend_request_id":58549,"partner_code":"MPOS","merchantId":"50708980","lendingRequestId":"NL25081158549","debitAmount":11529679.0,"requestTime":"20250812093959","lendingId":"MPOS-2505281707971-L2","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158550] {"req--->":{"nextlend_request_id":58550,"partner_code":"MPOS","merchantId":"50688964","lendingRequestId":"NL25081158550","debitAmount":17100000.0,"requestTime":"20250812093959","lendingId":"MPOS-2505310850564-L14","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158551] {"req--->":{"nextlend_request_id":58551,"partner_code":"MPOS","merchantId":"50688964","lendingRequestId":"NL25081158551","debitAmount":24600000.0,"requestTime":"20250812093959","lendingId":"MPOS-2505301344742-L12","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158552] {"req--->":{"nextlend_request_id":58552,"partner_code":"MPOS","merchantId":"50724382","lendingRequestId":"NL25081158552","debitAmount":14699674.0,"requestTime":"20250812093959","lendingId":"MPOS-2506101709224-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158553] {"req--->":{"nextlend_request_id":58553,"partner_code":"MPOS","merchantId":"50722600","lendingRequestId":"NL25081158553","debitAmount":14320000.0,"requestTime":"20250812093959","lendingId":"MPOS-2506131128418-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158554] {"req--->":{"nextlend_request_id":58554,"partner_code":"MPOS","merchantId":"50725159","lendingRequestId":"NL25081158554","debitAmount":16200000.0,"requestTime":"20250812093959","lendingId":"MPOS-2506101042259-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158555] {"req--->":{"nextlend_request_id":58555,"partner_code":"MPOS","merchantId":"50707778","lendingRequestId":"NL25081158555","debitAmount":1680000.0,"requestTime":"20250812093959","lendingId":"MPOS-2506050948813-L2","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158556] {"req--->":{"nextlend_request_id":58556,"partner_code":"MPOS","merchantId":"50687580","lendingRequestId":"NL25081158556","debitAmount":91800000.0,"requestTime":"20250812093959","lendingId":"MPOS-2505251534225-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158557] {"req--->":{"nextlend_request_id":58557,"partner_code":"MPOS","merchantId":"50692338","lendingRequestId":"NL25081158557","debitAmount":6486000.0,"requestTime":"20250812093959","lendingId":"MPOS-2506031717327-L10","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158558] {"req--->":{"nextlend_request_id":58558,"partner_code":"MPOS","merchantId":"50688964","lendingRequestId":"NL25081158558","debitAmount":18750000.0,"requestTime":"20250812093959","lendingId":"MPOS-2505241559126-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:39:59] local.INFO: [checkDebt--->NL25081158559] {"req--->":{"nextlend_request_id":58559,"partner_code":"MPOS","merchantId":"50688964","lendingRequestId":"NL25081158559","debitAmount":17220000.0,"requestTime":"20250812093959","lendingId":"MPOS-2505241640550-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158560] {"req--->":{"nextlend_request_id":58560,"partner_code":"MPOS","merchantId":"50685807","lendingRequestId":"NL25081158560","debitAmount":54045000.0,"requestTime":"20250812094000","lendingId":"MPOS-2505151644266-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158561] {"req--->":{"nextlend_request_id":58561,"partner_code":"MPOS","merchantId":"50683230","lendingRequestId":"NL25081158561","debitAmount":66134000.0,"requestTime":"20250812094000","lendingId":"MPOS-2505120851998-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158562] {"req--->":{"nextlend_request_id":58562,"partner_code":"MPOS","merchantId":"50692338","lendingRequestId":"NL25081158562","debitAmount":3263000.0,"requestTime":"20250812094000","lendingId":"MPOS-2506031601376-L6","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158563] {"req--->":{"nextlend_request_id":58563,"partner_code":"MPOS","merchantId":"50678258","lendingRequestId":"NL25081158563","debitAmount":90650000.0,"requestTime":"20250812094000","lendingId":"MPOS-2505080937929-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158564] {"req--->":{"nextlend_request_id":58564,"partner_code":"MPOS","merchantId":"50692338","lendingRequestId":"NL25081158564","debitAmount":8157500.0,"requestTime":"20250812094000","lendingId":"MPOS-2506031611196-L7","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158568] {"req--->":{"nextlend_request_id":58568,"partner_code":"MPOS","merchantId":"22732","lendingRequestId":"NL25081158568","debitAmount":4673332.0,"requestTime":"20250812094000","lendingId":"TEST-FCMJOD-L54","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158567] {"req--->":{"nextlend_request_id":58567,"partner_code":"MPOS","merchantId":"50688964","lendingRequestId":"NL25081158567","debitAmount":24000000.0,"requestTime":"20250812094000","lendingId":"MPOS-2505271559666-L8","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158569] {"req--->":{"nextlend_request_id":58569,"partner_code":"MPOS","merchantId":"50692338","lendingRequestId":"NL25081158569","debitAmount":8207500.0,"requestTime":"20250812094000","lendingId":"MPOS-2506031703937-L9","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158566] {"req--->":{"nextlend_request_id":58566,"partner_code":"MPOS","merchantId":"141389755","lendingRequestId":"NL25081158566","debitAmount":23002608.0,"requestTime":"20250812094000","lendingId":"MPOS-2506261133236-L2","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":141389755,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158566\\\",\\\"mposDebtId\\\":141484789,\\\"debtAmount\\\":23002608},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"741ecafefac4f837f5c1a9c2a49be63f","Description":"success"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158565] {"req--->":{"nextlend_request_id":58565,"partner_code":"MPOS","merchantId":"50678849","lendingRequestId":"NL25081158565","debitAmount":100900000.0,"requestTime":"20250812094000","lendingId":"MPOS-2505081502928-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50678849,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158565\\\",\\\"mposDebtId\\\":141484788,\\\"debtAmount\\\":100900000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"0b030b37863d482031b6c612468bd91d","Description":"success"}} 
[2025-08-12 09:40:00] local.INFO: [checkDebt--->NL25081158572] {"req--->":{"nextlend_request_id":58572,"partner_code":"MPOS","merchantId":"50726558","lendingRequestId":"NL25081158572","debitAmount":1251000.0,"requestTime":"20250812094000","lendingId":"MPOS-2506131347633-L2","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:01] local.INFO: [checkDebt--->NL25081158570] {"req--->":{"nextlend_request_id":58570,"partner_code":"MPOS","merchantId":"50692338","lendingRequestId":"NL25081158570","debitAmount":8007500.0,"requestTime":"20250812094001","lendingId":"MPOS-2506040924786-L12","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:01] local.INFO: [checkDebt--->NL25081158571] {"req--->":{"nextlend_request_id":58571,"partner_code":"MPOS","merchantId":"50605403","lendingRequestId":"NL25081158571","debitAmount":311110000.0,"requestTime":"20250812094001","lendingId":"MPOS-2504231359677-L2","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50605403,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158571\\\",\\\"mposDebtId\\\":141484794,\\\"debtAmount\\\":311110000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"97e890adab2fc8e6be6d361b7113428e","Description":"success"}} 
[2025-08-12 09:40:01] local.INFO: [checkDebt--->NL25081158573] {"req--->":{"nextlend_request_id":58573,"partner_code":"MPOS","merchantId":"50688964","lendingRequestId":"NL25081158573","debitAmount":11930000.0,"requestTime":"20250812094001","lendingId":"MPOS-2505310917453-L15","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50688964,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158573\\\",\\\"mposDebtId\\\":141484815,\\\"debtAmount\\\":11930000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"e197687eddc1344aae27a8af688e304a","Description":"success"}} 
[2025-08-12 09:40:02] local.INFO: [checkDebt--->NL25081158578] {"req--->":{"nextlend_request_id":58578,"partner_code":"MPOS","merchantId":"50709324","lendingRequestId":"NL25081158578","debitAmount":15900000.0,"requestTime":"20250812094002","lendingId":"MPOS-2506051403547-L5","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:02] local.INFO: [checkDebt--->NL25081158577] {"req--->":{"nextlend_request_id":58577,"partner_code":"MPOS","merchantId":"50708980","lendingRequestId":"NL25081158577","debitAmount":74816000.0,"requestTime":"20250812094002","lendingId":"MPOS-2505281134464-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:02] local.INFO: [checkDebt--->NL25081158574] {"req--->":{"nextlend_request_id":58574,"partner_code":"MPOS","merchantId":"50348399","lendingRequestId":"NL25081158574","debitAmount":7500000.0,"requestTime":"20250812094002","lendingId":"TEST-7EH8SF-L4","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:02] local.INFO: [checkDebt--->NL25081158576] {"req--->":{"nextlend_request_id":58576,"partner_code":"MPOS","merchantId":"22732","lendingRequestId":"NL25081158576","debitAmount":8750000.0,"requestTime":"20250812094002","lendingId":"TEST-JNOCM9-L30","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:02] local.INFO: [checkDebt--->NL25081158575] {"req--->":{"nextlend_request_id":58575,"partner_code":"MPOS","merchantId":"22732","lendingRequestId":"NL25081158575","debitAmount":6000000.0,"requestTime":"20250812094002","lendingId":"TEST-33GCTC-L21","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":22732,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158575\\\",\\\"mposDebtId\\\":141484817,\\\"debtAmount\\\":6000000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"71428093c28633053a1b7fb717e5fdc1","Description":"success"}} 
[2025-08-12 09:40:02] local.INFO: [checkDebt--->NL25081158581] {"req--->":{"nextlend_request_id":58581,"partner_code":"MPOS","merchantId":"50731633","lendingRequestId":"NL25081158581","debitAmount":11163016.0,"requestTime":"20250812094002","lendingId":"MPOS-2506190906600-L2","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:02] local.INFO: [checkDebt--->NL25081158580] {"req--->":{"nextlend_request_id":58580,"partner_code":"MPOS","merchantId":"141380678","lendingRequestId":"NL25081158580","debitAmount":11142468.0,"requestTime":"20250812094002","lendingId":"MPOS-2506201359798-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:02] local.INFO: [checkDebt--->NL25081158582] {"req--->":{"nextlend_request_id":58582,"partner_code":"MPOS","merchantId":"50673867","lendingRequestId":"NL25081158582","debitAmount":191070000.0,"requestTime":"20250812094002","lendingId":"MPOS-2505051141356-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158583] {"req--->":{"nextlend_request_id":58583,"partner_code":"MPOS","merchantId":"141387976","lendingRequestId":"NL25081158583","debitAmount":11060276.0,"requestTime":"20250812094003","lendingId":"MPOS-2506241025541-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158585] {"req--->":{"nextlend_request_id":58585,"partner_code":"MPOS","merchantId":"141389755","lendingRequestId":"NL25081158585","debitAmount":11286304.0,"requestTime":"20250812094003","lendingId":"MPOS-2506261151219-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158586] {"req--->":{"nextlend_request_id":58586,"partner_code":"MPOS","merchantId":"50709324","lendingRequestId":"NL25081158586","debitAmount":14665000.0,"requestTime":"20250812094003","lendingId":"MPOS-2506030956840-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158579] {"req--->":{"nextlend_request_id":58579,"partner_code":"MPOS","merchantId":"50723341","lendingRequestId":"NL25081158579","debitAmount":11027400.0,"requestTime":"20250812094003","lendingId":"MPOS-2506111120510-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50723341,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158579\\\",\\\"mposDebtId\\\":141484821,\\\"debtAmount\\\":11027400},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"5fbb2af9c8c3bb93c534a58708224cd0","Description":"success"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158584] {"req--->":{"nextlend_request_id":58584,"partner_code":"MPOS","merchantId":"141403565","lendingRequestId":"NL25081158584","debitAmount":12400000.0,"requestTime":"20250812094003","lendingId":"MPOS-2507101548881-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":141403565,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158584\\\",\\\"mposDebtId\\\":141484798,\\\"debtAmount\\\":12400000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"06f49a9586bd449155bd3e56073aa096","Description":"success"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158588] {"req--->":{"nextlend_request_id":58588,"partner_code":"MPOS","merchantId":"50692338","lendingRequestId":"NL25081158588","debitAmount":1690000.0,"requestTime":"20250812094003","lendingId":"MPOS-2506031543650-L5","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158589] {"req--->":{"nextlend_request_id":58589,"partner_code":"MPOS","merchantId":"50723486","lendingRequestId":"NL25081158589","debitAmount":70970000.0,"requestTime":"20250812094003","lendingId":"MPOS-2506071026624-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158590] {"req--->":{"nextlend_request_id":58590,"partner_code":"MPOS","merchantId":"50675974","lendingRequestId":"NL25081158590","debitAmount":115800000.0,"requestTime":"20250812094003","lendingId":"MPOS-2505060944140-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158592] {"req--->":{"nextlend_request_id":58592,"partner_code":"MPOS","merchantId":"141386640","lendingRequestId":"NL25081158592","debitAmount":11080824.0,"requestTime":"20250812094003","lendingId":"MPOS-2506231414283-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:03] local.INFO: [checkDebt--->NL25081158587] {"req--->":{"nextlend_request_id":58587,"partner_code":"MPOS","merchantId":"50677520","lendingRequestId":"NL25081158587","debitAmount":48250000.0,"requestTime":"20250812094003","lendingId":"MPOS-2505061610889-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50677520,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158587\\\",\\\"mposDebtId\\\":141484801,\\\"debtAmount\\\":48250000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"a0a0eb059878322e2ca585f4f4c50c98","Description":"success"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158593] {"req--->":{"nextlend_request_id":58593,"partner_code":"MPOS","merchantId":"50669424","lendingRequestId":"NL25081158593","debitAmount":56097380.0,"requestTime":"20250812094004","lendingId":"MPOS-2505061310331-L2","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158594] {"req--->":{"nextlend_request_id":58594,"partner_code":"MPOS","merchantId":"141382317","lendingRequestId":"NL25081158594","debitAmount":10028213.0,"requestTime":"20250812094004","lendingId":"MPOS-2506201434696-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158591] {"req--->":{"nextlend_request_id":58591,"partner_code":"MPOS","merchantId":"50676690","lendingRequestId":"NL25081158591","debitAmount":193000000.0,"requestTime":"20250812094004","lendingId":"MPOS-2505061142387-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50676690,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158591\\\",\\\"mposDebtId\\\":141484805,\\\"debtAmount\\\":193000000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"40005e902ae49fea5fd6b0bacca9b6a0","Description":"success"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158597] {"req--->":{"nextlend_request_id":58597,"partner_code":"MPOS","merchantId":"50677520","lendingRequestId":"NL25081158597","debitAmount":67550000.0,"requestTime":"20250812094004","lendingId":"MPOS-2505070913761-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158598] {"req--->":{"nextlend_request_id":58598,"partner_code":"MPOS","merchantId":"141377138","lendingRequestId":"NL25081158598","debitAmount":10863016.0,"requestTime":"20250812094004","lendingId":"MPOS-2506181630751-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158595] {"req--->":{"nextlend_request_id":58595,"partner_code":"MPOS","merchantId":"50730744","lendingRequestId":"NL25081158595","debitAmount":11224660.0,"requestTime":"20250812094004","lendingId":"MPOS-2506161650246-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50730744,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158595\\\",\\\"mposDebtId\\\":141484809,\\\"debtAmount\\\":11224660},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"1cc62d08ca96aaa8f43fcf09139678af","Description":"success"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158599] {"req--->":{"nextlend_request_id":58599,"partner_code":"MPOS","merchantId":"50723785","lendingRequestId":"NL25081158599","debitAmount":31198894.0,"requestTime":"20250812094004","lendingId":"MPOS-2506101606405-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158596] {"req--->":{"nextlend_request_id":58596,"partner_code":"MPOS","merchantId":"50707396","lendingRequestId":"NL25081158596","debitAmount":9795199.0,"requestTime":"20250812094004","lendingId":"MPOS-2506181645144-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50707396,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158596\\\",\\\"mposDebtId\\\":141484810,\\\"debtAmount\\\":9795199},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"af4e9e471bc4533bc2184d9c7f41a7f0","Description":"success"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158600] {"req--->":{"nextlend_request_id":58600,"partner_code":"MPOS","merchantId":"50688149","lendingRequestId":"NL25081158600","debitAmount":56625000.0,"requestTime":"20250812094004","lendingId":"MPOS-2505251719763-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:04] local.INFO: [checkDebt--->NL25081158602] {"req--->":{"nextlend_request_id":58602,"partner_code":"MPOS","merchantId":"50585324","lendingRequestId":"NL25081158602","debitAmount":107242500.0,"requestTime":"20250812094004","lendingId":"MPOS-2505251656955-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:05] local.INFO: [checkDebt--->NL25081158603] {"req--->":{"nextlend_request_id":58603,"partner_code":"MPOS","merchantId":"50261258","lendingRequestId":"NL25081158603","debitAmount":170705550.0,"requestTime":"20250812094005","lendingId":"MPOS-2411261307386-L12","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:05] local.INFO: [checkDebt--->NL25081158601] {"req--->":{"nextlend_request_id":58601,"partner_code":"MPOS","merchantId":"50723950","lendingRequestId":"NL25081158601","debitAmount":32810964.0,"requestTime":"20250812094005","lendingId":"MPOS-2506101624540-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":50723950,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158601\\\",\\\"mposDebtId\\\":141484825,\\\"debtAmount\\\":32810964},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"25fea390d6dbcd2e71ae9a0773b72fbb","Description":"success"}} 
[2025-08-12 09:40:05] local.INFO: [checkDebt--->NL25081158608] {"req--->":{"nextlend_request_id":58608,"partner_code":"MPOS","merchantId":"50688964","lendingRequestId":"NL25081158608","debitAmount":37200000.0,"requestTime":"20250812094005","lendingId":"MPOS-2505211131925-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:05] local.INFO: [checkDebt--->NL25081158604] {"req--->":{"nextlend_request_id":58604,"partner_code":"MPOS","merchantId":"50708119","lendingRequestId":"NL25081158604","debitAmount":56580000.0,"requestTime":"20250812094005","lendingId":"MPOS-2506061330924-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:05] local.INFO: [checkDebt--->NL25081158606] {"req--->":{"nextlend_request_id":58606,"partner_code":"MPOS","merchantId":"141387976","lendingRequestId":"NL25081158606","debitAmount":18138758.0,"requestTime":"20250812094005","lendingId":"MPOS-2506231658272-L2","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:05] local.INFO: [checkDebt--->NL25081158607] {"req--->":{"nextlend_request_id":58607,"partner_code":"MPOS","merchantId":"50688964","lendingRequestId":"NL25081158607","debitAmount":5298476.0,"requestTime":"20250812094005","lendingId":"MPOS-2505251755609-L7","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":1004,"data":"{\"errorCode\":1004,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":1004,\\\"message\\\":\\\"Unhandled error\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"414ec30a55a81eef4770ff22a0236e38","Description":"ERROR"}} 
[2025-08-12 09:40:05] local.INFO: [checkDebt--->NL25081158605] {"req--->":{"nextlend_request_id":58605,"partner_code":"MPOS","merchantId":"141399896","lendingRequestId":"NL25081158605","debitAmount":1995000.0,"requestTime":"20250812094005","lendingId":"MPOS-2507071621359-L3","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":141399896,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158605\\\",\\\"mposDebtId\\\":141484829,\\\"debtAmount\\\":1995000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"1993ac8dabf521328a6b8d8cbac18f63","Description":"success"}} 
[2025-08-12 09:40:06] local.INFO: [checkDebt--->NL25081158613] {"req--->":{"nextlend_request_id":58613,"partner_code":"MPOS","merchantId":"141480620","lendingRequestId":"NL25081158613","debitAmount":1500000.0,"requestTime":"20250812094006","lendingId":"MPOS-2508081716984-L1","loanOriginalAmount":null,"deductionPerDayAmount":null,"loanBalance":null,"_prefix":""},"res--->":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":141480620,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081158613\\\",\\\"mposDebtId\\\":141485317,\\\"debtAmount\\\":1500000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"c308d57fa572f20ea805c077f26c4413","Description":"success"}} 
