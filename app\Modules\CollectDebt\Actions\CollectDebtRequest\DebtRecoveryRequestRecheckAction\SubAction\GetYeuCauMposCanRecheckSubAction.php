<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Database\Eloquent\Collection;

class GetYeuCauMposCanRecheckSubAction
{
	public function run(int $limit=30): Collection
	{
		$listYcCanRecheck = CollectDebtRequest::query()
			->join('debt_recovery_ledger', 'debt_recovery_ledger.request_id', '=', 'debt_recovery_request.id')
			->where('payment_method_code', 'MPOS')
			->where('debt_recovery_request.status', CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA)
			->where('debt_recovery_ledger.status', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
			->whereRaw(
				sprintf(
					"(debt_recovery_request.time_completed_recheck IS NULL OR debt_recovery_request.time_completed_recheck + %d < %d)",
					30 * 60,
					now()->timestamp
				)
			)
			->orderByRaw('debt_recovery_request.id ASC')
			->selectRaw('debt_recovery_request.*')
			->limit($limit)
			->get();

		if ($listYcCanRecheck->isEmpty()) {
			mylog(['EMPTY' => 'khong co yc can recheck']);
			return Collection::make([]);
		}

		// Khong recheck cho yc da huy hoac khong co ma chung tu
		$listYcSeKhongRecheck = $listYcCanRecheck->filter(function (CollectDebtRequest $rq) {
			return !empty($rq->time_canceled) || empty($rq->partner_transaction_id) || !empty($rq->time_receivered);
		});

		if ($listYcSeKhongRecheck->isEmpty()) {
			mylog(['Danh sach yc can recheck' => $listYcCanRecheck]);
			return $listYcCanRecheck;
		}
	
		// Danh dau cac yc nay la da hoan thanh
		if ($listYcSeKhongRecheck->isNotEmpty()) {
			foreach ($listYcSeKhongRecheck as $rq) {
				mylog([
					'xu ly danh dau yc recheck ve tt cuoi luon:' => $rq->partner_request_id,
					'contract_code' => $rq->contract_code
				]);

				$otherData = $rq->getRequestOtherData();
				$otherData[] = [
          'type' => 'RE_CHECK_PAYMENT',
          'time_modified' => time(),
          'note' => 'Recheck yêu cầu đã hạch toán',
          'data' => []
        ];

				$rq->status = CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH;
				$rq->time_completed_recheck = now()->timestamp;
				$rq->completed_recheck_by = Helper::getCronJobUser();
				$rq->other_data = json_encode($otherData);
				$result = $rq->save();

				mylog(['ket qua cap nhat recheck: ' => $result]);
			}
		}

		$yeuCauDaBiHuyIds = $listYcSeKhongRecheck->pluck('id')->toArray();

		$listCanXuLyRecheck = $listYcCanRecheck->filter(function (CollectDebtRequest $rq) use ($yeuCauDaBiHuyIds) {
			return !in_array($rq->id, $yeuCauDaBiHuyIds);
		});

		mylog(['Danh sach yc can recheck' => $listCanXuLyRecheck]);
		return $listCanXuLyRecheck;
	}
} // End clas
