<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class XuLySoBaoMuonTask
{
  public function run(CollectDebtLedger $ledger)
  {
    $otherData = $ledger->getLedgerOtherData();

    $otherData[] = [
      'type' => 'EXCESS',
      'time_modified' => time(),
      'data' => [
        [
          'amount_excess' => $ledger->amount,
          'payment_method_code' => 'MPOS',
          'line' => 20,
        ]
      ],
      'note' => 'Ghi nhận số tiền thu thừa'
    ];

    foreach ($otherData as &$ot) {
      if ($ot['type'] == 'SUMMARY') {
        $ot['data']['total_amount_excess_revenue'] = $ledger->amount;
				$ot['data']['total_amount_receiver'] = $ledger->amount;
      }
    }

    return CollectDebtLedger::query()->where('id', $ledger->id)->update([
      'status' => CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN,
			'status_summary' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
      'other_data' => json_encode($otherData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
    ]);
  }
}
