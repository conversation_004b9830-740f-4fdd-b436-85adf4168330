<body style="margin: 0;">
	<style>
		.container { font-size: 14px; line-height: 1.8; }
		.row { display: block; margin-bottom: 10px; justify-content: space-between;  }
		.label { width: 115px; color: gray; display: inline-block; }
		.value { font-weight: 500; text-align: right;}
		.badge { display: inline-block; padding: 2px 8px; font-size: 12px; border-radius: 12px; color: white; font-weight: 500; line-height: 1; }
	  .badge-warning { background-color:rgb(250, 173, 59); }
	</style>

	<div class="container">
		<div class="row">
			<span class="label">Thời gian:</span>
			<span class="value">{{ $dataSapDenKy['thanhToanTruocNgay'] }}</span>
		</div>

		<div class="row">
			<span class="label">Mã hợp đồng:</span>
			<span class="value">{{ $dataSapDenKy['contractCode'] }}</span>
		</div>

		<div class="row">
			<span class="label">Thời hạn:</span>
			<span class="value">Còn {{ $dataSapDenKy['soNgayDenKy'] }} ngày</span>
		</div>

		<div class="row">
			<span class="label">Số tiền thanh toán:</span>
			<span class="value">{{ str_replace(',', '.', $dataSapDenKy['duNoCuoiKy']) }}</span>
		</div>

		<div class="row">
			<span class="label">Tình trạng:</span>
			<span class="value badge badge-warning">Sắp đến kỳ</span>
		</div>
	</div>
</body>