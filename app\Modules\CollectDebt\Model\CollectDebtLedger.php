<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use Exception;

class CollectDebtLedger extends Model
{
	protected $table   = 'debt_recovery_ledger';
	public $timestamps = false;
	protected $appends = [];
	protected $guarded = [];

/* -------------------- Relationship ----------------- */
	public function collectDebtRequest()
	{
		return $this->belongsTo(CollectDebtRequest::class, 'request_id', 'id');
	}

	public function collectDebtSummary()
	{
		return $this->belongsTo(CollectDebtSummary::class, 'contract_code', 'contract_code');
	}

	public function collectDebtGuide()
	{
		return $this->belongsTo(CollectDebtGuide::class, 'contract_code', 'contract_code');
	}

/* -------------------- Method ----------------- */
	public function isLedgerThuNhieuLich(): bool {
		$planIds = $this->getPlanIds();
		return count($planIds) > 1;
	}
	public static function listStatus(): array
	{
		return [
			CollectDebtEnum::LEDGER_STT_CHUA_XU_LY => 'Chưa xử lý',
			CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN => 'Đang hạch toán',
			CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN => 'Đã hạch toán',
			CollectDebtEnum::LEDGER_STT_DA_TU_CHOI => 'Từ chối'
		];
	}
	public static function listStatusAction(): array
	{
		return [
			CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT => 'Chưa xử lý',
			CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT => 'Đang xử lý',
			CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT => 'Đã xử lý',
			CollectDebtEnum::LEDGER_STT_ACTION_LOI => 'Từ chối'
		];
	}

	public function isNoProcess(): bool
	{
		return $this->status == CollectDebtEnum::LEDGER_STT_CHUA_XU_LY;
	}

	public function isAccouting(): bool
	{
		return $this->status == CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN;
	}

	public function isAccounted(): bool
	{
		return $this->status == CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN;
	}

	public function isCancel(): bool
	{
		return $this->status == CollectDebtEnum::LEDGER_STT_DA_TU_CHOI;
	}

	public function getPlanIds(): array
	{
		if (empty($this->plan_ids)) {
			return [];
		}

		return explode(',', $this->plan_ids);
	}

	public static function loadCacLichThuCuaSo(Collection $collectDebtLedgers, array $with = [])
	{
		$planIds = [];
		foreach ($collectDebtLedgers as $ledger) {
			$planIds = array_merge($planIds, $ledger->getPlanIds());
		}

		$collectDebtSchedules = CollectDebtSchedule::query()
																							 ->whereIn('id', $planIds)
																							 ->get();
  
		throw_if($collectDebtSchedules->isEmpty(), new Exception('Khong tim thay lich de hach toan'));
		
		$collectDebtSchedules = app(PlanSortableCollectionByRule::class)->sortCollection($collectDebtSchedules)->keyBy('id');

		$collectDebtLedgers = $collectDebtLedgers->transform(function (CollectDebtLedger $ledger) use ($collectDebtSchedules) {
			$ledger->schedules = Collection::make();

			foreach ($collectDebtSchedules as $plan) {
				if (Str::contains($ledger->plan_ids, $plan->id)) {
					$ledger->schedules->push($plan);
				}
			}
			
			return $ledger;
		});

		return $collectDebtLedgers;
	}

	
	public function getLedgerOtherData()
	{
		return json_decode($this->other_data, true);
	}

	public function getRequest(): CollectDebtRequest {
		$otherData = $this->getLedgerOtherData();
		$request = collect($otherData)->where('type', 'REQUEST')->first();
		return new CollectDebtRequest($request['data']);
	}

	public function getPlans(): Collection {
		$otherData = $this->getLedgerOtherData();
		$plans = collect($otherData)->where('type', 'PLAN')->first();
		return CollectDebtSchedule::hydrate($plans['data']);
	}

	public function getOtherDataTypeIndex(string $type): int {
		$otherData = $this->getLedgerOtherData();
		return collect($otherData)->search(function ($item) use ($type) {
			return $item['type'] == $type;
		});
	}

	public function getOtherDataItem(string $type): array {
		$otherData = $this->getLedgerOtherData($type);
		$item = collect($otherData)->where('type', $type)->first();
		
		return $item ?? [];
	}

	public function hasOtherDataItem(string $type): bool {
		$otherData = $this->getLedgerOtherData();
		return collect($otherData)->contains(function ($item) use ($type) {
			return $item['type'] == $type;
		});
	}

	public function isSoDoiTacBaoMuon(): bool {
		return Str::contains($this->description, 'TRICH_MUON');
	}

	public function isSoThuKhongCoLich(): bool {
		return empty($this->plan_ids);
	}
} // End class
