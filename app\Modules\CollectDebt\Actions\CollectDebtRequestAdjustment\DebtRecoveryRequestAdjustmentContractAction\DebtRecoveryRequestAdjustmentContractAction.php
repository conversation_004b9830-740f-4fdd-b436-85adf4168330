<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentContractAction;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;


class DebtRecoveryRequestAdjustmentContractAction
{
  public function run(Request $request)
  {
    $requestAdjustments = CollectDebtRequestAdjustment::query()
      ->where('contract_code', $request->json('data.contract_code'))
      ->latest('id')
      ->get();

    $requestAdjustments = $requestAdjustments->map(function (CollectDebtRequestAdjustment $ra) {
      $can[] = CollectDebtEnum::RA_STT_CAN_XEM_CHI_TIET;
      if ($ra->isStatusMoiTao()) {
        $can[] = CollectDebtEnum::RA_STT_CAN_DUYET_1;
        $can[] = CollectDebtEnum::RA_STT_CAN_HUY;
      }

      if ($ra->isStatusDuyet1()) {
        $can[] = CollectDebtEnum::RA_STT_CAN_DUYET_2;
        $can[] = CollectDebtEnum::RA_STT_CAN_HUY;
      }

      if ($ra->isStatusDuyet2()) {
        $can[] = CollectDebtEnum::RA_STT_CAN_HUY;
      }
      $ra->can = $can;
      return $ra;
    });

    return $requestAdjustments;
  }
}
