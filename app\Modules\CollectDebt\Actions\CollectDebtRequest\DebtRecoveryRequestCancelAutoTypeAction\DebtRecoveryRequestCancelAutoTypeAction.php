<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelAutoTypeAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\TaoCongNoSubAction;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCancelAutoTypeRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction\DebtRecoveryRequestCancelPaymentAction;
use App\Modules\CollectDebt\Model\CollectDebtPartner;

class DebtRecoveryRequestCancelAutoTypeAction
{
  public array $cancelRequestIds = [];

  /**
   * TH1: Yêu cầu được tạo ra nhưng CHƯA GỬI ĐỐI TÁC thì khi hủy yêu cầu
   ⁡⁢⁢⁢   Yêu cầu được đánh trạng thái đã hủy
      Không phát sinh partner
      Không ghi sổ
      Không hạch toán

    TH2: Yêu cầu được tạo ra ĐÃ GỬI ĐỐI TÁC thì khi hủy yêu cầu
      Yêu cầu được đánh dấu trích 0 đồng
      Tạo thông tin trên partner với mã chứng từ tương ứng <số tiền nhận được 0đ>
      Ghi sổ
      Hạch toán⁡

			[08.05.2024] - Chống đấm spam yêu cầu trích đa kênh để tránh sinh sai lịch
			1. Các yêu cầu != MPOS, phải đc hạch toán xong
			2. Các partners != MPOS, phải về trạng thái cuối
   *
   * @param DebtRecoveryRequestCancelAutoTypeRequest $request
   * @return void
   */
  public function run(DebtRecoveryRequestCancelAutoTypeRequest $request)
  {
		$contractCode = trim($request->json('data.contract_code'));

		// Đảm bảo các kênh khác về trạng thái cuối cùng rồi mới cho hủy lệnh MPOS -> tránh bị sinh sai lịch 08.05.2024
		$listYeuCauThuDaKenhCungHopDong = CollectDebtRequest::query()
																												->with('collectDebtLedger')
																												->where('contract_code', $contractCode)
																												->where('payment_method_code', '!=', 'MPOS')
																												->get();

		if ($listYeuCauThuDaKenhCungHopDong->isNotEmpty()) {
			$isToanBoYcKhacMposDaHachToan = $listYeuCauThuDaKenhCungHopDong->every(function (CollectDebtRequest $rq) {
				return ($rq->collectDebtLedger && $rq->collectDebtLedger->status == CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN) 
							|| ($rq->isManualDebt() && $rq->status == CollectDebtEnum::REQUEST_STT_TU_CHOI);
			});

			throw_if(
				!$isToanBoYcKhacMposDaHachToan,
				new Exception('Bạn phải đảm bảo các yêu cầu VIRTUALACCOUNT, IB_OFF và WALLET đã hạch toán đầy đủ')
			);
		}

		// Đảm bảo các partner KHÔNG PHẢI MPOS đã về: THẤT BẠI TRẠNG THÁI CUỐI hoặc THÀNH CÔNG VÀ CÓ YÊU CẦU
		$partners = CollectDebtPartner::query()
																	->where('payment_method_code', '!=', 'MPOS')
																	->where('contract_code', $contractCode)
																	->get();
		
		$isToanBoPartnerDaHuyHoacDaXuLy = $partners->every(function (CollectDebtPartner $partner) {
			return $partner->status == CollectDebtEnum::PARTNER_STT_DA_TU_CHOI || (
					$partner->status == CollectDebtEnum::PARTNER_STT_DA_XU_LY && !empty($partner->partner_request_id)
			);
		});

		throw_if(
			!$isToanBoPartnerDaHuyHoacDaXuLy,
			new Exception('Bạn phải đảm bảo các bản ghi lịch sử nhận tiền VIRTUALACCOUNT, IB_OFF và WALLET đã về trạng thái cuối')
		);



    $listYcMuonHuy = CollectDebtRequest::query()
                                        ->where('contract_code', $contractCode)
                                        ->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT)
                                        ->where('payment_method_code', 'MPOS')
                                        ->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
                                        ->where('status_payment', '!=', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA);

    if (!empty($request->json('data.request_id'))) {
      $listYcMuonHuy = $listYcMuonHuy->whereIn('id', $request->json('data.request_id'));
    }else {
      $listYcMuonHuy = $listYcMuonHuy->where('time_begin', '<=', time())
                                     ->where('time_expired', '>=', time());
    }
    
    $listYcMuonHuy = $listYcMuonHuy->get();

    throw_if($listYcMuonHuy->isEmpty(), new Exception('Không thể hủy yêu cầu do yêu cầu trích của bạn đã được ghi sổ hoặc lỗi hệ thống'));

    // Chưa gửi hoặc mới gửi chứ chưa có nhận đc kết quả gì
    $isChuaGuiHoacMoiChiGuiYeuCau = $listYcMuonHuy->every(function (CollectDebtRequest  $collectDebtRequest) {
      return $collectDebtRequest->isSentPayment() || $collectDebtRequest->isUnsentPayment();
    });

    throw_if(!$isChuaGuiHoacMoiChiGuiYeuCau, new Exception('Một trong các yêu cầu của bạn có trạng thái không hợp lệ. Không thể hủy'));

    $isCoYeuCauDaHuy = $listYcMuonHuy->contains(function (CollectDebtRequest $collectDebtRequest) {
      return $collectDebtRequest->isCanceled() || $collectDebtRequest->isCancelPayment();
    });

    throw_if($isCoYeuCauDaHuy, new Exception('Một trong số yêu cầu của bạn đã bị hủy. Không thể hủy lại'));

   
    DB::beginTransaction();
    try {
      foreach ($listYcMuonHuy as $rq) {
        $errorMsg = sprintf('Yêu cầu: `%s` không đủ điều kiện hủy. Lý do: Có thể đã có tiền về từ partner', $rq->partner_request_id);
        throw_if(!$rq->isCoTheHuyYeuCauSangDoiTac(), new Exception($errorMsg));

        // Nếu là YC đã gửi sang đối tác -> Gọi hủy thẳng sang MPOS
        if ($rq->isSentPayment()) {
          $cancelMposRequest = app(DebtRecoveryRequestCancelPaymentAction::class)->run($rq);
          
          throw_if(
            $cancelMposRequest['data']['error_code'] != '00' || empty($cancelMposRequest['data']['error_code']),
            new Exception('Lỗi không thể hủy lệnh: ' . json_encode($cancelMposRequest))
          );
        }
        
        $rq->canceled_by = $request->json('data.canceled_by');
        $rq->time_canceled = $request->json('data.time_canceled', time());
        $rq->description = $request->json('data.reason');

        /**
				 * Với case không có mã chứng từ thì thrown luôn, bắt phải có chứng từ mới cho hủy để tránh bị đấm 
				 * liên tục vào hệ thống
				 */
				if (empty($rq->partner_transaction_id)) {
					mylog([
						'Yc chua co chung tu' => 'Ok',
						'Ma Yeu Cau la' => $rq->partner_request_id
					]);
					
					throw new Exception(sprintf('Yêu cầu `%s` chưa có mã chứng từ. Từ chối xử lý hủy', $rq->partner_request_id));
				}

        // if (empty($rq->partner_transaction_id)) {
        //   // Chỉ bắt khi chưa gửi, vì nếu gọi cancel sang đối tác thì chắc chắn đã có thêm other_data rồi
        //   if ($rq->isUnsentPayment()) {
        //     $rq->other_data = $rq->putOtherData([
        //       'type'  => 'CANCEL',
        //       'time_modified' => time(),
        //       'data' => $cancelMposRequest ?? [],
        //       'note' => $request->json('data.reason', 'Hủy yêu cầu trích tự động sang đối tác MPOS'),
        //     ]);
        //   }
          
        //   $rq->status = CollectDebtEnum::REQUEST_STT_TU_CHOI;
        //   $rq->status_payment = CollectDebtEnum::REQUEST_STT_PM_TU_CHOI;
        //   $rq->save();
        // }
       

        // TH2
        if (!empty($rq->partner_transaction_id)) {
          $inputs = [
            'data' => [
              'payment_channel_code' => $rq->payment_channel_code,
              'payment_method_code' => $rq->payment_method_code,
              'payment_account_id' => $rq->contract_code,
              'partner_request_id' => $rq->partner_request_id,
              'partner_transaction_id' => $rq->partner_transaction_id,
              'amount_payment' => 0,
              'amount_receiver' => 0,
              // 'fee' => '',
              'request_exists' => CollectDebtEnum::PARTNER_REQUEST_DA_CO_YC_THANH_TOAN,
              'description' => 'Công nợ 0đ do hủy yc trích tự động. Vẫn hạch toán như bình thường',
              'created_by' => Helper::getCronJobUser(),
              'time_created' => time(),
              'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY
            ]
          ];

          $request->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
          app(TaoCongNoSubAction::class)->run($rq, $request);

          $rq->time_receivered = time();
          $rq->save();
        }

        // Push vào mảng các yc đã hủy thành công
        $this->cancelRequestIds[] = $rq->partner_request_id;
      } // End foreach

      $plans = CollectDebtRequest::loadSchedules($listYcMuonHuy);
      $plans->map(function (CollectDebtSchedule $plan) {
        $plan->other_data = $plan->resetKenhThu();
        return $plan;
      });
      
      DB::commit();
      return $this->cancelRequestIds;
    }catch(\Throwable $th) {
      DB::rollBack();
      throw $th;
    }
  } 
}  // End class