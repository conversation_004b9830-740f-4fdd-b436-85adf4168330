<?php
namespace App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareUpdateAction;

use App\Modules\CollectDebt\Model\CollectDebtShare;
use Exception;

class DebtRecoveryShareUpdateAction
{
    public function run($request)
    {
        $params = $this->mapData($request);

        $debtRecoveryShare = CollectDebtShare::where('id', $params['id'])->first();

        $debtRecoveryShare = $debtRecoveryShare->update($params);

        throw_if(!$debtRecoveryShare, new Exception('Không thể cập nhật được bản ghi'));

        return [
            'id' => $params['id']
        ];
    }

    protected function mapData($request)
    {
        $dataResponse = [];

        $dataResponse['id'] = $request['id'];

        if (isset($request['contract_code']) && !empty($request['contract_code'])) {
            $dataResponse['contract_code'] = $request['contract_code'];
        }

        if (isset($request['description']) && !empty($request['description'])) {
            $dataResponse['description'] = $request['description'];
        }

        if (isset($request['other_data']) && !empty($request['other_data'])) {
            $dataResponse['other_data'] = json_encode($request['other_data'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['contract_data']) && !empty($request['contract_data'])) {
            $dataResponse['contract_data'] = json_encode($request['contract_data'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['profile_data']) && !empty($request['profile_data'])) {
            $dataResponse['profile_data'] = json_encode($request['profile_data'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['company_data']) && !empty($request['company_data'])) {
            $dataResponse['company_data'] = json_encode($request['company_data'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['payment_guide']) && !empty($request['payment_guide'])) {
            $dataResponse['payment_guide'] = json_encode($request['payment_guide'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['updated_by']) && !empty($request['updated_by'])) {
            $dataResponse['updated_by'] = $request['updated_by'];
        }

        $dataResponse['time_updated'] = time();


        return $dataResponse;
    }
}