<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class DamBaoYcMposBiHuyPhaiDaHachToanTask {
	/**
	 * Rất có thể vận hành sẽ dùng tay để hủy yc mpos trước, sau đó mới có tiền về từ VA/IB_OFF
	 * Hàm này phải  đảm bảo, nếu như có lệnh MPOS đang hủy, thì lệnh đó phải đảm bảo là đã hạch toán
	 * 
	 * @param string $contractCode
	 * @return void
	 */
	public function run(string $contractCode): bool {
		$today = now()->startOfDay()->timestamp;
		$listYeuCauTrichHomNay = CollectDebtRequest::query()
																							 ->with('collectDebtLedger')
																							 ->where('contract_code', $contractCode)
																							 ->where('payment_method_code', 'MPOS')
																							 ->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
																							 ->where('time_created', '>=', $today)
																							 ->select(['*'])
																							 ->get();
		if ($listYeuCauTrichHomNay->isEmpty()) {
			mylog(['Khong co thong tin check' => 'ok']);
			return true;
		}

		foreach ($listYeuCauTrichHomNay as $collectDebtRequest) {
			if ( !empty($collectDebtRequest->partner_transaction_id) ) {
				// có mã chứng từ
				if ( !empty($collectDebtRequest->time_canceled) ) {
					mylog(['Yeu cau da bi huy' => $collectDebtRequest->partner_request_id]);
					
					if ($collectDebtRequest->isYeuCauChuaHachToanTrenSo()) {
						mylog(['Yeu cau chua co so hoac chua duoc hach toan' => 'Bat buojc phai doi']);
						return false;	
					}
				}
			}else {
				// không có mã chứng từ
				mylog(['Yeu cau khong co ma chung tu, ID: ' => $collectDebtRequest->partner_request_id]);
				continue;
			}
		}

		return true;
	}
} // End class