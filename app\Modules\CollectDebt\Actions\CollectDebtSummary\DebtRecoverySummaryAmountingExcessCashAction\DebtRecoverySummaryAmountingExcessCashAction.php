<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessCashAction;

use DB;
use Exception;
use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryAmountingExcessCashRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetAmountCanRefundAction\DebtRecoverySummaryGetAmountCanRefundAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessCashAction\SubAction\TaoYeuCauHoanTienQuaVer1SubAction;
use Mockery\CountValidator\Exact;

class DebtRecoverySummaryAmountingExcessCashAction
{
  public function run(DebtRecoverySummaryAmountingExcessCashRequest $request)
  {
    if ($request->isRefundAction()) {
      $params = $request->json('data');
      $soTienCoTheHoan = app(DebtRecoverySummaryGetAmountCanRefundAction::class)->run(['contract_code' => $params['contract_code']]);

      throw_if(
        $soTienCoTheHoan['total_amount_can_refund'] < $params['amount'],
        new Exception('Số tiền bạn muốn hoàn đang bị LỚN HƠN so với số tiền đã thu. Số tiền đã thu: ' . Helper::priceFormat($soTienCoTheHoan['total_amount_can_refund']))
      );

			$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $params['contract_code'])->first();

			// Kiem tra xem da co yc hoan tien nao dang chay hay khong
			$collectDebtLogChuaXuLy = CollectDebtLog::query()
																							->whereIn('service_code', [
																								CollectDebtEnum::RL_DICH_VU_HOAN_PHI,
																								CollectDebtEnum::RL_DICH_VU_HOAN_THU_THUA,
																							])
																							->where('reference_id', $collectDebtSummary->id)
																							->whereIn('status', [
																								CollectDebtEnum::RL_STT_MOI_TAO,
																								CollectDebtEnum::RL_STT_DANG_XU_LY,
																							])
																							->first();
			if ($collectDebtLogChuaXuLy) {
				mylog(['ERROR TON TAI' => 'Da ton tai yc hoan tien chua xu ly xong']);
				throw new Exception('ERROR TON TAI - Da ton tai yc hoan tien chua xu ly xong');
			}

			// Tao ban ghi log 
			$paramCreateLogRecord = [
				'service_code' => CollectDebtEnum::RL_DICH_VU_HOAN_THU_THUA,
				'reference_id' => $collectDebtSummary->id,
				'order_code'   => strtoupper('RFE-' . Str::random(12)),
				'created_by'   => $request->json('data.user_request_id'),
				'time_created' => now()->timestamp,
				'description'  => $request->json('data.description')
			];

			while (
				CollectDebtLog::query()->where('order_code', $paramCreateLogRecord['order_code'])->exists()
			) {
				$paramCreateLogRecord['order_code'] = strtoupper('RFE-' . Str::random(12));
			}
	
			$collectDebtLog = CollectDebtLog::query()->forceCreate($paramCreateLogRecord);
			
			if (!$collectDebtLog) {
				mylog([
					'[ERROR]' => 'khong the tao yc hoan thu thua',
					'Param Tao' => $paramCreateLogRecord,
				]);
	
				throw new Exception('khong the tao yc hoan thu thua');
			};
			
			$r = $collectDebtSummary->update([
				'amount_refunding' => DB::raw("amount_refunding + " . $params['amount'])
			]);

			if (!$r) {
				mylog(['LOI INCREASE AMOUNT REFUNDING' => 'ok']);
				throw new Exception('LOI INCREASE AMOUNT REFUNDING');
			}

			$paramHoanSangV1 = [
				'amount'          => $params['amount'],
				'admin_user_id' 	=> $params['admin_user_id'],
				'description'   	=> $collectDebtLog->description,
				'contract_code' 	=> $collectDebtSummary->contract_code,
				'summary_id'    	=> $collectDebtSummary->id,
				'service_code'  	=> 'CONTRACT_EXCESS',
				'data_source'      => [
					'summary_id' 	=> $collectDebtSummary->id,
					'success_url' => '/DebtRecoverySummaryPlusExcessRefund',
					'cancel_url' 	=> '/DebtRecoverySummaryCancelExcessRefund',
					'attachments'	=>  $request->json('data.attachments', []),
					'other_data' => []
				],
				'order_code'    => $collectDebtLog->order_code
			];

			mylog(['param hoan sang v1' => $paramHoanSangV1]);
      $ycHoanV1 = app(TaoYeuCauHoanTienQuaVer1SubAction::class)->run($paramHoanSangV1);
			mylog(['ket qua goi hoan sang v1' => $ycHoanV1]);

			$updated = $collectDebtLog->update([
				'response' => json_encode($ycHoanV1['response']),
				'request_data' => json_encode($ycHoanV1['request_data']),
			]);
	
			mylog(['ket qua cap nhat debt_recovery_log cho request_data va response' => $updated]);
	
			return $collectDebtLog;
    }

		throw new Exception('Khong biet la yeu cau gi');
  }
} // End class
