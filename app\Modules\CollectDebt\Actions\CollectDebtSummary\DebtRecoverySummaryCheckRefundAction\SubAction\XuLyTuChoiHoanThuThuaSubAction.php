<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\SubAction;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class XuLyTuChoiHoanThuThuaSubAction
{
	public function run(CollectDebtLog $collectDebtLog, CollectDebtSummary $collectDebtSummary)
	{
		// Từ chối hoàn tiền thu thừa thì chỉ cần hạ số tiền amount_refunding trên tổng hợp là xong
		$soTienYeuCauHoan = $collectDebtLog->getSoTienYeuCauHoan();
		
		$otherData = $collectDebtSummary->getSummaryOtherData();
		$otherData[] = [
			'type' => 'REFUND',
			'data' => [
				'soTienYeuCauHoan' => $soTienYeuCauHoan
			],
			'time_modified' => now()->timestamp,
			'note' => 'Từ chối yêu cầu hoàn từ nguồn thu thừa'
		];

		$r = $collectDebtSummary->update([
			'amount_refunding' => DB::raw('amount_refunding - ' . $soTienYeuCauHoan),
			'other_data' => json_encode($otherData),
			'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO
		]);
		
		if (!$r) {
			mylog(['Loi cap nhat summary tu choi yeu cau hoan thu thua' => 'Loi']);
			throw new Exception('Loi cap nhat summary tu choi yeu cau hoan thu thua');
		}

		return $collectDebtSummary;
	}
}
