<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowCreateAction;

use App\Modules\CollectDebt\Enums\DebtNowEnum;
use App\Modules\CollectDebt\Model\CollectDebtNotifyDebtNow;

class CollectDebtNotifyDebtNowCreateAction
{
	public function run($params)
	{
		$params = [
			'contract_code' => $params['contract_code'],
			'to'            => $params['to'],
			'cc'            => $params['cc'] ?? '',
			'sender'        => $params['sender'],
			'subject'       => $params['subject'],
			'description'   => $params['description'] ?? '',
			'created_at'    => now(),
			'other_data'    => json_encode($params['other_data']) ?? '{}',
			'request_id'		=> $params['request_id'],
			'status' 			  => $params['status'] ?? DebtNowEnum::STT_MOI_TAO
		];

		$collectDebtNotifyDebtNow = CollectDebtNotifyDebtNow::query()->forceCreate($params);

		return $collectDebtNotifyDebtNow;
	}
}
