<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\TrichNgayTask;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\DebtRecoveryContractGuideCreatePlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction\BuildDailyCollectScheduleSubAction;

class DongBoTaoLichThuGocTrichNgayChuaQuaHanTask
{
  public $soTienNoGocKyTruoc = 0;

  public $soTienDaTrichThanhCong = 0;

  public function run(CollectDebtGuide $collectDebtGuide, float $soTienDaTrichThanhCong=0)
  {
    $this->soTienDaTrichThanhCong = $soTienDaTrichThanhCong;

    $buildDaily = app(DebtRecoveryContractGuideCreatePlanAction::class)->handleCreateDailySchedule($collectDebtGuide);

    foreach ($buildDaily as &$schedule) {
      if ($soTienDaTrichThanhCong > 0) {
        $soTienMangDiThanhToanThanhCong = min($soTienDaTrichThanhCong, $schedule['request_amount_debit']);
        $schedule['success_amount_debit'] = $soTienMangDiThanhToanThanhCong;
        
        $soTienDaTrichThanhCong -= $soTienMangDiThanhToanThanhCong;
      }
    }
    
    $inserted = CollectDebtSchedule::insert($buildDaily);
    $plans = CollectDebtSchedule::where('contract_code', $collectDebtGuide->contract_code)->get();

    $plans->map(function (CollectDebtSchedule $plan)  {
      if ($this->soTienNoGocKyTruoc > 0) {
        $plan->request_amount_debit += $this->soTienNoGocKyTruoc;
        $this->soTienNoGocKyTruoc = 0;
      }

      if ($this->soTienDaTrichThanhCong > 0) {
        $soTienMangDiThanhToan = min($this->soTienDaTrichThanhCong, $plan->request_amount_debit);
        $plan->success_amount_debit = $soTienMangDiThanhToan;
        $plan->status = CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH;
        $this->soTienDaTrichThanhCong -= $soTienMangDiThanhToan;
        

        if ($plan->getSoTienConPhaiThanhToan() > 0) {
          $this->soTienNoGocKyTruoc = $plan->getSoTienConPhaiThanhToan();
        }
      }

      $plan->save();

      return $plan;
    });

    return $plans->last();
  }
} // End class