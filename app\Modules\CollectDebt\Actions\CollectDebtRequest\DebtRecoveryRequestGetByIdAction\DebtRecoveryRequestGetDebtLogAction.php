<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByIdAction;

use App\Lib\ApiCall;
use App\Utils\CommonVar;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class DebtRecoveryRequestGetDebtLogAction
{
	public function run(CollectDebtRequest $collectDebtRequest): array
	{
		$payload = [
			'module' => CommonVar::API_REQUEST_DEBT_MODULE,
			'path' => '/collect-debt-log/getAllByFollow',
			'params' => [
				// 'partner_request_id' => $collectDebtRequest->partner_request_id,
				'request_id' => $collectDebtRequest->id
			],
			'method' => 'GET'
		];

		$requestDebtLogs = (new ApiCall())->callFunctionApi($payload, true);

		return $requestDebtLogs['data'] ?? [];
	}
} // End class