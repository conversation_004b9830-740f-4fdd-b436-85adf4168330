<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\Task\DongBoTaoLichThuGocTask;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\TrichNgayTask\DongBoTaoLichThuGocTrichNgayTask;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\TrichNgayTask\DongBoTaoLichThuPhiQuaHanTrichNgayTask;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\TrichNgayTask\DongBoTaoLichThuGocTrichNgayChuaQuaHanTask;


class DongBoHopDongVer1SubAction
{
  const STATUS_CHUA_TRICH = 1;
  const STATUS_DA_TRICH = 2;

  const HD_TEST = 'MPOS-2312261550561-L3';

  public $noGocTinhTienPhaiThu = 0;

  public function run()
  {
    CollectDebtSchedule::where('contract_code', self::HD_TEST)->delete();
    CollectDebtRequest::where('contract_code', self::HD_TEST)->delete();
    CollectDebtLedger::where('contract_code', self::HD_TEST)->delete();
    
  
    $collectDebtGuide = CollectDebtGuide::where('contract_code', self::HD_TEST)->first();
  
    $guideOtherData = json_decode($collectDebtGuide->other_data, true);

    dump($collectDebtGuide);

    $soTienVayHopDong = $collectDebtGuide->amount;
    $noGocConPhaiThu = $guideOtherData['total_account_payment']['amount_debit'];
    
    $noGocDaThuThanhCong = $soTienVayHopDong - $noGocConPhaiThu;

  /*-------- Trích ngày ------*/
    if ($collectDebtGuide->isChiDanHopDongTrichNgay()) {
      $lichThuGocTrichNgay = null;

      if ($noGocConPhaiThu > 0) {
        if (now()->gte($collectDebtGuide->time_end_as_date)) {
          // quá hạn rồi thì chỉ tạo 1 lịch thu gốc
          $lichThuGocTrichNgay = app(DongBoTaoLichThuGocTrichNgayTask::class)->run($collectDebtGuide, $noGocConPhaiThu);
        }else {
          // đang thu thì build-up lịch như bình thường
          $lichThuGocTrichNgay = app(DongBoTaoLichThuGocTrichNgayChuaQuaHanTask::class)->run($collectDebtGuide, $noGocDaThuThanhCong);
        }
        
        throw_if(!$lichThuGocTrichNgay, new Exception('Không thể tạo được lịch thu gốc trích ngày'));
      }
      
      // Có phí quá hạn
      if ( !empty($guideOtherData['total_account_payment']['fee_deferred']) ) {
        $soPhiQuaHanConPhaiThu = $guideOtherData['total_account_payment']['fee_deferred'] - $guideOtherData['total_account_payment']['discount_fee_cycle_deferred'];
        if ($soPhiQuaHanConPhaiThu > 0) {
          $lichThuPhiQuaHanTrichNgay = app(DongBoTaoLichThuPhiQuaHanTrichNgayTask::class)->run($collectDebtGuide, $lichThuGocTrichNgay, $soPhiQuaHanConPhaiThu);
        }
      }
    }

  /*-------- Trích kỳ ------*/
    if ($collectDebtGuide->isChiDanHopDongTrichKy()) {
      $plan = app(DongBoTaoLichThuGocTask::class)->run(
        $collectDebtGuide, 
        $guideOtherData
      );
    } // end if trích kỳ
  } // end method

  public function buildPlanStatusV4($cycle = [])
  {
    if ($cycle['status'] == self::STATUS_CHUA_TRICH) {
      return CollectDebtEnum::SCHEDULE_STT_MOI;
    }

    if ($cycle['status'] == self::STATUS_DA_TRICH) {
      return CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH;
    }
  }
} // End class