<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtRequest;

use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

trait TimelineMethodTrait
{
  public function getTimelineAttribute(): string
  {
    $timeline = '';

    if (!empty($this->time_created)) {
      $p = json_decode($this->created_by, true);

      $timeline .= sprintf(
        '<span class="d-block" title="Tạo yêu cầu">Tạo: <code>%s</code> - <code>%s</code></span>', 
        date('H:i, d/m/Y', $this->time_created), 
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_approved)) {
      if ($this->isAutoDebt() || $this->isTrichTayThuGoc()) {
        $p = json_decode($this->approved_by, true);
      
        $timeline .= sprintf(
          '<span class="d-block">Duyệt: <code>%s</code> - <code>%s</code></span>', 
          date('H:i, d/m/Y', $this->time_approved),
          $p['username'] ?? 'N/A'
        );
      }
      

      if ($this->isTrichTayGiamPhi()) {
        if ( !empty($this->time_approved) ) {
          $p = json_decode($this->approved_by, true);
          $timeline .= sprintf(
            '<span class="d-block">Duyệt B1: <code>%s</code> - <code>%s</code></span>', 
            date('H:i, d/m/Y', $this->time_approved),
            $p['username'] ?? 'N/A'
          );
        }

        if ( !empty($this->isThoigianDuyet2()) && !empty($this->isNguoiDuyet2())) {
          $p = $this->isNguoiDuyet2();

          if (is_string($p)) {
            $p = json_decode($p, true);
          }
          
          $timeline .= sprintf(
            '<span class="d-block">Duyệt B2: <code>%s</code> - <code>%s</code></span>', 
            date('H:i, d/m/Y', $this->isThoigianDuyet2()),
            $p['username'] ?? 'N/A'
          );
        }
      }
    }

    if (!empty($this->time_updated)) {
      $p = json_decode($this->updated_by, true);

      $timeline .= sprintf(
        '<span class="d-block" title="Cập nhật">Cập nhật: <code>%s</code> - <code>%s</code></span>', 
        date('H:i, d/m/Y', $this->time_updated), 
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_sended)) {
      $p = json_decode($this->sended_by, true);

      $timeline .= sprintf(
        '<span class="d-block">Gửi TT: <code>%s</code> - <code>%s</code></span>', 
        date('H:i, d/m/Y', $this->time_sended),
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_canceled_payment)) {
      $p = json_decode($this->canceled_payment_by, true);

      $timeline .= sprintf(
        '<span class="d-block">Từ chối TT: <code>%s</code> - <code>%s</code></span>', 
        date('H:i, d/m/Y', $this->time_canceled_payment),
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_canceled)) {
      $p = json_decode($this->canceled_by, true);

      $timeline .= sprintf(
        '<span class="d-block">Hủy TT: <code>%s</code> - <code>%s</code></span>', 
        date('H:i, d/m/Y ', $this->time_canceled),
        $p['username'] ?? 'N/A'
      );
    }


    if (!empty($this->time_completed_recheck)) {
      $p = json_decode($this->completed_recheck_by, true);

      $timeline .= sprintf(
        '<span class="d-block">K.Tra: <code>%s</code> - <code>%s</code></span>',  
        date('H:i, d/m/Y', $this->time_completed_recheck),
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_receivered)) {
      $p = json_decode($this->receivered_by, true);

      $timeline .= sprintf(
        '<span class="d-block">Nhận TT: <code>%s</code> - <code>%s</code></span>', 
        date('H:i, d/m/Y', $this->time_receivered),
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_recored)) {
      $p = json_decode($this->recored_by, true);
      $timeline .= sprintf(
        '<span class="d-block">GS: <code>%s</code> - <code>%s</code></span>',  
        date('H:i, d/m/Y', $this->time_recored),
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_completed)) {
      $p = json_decode($this->completed_by, true);
      $timeline .= sprintf(
        '<span class="d-block">H.Thành: <code>%s</code> - <code>%s</code></span>', 
        date('H:i, d/m/Y', $this->time_completed),
        $p['username'] ?? 'N/A'
      );
    }

    return $timeline;
  }
} // End class
