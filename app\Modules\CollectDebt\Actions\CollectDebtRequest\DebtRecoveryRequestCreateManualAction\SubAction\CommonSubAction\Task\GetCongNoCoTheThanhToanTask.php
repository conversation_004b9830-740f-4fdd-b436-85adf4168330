<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtPartner;

class GetCongNoCoTheThanhToanTask
{
  /**
   * Kiểm tra số dư chuyển kho<PERSON>n, nếu số dư này không đủ thanh toán tổng tiền cần phải thanh toán thì bắn lỗi luôn
   * @param string $partnerTransactionId: Mã chứng từ
   * @param float $tongTienCanPhaiThuCacLich: Tổng tiền cần thu các lịch (đã ốp giảm phí)
   * @return CollectDebtPartner
   */
  public function run(string $partnerTransactionId, float $tongTienCanPhaiThuCacLich, string $paymentMethodCode): CollectDebtPartner {
    $collectDebtPartner = CollectDebtPartner::query()
                                            ->where('payment_method_code', $paymentMethodCode)
                                            ->where('partner_transaction_id', $partnerTransactionId)
                                            ->first();

    throw_if(
      !$collectDebtPartner, 
      new Exception(
        sprintf('Không tồn tại chứng từ `%s` qua kênh `%s`', $partnerTransactionId, $paymentMethodCode)
      )
    );

    $soDuKhaDungCoTheThanhToan = $collectDebtPartner->getAmountBalance();
    
    throw_if(
      $soDuKhaDungCoTheThanhToan < $tongTienCanPhaiThuCacLich,
      new Exception(
        sprintf('Số dư khả dụng không thể thanh toán các lịch thu hiện tại. Số dư khả dụng là: `%s`. Số tiền cần thanh toán các lịch là: %s',
          Helper::priceFormat($soDuKhaDungCoTheThanhToan, 'đ'),
          Helper::priceFormat($tongTienCanPhaiThuCacLich, 'đ')
        )
      )
    );

    return $collectDebtPartner;
  }
} // End class
