<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\SubAction\Task;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;

class LuuHopDongTatToanDigitalNotiTask
{
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$digitalNoti = CollectDebtDigitalNoti::query()->forceCreate([
			'contract_code' => $collectDebtSummary->contract_code,
			'type' => 'settled',
			'object_model' => CollectDebtSummary::class,
			'object_id' => $collectDebtSummary->id,
			'digital_request' => '',
			'digital_response' => '',
			'status' => CollectDebtDigitalNoti::STT_MOI_TAO,
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp,
		]);

		if (!$digitalNoti) {
			throw new Exception('Lỗi không lưu đ<PERSON> bản ghi noti');
		}

		return true;
	}
}
