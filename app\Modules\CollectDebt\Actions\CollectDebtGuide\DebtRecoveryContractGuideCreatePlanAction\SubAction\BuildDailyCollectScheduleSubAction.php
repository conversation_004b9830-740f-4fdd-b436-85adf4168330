<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use Carbon\CarbonPeriod;

class BuildDailyCollectScheduleSubAction
{
  private $__totoalMoneyNeedPayment = 0;
  
  /**
   * Tính toán lịch thu dựa vào chỉ dẫn
   *
   * @param CollectDebtGuide $collectDebtGuide [Bản ghi chỉ dẫn]
   * @param int $totalPeriod [Số kỳ thu nợ mà MC cần thanh toán]
   *
   * @return array 
   * $scheduleInfo = [
   *    [
   *      'payment_date' => 'Y-m-d H:i:s',
          'money_need_payment' => float,
          'money_need_payment_as_price' => string
   *    ]
   * ]
   */
  public function run(CollectDebtGuide $collectDebtGuide): array
  {
    $T = $collectDebtGuide->time_start_as_date->copy()->addDay(); // ngày bắt đầu thu hồi sẽ sau ngày bắt đầu HĐ 1 ngày
    $totalDateBetweenTdayAndTimeEnd = CarbonPeriod::create($T, $collectDebtGuide->time_end_as_date);

    $totalPeriod = $totalDateBetweenTdayAndTimeEnd->count();
    $averageAmountToBePaidPerPeriod = round($collectDebtGuide->amount / $totalPeriod);
    
    foreach ($totalDateBetweenTdayAndTimeEnd as $currentPeriod => $periodPaymentDate) {
      if ($currentPeriod != $totalPeriod-1) {
        // Không phải kỳ cuối. -1 là do mảng đếm từ vị trí 0
        $this->__totoalMoneyNeedPayment += $averageAmountToBePaidPerPeriod;

        $scheduleInfo[] = [
          'collect_debt_schedule' => $periodPaymentDate, // Ngày chu kỳ trích nợ
          'amount_to_be_paid' => $averageAmountToBePaidPerPeriod,
          'amount_to_be_paid_as_price' => Helper::priceFormat($averageAmountToBePaidPerPeriod),
          
          'debit_begin' => $collectDebtGuide->amount - $currentPeriod *  $averageAmountToBePaidPerPeriod,
          'debit_end' => $collectDebtGuide->amount - $this->__totoalMoneyNeedPayment,
          'is_settlement' => 0,
          'cycle_number'               => $currentPeriod,
          'other_data'                 => json_encode([
            [
              'type' => 'OTHER',
              'data' => [
                'request_created_channel' => '', // Yêu cầu được tạo qua kênh,
              ],
              'time_modified' => time(),
              'note' => 'Tạo lịch từ chỉ dẫn'
            ],
          ])
        ];
      } else {
        // Kỳ cuối
        $periodPaymentDate = $collectDebtGuide->time_end_as_date;
        $scheduleInfo[] = [
          'collect_debt_schedule' => $periodPaymentDate,
          'amount_to_be_paid' => $collectDebtGuide->amount - $this->__totoalMoneyNeedPayment,
          'amount_to_be_paid_as_price' => Helper::priceFormat($collectDebtGuide->amount - $this->__totoalMoneyNeedPayment),
          
          'debit_begin' => $collectDebtGuide->amount - $currentPeriod *  $averageAmountToBePaidPerPeriod,
          'debit_end' => 0,
          'is_settlement' => 1,
          'cycle_number'               => $currentPeriod,
          'other_data'                 => json_encode([
            [
              'type' => 'OTHER',
              'data' => [
                'request_created_channel' => '', // Yêu cầu được tạo qua kênh
              ],
              'time_modified' => time(),
              'note' => 'Tạo lịch từ chỉ dẫn'
            ],
          ])
        ];
      }
    }

    
    return $scheduleInfo;
  } // End method
} // End class
