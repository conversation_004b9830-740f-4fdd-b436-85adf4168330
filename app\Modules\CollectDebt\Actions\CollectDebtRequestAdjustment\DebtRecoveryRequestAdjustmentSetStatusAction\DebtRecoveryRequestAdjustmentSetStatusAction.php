<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentSetStatusAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentSetStatusRequest;
use Exception;

class DebtRecoveryRequestAdjustmentSetStatusAction
{
  private $__apiMessage = '';

  public function run(DebtRecoveryRequestAdjustmentSetStatusRequest $request)
  {
    $userHandler = $request->json('data.user_request_id');
    $collectDebtRequestAdjustment = CollectDebtRequestAdjustment::query()->find($request->json('data.id'));
    throw_if(!$collectDebtRequestAdjustment, new Exception('Yêu cầu điều chỉnh không tồn tại'));

    /**
     * Thực hiện theo logic sau
     * 1=>2,4
     * 2=>3,4
     * 3=>4,5
     * 5=>6,4
     * 6,4 là trạng thái cuối
     */
    $status = $request->json('data.status');

    if (!empty($request->json('data.description'))) {
      $collectDebtRequestAdjustment->description = $request->json('data.description');
    }

    // Moi tao -> duyet 1
    if ($collectDebtRequestAdjustment->isStatusMoiTao() && $status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_1) {

      $collectDebtRequestAdjustment->time_approved1 = time();
      $collectDebtRequestAdjustment->approved1_by = $userHandler;
      $collectDebtRequestAdjustment->other_data = $collectDebtRequestAdjustment->putRAOtherData([
        'type' => 'RA_APPROVED_1',
        'data' => $collectDebtRequestAdjustment->makeHidden(['other_data', 'reference_data']),
        'time_modified' => time(),
        'note' => $request->json('data.description')
      ]);

      $this->__apiMessage = 'Duyệt 1 cho yêu cầu điều chỉnh thành công';
    }

    // Moi tao -> huy
    if ($collectDebtRequestAdjustment->isStatusMoiTao() && $status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HUY) {
      $collectDebtRequestAdjustment->time_canceled = time();
      $collectDebtRequestAdjustment->canceled_by = $userHandler;
      $collectDebtRequestAdjustment->other_data = $collectDebtRequestAdjustment->putRAOtherData([
        'type' => 'RA_CANCEL',
        'data' => $collectDebtRequestAdjustment->makeHidden(['other_data', 'reference_data']),
        'time_modified' => time(),
        'note' => $request->json('data.description')
      ]);

      $this->__apiMessage = 'Hủy yêu cầu điều chỉnh thành công';
    }

    // Duyet 1 -> duyet 2
    if ($collectDebtRequestAdjustment->isStatusDuyet1() && $status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DUYET_2) {
      $collectDebtRequestAdjustment->time_approved2 = time();
      $collectDebtRequestAdjustment->approved2_by = $userHandler;
      $collectDebtRequestAdjustment->other_data = $collectDebtRequestAdjustment->putRAOtherData([
        'type' => 'RA_APPROVED_2',
        'data' => $collectDebtRequestAdjustment->makeHidden(['other_data', 'reference_data']),
        'time_modified' => time(),
        'note' => $request->json('data.description')
      ]);

      $this->__apiMessage = 'Duyệt 2 cho yêu cầu điều chỉnh thành công';
    }

    // Duyet 1 -> Huy 
    if ($collectDebtRequestAdjustment->isStatusDuyet1() && $status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HUY) {
      $collectDebtRequestAdjustment->time_canceled = time();
      $collectDebtRequestAdjustment->canceled_by = $userHandler;
      $collectDebtRequestAdjustment->other_data = $collectDebtRequestAdjustment->putRAOtherData([
        'type' => 'RA_CANCEL',
        'data' => $collectDebtRequestAdjustment->makeHidden(['other_data', 'reference_data']),
        'time_modified' => time(),
        'note' => $request->json('data.description')
      ]);

      $this->__apiMessage = 'Hủy yêu cầu điều chỉnh thành công';
    }

    // Duyet 2 -> Huy 
    if ($collectDebtRequestAdjustment->isStatusDuyet2() && $status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HUY) {
      $collectDebtRequestAdjustment->time_canceled = time();
      $collectDebtRequestAdjustment->canceled_by = $userHandler;
      $collectDebtRequestAdjustment->other_data = $collectDebtRequestAdjustment->putRAOtherData([
        'type' => 'RA_CANCEL',
        'data' => $collectDebtRequestAdjustment->makeHidden(['other_data', 'reference_data']),
        'time_modified' => time(),
        'note' => $request->json('data.description')
      ]);

      $this->__apiMessage = 'Hủy yêu cầu điều chỉnh thành công';
    }

    // Duyet 2 -> Dang Xu Ly
    if ($collectDebtRequestAdjustment->isStatusDuyet2() && $status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_DANG_XU_LY) {
      // Cho nay chua co thong tin xu ly
    }

    // Dang xu ly -> hoan thanh
    if ($collectDebtRequestAdjustment->isStatusDangXuLy() && $status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HOAN_THANH) {
      $collectDebtRequestAdjustment->time_completed = time();
      $collectDebtRequestAdjustment->completed_by = $userHandler;
      $collectDebtRequestAdjustment->other_data = $collectDebtRequestAdjustment->putRAOtherData([
        'type' => 'RA_DONE',
        'data' => $collectDebtRequestAdjustment->makeHidden(['other_data', 'reference_data']),
        'time_modified' => time(),
        'note' => $request->json('data.description')
      ]);

      $this->__apiMessage = 'Đã đánh dấu yêu cầu điều chỉnh là hoàn thành';
    }

    // Dang xu ly -> da huy
    if ($collectDebtRequestAdjustment->isStatusDangXuLy() && $status == CollectDebtEnum::REQUEST_ADJUSTMENT_STT_HUY) {
      $collectDebtRequestAdjustment->time_canceled = time();
      $collectDebtRequestAdjustment->canceled_by = $userHandler;
      $collectDebtRequestAdjustment->other_data = $collectDebtRequestAdjustment->putRAOtherData([
        'type' => 'RA_CANCEL',
        'data' => $collectDebtRequestAdjustment->makeHidden(['other_data', 'reference_data']),
        'time_modified' => time(),
        'note' => $request->json('data.description')
      ]);

      $this->__apiMessage = 'Hủy yêu cầu điều chỉnh thành công';
    }

    throw_if(empty($this->__apiMessage), new Exception('Hệ thống không hiểu nghiệp vụ mà bạn đang thao tác'));

    $collectDebtRequestAdjustment->status = $status;
    $collectDebtRequestAdjustment->save();
    $collectDebtRequestAdjustment->__apiMessage = $this->__apiMessage;
    return $collectDebtRequestAdjustment;
  }
}
