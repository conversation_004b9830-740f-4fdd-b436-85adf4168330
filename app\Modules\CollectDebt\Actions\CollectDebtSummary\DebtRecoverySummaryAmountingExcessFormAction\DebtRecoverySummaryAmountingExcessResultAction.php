<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessFormAction;


use Exception;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryAmountingExcessResultRequest;

class DebtRecoverySummaryAmountingExcessResultAction
{
  public function run(DebtRecoverySummaryAmountingExcessResultRequest $request): CollectDebtSummary
  {
    // Chỉ sử dụng SharedLock để tránh các lần gọi API cùng lúc sẽ phải đợi nhau và xử lý trên dữ liệu mới nhất
    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $request->json('data.contract_code'))->sharedLock()->first();
    throw_if(!$collectDebtSummary, new Exception('Hợp đồng không tồn tại'));

    $amountRefunding = $collectDebtSummary->amount_refunding;

    throw_if(
      $amountRefunding < $request->getSoTienYeuCauHoan(),
      new Exception('Số tiền refunding và số tiền bạn truyền lên không hợp lý')
    );


    $collectDebtSummary->__apiMessage = 'Hoàn tiền thành công';

    $soTienYeuCauHoan = $request->getSoTienYeuCauHoan();
    $soTienHoanThanhCong = $request->getSoTienHoanThanhCong();

    $soTienGiamTruAmountRefunding = $soTienYeuCauHoan; // Loại bỏ số tiền xử lý hoàn
    $otherData = $this->setRefundResultToOtherData($collectDebtSummary, $request->json('data.order_code'));

    $updated = CollectDebtSummary::query()
      ->where('contract_code', $request->json('data.contract_code'))
      ->update([
        'total_amount_refund' => DB::raw("total_amount_refund + " . $soTienHoanThanhCong),
        'amount_refunding' => DB::raw("amount_refunding - " . $soTienGiamTruAmountRefunding),
        'other_data' => $otherData
      ]);

    return $collectDebtSummary;
  }

  public function setRefundResultToOtherData(CollectDebtSummary $collectDebtSummary, string $orderCode=''): string {
    $refundItem = $collectDebtSummary->getSummaryOtherDataItem('REFUND');
    throw_if(empty($refundItem), new Exception('Không có thông tin hoàn tiền'));

    $refundRequest = collect($refundItem['data'])->where('order_code', $orderCode)->first();
    throw_if(empty($refundRequest), new Exception('Yêu cầu hoàn tiền này không tồn tại'));
    throw_if(!empty($refundRequest['time_receivered']), new Exception('Yêu cầu này đã được xử lý'));
    
    // Yêu cầu chưa được xử lý -> update
    foreach ($refundItem['data'] as &$item) {
      if ($item['order_code'] == $orderCode) {
        $item['time_receivered'] = time();
      }
    }

    $refundIndex = $collectDebtSummary->getSummaryOtherDataIndex('REFUND');
    $otherData = $collectDebtSummary->getSummaryOtherData();
    $otherData[$refundIndex] = $refundItem;

    return json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
  }
} // End class
