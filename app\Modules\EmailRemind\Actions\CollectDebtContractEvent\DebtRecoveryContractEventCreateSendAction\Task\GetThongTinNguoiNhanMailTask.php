<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task;

use Exception;
use App\Lib\NextlendCore;

class GetThongTinNguoiNhanMailTask
{
	/**
	 * array:3 [▼
			"Dev TEST ACTION" => array:1 [▼
				22 => "<EMAIL>"
			]
			"NV_TELE_SALE" => array:1 [▼
				14 => "<EMAIL>"
			]
			"NV_THU_HOI_ONLINE" => array:1 [▼
				24 => "<EMAIL>"
			]
		]
	 *
	 * @param string $contractCode
	 * @return void
	 */
	public function run(string $contractCode = '')
	{
		$nextlendCode = app(NextlendCore::class)->callRequest([
			'contract_code' => $contractCode
		], 'ContractV4_getEmailByContractCode', 'GET');

		$listNhanVienChamSoc = $nextlendCode->decryptData(true);

		mylog(['List nhan vien la' => @$listNhanVienChamSoc]);

		if (empty($listNhanVienChamSoc)) {
			mylog(['Loi khong co thong tin nhan vien cham soc' => 'ok']);
			throw new Exception('Loi khong co thong tin nhan vien cham soc');
		}

		$listCc = [];

		if (!empty($listNhanVienChamSoc['NV_THAM_DINH'])) {
			foreach ($listNhanVienChamSoc['NV_THAM_DINH'] as $email) {
				$listCc[] = $email;
			}
		}

		if (!empty($listNhanVienChamSoc['NV_THU_HOI_ONLINE'])) {
			foreach ($listNhanVienChamSoc['NV_THU_HOI_ONLINE'] as $email) {
				$listCc[] = $email;
			}
		}

		$listCc = array_unique($listCc);

		mylog(['Danh sach nhan vien duoc CC la' => $listCc]);

		if (empty($listCc)) {
			throw new Exception('Khong co thong tin nhan vien duoc CC');
		}

		$emailCcInfo = [];
		foreach ($listCc as $email) {
			$emailCcInfo[] = [
				'name' => '',
				'identifier_account' => trim($email),
				'identifier_data' => ''
			];
		}

		return $emailCcInfo;
	}
} // End class
