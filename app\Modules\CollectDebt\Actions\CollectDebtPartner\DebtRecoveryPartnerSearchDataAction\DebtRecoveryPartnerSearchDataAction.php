<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerSearchDataAction;

use Exception;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtPartner;

class DebtRecoveryPartnerSearchDataAction
{
  public function run(Request $request)
  {
    $collectDebtPartnerPagination = CollectDebtPartner::query();
    $collectDebtPartnerPagination = $collectDebtPartnerPagination->latest('id')->paginate(
      $request->json('data.limit', 10),
      ['*'],
      'pahe',
      $request->json('data.page', 1)
    );

    return $collectDebtPartnerPagination;
  }
} // End class