<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtCoreContract;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;

class CreatePartnerKhongDongSubAction
{
  public function run(CollectDebtRequest $collectDebtRequest, CollectDebtCoreContract $coreContract): CollectDebtPartner
  {
		mylog([
			'Dang thuc hien tao parner 0d' => 'Ok',
			'Ban ghi yeu cau' => $collectDebtRequest->partner_transaction_id
		]);

    $partnerTransactionId =  $collectDebtRequest->partner_transaction_id;

    // Nếu null thì là yêu cầu quá khứ, để nguyên là null
    if (!empty($partnerTransactionId)) {
      $partnerTransactionId = sprintf('%s_%s', $collectDebtRequest->partner_transaction_id, date('ymdHis'));
    }else {
      $partnerTransactionId = null;
    }

    $partner = CollectDebtPartner::firstOrCreate([
			'partner_request_id' => $collectDebtRequest->partner_request_id,
		],[
			'contract_code' => $collectDebtRequest->contract_code,
      'payment_channel_code' => 'MPOS',
      'payment_method_code' => 'MPOS',
      'payment_account_id' => $collectDebtRequest->contract_code,
      'partner_request_id' => $collectDebtRequest->partner_request_id,
      'partner_transaction_id' => $partnerTransactionId,
      'amount_payment' => 0,
      'amount_receiver' => 0,
      'request_exists' => CollectDebtEnum::PARTNER_REQUEST_DA_CO_YC_THANH_TOAN,
      'description' => 'CutOff MPOS 0đ',
      'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
      'created_by' => Helper::getCronJobUser(),
      'time_created' => time(),
      'updated_by' => Helper::getCronJobUser(),
      'time_updated' => time(),
      'created_request_by' => Helper::getCronJobUser(),
      'time_created_request' => time(),
      'processing_by' => Helper::getCronJobUser(),
      'time_processing' => time(),
      'complated_by' => Helper::getCronJobUser(),
      'time_complated' => time(),
      'approved_by' => Helper::getCronJobUser(),
      'time_approved' => time(),

      'other_data' => json_encode([
        [
          'type' => 'CONTRACT',
          'time_modified' => time(),
          'data' => $coreContract->only([
            'profile_id',
            'contract_code',
            'contract_time_start',
            'contract_type',
            'contract_cycle',
            'contract_intervals',
            'amount',
            'contract_time_end',
            'description',
          ]),
          'note' => 'Thông tin HĐ'
        ],

        [
          'type' => 'REQUEST',
          'time_modified' => time(),
          'data' => StandardizedDataFilter::getRequestCompactAttribute($collectDebtRequest),
          'note' => 'Cutoff yêu cầu 22h'
        ]
      ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
    ]);

    throw_if(!$partner, new Exception('Không thể tạo partner 0đ'));
    return $partner;
  }
} // End class