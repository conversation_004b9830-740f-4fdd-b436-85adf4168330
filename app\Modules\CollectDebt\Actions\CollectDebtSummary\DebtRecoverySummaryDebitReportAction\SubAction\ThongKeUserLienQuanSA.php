<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;

class ThongKeUserLienQuanSA
{
	public array $userActionMerchant = [];
	public array $userActionContract = [];
	/**
	 * Thống kê nhân viên vận hành liên quan
	 *	"user_action" => array:2 [
			"⁡⁣⁢⁢contract" => array:1 [
				0 => array:4 [
					"id" => 19
					"name" => "Chăm sóc khách hàng "
					"code" => "NV_CSKH"
					"user" => array:1 [
						0 => array:6 [
							"id" => 177
							"username" => "dgddddd"
							"email" => "<EMAIL>"
							"mobile" => "067293525"
							"fullname" => "Nguyễn Văn Anh"
							"address" => null
						]
					]
				]
			]
			"merchant" => array:1 [
				0 => array:4 [
					"id" => 14
					"name" => "Telesales"
					"code" => "NV_TELE_SALE"
					"user" => array:1 [
						0 => array:6 [
							"id" => 172
							"username" => "loanht"
							"email" => "<EMAIL>"
							"mobile" => "0985371625"
							"fullname" => "Hà Thị Loan"
							"address" => null
						]
					]
				]
			]⁡
	 * @param CollectDebtSummary $collectDebtSummary
	 * @return array
	 * array:3 [
	 */
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$contractData = $collectDebtSummary->getContractData();
		if (!empty($contractData['user_action'])) {
			if (!empty($contractData['user_action']['merchant'])) {
				foreach ($contractData['user_action']['merchant'] as $merchant) {
					foreach ($merchant['user'] as $user) {
						
						if (empty($user['id'])) {
							$this->userActionMerchant[] = [
								'name' => $merchant['name'],
								'value' => $user['fullname'],
								'mobile' => $user['mobile'],
								'email' => $user['email'] ?? 'unknow',
								'code' => $merchant['code'],
							];
						}else {
							$this->userActionMerchant[$user['id']] = [
								'name' => $merchant['name'],
								'value' => $user['fullname'],
								'mobile' => $user['mobile'],
								'email' => $user['email'] ?? 'unknow',
								'code' => $merchant['code'],
							];
						}
					}
				}
			}
			
			if (!empty($contractData['user_action']['contract'])) {
				foreach ($contractData['user_action']['contract'] as $contract) {
					foreach ($contract['user'] as $user) {
						if (empty($user['id'])) {
							$this->userActionContract[] = [
								'name' => $contract['name'],
								'value' => $user['fullname'],
								'mobile' => $user['mobile'],
								'email' => $user['email'] ?? 'unknow',
								'code' => $contract['code'],
							];
						}else {
							$this->userActionContract[$user['id']] = [
								'name' => $contract['name'],
								'value' => $user['fullname'],
								'mobile' => $user['mobile'],
								'email' => $user['email'] ?? 'unknow',
								'code' => $contract['code'],
							];
						}
					}
				}
			}
		}

		$userActionMerchant = array_values($this->userActionMerchant);
		$userActionContract = array_values($this->userActionContract);
		$merge = array_merge($userActionContract, $userActionMerchant);	

		return $merge;
	}
}
