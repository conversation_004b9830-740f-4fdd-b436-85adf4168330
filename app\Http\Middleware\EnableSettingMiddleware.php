<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Model\CollectDebtSetting;

class EnableSettingMiddleware
{

  public function handle(Request $request, Closure $next, string $cronJobSettingName='')
  {
		$listSetting = Cache::remember(CacheEnum::LIST_CAU_HINH_FIRE_JOB, now()->addDays(7), function () {
			return CollectDebtSetting::all();
		});

		$settingName = trim($cronJobSettingName);
		$collectDebtSetting = $listSetting->firstWhere('code', $settingName);

    $isEnableSetting = CollectDebtSetting::checkEnableSetting($collectDebtSetting);
    
    throw_if(
      !$isEnableSetting, 
      new Exception(sprintf('Setting: %s chua duoc bat', $cronJobSettingName))
    );
    
    return $next($request);
  }
}
