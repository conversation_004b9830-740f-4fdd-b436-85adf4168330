<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessFormAction\DebtRecoverySummaryAmountingExcessResultAction;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryAmountingExcessCashRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryAmountingExcessFormRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessCashAction\DebtRecoverySummaryAmountingExcessCashAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessFormAction\DebtRecoverySummaryAmountingExcessFormAction;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryAmountingExcessResultRequest;
use DB;
use Illuminate\Http\Request;

class CollectDebtSummaryExcessController extends Controller
{
  public function excessForm(DebtRecoverySummaryAmountingExcessFormRequest $request)
  {
    try {
      $formHoanTien = app(DebtRecoverySummaryAmountingExcessFormAction::class)->run($request);
      return $this->successResponse($formHoanTien, $request);
    } catch (\Throwable $th) {
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }

  public function createExcessCash(DebtRecoverySummaryAmountingExcessCashRequest $request)
  {
		DB::beginTransaction();
    try {
      $collectDebtLog = app(DebtRecoverySummaryAmountingExcessCashAction::class)->run($request);
			DB::commit();
      return $this->successResponse(['id' => $collectDebtLog->id], $request, 200, __('Tạo yêu cầu hoàn tiền từ nguồn thu thừa thành công'));
    } catch (\Throwable $th) {
			DB::rollBack();
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }

  public function result(Request $request)
  {
    return $this->successResponse(['data' => 'ok'], $request, 200);
  }
}
