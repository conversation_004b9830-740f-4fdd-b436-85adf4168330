<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\DigitalNotiTask;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;

class LuuLenhTrichCoTienDigitalNotiTask
{
	public function run(CollectDebtLedger $collectDebtLedger)
	{
		if (empty($collectDebtLedger->amount)) {
			return true;
		}

		$digitalNoti = CollectDebtDigitalNoti::query()->forceCreate([
			'contract_code' => $collectDebtLedger->contract_code,
			'type' => 'accounting',
			'object_model' => CollectDebtLedger::class,
			'object_id' => $collectDebtLedger->id,
			'digital_request' => '',
			'digital_response' => '',
			'status' => CollectDebtDigitalNoti::STT_MOI_TAO,
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp,
		]);

		if (!$digitalNoti) {
			throw new Exception('Lỗi không lưu được bản ghi noti');
		}
	}
} // Ebd class