<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;

class ThongKePhiSA
{
	/**
	 * Undocumented function
	 *
	 * @param CollectDebtSummary $collectDebtSummary
	 * @return array
	 * array:3 [
	 */
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$fees = [
			[
				'name' => 'Phí chậm kỳ',
				'value' => $collectDebtSummary->fee_overdue_cycle 
			],

			[
				'name' => 'Phí quá hạn',
				'value' => $collectDebtSummary->fee_overdue 
			]
		];

		return $fees;	
	}
}
