<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TaoLichThuPhiChamKyST
{
	public function run(CollectDebtSchedule $lichDangHachToan, float $phiChamKy = 0)
	{
		$otherData = [];
		$otherData[] = [
			'type' => 'OTHER',
			'time_modified' => time(),
			'data' => [
				'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_CHAM_KY,
				'fee_config' => [],
				'request_created_channel' => '',
			],
			'note' => 'Sinh phi cham ky'
		];

		$scheduleParam = [
			'profile_id'           => $lichDangHachToan->profile_id,
			'contract_code'        => $lichDangHachToan->contract_code,
			'contract_type'        => $lichDangHachToan->contract_type,
			'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
			'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
			'debit_begin'          => $phiChamKy,
			'debit_end'            => 0,
			'rundate'              => $lichDangHachToan->rundate,
			'time_start'           => time(),
			'time_end'             => $lichDangHachToan->time_end,
			'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
			'request_amount_debit' => $phiChamKy,
			'success_amount_debit' => 0,
			'other_data'           => json_encode($otherData),
			'description'          => $lichDangHachToan->collectDebtSchedule,
			'is_settlement'        => $lichDangHachToan->is_settlement,
			'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
			'created_by'           => $lichDangHachToan->created_by,
			'time_created'         => time(),
			'cycle_number'         => $lichDangHachToan->cycle_number,
			'master_id'         	 => $lichDangHachToan->master_id,
		];

		mylog(['[Param tao lich thu phi cham ky]' => $scheduleParam]);

		$lichThuPhiChamKy = CollectDebtSchedule::forceCreate($scheduleParam);

		if (!$lichThuPhiChamKy) {
			mylog(['[LOI KHONG TAO DUOC LICH THU PHI CHAM KY]' => $lichThuPhiChamKy]);
			throw new Exception('[LOI KHONG TAO DUOC LICH THU PHI CHAM KY]');
		}

		return $lichThuPhiChamKy;
	}
}
