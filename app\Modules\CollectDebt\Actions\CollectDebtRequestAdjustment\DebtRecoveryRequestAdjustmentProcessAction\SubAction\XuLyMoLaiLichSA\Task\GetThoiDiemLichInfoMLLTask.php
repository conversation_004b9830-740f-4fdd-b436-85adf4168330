<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class GetThoiDiemLichInfoMLLTask
{
  public function run(CollectDebtSchedule $lichDangMoLai): array
  {
    $runDate = $lichDangMoLai->rundate_as_date;

    // Mở lại lịch tương lai, gi<PERSON> nguyên các thông số của lịch tương lai
    if (now()->lt($runDate) && !now()->isSameDay($runDate)) {
      return [
        'rundate'    => $lichDangMoLai->rundate,
        'time_start' => $lichDangMoLai->time_start,
        'time_end'   => $lichDangMoLai->time_end,
        'type'       => $lichDangMoLai->type,
      ];
    }

    return [
      'rundate'              => date('Ymd'),
      'time_start'           => now()->timestamp,
      'time_end'             => now()->endOfDay()->timestamp,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU
    ];
  }
}
