<?php

namespace App\Modules\CollectDebtGateway\Requests\MposTestCollectDebtGateway;

use Illuminate\Foundation\Http\FormRequest;

class CheckDebtRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data.request_id' => 'required|integer',
            'data.users_admin_id' => 'required|string|max:255',
            'data.partner_request_id' => 'required|string|max:50',
            'data.partner_merchant_id' => 'required|string',
            'data.amount_payment' => 'required|numeric',
            'data.contract_code' => 'required|string|max:255',
            'data.loan_original_amount' => 'required|numeric',
            'data.deduction_per_day_amount' => 'required|numeric',
            'data.loan_balance' => 'required|numeric',
            'data.amount' => 'required|numeric'
        ];
    }

    public function messages()
    {
        return [
            'data.request_id.required' => 'Trường request_id không để trống',
            'data.request_id.integer' => 'Trường request_id phải đúng định dạng',
            'data.users_admin_id.required' => 'Trường users_admin_id không để trống',
            'data.users_admin_id.string' => 'Trường users_admin_id phải đúng định dạng',
            'data.users_admin_id.max' => 'Trường users_admin_id không được lớn hơn :max',
            'data.partner_request_id.required' => 'Trường partner_request_id không để trống',
            'data.partner_request_id.string' => 'Trường partner_request_id phải đúng định dạng',
            'data.partner_request_id.max' => 'Trường partner_request_id không được lớn hơn :max',
            'data.partner_merchant_id.required' => 'Trường partner_merchant_id không để trống',
            'data.partner_merchant_id.string' => 'Trường partner_merchant_id phải đúng định dạng',
            'data.amount_payment.required' => 'Trường amount_payment không để trống',
            'data.amount_payment.numeric' => 'Trường amount_payment phải đúng định dạng',
            'data.contract_code.required' => 'Trường contract_code không để trống',
            'data.contract_code.string' => 'Trường contract_code phải đúng định dạng',
            'data.contract_code.max' => 'Trường contract_code không được lớn hơn :max',
            'data.loan_original_amount.required' => 'Trường loan_original_amount không để trống',
            'data.loan_original_amount.numeric' => 'Trường loan_original_amount phải đúng định dạng',
            'data.deduction_per_day_amount.required' => 'Trường deduction_per_day_amount không để trống',
            'data.deduction_per_day_amount.numeric' => 'Trường deduction_per_day_amount phải đúng định dạng',
            'data.loan_balance.required' => 'Trường loan_balance không để trống',
            'data.loan_balance.numeric' => 'Trường loan_balance phải đúng định dạng',
            'data.amount.required' => 'Trường amount không để trống',
            'data.amount.numeric' => 'Trường amount phải đúng định dạng',
        ];

    }
}
