<?php

namespace App\Modules\CollectDebt\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class CollectDebtLedgerResourceCollection extends ResourceCollection
{
  /**
   * Transform the resource collection into an array.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return array
   */
  public function toArray($request)
  {
    // dd($this->resource->toArray());
    return [
      'data' => $this->collection->toArray(),
      'links' => [
      
      ],
      'meta' => [
        
      ]
    ];
  }
}
