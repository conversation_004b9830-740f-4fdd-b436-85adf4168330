<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Exception;

class DebtRecoveryRequestFindRawQuerySubAction
{
  public function run(string $whereRawQueryString = '', array $fields=['*']): CollectDebtRequest
  {
    throw_if(empty($whereRawQueryString), new Exception('Điều kiện truy vấn không được để trống'));
    $collectDebtRequest = CollectDebtRequest::whereRaw($whereRawQueryString)->select($fields)->first();
    throw_if(!$collectDebtRequest, new Exception('Không tìm thấy yêu cầu thanh toán'));
    return $collectDebtRequest;
  }
}  // End class