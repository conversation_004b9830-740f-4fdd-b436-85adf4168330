<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhYeuCauTrichSA;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;

class XuLyDieuChinhYeuCauTrichSA
{
	public function run(CollectDebtRequestAdjustment $ra)
	{
		$collectDebtPartnerReference = $ra->getReferenceDataAsPartner();
		$collectDebtRequest = CollectDebtRequest::query()->where('partner_request_id', $collectDebtPartnerReference->partner_request_id)->first();

		$result = $collectDebtRequest->update([
			'amount_receiver' => $ra->getSoTienTrichThanhCongThucTe(),
			'other_data' => $collectDebtRequest->putOtherData([
				'type' => 'REQUEST_ADJUSTMENT',
				'data' => [],
				'note' => sprintf(
					'Điều chỉnh số tiền từ %s thành %s',
					Helper::priceFormat($collectDebtRequest->amount_receiver),
					Helper::priceFormat($ra->getSoTienTrichThanhCongThucTe())
				),
				'time_modified' => now()->timestamp
			])
		]);

		if (!$result) {
			mylog(['Loi cap nhat dieu chinh yc trich' => $result]);
			throw new Exception('Loi cap nhat dieu chinh yc trich');
		}

		return $collectDebtRequest;
	}
}
