<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideQuickStatisticAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class GetYeuCauThuTuDongHomNaySubAction
{
  public function run(string $contractCode)
  {
    $collectDebtRequests = CollectDebtRequest::query()
                                             ->where('contract_code', $contractCode)
                                             ->where('time_begin', '<=', time())
                                             ->where('time_expired', '>=', time())
                                             ->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT)
                                             ->get();

    return $collectDebtRequests;
  }
}
