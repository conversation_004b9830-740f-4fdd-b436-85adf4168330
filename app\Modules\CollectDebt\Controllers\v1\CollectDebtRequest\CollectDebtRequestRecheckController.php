<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use DB;
use Exception;
use App\Lib\Helper;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\TaoCongNoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\XuLyRecheckChoLenhTrichNgaySubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\SubAction\GetYeuCauMposCanRecheckSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\SubAction\GetYeuCauTrichNgayCanRecheckSubAction;

class CollectDebtRequestRecheckController extends Controller
{
  public array $yeuCauDuocXuLy = [];

	public function handleRecheck(Request $request)
	{
		
		$ycTrichMposCanRecheck = app(GetYeuCauMposCanRecheckSubAction::class)->run();
		$ycTrichNgayPhaiRecheck = app(GetYeuCauTrichNgayCanRecheckSubAction::class)->run();
		
		$collectDebtRequests = Collection::make()->concat($ycTrichMposCanRecheck)->concat($ycTrichNgayPhaiRecheck);
		
		if ($collectDebtRequests->isEmpty()) {	
			return 'EMPTY YC RECHECK';
		}
		

		// throw_if($collectDebtRequests->isEmpty(), new Exception('Khong co thong tin yeu cau can RECHECK'));

		mylog([
			'Yeu cau duoc recheck co ids: ' => $collectDebtRequests->pluck('id')->toArray()
		]);

		
    DB::beginTransaction();
		try {
      $collectDebtRequests->map(function (CollectDebtRequest $collectDebtRequest) use ($request) {
				mylog(['Xu ly yc' => $collectDebtRequest->only([ 'id', 'partner_request_id', 'partner_transaction_id', 'contract_code' ]) ]);

				$checkMposResult = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request, true);

				// xu ly cho phan rut tien nhanh
				if ($collectDebtRequest->isRutTienNhanh()) {
					mylog(['La yc TRICH NGAY' => 'yes']);
					$collectDebtRequestAfterHandler = app(XuLyRecheckChoLenhTrichNgaySubAction::class)->run($checkMposResult, $collectDebtRequest);

					$this->yeuCauDuocXuLy[] = $collectDebtRequestAfterHandler->id;
					
					DB::commit();
					$this->xuLyBanGhiLog($collectDebtRequestAfterHandler, $checkMposResult);

					return $collectDebtRequestAfterHandler;
				}

				mylog(['La yc trich TU DONG thong thuong' => 'yes']);

        if (!empty($checkMposResult['data']['status'])) {
          switch ($checkMposResult['data']['status']) {
            case 'PENDING':
              $this->xuLyPending($collectDebtRequest);
              break;

            case 'TIMEOUT':
              $this->xuLyPending($collectDebtRequest);
              break;
            
            // Thành công -> cuối cùng
            case 'SUCCESS':
            case 'APPROVE': 
            case 'EXPIRED': 
              $this->xuLyTrangThaiThanhCongCuoiCung($collectDebtRequest, $checkMposResult, $request);
              break;
              
            
            // Thất bại -> cuối cùng
            case 'CANCEL': 
              $this->xuLyTrangThaiThatBaiCuoiCung($collectDebtRequest);
              break;

            default:
              $this->xuLyPending($collectDebtRequest);
              break;
          }
        }

        $collectDebtRequest->refresh();
        
        $collectDebtRequest->other_data = $collectDebtRequest->checkAndReplaceCheckPaymentType([
          'type' => 'RE_CHECK_PAYMENT',
          'time_modified' => time(),
          'note' => 'Thực hiện ReCheck cho yêu cầu đã CutOff',
          'data' => $checkMposResult
        ], 'RE_CHECK_PAYMENT');
        $collectDebtRequest->save();
        
				$this->xuLyBanGhiLog($collectDebtRequest, $checkMposResult);

        DB::commit();
        return $collectDebtRequest;
      });
      
			return $this->successResponse([
        'collect_request_ids' => $this->yeuCauDuocXuLy,
      ], $request);
		} catch (\Throwable $th) {
      DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

  // Pending đến chết
  public function xuLyPending(CollectDebtRequest $collectDebtRequest) {
		$this->yeuCauDuocXuLy[] = $collectDebtRequest->id;
    return $collectDebtRequest->forceFill([
      'time_completed_recheck' => time()
    ])->update();
  }

  public function xuLyTrangThaiThanhCongCuoiCung(CollectDebtRequest $collectDebtRequest, array $mposCheckResult, $request) {
    $mposStatus = $mposCheckResult['data']['status'];
    $soTienTrichThanhCong = 0;

    if ($mposStatus == 'SUCCESS') {
      $soTienTrichThanhCong = !empty($mposCheckResult['data']['amount_debit']) ? $mposCheckResult['data']['amount_debit'] : 0;
    }

    if ($mposStatus == 'APPROVE') {
      $soTienTrichThanhCong = !empty($mposCheckResult['data']['amount_debit']) ? $mposCheckResult['data']['amount_debit'] : 0;
    }

    if ($mposStatus == 'EXPIRED') {
      $soTienTrichThanhCong = 0;
    }

    /**
		 * Recheck MPOS, thì payment_account_id phải là MÃ HỢP ĐỒNG
		 */
    $request->merge([
      'data' => [
        'payment_channel_code'   => $collectDebtRequest->payment_channel_code,
        'payment_method_code'    => $collectDebtRequest->payment_method_code,
        'payment_account_id'     => $collectDebtRequest->contract_code,
        'partner_request_id'     => $collectDebtRequest->partner_request_id,
        'partner_transaction_id' => $collectDebtRequest->partner_transaction_id,
        'amount_payment'         => 0,
        'amount_receiver'        => $soTienTrichThanhCong,
        'fee'                    => 0,
        'request_exists'         => CollectDebtEnum::PARTNER_REQUEST_DA_CO_YC_THANH_TOAN,
        'response'               => '[]',
        'description'            => 'Luồng Nextlend chủ động kiểm tra yêu cầu ReCheck',

				// các loại time recheck
        'created_by'             => Helper::getCronJobUser(),
        'time_created'           => time(),

				'time_updated'					 => time(),
				'updated_by'						 => Helper::getCronJobUser(),

				'time_complated'				 => time(),
				'complated_by'					 => Helper::getCronJobUser(),

				'time_created_request'   => time(),
				'created_request_by'		 => Helper::getCronJobUser(),

        'status'                 => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
      ]
    ]);

    $collectDebtPartner = app(TaoCongNoSubAction::class)->run($collectDebtRequest, $request, true);
    $collectDebtRequest->forceFill(['status' => CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH])->update();

    $this->yeuCauDuocXuLy[] = $collectDebtRequest->id;

    return $collectDebtPartner;
  }

  public function xuLyTrangThaiThatBaiCuoiCung(CollectDebtRequest $collectDebtRequest) {
    $this->yeuCauDuocXuLy[] = $collectDebtRequest->id;
    
    return $collectDebtRequest->forceFill([
      'status' => CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH
    ])->update();
  }

	public function xuLyBanGhiLog(CollectDebtRequest $collectDebtRequest, $mposCheckResult=[]) {
		if (!empty($collectDebtRequest->time_receivered)) {
			mylog(['ve trang thai cuoi' => 'ok']);

			$updated = CollectDebtLog::query()
				->where('partner_transaction_id', $collectDebtRequest->partner_transaction_id)
				->where('service_code', CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY)
				// ->where('status', CollectDebtEnum::RL_STT_DANG_XU_LY)
				->update([
					'status' => CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG,
					'time_updated' => now()->timestamp,
					'updated_by' => Helper::getCronJobUser(),
					'other_data' => json_encode($mposCheckResult)
				]);

			if (!$updated) {
				mylog(['Loi cap nhat log tu DANG XU LY -> DA XU LY' => 'yes']);
				throw new Exception('Loi cap nhat log tu DANG XU LY -> DA XU LY. MaHD: ' . $collectDebtRequest->contract_code);
			}

			return 'ok';
		}

		if (empty($collectDebtRequest->time_receivered)) {
			mylog(['khong ve trang thai cuoi' => 'ok']);
			
			$updated = CollectDebtLog::query()
				->where('partner_transaction_id', $collectDebtRequest->partner_transaction_id)
				->where('service_code', CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY)
				->where('status', CollectDebtEnum::RL_STT_DANG_XU_LY)
				->update([
					'status' => CollectDebtEnum::RL_STT_MOI_TAO,
					'time_updated' => now()->timestamp,
					'updated_by' => Helper::getCronJobUser(),
					'other_data' => json_encode($mposCheckResult),
				]);

			if (!$updated) {
				mylog(['Loi cap nhat log tu DANG XU LY -> MOI TAO' => 'yes']);
				throw new Exception('Loi cap nhat log tu DANG XU LY -> MOI TAO. MaHD: ' . $collectDebtRequest->contract_code);
			}

			return 'pending';
		}
	}
} // End class
