<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\TaoCongNoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\CapNhatCongNoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\TaoCongNoDoiTacBaoMuonSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction\XuLyPartnerTrichNgayMposSubAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtWaitProcess;

class DebtRecoveryPartnerCreateAction
{
  // Dành riêng cho MPOS
  public function run(DebtRecoveryPartnerCreateRequest $request): CollectDebtPartner
  {
    $partnerRequestId = $request->json('data.partner_request_id', '');
    $collectDebtRequest = CollectDebtRequest::query()->with('collectDebtLedger')->where('partner_request_id', $partnerRequestId)->first();
    
    throw_if(
      !$collectDebtRequest, 
      new Exception(sprintf('Yêu cầu thu hồi `%s` không tồn tại', $partnerRequestId))
    );

		// Kiểm tra xem yêu cầu có đang nằm trong danh sách sẽ hủy hay không, nếu nằm thì không cho tạo partner
		// ở luồng bị động
		$waitProcess = CollectDebtWaitProcess::query()->where([
			'obj_id' => $collectDebtRequest->id,
			'obj_type' => 'debt_recovery_request',
			'action_code' => 'CANCEL',
		])->whereNotIn('status', [
			CollectDebtEnum::WAIT_PROCESS_STT_DA_XU_LY,
			CollectDebtEnum::WAIT_PROCESS_STT_DUOC_HUY,
		])->first();

		if ($waitProcess) {
			$msg = sprintf('YC: %s đã có chiến lược để hủy, từ chối tạo partner', $collectDebtRequest->partner_request_id);
			TelegramAlert::sendMessage($msg);
			throw new Exception($msg);
		}
    
    // Kiểm tra partner của yêu cầu
    $collectDebtPartner = CollectDebtPartner::where('partner_request_id', $partnerRequestId)
                                            ->where('payment_method_code', $request->json('data.payment_method_code'))
                                            ->first();
    
    // Chưa có thì tạo ngay lập tức
    if (!$collectDebtPartner) {
      return app(TaoCongNoSubAction::class)->run($collectDebtRequest, $request);
    }
    
    $isPhaiXuLyTrichNgayMpos = app(XuLyPartnerTrichNgayMposSubAction::class)->run($collectDebtPartner, $collectDebtRequest, $request);
    
    if ($isPhaiXuLyTrichNgayMpos) {
      return $collectDebtPartner;
    }
    
		/**
     * Update 02.02.2024, yêu cầu còn hạn thì update thoải mái, nhưng yc đã hết hạn thì từ chối
     */
    if ($collectDebtRequest->isYeuCauConHan() && $collectDebtRequest->isYeuCauChuaCoSo()) {
      return app(CapNhatCongNoSubAction::class)->run($collectDebtPartner, $request);
    }

    return $collectDebtPartner;
  }
} // End class