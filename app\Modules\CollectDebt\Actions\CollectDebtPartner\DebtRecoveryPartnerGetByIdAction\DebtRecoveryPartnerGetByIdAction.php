<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerGetByIdAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerGetByIdRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerFindWhereRawAction\SubAction\DebtRecoveryPartnerFindWhereRawSubAction;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class DebtRecoveryPartnerGetByIdAction
{
  public function run(DebtRecoveryPartnerGetByIdRequest $request): CollectDebtPartner
  {
    $id = $request->json('data.id');

    $collectDebtPartner = CollectDebtPartner::query()->findOrFail($id);
    $coreContract = $collectDebtPartner->getCoreContract();

    $collectDebtShare = CollectDebtShare::query()->where('contract_code', $coreContract->contract_code)->first();
    $profileData = $collectDebtShare->getProfileDataAsArray();

    $collectDebtPartner->contract_code = $coreContract->contract_code;

    $collectDebtPartner->merchant = [
      'id' => $profileData['merchant']['id'],
      'fullname' => $profileData['merchant']['fullname'],
      'mobile' => $profileData['merchant']['mobile'] ?? $profileData['merchant']['phone'],
      'email' => $profileData['merchant']['email']
    ];

    $collectDebtPartner->payment_guide_va = $collectDebtShare->getPaymentGuideItem('VIRTUALACCOUNT');
    return $collectDebtPartner;
  }
} // End class