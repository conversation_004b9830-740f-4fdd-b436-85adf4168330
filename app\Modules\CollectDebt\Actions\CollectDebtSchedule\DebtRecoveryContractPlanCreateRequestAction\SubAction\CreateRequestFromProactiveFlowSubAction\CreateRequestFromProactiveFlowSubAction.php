<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSetting;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\TaoYeuCauGopLichCungRunDateTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\InsertBatchCollectDebtRequestTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\GetLichThuDungDeTaoYeuCauLuongChuDongTask;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;

/**
 * Class tạo yêu cầu thu tự động
 * Chỉ có kênh trích MPOS mới cần tạo yêu cầu mà thôi
 */
class CreateRequestFromProactiveFlowSubAction
{ 
  private array $__listLichCanTaoYeuCau = [];

  public $yeuCauTaoThanhCong;

  public function __construct()
  {
    /*
    xử lý trước trước khi đẩy lên
    update hết các PLAN về trạng thái = ĐANG XỬ LÝ --- có trạng thái = 1 mà nằm trong request debt có trạng thái đang xử lý

    conđition (A) những PLAN  thằng time và trạng thái hợp lệ thuộc
      Xử lý job giống với thằng CHECK PARTNER
     1- Chỉ lấy 1 dữ liệu Điều kiện lấy ra plan thu gọn lại trong plan với trạng thái là gì đã 
     2 -NÉ hợp đồng contract_code not in (
        + trạng thái # 1 trong plan ( 2,3,4,5 chưa hoàn thành)
        + có trong tắt job tự động
     )
     3 - UPDATE trạng thái 1 bản ghi cho đến hết  
     4 - UPDATE hết sang trạng thái theo 3 với  những PLAN  thằng time và trạng thái hợp lệ thuộc (hợp đồng đã được lấy ra từ 1)
     Lấy ra nốt  những PLAN  thằng time và trạng thái hợp lệ thuộc hợp đồng
     gộp với id của thằng vừa xử lý
     5 
     BEGIN TRANSACTION 
      để lấy ra những thằng 
      lấy ra những thằng (A) thuộc hợp đồng đang xử lý và trạng thái PLAN là vừa update 
      ---> vào luồng
     END TRANSACTION 
    */



    $this->yeuCauTaoThanhCong = Collection::make();  
  }

  public function run($exceptPlanProcessingIds=[]): Collection
  {
    $collectDebtScheduleCollection = app(GetLichThuDungDeTaoYeuCauLuongChuDongTask::class)->run($exceptPlanProcessingIds);
    
    throw_if($collectDebtScheduleCollection->isEmpty(), new Exception('Không có lịch để tạo yêu cầu'));

    foreach ($collectDebtScheduleCollection as $contractCode => $planCollections) {
      $hasLichQuaKhu = $planCollections->first(function (CollectDebtSchedule $plan) {
        return now()->gt($plan->rundate_as_date) && !now()->isSameDay($plan->rundate_as_date);
      });

    
      // Không có lịch quá khứ tạo lịch thu có gộp có cùng rundate
      if ( !$hasLichQuaKhu ) {
        $isCacLichCoCungRunDate = $planCollections->every(function (CollectDebtSchedule $plan) {
          return $plan->rundate == date('Ymd');
        });

        if ($isCacLichCoCungRunDate) {
          $sortableCollection = app(PlanSortableCollectionByRule::class)->sortCollection($planCollections);
          $yeuCauThuGopDaTaoBoolean = app(TaoYeuCauGopLichCungRunDateTask::class)->run($sortableCollection);  

          $message = [
            'Flow' => 'Luồng chủ động',
            'LogID' => request('api_request_id'),
          ];
  
          $message['Message'] = 'Tạo yêu cầu thu gộp thất bại';
  
          if ($yeuCauThuGopDaTaoBoolean) {
            $message['Message'] = 'Tạo yêu cầu thành công';
            app(DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST::class)->run($planCollections);
          }
  
          TelegramAlert::sendCreateRequest(parseErr($message));

          $this->yeuCauTaoThanhCong = $this->yeuCauTaoThanhCong->merge($yeuCauThuGopDaTaoBoolean);
        }

        if (!$isCacLichCoCungRunDate) {
          // Không cùng rundate thì foreach ra mà cho vào thôi
          foreach ($planCollections as $plan) {
            $this->__listLichCanTaoYeuCau[$contractCode][] = $plan;
          }
        }
      }

      /**
       * Tập hợp có thể có lịch thu QK và lịch thu hiện tại.
       * Có lịch thu quá khứ chỉ lấy 1 lịch thu trên mỗi HĐ mà thôi
       */
      if ( $hasLichQuaKhu ) {
        $sortableCollection = app(PlanSortableCollectionByRule::class)->sortCollection($planCollections);
        $yeuCauThuGopDaTaoBoolean = app(TaoYeuCauGopLichCungRunDateTask::class)->run($sortableCollection);  
        $this->yeuCauTaoThanhCong = $this->yeuCauTaoThanhCong->merge($yeuCauThuGopDaTaoBoolean);
        app(DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST::class)->run($planCollections);
        return  $this->yeuCauTaoThanhCong;
      }
    }

    $collectDebtRequestCreateParams = [];

    foreach ($this->__listLichCanTaoYeuCau as $schedule) {
      if (gettype($schedule) == 'array') {
        foreach ($schedule as $p) {
          // if (CollectDebtConfigAuto::isPauseContractJob($p->contract_code)) {
          //   continue;
          // }

          $collectDebtRequestCreateParams = array_merge($collectDebtRequestCreateParams, $this->__buildParamsCreateRequest($p));
        }
      }else {
        // Nếu đang dừng job thu tự động => thì bỏ qua vòng lặp hiện tại
        if (CollectDebtConfigAuto::isPauseContractJob($schedule->contract_code)) {
          continue;
        }

        $collectDebtRequestCreateParams = array_merge($collectDebtRequestCreateParams, $this->__buildParamsCreateRequest($schedule));
      }
    }
    
    if (empty($collectDebtRequestCreateParams)) {
      mylog(['Không có yêu cầu để tạo' => 'ok']);
      return Collection::make();
    }

    $collectDebtRequestInserted = app(InsertBatchCollectDebtRequestTask::class)->run($collectDebtRequestCreateParams);
    $this->yeuCauTaoThanhCong = $this->yeuCauTaoThanhCong->merge($collectDebtRequestInserted);
    return $this->yeuCauTaoThanhCong;
  }

  private function __buildParamsCreateRequest(CollectDebtSchedule $collectDebtSchedule)
  {
    $paymentGuide = $collectDebtSchedule->getSpecialPaymentGuide('MPOS');
    $planAsCollection = Collection::make()->push($collectDebtSchedule);

    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $collectDebtSchedule->contract_code)->first();

    $returnData[] = [
      'type'                        => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
      'profile_id'                  => $collectDebtSchedule->profile_id,
      'contract_code'               => $collectDebtSchedule->contract_code,
      'plan_ids'                    => $collectDebtSchedule->id,
      'payment_method_code'         => $paymentGuide['payment_method_code'],
      'payment_channel_code'        => $paymentGuide['payment_channel_code'],
      'payment_account_id'          => $paymentGuide['payment_account_id'],
      'payment_account_holder_name' => $paymentGuide['other_data']['payment_account_holder_name'] ?? '',
      'payment_account_bank_code'   => $paymentGuide['other_data']['payment_account_bank_code'] ?? '',
      'payment_account_bank_branch' => $paymentGuide['other_data']['payment_account_bank_branch'] ?? '',
      'partner_request_id'          => '',
      'partner_transaction_id'      => '',
      'time_begin'                  => $collectDebtSchedule->rundate_as_date->copy()->timestamp,
      'time_expired'                => $collectDebtSchedule->rundate_as_date->copy()->setTimeFromTimeString(Helper::getCutOffTime())->timestamp,
      'is_payment'                  => CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT,
      'version'                     => CollectDebtEnum::REQUEST_VERSION_4,
      'status'                      => CollectDebtEnum::REQUEST_STT_MOI_TAO,
      'status_payment'              => CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI,
      'status_recored'              => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO,
      'currency'                    => $collectDebtSummary->getCurrency(),
      'amount_request'              => $collectDebtSchedule->request_amount_debit, // Số tiền yêu cầu thanh toán
      'amount_payment'              => 0, // Số tiền đã thu hồi được
      'amount_receiver'             => 0,
      'fee'                         => 0,
      'other_data'                  => '{}',
      'plan_data'                   => Helper::getPlanCompact($planAsCollection),
      'description'                 => '',
      'created_by'                  => Helper::getCronJobUser(),
      'time_created'                => time(),
    ];
    
    $collectDebtSchedule->other_data = $collectDebtSchedule->setTaoLichQuaKenhNao($paymentGuide['payment_method_code']);
    $collectDebtSchedule->save();

    return $returnData;
  }
} // End class
