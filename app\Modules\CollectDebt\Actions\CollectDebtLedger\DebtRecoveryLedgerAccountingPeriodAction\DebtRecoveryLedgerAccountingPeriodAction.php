<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingPeriodAction;

use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingPeriodAction\SubAction\GetSoCanXuLyPeriodSubAction;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use Exception;
use Illuminate\Http\Request;

class DebtRecoveryLedgerAccountingPeriodAction
{
  public function run(Request $request) {

    $ledgers = app(GetSoCanXuLyPeriodSubAction::class)->run();
    
    if ($ledgers->isEmpty()) {
      return;
    }

    $ledgers->map(function (CollectDebtLedger $ledger) {
      $collectDebtRequest = $ledger->getRequest();
      $ledgerOtherDataSummaryType = $ledger->getOtherDataItem('SUMMARY');
      $ledgerOtherDataPlanType = $ledger->getOtherDataItem('PLAN');
      // dd($ledgerOtherDataPlanType, $ledgerOtherDataSummaryType, $collectDebtRequest->toArray());
    });
  }
} // End class
