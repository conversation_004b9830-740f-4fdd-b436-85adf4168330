<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestUpdateAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestUpdateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\DebtRecoveryRequestFindRawQuerySubAction;

class DebtRecoveryRequestUpdateAction
{
  public function run(DebtRecoveryRequestUpdateRequest $request): CollectDebtRequest
  {
    $whereRaw = sprintf('id = %s', $request->json('data.id'));
    $collectDebtRequest = app(DebtRecoveryRequestFindRawQuerySubAction::class)->run($whereRaw);
    throw_if(!$collectDebtRequest, new Exception('Không tìm thấy yêu cầu thanh toán'));
    throw_if(!$collectDebtRequest->isNew(), new Exception('Yêu cầu thanh toán muốn cập nhật thì phải có trạng thái mới tạo'));

    $updateParam = $request->only([
      'data.type',
      'data.profile_id',
      'data.contract_code',
      'data.plan_ids',
      'data.payment_method_code',
      'data.payment_channel_code',
      'data.payment_account_id',
      'data.payment_account_holder_name',
      'data.payment_account_bank_code',
      'data.payment_account_bank_branch',
      'data.partner_request_id',
      'data.partner_transaction_id',
      'data.time_begin',
      'data.time_expired',
      'data.is_payment',
      'data.version',
      'data.status',
      'data.status_payment',
      'data.status_recored',
      'data.currency',
      'data.amount_request',
      'data.amount_payment',
      'data.amount_receiver',
      'data.fee',
      'data.other_data',
      'data.profile_data',
      'data.plan_data',
      'data.description',
      'data.created_by',
      'data.time_updated',
    ])['data'];

    $collectDebtRequest->forceFill($updateParam)->update();
    return $collectDebtRequest->refresh();
  }
} // End class