<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\HopDongChuaTatToanRule;
use App\Modules\CollectDebt\Rules\CollectDebtRequest\CacYeuCauDaHachToanXongKhiLamRutTienNhanhRule;
use App\Modules\CollectDebt\Rules\CollectDebtRequest\ChiGiamPhiKhiLaDeXuatGiamPhiRule;
use App\Modules\CollectDebt\Rules\CollectDebtRequest\HopDongKhongCoLichTuDongDangChayRule;
use App\Modules\CollectDebt\Rules\CollectDebtRequest\ReduceOverPeriodPlanSameContractRule;

class DebtRecoveryRequestCreateManualRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],

      // mã hđ
      'data.contract_code'  => [
        'bail',
        'required',
        'string',
        'max:50',
        new HopDongChuaTatToanRule(),
        new HopDongKhongCoLichTuDongDangChayRule()
      ],

      // phương thức thanh toán
      'data.payment_method_code' => [
        'required',
        Rule::in([
          'MPOS',
          'IB_OFF',
          'VIRTUALACCOUNT'
        ])
      ],

      // là trích ngay lập tức?
      'data.is_debt_now' => [
        // chỉ required khi không phải là giảm phí
        Rule::requiredIf(function () {
          return $this->json('data.manual_request_type') != 'GIAM_PHI';
        }),

        'string',
        Rule::in(['YES', 'NO']),
				new CacYeuCauDaHachToanXongKhiLamRutTienNhanhRule($this->json('data.contract_code'))
      ],

      // hình thức trích
      'data.manual_request_type' => ['bail', 'required', 'string', Rule::in(['TRICH_MOT_PHAN', 'TAT_TOAN', 'GIAM_PHI'])],

      // loại trích: trích tay
      'data.create_from' => ['required'],

      // số tiền muốn trích
      'data.amount_request' => [
        Rule::requiredIf(function () {
          return $this->json('data.manual_request_type') != 'GIAM_PHI';
        }),
        'numeric',
				'integer',
        'min:0'
      ],

      // mã chứng từ
      'data.partner_transaction_id' => [
        'bail',
        'string',
        'max:50',
        Rule::requiredIf(function () {
          if ($this->json('data.payment_method_code') == 'MPOS') {
						return false;
					}

          if ($this->json('data.manual_request_type') == 'GIAM_PHI') {
            return false;
          }

					return true; // nếu mà rơi xuống đây thì required
        })
      ],

      'data.description'  => ['required', 'string', 'max:255'],
      'data.created_by' => ['required', 'string', 'max:255'],
      'data.manual_request_attachment' => [
        'bail', 
				'array',
				Rule::requiredIf(function () {
					return $this->json('data.manual_request_type') == 'GIAM_PHI';
				})
      ]
    ];
  }

	public function messages()
	{
		return [
			'data.contract_code.required' => 'Mã hợp đồng là bắt buộc',
			'data.contract_code.max' => 'Mã hợp đồng phải có độ dài tối đa là 50 ký tự',
			'data.payment_method_code.required' => 'Phương thức thu hồi là bắt buộc',
			'data.payment_method_code.in' => 'Phương thức thu hồi phải thuộc trong các giá trị: MPOS, IB_OFF hoặc VIRTUALACCOUNT',
			'data.manual_request_type.required' => 'Hình thức trích là bắt buộc',
			'data.manual_request_type.in' => 'Hình thức trích phải thuộc một trong các giá trị: TRICH_MOT_PHAN, TAT_TOAN hoặc GIAM_PHI',
			'data.create_from.required' => 'Loại trích là bắt buộc',

			'data.amount_request.required' => 'Số tiền thanh toán là bắt buộc',
			'data.amount_request.numeric' => 'Số tiền thanh toán phải là một con số',
			'data.amount_request.min' => 'Số tiền thanh toán phải >= 0',
			'data.amount_request.integer' => 'Số tiền thanh toán phải là số nguyên',

			'data.partner_transaction_id.required_if' => 'Mã chứng từ là bắt buộc',
			'data.partner_transaction_id.max' => 'Mã chứng từ phải có độ dài tối đa là 50 ký tự',
			'data.description.required' => 'Nội dung là bắt buộc',
			'data.description.max' => 'Nội dung phải có độ dài tối đa là 255 kỳ tự',
		];
	}

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['create_from'] = CollectDebtEnum::REQUEST_LOAI_TRICH_TAY;

    if ($params['data']['manual_request_type'] == 'GIAM_PHI') {
      $params['data']['payment_method_code'] = 'IB_OFF';
    }

    $params['data']['created_by'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['created_by']);
    $this->merge($params);
  }

  public function getLichThuGiamPhiIds(): array
  {
    return Arr::pluck($this->json('data.reduce_over_period_plans'), 'plan_id');
  }

  public function isDeXuatGiamPhi(): bool
  {
    return $this->json('data.manual_request_type') == 'GIAM_PHI';
  }

  public function isTrichNgay(): bool
  {
    return !empty($this->json('data.is_debt_now') == 'YES');
  }

  public function isTrichMotPhan(): bool
  {
    return $this->json('data.manual_request_type') == 'TRICH_MOT_PHAN';
  }

  public function isTatToan(): bool
  {
    return $this->json('data.manual_request_type') == 'TAT_TOAN';
  }

  public function isCoGiamPhiQuaHan(): bool
  {
    return !empty($this->json('data.reduce_over_due_fee'));
  }

  public function isCoGiamPhiChamKy(): bool
  {
    return !empty($this->json('data.reduce_over_period_plans'));
  }

  public function isYeuCauKhongCanTrichQuaDoiTac(): bool
  {
    return $this->json('data.payment_method_code') != 'MPOS';
  }
} // End class
