<?php

namespace App\Modules\CollectDebtGateway\Requests\PartnerDebtGateway;

use App\Modules\CollectDebtGateway\Requests\MposCollectDebtGateway\CheckDebtRequest as MposCollectDebtGatewayCheckDebtRequest;
use App\Modules\CollectDebtGateway\Requests\MposTestCollectDebtGateway\CheckDebtRequest as MposTestCollectDebtGatewayCheckDebtRequest;
use Illuminate\Foundation\Http\FormRequest;

class CheckDebtRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $dataValidate = [];

        $paymentChannel = request()->json('data.payment_channel', '');

        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataValidate = app(MposCollectDebtGatewayCheckDebtRequest::class)->rules();
                break;
            case config('collect_debt_gateway_config.payment_channel.mpos_test'):
                $dataValidate = app(MposTestCollectDebtGatewayCheckDebtRequest::class)->rules();
                break;
            default:
                $dataValidate = $this->__paymentChannel();
        }

        return $dataValidate;
    }

    public function messages()
    {

        $dataMessage = [
            'data.payment_channel.required' => 'Trường payment_channel không để trống',
            'data.payment_channel.string' => 'Trường payment_channel phải đúng định dạng',
            'data.payment_channel.max' => 'Trường payment_channel không được lớn hơn :max',
            'data.payment_channel.in' => 'Trường payment_channel phải thuộc các kênh đã khai báo',
        ];
        $paymentChannel = request()->json('data.payment_channel', '');
        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataMessage = app(MposCollectDebtGatewayCheckDebtRequest::class)->messages();
                break;
            case config('collect_debt_gateway_config.payment_channel.mpos_test'):
                $dataMessage = app(MposTestCollectDebtGatewayCheckDebtRequest::class)->messages();
                break;
            default:
                $dataMessage;
        }

        return $dataMessage;
    }



    protected function __paymentChannel()
    {
        $paymentChannel = array_values(config('collect_debt_gateway_config.payment_channel'));

        return [
            'data' => 'required|array',
            'data.payment_channel' => 'required|string|max:50|in:' . implode(',', $paymentChannel),
        ];
    }
}
