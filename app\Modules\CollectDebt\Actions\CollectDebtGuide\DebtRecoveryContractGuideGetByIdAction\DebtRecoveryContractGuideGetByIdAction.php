<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetByIdAction;

use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Enums\CollectDebtGuideSampleDataEnum;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideGetById;
use Exception;

class DebtRecoveryContractGuideGetByIdAction
{
  public function run(string $id=''): CollectDebtGuide
  {
    $collectDebtGuide = CollectDebtGuide::find($id);
    throw_if(!$collectDebtGuide, new Exception('Không tìm thấy chỉ dẫn'));
    return $collectDebtGuide;
  }
}
