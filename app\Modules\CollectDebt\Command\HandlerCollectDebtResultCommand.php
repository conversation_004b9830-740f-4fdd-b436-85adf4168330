<?php

namespace App\Modules\CollectDebt\Command;

use Illuminate\Console\Command;

class XuLyKetQuaTrichNoCommand extends Command
{
    /**
     * Job tạo lịch thu từ chỉ dẫn
     *
     * @var string
     */
    protected $signature = 'command:name';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        return 0;
    }
}
