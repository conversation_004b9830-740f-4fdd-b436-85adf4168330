<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtLedger;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Rules\CollectDebtRequest\ExplodeItemIsIntegerRule;

class DebtRecoveryLedgerUpdateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    $ledgerListStatus = CollectDebtLedger::listStatus();
    
    return [
      'data'               => ['required', 'array'],
      'data.id'            => ['required', 'numeric', 'min:1'], 
      'data.profile_id'    => [
        'required', 
        'numeric', 
      ],

      'data.contract_code' => [
        'required', 
        'string', 
        'max:50',
      ],

      'data.plan_ids' => ['required', 'string', 'max:30', new ExplodeItemIsIntegerRule()],

      'data.request_id' => [
        'required', 
        'numeric', 
        'min:1',
      ],

      'data.currency'       => ['required', 'string', 'max:3'],
      'data.amount'        => ['required', 'numeric', 'min:1'],
      'data.other_data'    => ['required', 'json'],
      'data.profile_data'  => ['required', 'json'],
      'data.description'   => ['nullable', 'string', 'max:255'],
      'data.status'        => ['required', Rule::in(array_keys($ledgerListStatus))],
      'data.time_record'   => ['required', 'numeric'],
      'data.updated_by'    => ['required', 'string', 'max:255'],
      'data.time_updated'  => ['required'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['time_updated'] = time();
    
    $this->merge($params);
  }

  public function messages() {
    return [
      'data.profile_id.exists' => 'ProfileID không tồn tại trong hệ'
    ];
  }
} // End class
