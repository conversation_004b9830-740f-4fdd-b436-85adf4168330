<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayCoChungTuSubAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\TaoYeuCauTrichTayTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\GetCongNoCoTheThanhToanTask;

class TrichMotPhanCoChungTuSubAction
{
  public string $paymentMethodCode;

  public function __construct(string $paymentMethodCode = '')
  {
    $this->paymentMethodCode = $paymentMethodCode;
    throw_if(empty($this->paymentMethodCode), new Exception('Hệ thống không hiểu payment_method_code của bạn'));
  }

  public function run(
    CollectDebtShare $collectDebtShare,
    Collection $danhSachToanBoLichThu,
    DebtRecoveryRequestCreateManualRequest $request
  ) {
    $tongTienCanPhaiThuCacLich = $danhSachToanBoLichThu->sum('request_amount_debit');
    $soTienGhiNhanThanhCong = $request->json('data.amount_request');

    $errorMessage = sprintf(
      'Thao tác bị từ chối. Lý do: số tiền bạn mong muốn thanh toán `%s` đang LỚN HƠN số tiền cần phải thanh toán là `%s`. Hãy sửa lại số tiền trích để có thể tạo yêu cầu thành công',
      Helper::priceFormat($soTienGhiNhanThanhCong, 'đ'),
      Helper::priceFormat($tongTienCanPhaiThuCacLich, 'đ'),
    );

    // Số tiền mang đi thanh toán cần phải >= số tiền TRÊN CÁC LỊCH
    throw_if($soTienGhiNhanThanhCong > $tongTienCanPhaiThuCacLich, new Exception($errorMessage));

    $soTienMangDiThanhToan = min($soTienGhiNhanThanhCong, $tongTienCanPhaiThuCacLich);

    $collectDebtPartner = app(GetCongNoCoTheThanhToanTask::class)->run(
      $request->json('data.partner_transaction_id'),
      $soTienMangDiThanhToan,
      $this->paymentMethodCode
    );

    $collectDebtRequest = app(TaoYeuCauTrichTayTask::class)->run(
      $danhSachToanBoLichThu,
      $collectDebtShare,
      $request,
      $this->paymentMethodCode
    );

    return $collectDebtRequest;
  }
} // End class