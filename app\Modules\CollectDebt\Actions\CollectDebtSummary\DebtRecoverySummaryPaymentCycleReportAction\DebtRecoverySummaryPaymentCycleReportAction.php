<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPaymentCycleReportAction;

use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction\ThongKeUserLienQuanSA;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPaymentCycleReportRequest;
use DB;

class DebtRecoverySummaryPaymentCycleReportAction
{
	public array $returnData = [];

	/**
	 * Mindset: 
	 *  	+ <PERSON>ỳ thu có thể kéo dài, vậy nên cần SELECT lịch max của các từng kỳ để lấy được lịch master_id
	 * 		+ <PERSON><PERSON> khi có lịch master_id thì truy vân whereIn đơn giản, 
	 * 		+ <PERSON><PERSON><PERSON>i cùng lấy thêm dữ liệu từ summary để ghép data và trả về cho người dùng
	 *
	 * @param DebtRecoverySummaryPaymentCycleReportRequest $request
	 * @return void
	 */
	public function run(DebtRecoverySummaryPaymentCycleReportRequest $request)
	{
		$plans = DB::table('debt_recovery_contract_plan')
								->select([
									'contract_code',
									'master_id',
									DB::raw('MAX(id) as max_id')
								])
							 ->whereBetween('rundate', [
								$request->getTimeStartFromAsTimestamp(),
								$request->getTimeStartToAsTimestamp()
							]);

	/* ----------------- bắt contract code ----------------- */
		if (!empty($request->json('data.filter.contract_code'))) {
			$plans = $plans->where('contract_code', trim($request->json('data.filter.contract_code')));
		}

	/* ----------------- bắt profile_id (không truyền, và truyền empty là 2 case khác nhau) ----------------- */
		$filter = $request->json('data.filter');

		if ( isset($filter['profile_ids']) ) {
				$plans->whereIn('profile_id', $filter['profile_ids']);
		}
	/* ----------------- bắt đã thanh toán hay chưa (1: đã thanh toán | 2: chưa thanh toán) ----------------- */
		$isPaid = $request->json('data.filter.is_paid', 0);

		if (!empty($isPaid)) {
				// Lọc theo đk: kỳ đó chưa thanh toán
				$conditional = ' = ';
				
				// Lọc theo đk: kỳ đó đã thanh toán
				if ($isPaid == 1) {
						$conditional = ' > ';
				}

				$plans->whereRaw("
						EXISTS (
								SELECT SUM(`t2`.`success_amount_debit`), `t2`.`contract_code`, `t2`.`master_id`
								FROM debt_recovery_contract_plan as `t2`
								WHERE `t2`.`contract_code` = `debt_recovery_contract_plan`.`contract_code` 
										AND `t2`.`master_id` = `debt_recovery_contract_plan`.`master_id`
								GROUP BY `t2`.`contract_code`, `t2`.`master_id`
								HAVING SUM(`t2`.`success_amount_debit`) $conditional 0
						)
				");
		}

		$plans = $plans->groupByRaw(" contract_code, master_id ")
									 ->get();

		
		$planIds = collect($plans)->pluck('master_id')->toArray();
		$maxIds = collect($plans)->pluck('max_id')->toArray();

		$lichThuMax = CollectDebtSchedule::query()->whereIn('id', $maxIds)->get()->keyBy('master_id');

		$plansResults = CollectDebtSchedule::query()
																			 ->whereIn('id', $planIds)
																			 ->whereHas('collectDebtSummary', function ($q) {
																				return $q->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
																			 })
																			 ->select([
																				'id',
                                        'contract_code',
                                        'time_start',
                                        'master_id',
                                        'request_amount_debit',
                                        'success_amount_debit',
                                        'time_start',
                                        'time_end',
                                        'rundate',
                                        'profile_id',
																			 ]);

		if (empty($request->json('data.is_export'))) {
			$plansResults = $plansResults->paginate(
				$request->json('data.limit', 10), 
				['*'], 
				'page',
				$request->json('data.page', 1)
		 	);
		}

		// Nếu là export thì nới rộng cái phân trang lên để lấy hết bản ghi
		if (!empty($request->json('data.is_export'))) {
			$plansResults = $plansResults->paginate(
				500, 
				['*'], 
				'page',
				1
		 	);
		}
																			
		$collection = $plansResults->getCollection();

		$contractCodes = $collection->pluck('contract_code')->toArray();
		mylog(['Danh sach Ma HD co lich thu la' => $contractCodes]);
		$collectDebtSummaries = CollectDebtSummary::query()
																							->with('collectDebtShare')->whereIn('contract_code', $contractCodes)
																							->get()
																							->keyBy('contract_code');

		$collection = $collection->map(function (CollectDebtSchedule $plan) use ($collectDebtSummaries, $lichThuMax) {
			mylog([
				'Dang xu ly cho HD' => $plan->contract_code,
				'ID Lich Dang Xu Ly' => $plan->id,
			]);

			$collectDebtSummary = $collectDebtSummaries->get($plan->contract_code);

			$profileDataArray = $collectDebtSummary->collectDebtShare->getProfileDataAsArray();
				
				$merchantInfo = [
					'id' => $profileDataArray['merchant']['id'],
					'fullname' => $profileDataArray['merchant']['fullname'],
					'email' => $profileDataArray['merchant']['email'],
					'mobile' => $profileDataArray['merchant']['mobile'],
					'address' => $profileDataArray['merchant']['address'],
				];
	
				$contractInfo = [
					'contract_code' => $collectDebtSummary->contract_code,
					'amount' => $collectDebtSummary->contract_amount,
					'contract_cycle' => $collectDebtSummary->contract_cycle,
					'contract_intervals' => $collectDebtSummary->contract_intervals,
					'time_start_end' => sprintf('%s - %s', $collectDebtSummary->collectDebtShare->time_start_as_vn_date, $collectDebtSummary->collectDebtShare->time_end_as_vn_date)
				];
				
				$summaryPlanOtherData = $collectDebtSummary->getSummaryOtherDataItem('PLAN');
				if (empty($summaryPlanOtherData)) {
					mylog(['Loi HD' => $collectDebtSummary->contract_code]);
					mylog(['other data' => @$collectDebtSummary->getSummaryOtherData()]);
				}
				$planOtherDataKeyById = collect($summaryPlanOtherData['data'])->keyBy('id')->toArray();

				if (isset($planOtherDataKeyById[$plan->id]['amount']) && isset($planOtherDataKeyById[$plan->id]['amount_paid'])) {
					$amount = $planOtherDataKeyById[$plan->id]['amount'] ?? 0; 
					$amountPaid = $planOtherDataKeyById[$plan->id]['amount_paid'] ?? 0;
				}else {
					$amount = $plan->request_amount_debit; 
					$amountPaid = $plan->success_amount_debit;
				}

				$paymentInfo = [
					'payment_date' => $plan->time_start_as_date->format('d/m/Y'),
					'total_amount_must_payment' => $amount,
					'total_amount_paid' => $amountPaid,
					'totla_amount_continous_payment' => $amount - $amountPaid
				];
				
	
				$userAction = app(ThongKeUserLienQuanSA::class)->run($collectDebtSummary);
				
				unset($plan->request_amount_debit);
				unset($plan->success_amount_debit);
				unset($plan->contract_code);

				$plan->merchant_info = $merchantInfo;
				$plan->contract_info = $contractInfo;
				$plan->payment_info = $paymentInfo;
				$plan->user_action = $userAction;
				$plan->profile_info = [
					'id' => $plan->profile_id
				];
				
				unset($plan->profile_id);

				$plan->rundate = $lichThuMax->get($plan->id)->rundate;
				$plan->status = $lichThuMax->get($plan->id)->status;
				return $plan;
		});

		$plansResults->setCollection($collection);
		
		return $plansResults;
	}
} // End class
