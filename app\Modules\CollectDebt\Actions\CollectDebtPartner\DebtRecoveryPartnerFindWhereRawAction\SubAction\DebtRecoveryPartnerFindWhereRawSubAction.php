<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerFindWhereRawAction\SubAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtPartner;

class DebtRecoveryPartnerFindWhereRawSubAction                 
{  
  /**
   * Method run
   *
   * @param string $whereRaw [explicite description]
   * @param bool $markFail [explicite description]
   *
   * @return mix CollectionDebtPartner|null
   */
  public function run(string $whereRaw='', bool $markFail=false)
  {
    throw_if(empty($whereRaw), new Exception('Thiếu điều kiện truy vấn'));
    $collectDebtPartner = CollectDebtPartner::whereRaw($whereRaw)->first();

    throw_if(!$collectDebtPartner && $markFail, new Exception('Không tìm thấy bản ghi công nợ (partner)'));

    return $collectDebtPartner;
  }
} // End class