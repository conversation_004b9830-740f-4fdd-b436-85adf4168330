<?php

namespace App\Modules\CollectDebt\ValueObjects;

use Exception;
use Carbon\Carbon;
use App\Lib\Helper;

class CutOffTimeValueObject
{
	public Carbon $rundate;
	public string $partnerCode;

	public function __construct(Carbon $rundate, string $partnerCode)
	{
		$this->rundate = $rundate;
		$this->partnerCode = $partnerCode;

		if (empty($partnerCode)) {
			throw new Exception('Partner code khong hop le');
		}
	}

	public function getCutOffTimeTimestamp(): int
	{
		$cutOffTimeNextlend = Helper::getCutOffTime();

		if ($this->partnerCode == 'NEXTLEND') {
			return $this->rundate->setTimeFromTimeString($cutOffTimeNextlend)->timestamp;
		}

		if ($this->partnerCode == 'TNEX') {
			return $this->rundate->setTimeFromTimeString(env('API_REQUEST_DEBT_CUTOFF_TIME_TNEX', '22:30:00'))->timestamp;
		}

		return $this->rundate->setTimeFromTimeString($cutOffTimeNextlend)->timestamp;
	}
}
