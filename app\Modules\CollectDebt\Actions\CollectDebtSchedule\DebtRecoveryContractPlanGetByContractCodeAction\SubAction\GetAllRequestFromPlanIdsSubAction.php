<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanGetByContractCodeAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Database\Eloquent\Collection;

class GetAllRequestFromPlanIdsSubAction
{
  public function run(array $planIds = [], array $fields=['*']): Collection
  {
    if (empty($planIds)) {
      return Collection::make([]);
    }

    $requests = CollectDebtRequest::query()
                                  ->where(function ($query) use ($planIds) {
                                    foreach ($planIds as $planId) {
                                      $query->orWhereRaw("FIND_IN_SET(?, `plan_ids`) > 0", [(string)$planId]);
                                    }
                                  })
                                  ->select($fields)
                                  ->get();
    return $requests;
  }
}
