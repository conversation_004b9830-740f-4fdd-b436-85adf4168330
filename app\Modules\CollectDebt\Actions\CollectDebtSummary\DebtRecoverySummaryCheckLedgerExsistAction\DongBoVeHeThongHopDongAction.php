<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction;

use DB;
use Exception;
use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareGetContractCodeAction\DebtRecoveryShareGetContractCodeAction;

class DongBoVeHeThongHopDongAction
{

	private array $__summaryIdsExcept = [];
	private string $__contractCodeSync = '';

	public function initDongBoHopDong($contractCode = '')
	{
		$this->__contractCodeSync = $contractCode;

		$returnData = [];

		for ($i = 0; $i <= 50; $i++) {
			mylog(['--------------------------------------' => sprintf('%s ------------------------------', $i)]);

			try {
				$result = $this->run();

				if ($result == 'EMPTY') {
					$returnData[] = 'khong co thong tin HD can dong bo';
					break;
				}

				if ($result && optional($result)->id) {
					$returnData[] = $result->only(['id', 'contract_code']);
				}
			} catch (\Throwable $th) {
				mylog(['[LOI XY LY]' => Helper::traceError($th)]);
				@TelegramAlert::sendEmails(Helper::traceError($th));
				//throw $th;
				continue;
			} finally {
				
			}
		}

		return $returnData;
	}


	public function run()
	{
		$collectDebtSummary = CollectDebtSummary::query()
																						->where('is_request_sync', CollectDebtEnum::SUMMARY_CO_DONG_BO);

		if (!empty($this->__summaryIdsExcept)) {
			$collectDebtSummary = $collectDebtSummary->whereNotIn('id', $this->__summaryIdsExcept);
		}

		if (!empty($this->__contractCodeSync)) {
			$collectDebtSummary = $collectDebtSummary->where('contract_code', $this->__contractCodeSync);
		}

		$collectDebtSummary = $collectDebtSummary->first();

		if (!$collectDebtSummary) {
			mylog(['[EMPTY]' => 'khong co thong tin HD can dong bo']);
			return 'EMPTY';
		}

		$this->__summaryIdsExcept[] = $collectDebtSummary->id;
		mylog(['Ban ghi HD dong bo la' => $collectDebtSummary->contract_code]);

		$updateLenDangDongBo = CollectDebtSummary::query()
			->where('id', $collectDebtSummary->id)
			->where('is_request_sync', CollectDebtEnum::SUMMARY_CO_DONG_BO)
			->update([
				'is_request_sync' => CollectDebtEnum::SUMMARY_DANG_DONG_BO
			]);

		if (!$updateLenDangDongBo) {
			mylog(['[LOI]' => 'khong the cap nhat trang thai dong bo thanh DANG DONG BO']);
			throw new Exception('khong the cap nhat trang thai dong bo thanh DANG DONG BO');
		}

		// khong can refresh vi day chi la dong bo
		DB::beginTransaction();
		try {
			$dataShare = $this->__getDataShare($collectDebtSummary);
			mylog(['dataShare' => $dataShare['contract_code'] ?? 'N/A']);

			$summaryDataSync = $this->__caculateTime($collectDebtSummary);
			mylog(['summaryDataSync' => $summaryDataSync]);

			$summaryMerge = array_merge($collectDebtSummary->toArray(), $summaryDataSync);
			mylog(['summaryMerge' => $summaryMerge]);

			$createdEventResult = $this->__createEvent($dataShare, $summaryMerge);
			mylog(['createdEventResult' => $createdEventResult]);

			if (!$createdEventResult) {
				mylog(['[LOI CREATE EVENT]' => 'Khong the tao duoc ban ghi event dong bo']);
				throw new Exception('Khong the tao duoc ban ghi Event dong bo');
			}

			$updateVeKhongPhaiDongBo = CollectDebtSummary::query()
																									 ->where('id', $collectDebtSummary->id)
																									 ->update(['is_request_sync' => CollectDebtEnum::SUMMARY_KHONG_DONG_BO]);
			if (!$updateVeKhongPhaiDongBo) {
				mylog(['[LOI CAP NHAT VE KHONG PHAI DONG BO NUA]' => 'loi']);
			}
			
			DB::commit();

			return $collectDebtSummary;
		} catch (\Throwable $th) {
			mylog(['[LOI DONG BO]' => Helper::traceError($th)]);

			DB::rollBack();

			$updateVeCoDongBo = CollectDebtSummary::query()
				->where('id', $collectDebtSummary->id)
				->where('is_request_sync', CollectDebtEnum::SUMMARY_DANG_DONG_BO)
				->update([
					'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO
				]);

			if (!$updateVeCoDongBo) {
				mylog([
					'[LOI]' => 'Khong the update tu DANG DONG BO -> CO DONG BO',
					'KetQuaUpdate' => $updateVeCoDongBo
				]);
			}

			throw $th;
		}

		return [];
	}

	protected function __getDataShare(CollectDebtSummary $collectDebtSummary)
	{
		$inputs = [ 'contract_code' => $collectDebtSummary->contract_code];
		return app(DebtRecoveryShareGetContractCodeAction::class)->run($inputs);
	}


	protected function __setDataEvent($dataShare)
	{
		$data = [];

		if ($dataShare) {
			if (isset($dataShare['company_data']) && $dataShare['company_data']) {
				$data['company'] = json_decode($dataShare['company_data'], true);
			}
			if (isset($dataShare['contract_data']) && $dataShare['contract_data']) {
				$data['contract'] = json_decode($dataShare['contract_data'], true);
			}
			if (isset($dataShare['profile_data']) && $dataShare['profile_data']) {
				$data['profile'] = json_decode($dataShare['profile_data'], true);
			}
			if (isset($dataShare['payment_guide']) && $dataShare['payment_guide']) {
				$data['payment'] = json_decode($dataShare['payment_guide'], true);
			}
			if (isset($dataShare['list_fee']) && $dataShare['list_fee']) {
				$data['list_fee'] = json_decode($dataShare['list_fee'], true);
			}
		}

		return $data;
	}

	protected function __createEvent(array $dataShare, array $dataSummary)
	{

		$inputs = [
			'category_care_code' => 'CONTRACT',
			'service_care_code' => 'SYNC',
			'data' => $this->__setDataEvent($dataShare),
			'description' => 'Tao Event dong bo',
			'other_data' => [
				'summary' => $dataSummary,
			],
			'time_start' => time(),
			'contract_code' => $dataSummary['contract_code'],
		];
		$create = (new \App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateAction\DebtRecoveryContractEventCreateAction())->run($inputs);
		if (isset($create['id']) && $create['id']) {
			return true;
		}
		return false;
	}

	/**
	 * Tinh toan:
	 * - Ky tiep theo la ky so may
	 * - Tu thoi diem hien tai, con bao nhieu ngay nua la toi ky tiep theo do
	 * @param CollectDebtSummary $collectDebtSummary
	 * @return array
	 */
	public function __caculateTime(CollectDebtSummary $collectDebtSummary): array
	{
		$contractData = $collectDebtSummary->getContractData();
		$timeEndAsDate = Carbon::createFromTimestamp($contractData['time_end']);

		if ($collectDebtSummary->isHopDongDaTatToan() || $collectDebtSummary->isDaQuaHanHopDong()) {
			return [
				'number_day_status_debt' => 0,
				'next_payment_period' => 0,
			];
		}


		// HĐ trích ngày
		if ($collectDebtSummary->isHopDongSummaryTrichNgay()) {
			$chenhLechGiuaNgayTatToanVaNgayHienTai = $timeEndAsDate->diffInDays(now());

			$returnData = [
				'number_day_status_debt' => $chenhLechGiuaNgayTatToanVaNgayHienTai,
				'next_payment_period' => 0, // HĐ trích ngày thì truyền cái giá trị kỳ thứ mấy = 0;
			];
		}


		// HĐ trích kỳ
		if ($collectDebtSummary->isHopDongSummaryTrichKy()) {
			// $plans = CollectDebtSchedule::query()
			// 	->where('contract_code', $collectDebtSummary->contract_code)
			// 	->where('type', CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH)
			// 	->where('isfee', CollectDebtEnum::METADATA_THU_GOC)
			// 	->get();

			// $kyTiepTheo = 1;

			// foreach ($plans as $index => $p) {
			// 	if (now()->gte($p->time_start_as_date)) {
			// 		$kyTiepTheo = $p->cycle_number + 2; // +2 là do cycle_number bắt đầu từ 0
			// 		mylog(['Ky tiep theo' => $kyTiepTheo]);
			// 	}
			// }

			// $planTiepTheo = $plans->first(function (CollectDebtSchedule $plan) use ($kyTiepTheo) {
			// 	return $plan->cycle_number == $kyTiepTheo - 1;
			// });
			$planTiepTheo = CollectDebtSchedule::query()->where('contract_code', $collectDebtSummary->contract_code)
																				 ->where('type', CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH)
																				 ->where('isfee', CollectDebtEnum::METADATA_THU_GOC)
																				 ->where('time_start', '>=', now()->timestamp)
																				 ->orderBy('cycle_number', 'ASC')
																				 ->first();

			if (!$planTiepTheo) {
				mylog(['Khong co thong tin plan tiep theo' => 'ok']);
				return [
					'number_day_status_debt' => 0,
					'next_payment_period' => 0,
				];
			}

			$chenhLechNgayHienTaiVaKyTiepTheo = $planTiepTheo->time_start_as_date->endOfDay()->diffInDays(now());

			if ($planTiepTheo->rundate_as_date->isSameDay(now())) {
				$chenhLechNgayHienTaiVaKyTiepTheo = 1;
			}

			mylog(['chenhLechNgayHienTaiVaKyTiepTheo' => $chenhLechNgayHienTaiVaKyTiepTheo]);

			$returnData = [
				'number_day_status_debt' => $chenhLechNgayHienTaiVaKyTiepTheo,
				'next_payment_period' => $planTiepTheo->cycle_number+1,
			];
		}

		return $returnData;
	}
} // End class
