<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DebtRecoverySummaryRequestRefundFeeRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.contract_code' => ['required', 'string', 'max:50'],
      'data.fee_over_cycle_want_refund' => [ 'required', 'numeric', 'integer', 'min:0' ],
      'data.fee_overdue_want_refund' => ['required', 'numeric', 'integer', 'min:0'],
      'data.reason' => ['required', 'string', 'max:255'],
      'data.attachments' => ['array'],
      'data.attachments.*' => ['string'],
			'data.created_by' => ['required', 'array']
    ];
  }

	protected function passedValidation() {
		$params = $this->all();
		$params['data']['created_by'] = StandardizedDataFilter::getUserAdminStructCompact($params['data']['created_by']);
		$this->merge($params);
	}
} // End class
