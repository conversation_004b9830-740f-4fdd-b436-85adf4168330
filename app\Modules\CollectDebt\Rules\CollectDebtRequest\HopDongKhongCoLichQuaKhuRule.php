<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtRequest;

use Illuminate\Contracts\Validation\Rule;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class HopDongKhongCoLichQuaKhuRule implements Rule
{
  /**
   * Create a new rule instance.
   *
   * @return void
   */
  public function __construct()
  {
    //
  }

  /**
   * Determine if the validation rule passes.
   *
   * @param  string  $contractField
   * @param  mixed  $contractCode
   * @return bool
   */
  public function passes($contractField, $contractCode)
  {
    $plans = CollectDebtSchedule::where('contract_code', $contractCode)
															  ->where('rundate', '<', date('Ymd'))
																->whereNotFinalStatus()
																->get();
    
    if ($plans->count() == 0) {
      return true;
    }

    // Nếu false, thì toàn bộ các lịch phải có rundate
    if (now()->isMonday()) {
      // Toàn bộ lịch ko hợp lệ phải có rundate là chủ nhật và kém hơn ngày hiện tại 1 ngày
      $isRunDateChuNhatGanNhat = $plans->every(function (CollectDebtSchedule $plan) {
        $yesterday = now()->subDay()->format('Ymd');
        return $plan->rundate_as_date->isSunday() && $yesterday == $plan->rundate;
      });

      return $isRunDateChuNhatGanNhat;
    }
    
    return false;
  }

  /**
   * Get the validation error message.
   *
   * @return string
   */
  public function message()
  {
    return 'Hợp đồng này vẫn còn lịch quá khứ đang thực thi nên không thể dừng job được';
  }
}
