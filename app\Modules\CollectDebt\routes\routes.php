<?php

use Carbon\Carbon;
use App\Lib\TelegramAlert;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\StripHtmlTagMiddleware;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\PushSoLieuBangTongHopTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\DongBoHopDongVer1SubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\GetPlanSummaryHandleAfterAccoutingAction\GetPlanSummaryHandleAfterAccoutingAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetAmountCanRefundAction\DebtRecoverySummaryGetAmountCanRefundAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentStatisticAction\DebtRecoveryRequestAdjustmentStatisticAction;


include_once('test.php');

include_once('cronjob.php');

include_once('digitalNoti.php');

Route::group([
	'middleware' => [
		'checktoken',
		StripHtmlTagMiddleware::class
	],
], function () {
	/* ----------------------------------- Trích  tay ------------------------------------------- */
	include_once('trichtay.php');
	/* ------------------------------------ Danh sác cronjob ------------------------------------ */
	
	/* ------------------------------------ Configs ------------------------------------ */
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtConfig'], function () {
		Route::any('/configs', 'ConfigController@index')->name('configs');
		Route::any('/show-off', 'ConfigShowOffController@index')->name('showOff');
	});
	/* ------------------------------------ Guide  ------------------------------------ */
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide'], function () {
		Route::post('/DebtRecoveryContractGuideCreate', 'CollectDebtGuideController@store')->name('DebtRecoveryContractGuideCreate');
		Route::post('/DebtRecoveryContractGuideUpdate', 'CollectDebtGuideController@update')->name('DebtRecoveryContractGuideUpdate');
		Route::post('/DebtRecoveryContractGuideSetStatus', 'CollectDebtGuideController@setStatus')->name('DebtRecoveryContractGuideSetStatus');
		Route::post('/DebtRecoveryContractGuideGetById', 'CollectDebtGuideController@show')->name('DebtRecoveryContractGuideGetById');
		Route::post('/DebtRecoveryContractGuideSearchData', 'CollectDebtGuideController@index')->name('DebtRecoveryContractGuideSearchData');
		Route::post('/DebtRecoveryContractGuideGetByContractCode', 'CollectDebtGuideController@getByContractCode')->name('DebtRecoveryContractGuideGetByContractCode');
		Route::post('/DebtRecoveryContractGuideQuickStatistic', 'CollectDebtGuideController@quickStatistic')->name('DebtRecoveryContractGuideGetByContractCode');
	});

	/* ------------------------------------- Schedule ------------------------------------ */
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule'], function () {
		Route::post('/DebtRecoveryContractPlanCreate', 'CollectDebtScheduleController@planCreate')->name('DebtRecoveryContractPlanCreate');
		Route::post('/DebtRecoveryContractPlanGetById', 'CollectDebtScheduleController@getById')->name('DebtRecoveryContractPlanGetById');
		Route::post('/DebtRecoveryContractPlanSetStatus', 'CollectDebtScheduleController@setStatus')->name('DebtRecoveryContractPlanSetStatus');
		Route::post('/DebtRecoveryContractPlanUpdate', 'CollectDebtScheduleController@planUpdate')->name('DebtRecoveryContractPlanSetStatus');
		Route::post('/DebtRecoveryContractPlanSearchData', 'CollectDebtScheduleController@searchData')->name('DebtRecoveryContractPlanSearchData');
		Route::post('/DebtRecoveryContractPlanGetByContractCode', 'CollectDebtScheduleController@getByContractCode')->name('DebtRecoveryContractPlanGetByContractCode');
	});
	/* ------------------------------------ Request (Yêu cầu trích) ------------------------------------ */
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest'], function () {
		Route::post('/DebtRecoveryRequestSetStatus', 'CollectDebtRequestController@setStatus')->name('DebtRecoveryRequestSetStatus');
		Route::post('/DebtRecoveryRequestSetStatusPayment', 'CollectDebtRequestController@setStatusPayment')->name('DebtRecoveryRequestSetStatusPayment');
		Route::post('/DebtRecoveryRequestSetStatusRecored', 'CollectDebtRequestController@setStatusRecored')->name('DebtRecoveryRequestSetStatusRecored');
		Route::post('/DebtRecoveryRequestGetListPlanProcessing', 'CollectDebtRequestController@getListPlanProcessing')->name('DebtRecoveryRequestGetListPlanProcessing');
		Route::post('/DebtRecoveryRequestGetById', 'CollectDebtRequestController@getById')->name('DebtRecoveryRequestGetById');
		Route::post('/DebtRecoveryRequestSearchData', 'CollectDebtRequestController@searchData')->name('DebtRecoveryRequestSearchData');
		Route::post('/DebtRecoveryRequestCreate', 'CollectDebtRequestController@create')->name('DebtRecoveryRequestCreate');
		Route::post('/DebtRecoveryRequestUpdate', 'CollectDebtRequestController@update')->name('DebtRecoveryRequestUpdate');

		Route::post('/DebtRecoveryRequestGetByParam', 'CollectDebtRequestGatewayController@getByParam')->name('DebtRecoveryRequestGetByParam');
		Route::post('/DebtRecoveryRequestForContract', 'CollectDebtRequestContractController@getByContract')->name('DebtRecoveryRequestForContract');
		Route::any('/DebtRecoveryRequestReduceFee', 'CollectDebtRequestContractController@DebtRecoveryRequestReduceFee')->name('DebtRecoveryRequestReduceFee');
		Route::any('/DebtRecoveryRequestReduceFeeSearchData', 'CollectDebtRequestContractController@DebtRecoveryRequestReduceFeeSearchData')->name('DebtRecoveryRequestReduceFeeSearchData');
		Route::post('/DebtRecoveryRequestStatisticContract', 'CollectDebtRequestContractController@statisticContract')->name('DebtRecoveryRequestStatisticContract');
		Route::post('/DebtRecoveryRequestGetByPlanId', 'CollectDebtRequestPlanController@getByPlanId')->name('DebtRecoveryRequestGetByPlanId');
		
		// Giao dịch hoàn
		Route::post('/DebtRecoveryRequestGetRefundTransByContract', 'CollectDebtRequestRefundController@getRefundTransByContract')->name('DebtRecoveryRequestGetRefundTransByContract');

		// Hủy lại lệnh đã hủy
		Route::post('/DebtRecoveryRequestReCancel', 'CollectDebtRequestController@DebtRecoveryRequestReCancel')->name('DebtRecoveryRequestReCancel');
	});

	/* ------------------------------------ Partner (Công nợ trên đối tác) ------------------------------------ */
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtPartner'], function () {
		Route::post('/DebtRecoveryPartnerCreate', 'CollectDebtPartnerController@create')->name('DebtRecoveryPartnerCreate');
		// Route::post('/DebtRecoveryPartnerUpdate', 'CollectDebtPartnerController@update')->name('DebtRecoveryPartnerCreate');
		Route::post('/DebtRecoveryPartnerSetStatus', 'CollectDebtPartnerController@setStatus')->name('DebtRecoveryPartnerSetStatus');
		Route::post('/DebtRecoveryPartnerGetById', 'CollectDebtPartnerController@getById')->name('DebtRecoveryPartnerGetById');
		// Route::post('/DebtRecoveryRequestSearchData', 'CollectDebtPartnerController@searchData')->name('DebtRecoveryRequestSearchData');
		Route::get('/DebtRecoveryPartnerGetByPartnerTransactionId', 'CollectDebtPartnerController@getByPartnerTransactionId')->name('DebtRecoveryPartnerGetByPartnerTransactionId');
		Route::any('/DebtRecoveryPartnerGetByContractCode', 'CollectDebtPartnerController@getByContractCode')->name('DebtRecoveryPartnerGetByContractCode');
		Route::any('/DebtRecoveryPartnerGetListRefundTransactionOnPartner', 'CollectDebtPartnerController@getListRefundTransactionOnPartner')->name('DebtRecoveryPartnerGetListRefundTransactionOnPartner');
	});

	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtEmail'], function () {
		Route::any('/DebtRecoveryEmailGetByContract', 'CollectDebtEmailController@getEmailByContract')->name('DebtRecoveryEmailGetByContract');
		Route::any('/DebtRecoveryEmailGetDetailById', 'CollectDebtEmailController@getDetailById')->name('DebtRecoveryEmailGetByContract');
	});

	/* ------------------------------------ SỔ ------------------------------------ */
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger'], function () {
		Route::post('/DebtRecoveryLedgerCreate', 'CollectDebtLedgerController@create')->name('DebtRecoveryLedgerCreate');
		Route::post('/DebtRecoveryLedgerUpdate', 'CollectDebtLedgerController@update')->name('DebtRecoveryLedgerUpdate');
		Route::post('/DebtRecoveryLedgerGetById', 'CollectDebtLedgerController@getById')->name('DebtRecoveryLedgerGetById');
		Route::post('/DebtRecoveryLedgerGetByContract', 'CollectDebtLedgerController@getByContract')->name('DebtRecoveryLedgerGetByContract');
		Route::post('/DebtRecoveryLedgerSearchData', 'CollectDebtLedgerController@searchData')->name('DebtRecoveryLedgerSearchData');
		Route::post('/DebtRecoveryLedgerSetStatus', 'CollectDebtLedgerController@setStatus')->name('DebtRecoveryLedgerSetStatus');
		Route::post('/DebtRecoveryLedgerSetStatusAction', 'CollectDebtLedgerController@setStatusAction')->name('DebtRecoveryLedgerSetStatusAction');
	});


	// Summary
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary'], function () {
		Route::post('/DebtRecoverySummaryCreate', 'CollectDebtSummaryController@create')->name('DebtRecoverySummaryCreate');
		Route::post('/DebtRecoverySummaryUpdate', 'CollectDebtSummaryController@update')->name('DebtRecoverySummaryUpdate');
		Route::any('/DebtRecoverySummaryGetByContractCode', 'CollectDebtSummaryController@getByContractCode')->name('DebtRecoverySummaryGetByContractCodeAction');
		Route::post('/DebtRecoverySummarySetByAccounting', 'CollectDebtSummaryController@setByAccounting')->name('DebtRecoverySummarySetByAccounting');
		Route::post('/DebtRecoverySummaryAmountingExcessCash', 'CollectDebtSummaryExcessController@createExcessCash')->name('DebtRecoverySummaryAmountingExcessCash');
		Route::post('/DebtRecoverySummaryAmountingExcessForm', 'CollectDebtSummaryExcessController@excessForm')->name('DebtRecoverySummaryAmountingExcessForm');
		Route::any('/DebtRecoverySummarySetAmountRefund', 'CollectDebtSummaryExcessController@result')->name('DebtRecoverySummaryAmountingExcessResult');
		Route::post('/DebtRecoverySummaryDetail', 'CollectDebtSummaryController@detail')->name('DebtRecoverySummaryDetail');
		
		// notify cho excess
		Route::any('/DebtRecoverySummaryPlusExcessRefund', function () {
			return 'ok';
		});

		Route::any('/DebtRecoverySummaryCancelExcessRefund', function () {
			return 'ok';
		});

		// Báo cáo chi tiết thu hồi
		Route::post('/DebtRecoverySummaryDebitReport', 'CollectDebtSummaryReportController@debitReport')->name('DebtRecoverySummaryDebitReport');
		
		// Báo cáo đến kỳ
		Route::post('/DebtRecoverySummaryPaymentCycleReport', 'CollectDebtSummaryReportController@paymentCycleReport')->name('DebtRecoverySummaryPaymentCycleReport');

		Route::post('/DebtRecoverySummaryResyncContract', 'CollectDebtSummaryController@resyncContract')->name('DebtRecoverySummaryResyncContract');
		Route::post('/DebtRecoverySummaryPlusAmountRepayment', 'CollectDebtSummaryCashInController@DebtRecoverySummaryPlusAmountRepayment')->name('DebtRecoverySummaryPlusAmountRepayment');
		Route::post('/DebtRecoverySummaryMinusAmountRepayment', 'CollectDebtSummaryCashInController@DebtRecoverySummaryMinusAmountRepayment')->name('DebtRecoverySummaryMinusAmountRepayment');
		Route::post('/DebtRecoverySummaryBuildFormRepayment', 'CollectDebtSummaryCashInController@DebtRecoverySummaryBuildFormRepayment')->name('DebtRecoverySummaryBuildFormRepayment');
		
		// Đồng bộ việc cho tiền, nhận tiền thu]à
		Route::post('/DebtRecoverySummarySyncHandleExcess', 'CollectDebtSummaryCashInController@DebtRecoverySummarySyncHandleExcess')->name('DebtRecoverySummarySyncHandleExcess');
		
		// Cac route cho phan hoan phi
		Route::post('/DebtRecoverySummaryRefundFeeBuildForm', 'CollectDebtSummaryRefundFeeController@DebtRecoverySummaryRefundFeeBuildForm')->name('DebtRecoverySummaryRefundFeeBuildForm');
		Route::post('/DebtRecoverySummaryRequestRefundFee', 'CollectDebtSummaryRefundFeeController@DebtRecoverySummaryRequestRefundFee')->name('DebtRecoverySummaryRequestRefundFee');
		Route::any('/DebtRecoverySummaryPlusFeeRefund', 'CollectDebtSummaryRefundFeeController@DebtRecoverySummaryPlusFeeRefund')->name('DebtRecoverySummaryPlusFeeRefund');
		Route::any('/DebtRecoverySummaryCancelFeeRefund', 'CollectDebtSummaryRefundFeeController@DebtRecoverySummaryCancelFeeRefund')->name('DebtRecoverySummaryCancelFeeRefund');
	});
	/* ------------------------------------ Xử lý dừng job ------------------------------------ */
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtConfigAuto'], function () {
		Route::post('/DebtRecoveryAllSetting', 'CollectDebtSettingController@allSetting')->name('DebtRecoveryAllSetting');
		Route::post('/DebtRecoveryCreateSetting', 'CollectDebtSettingController@createSetting')->name('DebtRecoveryAllSetting');
		Route::post('/DebtRecoveryUpdateSetting', 'CollectDebtSettingController@updateSetting')->name('DebtRecoveryUpdateSetting');
		Route::post('/DebtRecoveryFindSetting', 'CollectDebtSettingController@findSetting')->name('DebtRecoveryFindSetting');
	});

	// Share
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtShare'], function () {
		Route::post('/DebtRecoveryShareCreate', 'CollectDebtShareController@create')->name('DebtRecoveryShareCraete');
		Route::post('/DebtRecoveryShareUpdate', 'CollectDebtShareController@update')->name('DebtRecoveryShareUpdate');
		Route::get('/DebtRecoveryShareGetByContractCode', 'CollectDebtShareController@getByContractCode')->name('DebtRecoveryShareGetByContractCode');
	});

	// Yêu cầu điều chỉnh
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequestAdjustment'], function () {
		Route::post('/DebtRecoveryRequestAdjustmentStore', 'CollectDebtRequestAdjustmentController@store')->name('DebtRecoveryRequestAdjustmentStore');
		Route::post('/DebtRecoveryRequestAdjustmentCreate', 'CollectDebtRequestAdjustmentController@create')->name('DebtRecoveryRequestAdjustmentCreateAction');
		Route::post('/DebtRecoveryRequestAdjustmentUpdate', 'CollectDebtRequestAdjustmentController@update')->name('DebtRecoveryRequestAdjustmentUpdate');
		Route::post('/DebtRecoveryRequestAdjustmentContract', 'CollectDebtRequestAdjustmentController@getByContract')->name('DebtRecoveryRequestAdjustmentContract');
		Route::post('/DebtRecoveryRequestAdjustmentGetById', 'CollectDebtRequestAdjustmentController@getById')->name('DebtRecoveryRequestAdjustmentGetById');
		Route::post('/DebtRecoveryRequestAdjustmentSearchData', 'CollectDebtRequestAdjustmentController@searchData')->name('DebtRecoveryRequestAdjustmentSearchData');
		Route::post('/DebtRecoveryRequestAdjustmentSetStatus', 'CollectDebtRequestAdjustmentController@setStatus')->name('DebtRecoveryRequestAdjustmentSetStatus');
		Route::post('/DebtRecoveryRequestAdjustmentStatistic', 'CollectDebtRequestAdjustmentController@statistic')->name('DebtRecoveryRequestAdjustmentStatisticAction');
	});

	// Hợp đồng ưu tiên
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtContractPriority'], function () {
		Route::any('/DebtRecoveryContractPriorityCreate', 'CollectDebtContractPriorityController@DebtRecoveryContractPriorityCreate')->name('DebtRecoveryContractPriorityCreate');
		Route::any('/DebtRecoveryContractPriorityDelete', 'CollectDebtContractPriorityController@DebtRecoveryContractPriorityDelete')->name('DebtRecoveryContractPriorityDelete');
	});

	// Log xu ly yc trich
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtLog'], function () {
		Route::any('/DebtRecoveryLogSearchData', 'CollectDebtLogController@DebtRecoveryLogSearchData')->name('DebtRecoveryLogSearchData');
		Route::any('/DebtRecoveryLogGetById', 'CollectDebtLogController@DebtRecoveryLogGetById')->name('DebtRecoveryLogGetById');
	});
	
	// Thông báo quả chuông
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtNotification'], function () {
		Route::any('/GetListNotification', 'CollectDebtNotificationController@GetListNotification')->name('GetListNotification');
		Route::any('/GetDetailNotification', 'CollectDebtNotificationController@GetDetailNotification')->name('GetDetailNotification');
	});

    // Gui email neu trich ngay bi lỗi
	Route::group(['namespace' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtNotifyDebtNow'], function () {
		Route::any('/DebtRecoveryCreateNotifyDebtNow', 'CollectDebtNotifyDebtNowController@createNotifyDebtNow')->name('DebtRecoveryCreateNotifyDebtNow');
	});
});


Route::any('/jobs', function () {
	return view('jobs.index', [
		'baseUrl' => app()->environment() == 'production' 
											? 'https://api.nextlend.vn/request-debt' 
											: config('app.url')
	]);
});