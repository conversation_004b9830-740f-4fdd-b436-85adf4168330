<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\DailyCollect;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\DailyCollect\DailyCutOffTimeTnex;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\DailyCollect\DailyInsideTimeTnexTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\DailyCollect\DailyXuLyCutOffTimeTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\DailyCollect\DailyXuLyInsideTimeTask;

class DailyXuLyThuThieuLamLaiLichTask
{
	public $metaData = [];

	public function run(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong, CollectDebtRequest $yeuCauDangDuocHachToan)
	{
		if (
			$yeuCauDangDuocHachToan->isYeuCauTrichLichThuQuaKhu() ||
			$yeuCauDangDuocHachToan->isYeuCauKeoDaiTimeExpired()  ||
			$yeuCauDangDuocHachToan->isYeuCauCutOff()
		) {
			return $this->__switchingHachToanCutOffTime($lichDangHachToan, $soTienTrichThanhCong);
		}

		if ($yeuCauDangDuocHachToan->isThoiDiemHachToanYeuCauTrungVoiHienTai()) {
			$cutOffTime = Carbon::parse(Helper::getCutOffTime());
			$currentTime = Carbon::parse(now()->format('H:i:s'));

			if ($currentTime->lessThan($cutOffTime)) {
				return $this->__switchingHachToanInsideTime($lichDangHachToan, $soTienTrichThanhCong);
			} else {
				return $this->__switchingHachToanCutOffTime($lichDangHachToan, $soTienTrichThanhCong);
			}
		}

		// khong ky vong xuong day
		if (Helper::isInsideTime()) {
			return $this->__switchingHachToanInsideTime($lichDangHachToan, $soTienTrichThanhCong);
		}

		if (Helper::isCutOffTime()) {
			return $this->__switchingHachToanCutOffTime($lichDangHachToan, $soTienTrichThanhCong);
		}
	} // End method

	private function __switchingHachToanInsideTime($lichDangHachToan, $soTienTrichThanhCong)
	{
		if ($lichDangHachToan->isLichThuTnex()) {
			return app(DailyInsideTimeTnexTask::class)->run($lichDangHachToan, $soTienTrichThanhCong);
		}

		return app(DailyXuLyInsideTimeTask::class)->run($lichDangHachToan, $soTienTrichThanhCong);
	}

	private function __switchingHachToanCutOffTime($lichDangHachToan, $soTienTrichThanhCong)
	{
		if ($lichDangHachToan->isLichThuTnex()) {
			return app(DailyCutOffTimeTnex::class)->run($lichDangHachToan, $soTienTrichThanhCong);
		}

		return app(DailyXuLyCutOffTimeTask::class)->run($lichDangHachToan, $soTienTrichThanhCong);
	}
} // End class