<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\LichQuaKhu;

use App\Lib\TelegramAlert;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class QuaKhuTaoLichThuVetGocTrongNgayST
{
  public function run(CollectDebtSchedule $lichDangHachToan, float $soTienConPhaiThuCuaLich=0, float $soTienDaThuThanhCong=0)
  {
    $debitBegin = $lichDangHachToan->debit_begin - $soTienDaThuThanhCong;
    $otherData = json_encode([
      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'request_created_channel' => '',
        ],
        'note' => sprintf('Thu vét nợ gốc trong ngày. Lịch đang hạch toán là: %s', $lichDangHachToan->id),
      ]
    ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

		$runDate = $lichDangHachToan->rundate;
		$timeStart = $lichDangHachToan->time_start;
		$timeEnd = $lichDangHachToan->time_end;

		if ($lichDangHachToan->rundate_as_date->isSunday()) {
			$runDate = $lichDangHachToan->rundate_as_date->copy()->addDay()->format('Ymd');
			$timeStart = $lichDangHachToan->rundate_as_date->copy()->addDay()->setTime(now()->format('H'), now()->format('i'))->timestamp;
			$timeEnd = $lichDangHachToan->rundate_as_date->copy()->addDay()->setTime(22, 0)->timestamp;
		}

    $scheduleParam = [
      'profile_id'           => $lichDangHachToan->profile_id,
      'contract_code'        => $lichDangHachToan->contract_code,
      'contract_type'        => $lichDangHachToan->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
      'debit_begin'          => $debitBegin,
      'debit_end'            => $lichDangHachToan->debit_end,
      'rundate'              => $runDate,
      'time_start'           => $timeStart,
      'time_end'             => $timeEnd,
      'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
      'request_amount_debit' => $soTienConPhaiThuCuaLich,
      'success_amount_debit' => 0,
      'other_data'           => $otherData,
      'description'          => $lichDangHachToan->collectDebtSchedule,
      'is_settlement'        => $lichDangHachToan->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangHachToan->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangHachToan->cycle_number,
      'master_id'         => $lichDangHachToan->master_id,
    ];

    return CollectDebtSchedule::forceCreate($scheduleParam);
  }
} // End class