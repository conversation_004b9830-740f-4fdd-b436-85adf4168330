<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtRequest;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Contracts\Validation\Rule;

class CacYeuCauDaHachToanXongKhiLamRutTienNhanhRule implements Rule
{
	private $__errorMessage = '';

	private string $__contractCode;
  /**
   * Create a new rule instance.
   *
   * @return void
   */
  public function __construct(string $contractCode)
  {
    $this->__contractCode = $contractCode;
  }

  /**
   * Tất cả yêu cầu của HĐ đang chạy đã về trạng thái cuối                    
   *
   * @param  string  $isDebtNowField: Trường tích "TRÍCH NGAY"
   * @param  mixed  $isDebtNowValue: Chấp nhận: "YES", "NO"
   * @return bool
   */
  public function passes($isDebtNowField, $isDebtNowValue)
  {
		if (empty($isDebtNowValue) || $isDebtNowValue == 'NO') {
			return true;
		}

    $collectDebtRequests = CollectDebtRequest::query()
															->with('ledgers:id,contract_code,request_id,status')
                              ->where('contract_code', $this->__contractCode)
															->select([
																'id',
																'contract_code',
																'status'
															])
                              ->get();
  
    return $collectDebtRequests->every(function (CollectDebtRequest $collectDebtRequest) {
			if ($collectDebtRequest->status == CollectDebtEnum::REQUEST_STT_TU_CHOI) {
				return true;
			}

			if (
				(		
					$collectDebtRequest->status == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH 
					|| $collectDebtRequest->status == CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA
				)
					&& $collectDebtRequest->ledgers->isNotEmpty()
					&& $collectDebtRequest->ledgers->every(function (CollectDebtLedger $ledger) {
						return $ledger->status == CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN;
					})
			) {
				return true;
			}

			$this->__errorMessage = 'Tồn tại yêu cầu chưa hạch toán. Từ chối yêu cầu trích ngay';
			return false;
		});
  }

  /**
   * Get the validation error message.
   *
   * @return string
   */
  public function message()
  {
    return $this->__errorMessage;
  }
}
