<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction\CollectDebtNotifyDebtNowSendMailSubAction;

class CollectDebtNotifyDebtNowSendMailSubAction
{

    public function buildContent($data)
    {
        $template = '
    <div class="mail-content" style="font-family:\'Times New Roman\', Times, serif; font-size:16px; text-align:left;">
        <h2 style="color: #d9534f;">Cảnh báo lệnh trích</h2>
        <p><strong>Mã hợp đồng:</strong> [@MaHd]</p>
        <p><strong>Mã lệnh trích:</strong> [@MaLenhTrich]</p>
        <p><strong>Trạng thái:</strong> [@TrangThai]</p>

        <p>Hệ thống phát hiện ra lệnh trích ngay <strong>[@MaLenhTrich]</strong> đang không thu đủ tiền.</p>
        <p><strong>Số tiền cần phải thu:</strong> [@SoTienCanPhaiThu]</p>
        <p><strong>Số tiền đã thu được thực tế:</strong> [@SoTienDaThuDuocThucTe]</p>

        <p>Kiểm tra chi tiết tại: 
            <a href="https://admin.nextlend.vn/apps/web/debtcollectionv4/debt-recovery-request/for-contract-new?contract_code=[@MaHd]&version=4" target="_blank" style="color: #0275d8;">
            https://admin.nextlend.vn/apps/web/debtcollectionv4/debt-recovery-request/for-contract-new?contract_code=[@MaHd]&version=4</a>
        </p>
        <br>
        <p>Trân trọng,</p>
    </div>';

        $variables = [
            '[@MaHd]' => $data['MaHd'] ?? "",
            '[@MaLenhTrich]' => $data['MaLenhTrich'],
            '[@TrangThai]' => $data['TrangThai'],
            '[@SoTienCanPhaiThu]' => $data['SoTienCanPhaiThu'],
            '[@SoTienDaThuDuocThucTe]' => $data['SoTienDaThuDuocThucTe'],
        ];
    $message = str_replace(array_keys($variables), array_values($variables), $template);

    return $message;

    }
}