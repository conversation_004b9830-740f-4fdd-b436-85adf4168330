<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummaryCompleteRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data'                              => ['required', 'array'],
      'data.contract_code'                => ['required', 'string', 'max:50'],
      'data.settlement_by'                => ['required', 'string', 'max:255']
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['settlement_by'] = StandardizedDataFilter::getUserAdminStructCompact($params['data']['settlement_by']);
    $this->merge($params);
  }
} // End class
