<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtPartner;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;

trait PartnerStatusable
{
  public function isPartnerChuaXuLy(): bool
  {
    return $this->status == CollectDebtEnum::PARTNER_STT_CHUA_XU_LY;
  }

  public function isPartnerDangXuLy(): bool
  {
    return $this->status == CollectDebtEnum::PARTNER_STT_DANG_XU_LY;
  }

  public function isPartnerXuLyLoi(): bool
  {
    return $this->status == CollectDebtEnum::PARTNER_STT_XU_LY_LOI;
  }

  public function isPartnerDaTuChoi(): bool {
    return $this->status == CollectDebtEnum::PARTNER_STT_DA_TU_CHOI;
  }

  public function isPartnerDaXuLy(): bool
  {
    return $this->status == CollectDebtEnum::PARTNER_STT_DA_XU_LY;
  }

  public function isPartnerKhongLoiHoacTuChoi(): bool {
    return !$this->isPartnerXuLyLoi() && !$this->isPartnerDaTuChoi() && !$this->isPartnerChoDuyet();
  }


  public function isPartnerChoDuyet(): bool
  {
    return $this->status == CollectDebtEnum::PARTNER_STT_CHO_DUYET;
  }

  public function isPartnerTrangThaiCuoi(): bool
  {
    return $this->isPartnerDaXuLy() || $this->isPartnerXuLyLoi();
  }

  public function hasRequest(): bool
  {
    return !empty($this->partner_request_id);
  }

  public static function listStatus(): array
  {
    return [
      CollectDebtEnum::PARTNER_STT_CHUA_XU_LY => 'Chưa xử lý',
      CollectDebtEnum::PARTNER_STT_DANG_XU_LY => 'Đang xử lý',
      CollectDebtEnum::PARTNER_STT_DA_XU_LY   => 'Đã xử lý',
      CollectDebtEnum::PARTNER_STT_XU_LY_LOI  => 'Xử lý lỗi',
      CollectDebtEnum::PARTNER_STT_CHO_DUYET  => 'Chờ duyệt',
      CollectDebtEnum::PARTNER_STT_DA_TU_CHOI => 'Đã từ chối',
    ];
  }
} // End class