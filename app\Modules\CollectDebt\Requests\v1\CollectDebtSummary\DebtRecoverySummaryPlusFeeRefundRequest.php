<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummaryPlusFeeRefundRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required'], // dung contract_code no ok hon
      'data.amount_refund' => [ 'required', 'numeric', 'integer', 'min:0' ], // so tien da hoan thanh cong
      'data.order_code' => ['required', 'string', 'max:50'], // order_code trong bang log
      'data.description' => ['nullable', 'string'], // description
      'data.admin_user_id' => ['required'], // description
    ];
  }
} // End class
