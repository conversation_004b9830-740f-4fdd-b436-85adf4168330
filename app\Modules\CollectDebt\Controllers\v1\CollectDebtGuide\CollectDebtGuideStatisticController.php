<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide;

use App\Lib\Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\DebtRecoveryContractGuideStatisticManualAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\DebtRecoveryContractGuideCheckPartnerBalanceAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\DebtRecoveryContractGuideStatisticManualDoiLuongAction;

class CollectDebtGuideStatisticController extends Controller
{
	public function statisticManual(DebtRecoveryContractGuideStatisticManualRequest $request)
	{
		try {
			$statisticReturnData = app(DebtRecoveryContractGuideStatisticManualDoiLuongAction::class)->run($request);
			return $this->successResponse($statisticReturnData, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function checkPartnerBalance(DebtRecoveryContractGuideStatisticManualRequest $request)
	{
		try {
			$checkPartnerReturnData = app(DebtRecoveryContractGuideCheckPartnerBalanceAction::class)->run($request);
			return $this->successResponse($checkPartnerReturnData, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
