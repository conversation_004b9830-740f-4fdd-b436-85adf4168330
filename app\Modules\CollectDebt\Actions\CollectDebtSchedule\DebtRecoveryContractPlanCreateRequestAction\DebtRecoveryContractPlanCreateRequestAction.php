<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction;

use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtSetting;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanCreateRqRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetListPlanProcessingAction\DebtRecoveryRequestGetListPlanProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\CreateRequestFromProactiveFlowSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromPartnerCheckFlowSubAction\CreateRequestFromPartnerCheckFlowSubAction;

class DebtRecoveryContractPlanCreateRequestAction
{  
  /**
   * Tạo yêu cầu thanh toán
   * LƯU Ý: Không được tạo yêu cầu cho các lịch đã có yêu cầu 
   * @param array $exceptPlanProcessingIds <int> [
   *  List A: Mảng danh sách các lịch thu đang có lệnh đã duyệt chạy lệnh
   *  Khi xử lý tạo yêu cầu thì phải SELECT các bản ghi Lịch KHÔNG NẰM TRONG "List A" này
   * ]
   *
   * @return void
   */
  public function run(DebtRecoveryContractPlanCreateRqRequest $request)
  {
    // Luồng chủ động
    if ( $request->isCreateRequestFromProactiveFlow() ) {
      return app(CreateRequestFromProactiveFlowSubAction::class)->run();
    } 

    // Luồng bị động, luồng này đã đc luồng PartnerCheck gọi nội bộ rồi (pausing...)
    if ( $request->isCreateRequestFromPartnerCheckFlow() ) {
      $exceptPlanProcessingIds = app(DebtRecoveryRequestGetListPlanProcessingAction::class)->run();
      return app(CreateRequestFromPartnerCheckFlowSubAction::class)->run($request, $exceptPlanProcessingIds);
    }
  } // End method
} // End class