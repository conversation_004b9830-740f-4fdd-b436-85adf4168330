<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhSummarySA\Task;

use Exception;
use App\Lib\NextlendCore;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class MoLaiHopDongVer1Task
{
  public function run(CollectDebtSummary $collectDebtSummary)
  {
		mylog(['MO_LAI_HD' => $collectDebtSummary->contract_code]);

    if ($collectDebtSummary->isHopDongTest()) {
			mylog(['Day la hop dong test' => 'khong xu ly']);
      return;
    }


    $nextlendCore = app(NextlendCore::class)->callRequest([
      'contract_code' => $collectDebtSummary->contract_code
    ], 'ContractV4_requestContinueDebt', 'post');


    $decryptData = $nextlendCore->decryptData();

		if ( empty($decryptData['contract_id']) ) {
			mylog(['Loi khong descrypt duoc thong tin']);
			throw new Exception('Không thể mở lại HĐ: ' . $collectDebtSummary->contract_code);
		}

		@TelegramAlert::sendCancelMpos('MLL: Mo lai  hop dong, LogID: ' . request('api_request_id') );

    return $decryptData;
  }
} // End class