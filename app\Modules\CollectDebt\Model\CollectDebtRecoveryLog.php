<?php

namespace App\Modules\CollectDebt\Model;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Database\Eloquent\Model;

class CollectDebtRecoveryLog extends Model
{
	protected $table   = 'debt_recovery_logs';
	public $timestamps = false;
	protected $guarded = [];

	public function isThanhCong(): bool {
		return $this->status == CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG;
	}

	public function isThatBai(): bool {
		return $this->status == CollectDebtEnum::RL_STT_DA_XU_LY_THAT_BAI;
	}

	public function isTrangThaiCuoi(): bool {
		return $this->isThanhCong() || $this->isThatBai();
	}

	public function getPhiHoanTrenYeuCau() {
		$requestData = json_decode($this->request_data, true);
		return $requestData['amount'] ?? 0;
	}
} // End class
