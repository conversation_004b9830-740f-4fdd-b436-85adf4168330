<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummaryDebitReportRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
			'data.filter' => ['nullable', 'array'],
			'data.filter.contract_codes' => ['nullable', 'array'],
			'data.filter.contract_codes.*' => ['nullable', 'string'],
    ];
  }
} // End class
