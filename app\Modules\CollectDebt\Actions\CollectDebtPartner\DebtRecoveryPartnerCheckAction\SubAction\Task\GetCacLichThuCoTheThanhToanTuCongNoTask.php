<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task;

use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST\GetHopDongDangDungJobTuDongST;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;

class GetCacLichThuCoTheThanhToanTuCongNoTask
{  
  private $refillAmount = [];

  public $soTienNhanTuPartnerThanhCong = 0;

  /**
   * Hàm lấy ra toàn bộ lịch có thể mang đi thanh toán từ công nợ
   *    + Trạng thái: MỚI TẠO
   *    + debit_end >= thời gian hiện tại
   *    + HĐ chưa tất toán
   * @param string $contractCode [Mã code hợp đồng]
   * @param float $totalAmountReceiverFromVA [Số tiền mà các partner đã thu được]
   *
   * @return array [
   *  'plans' => Collection <CollectDebtSchedule> [Danh sách lịch phù hợp],
   *  'amount_request' => Số tiền amount_request được tính toán
   * ]
   */
  public function run(string $contractCode, float $soTienKhaDungMangDiThanhToan=0): array
  {
    $this->soTienNhanTuPartnerThanhCong = $soTienKhaDungMangDiThanhToan;
    
    $plans = CollectDebtSchedule::query()
                                ->where('contract_code', $contractCode)
                                ->whereIn('status', [
                                CollectDebtEnum::SCHEDULE_STT_MOI,
                                CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN
                              ])
                              ->get();
    
    $plans = app(PlanSortableCollectionByRule::class)->sortCollection($plans);

    $scheduleAsCollection = Collection::make();

    if ($plans->isEmpty()) {
      return [
        'plans' => $scheduleAsCollection,
        'amount_request' => 0
      ];
    }

    // Chỉ lấy ra các lịch khi tổng số tiền cộng gộp nhỏ hơn <= số tiền có thể mang đi thanh toán
    foreach ($plans as $plan) {
      if ($soTienKhaDungMangDiThanhToan <= 0) {
        break;
      }

      $this->refillAmount[] = [
        'plan_id' => $plan->id,
        'request_amount' => $plan->getSoTienConPhaiThanhToan(), // Tổng số tiền còn phải thanh toán của các lịch
      ];
      
      $soTienKhaDungMangDiThanhToan -= $plan->getSoTienConPhaiThanhToan();

      $scheduleAsCollection->push($plan);
    }

    $tongTienPhaiThanhToan = collect($this->refillAmount)->sum('request_amount');

    $returnData = [
      // Các lịch phù hợp để trích qua VA
      'plans' => $scheduleAsCollection, 

      // Số tiền yêu cầu trích của các lịch
      'amount_request' => $tongTienPhaiThanhToan, 

      // Số tiền thu thành công của kênh VA đc fill vào các lịch 
			// update 14.03.24, thay vì lấy min thì mang đi thanh toán hết
      'amount_receiver' => $this->soTienNhanTuPartnerThanhCong,  
    ];

    return $returnData;
  }
}
