<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\GetReduceAndPrepayPlanRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCheckRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestListOfTodayRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestApproveStep2Request;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCancelDebtNowRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCancelAutoTypeRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestAddingAttachmentRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\GetReduceAndPrepayPlanAction\GetReduceAndPrepayPlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\DebtRecoveryRequestApproveStep2Action;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestListOfTodayAction\DebtRecoveryRequestListOfTodayAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\DebtRecoveryRequestCreateManualAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelDebtNowAction\DebtRecoveryRequestCancelDebtNowAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelAutoTypeAction\DebtRecoveryRequestCancelAutoTypeAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestAddingAttachmentAction\DebtRecoveryRequestAddingAttachmentAction;

class CollectDebtRequestManualController extends Controller
{
	private function __checkPauseContractJob($request)
	{
		$isPauseContractJob = CollectDebtConfigAuto::isPauseContractJob($request->json('data.contract_code'));
		throw_if(
			!$isPauseContractJob,
			new Exception(sprintf('Bạn cần phải dừng job trích tự động cho hợp đồng `%s` trước đã', $request->json('data.contract_code')))
		);
	}

	public function cancelAutoType(DebtRecoveryRequestCancelAutoTypeRequest $request)
	{
		try {
			
			$cancelRequestIds = app(DebtRecoveryRequestCancelAutoTypeAction::class)->run($request);
			$message = sprintf('Đã hủy toàn bộ các yêu cầu thu hồi tự động của HĐ: %s', $request->json('data.contract_code'));
			return $this->successResponse([
				'rows' => count($cancelRequestIds),
				'data' => $cancelRequestIds 
			], $request, 200, $message);
		} catch (\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function createManual(DebtRecoveryRequestCreateManualRequest $request)
	{
		try {
			$this->__checkPauseContractJob($request);

			$collectDebtPartner = app(DebtRecoveryRequestCreateManualAction::class)->run($request);
			
			$message = 'Đã tạo yêu cầu trích tay';

      if ($request->isTrichNgay()) {
        $message = 'Đã tạo yêu cầu TRÍCH NGAY';
      }

			if ($request->isDeXuatGiamPhi()) {
				$message = 'Đã tạo đề xuất giảm phí';
			}

			return $this->successResponse($collectDebtPartner->toArray(), $request, 200, $message);
		} catch (\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	// Duyệt giảm phí lần 2
	public function approveStep2(DebtRecoveryRequestApproveStep2Request $request)
	{
		try {
			// $this->__checkPauseContractJob($request);

			$collectDebtRequest = app(DebtRecoveryRequestApproveStep2Action::class)->run($request);
			return $this->successResponse($collectDebtRequest->toArray(), $request, 200, $collectDebtRequest->__apiMessage);
		} catch (\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function listOfToday(DebtRecoveryRequestListOfTodayRequest $request)
	{
		try {
			$collectDebtRequests = app(DebtRecoveryRequestListOfTodayAction::class)->run($request);

			return $this->successResponse([
				'rows' => $collectDebtRequests->count(),
				'data' => $collectDebtRequests->toArray()
			], $request);
		} catch (\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function checkAutoDebt(DebtRecoveryRequestCheckRequest $request) {
		try {
			$collectDebtRequest = CollectDebtRequest::find($request->json('data.request_id'));
			$checkDebtResult = app(CheckRequestViaMposSubAction::class)->checkWithoutAnyAction($collectDebtRequest, $request, true);

			return $this->successResponse($checkDebtResult, $request);	
		} catch (\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}


	public function cancelDebtNow(DebtRecoveryRequestCancelDebtNowRequest $request) {
		try {
			$cancelRequestIds = app(DebtRecoveryRequestCancelDebtNowAction::class)->run($request);

			return $this->successResponse(['result' => $cancelRequestIds], $request, 200, 'Hủy yêu cầu rút tiền nhanh thành công');	
		} catch (\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
