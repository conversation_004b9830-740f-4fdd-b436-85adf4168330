<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\LichQuaKhu;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TaoLichPhiChamKySangHomSauST
{
  public function run(CollectDebtSchedule $lichDangHachToan, float $phiChamKy = 0)
  {
		$timeStart = $lichDangHachToan->time_start_as_date->addDay()->timestamp;
		$timeEnd = $lichDangHachToan->time_end_as_date->addDay()->timestamp;
		$runDate = $lichDangHachToan->rundate_as_date->addDay()->format('Ymd');

    $feeScheduleParam = [
      'profile_id'           => $lichDangHachToan->profile_id,
      'contract_code'        => $lichDangHachToan->contract_code,
      'contract_type'        => $lichDangHachToan->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
      'debit_begin'          => $phiChamKy,
      'debit_end'            => 0,
      'rundate'              => $runDate,
      'time_start'           => $timeStart,
      'time_end'             => $timeEnd,
      'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
      'request_amount_debit' => $phiChamKy,
      'success_amount_debit' => 0,
      'other_data'           => $lichDangHachToan->other_data,
      'description'          => $lichDangHachToan->collectDebtSchedule,
      'is_settlement'        => $lichDangHachToan->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangHachToan->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangHachToan->cycle_number,
      'master_id'         => $lichDangHachToan->master_id,
    ];

    $feeSchedule = CollectDebtSchedule::forceCreate($feeScheduleParam);
    return $feeSchedule;
  }
}
