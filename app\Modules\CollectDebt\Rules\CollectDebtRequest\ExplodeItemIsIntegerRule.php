<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtRequest;

use Illuminate\Contracts\Validation\Rule;

class ExplodeItemIsIntegerRule implements Rule
{
  /**
   * Create a new rule instance.
   *
   * @return void
   */
  public function __construct()
  {
    //
  }

  /**
   * Determine if the validation rule passes.
   *
   * @param  string  $attribute
   * @param  mixed  $value
   * @return bool
   */
  public function passes($planIdsField, $planIdsValue)
  {
    $explodeValues = explode(',', $planIdsValue);
    return collect($explodeValues)->every(function ($item) {
      return is_numeric($item);
    });
  }

  /**
   * Get the validation error message.
   *
   * @return string
   */
  public function message()
  {
    return 'Plan-Ids mỗi item phải là kiểu số';
  }
}
