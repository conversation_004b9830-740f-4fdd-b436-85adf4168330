<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Support\Facades\Log;

class XuLyYeuCauLoiKhongRoRangTask
{
  /**
   * Bối cảnh: YC thực hiện gửi và bị lỗi rõ ràng -> đánh dấu time_sended và sẽ gọi lại sau 20p
   * B1: Gửi lại qua MPOS
   * B2: Trả về kết quả là trùng
   *
   * @param CollectDebtRequest $collectDebtRequest
   * @return CollectDebtRequest
   */
  public function run(CollectDebtRequest $collectDebtRequest, $sendDebtResult): CollectDebtRequest
  {
		$logContext = [];
		$logContext['YeuCauBiLoiKhongRoRang'] = true;
		$logContext['LogId'] = request('api_request_id');
    

    $collectDebtRequest->time_sended = time();
    $collectDebtRequest->other_data = $collectDebtRequest->putOtherData([
      'type' => 'SENDER',
      'time_modified' => now()->timestamp,
      'data' => $sendDebtResult,
      'note' => 'Gửi lệnh bị lỗi KHÔNG RÕ RÀNG'
    ]);
    $collectDebtRequest->save();

    $logContext['SendResult'] = $sendDebtResult;

		Log::info("[GuiLenhTrich][$collectDebtRequest->partner_request_id] Loi gui lenh trich", $logContext);
    return $collectDebtRequest;
  }
} // End class