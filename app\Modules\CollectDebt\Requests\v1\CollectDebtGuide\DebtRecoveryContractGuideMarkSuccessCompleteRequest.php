<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtGuide;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;

class DebtRecoveryContractGuideMarkSuccessCompleteRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'], 
      'data.collect_done_by' => ['required', 'string', 'max:255', new UserInteractiveRule()]
    ]; 
  }
} // End class
