<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtPartner;

use App\Lib\TelegramAlert;
use Exception;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtCoreContract;

trait PartnerOtherData
{
  public function getPartnerOtherData()
  {
    if (!$this->other_data) {
      return [];
    }

    $otherData = json_decode($this->other_data, true);
    return $otherData;
  }

  public function putOtherData($data = []): string
  {
    $otherData = json_decode($this->other_data, true);
    $otherData[] = $data;
    return json_encode($otherData, JSON_UNESCAPED_UNICODE);
  }
  
  public function getCoreContract(): CollectDebtCoreContract
  {
    $otherData = $this->getPartnerOtherData();
    $otherDataContract = collect($otherData)->where('type', 'CONTRACT')->first();

    if (!$otherDataContract) {
      TelegramAlert::sendCreateRequest(parseErr([
        'LogId' => request('api_request_id'),
        'Error' => 'Lỗi tạo yêu cầu do sai struct json thông tin partner: ' . $this->id,
        'File lỗi' => self::class
      ]));
    }
    
    throw_if(!$otherData, new Exception(sprintf('Công nợ id `%s` không thể make chỉ dẫn', $this->id)));
    
    $coreContract =  new CollectDebtCoreContract($otherDataContract['data']);
    return $coreContract;
  }

  public function isYeuCauChuaThanhToan(int $requestId): bool {
    $otherData = $this->getPartnerOtherData();
    $requests = collect($otherData)->where('type', 'REQUEST')->first();
    
    if (empty($requests)) {
      return true;
    }

    foreach ($requests['data'] as $item) {
      if ($item['id'] == $requestId) {
        return false;
      }
    }
    
    return true;
  }

	public function getContractCodeOtherData(): array {
		$contractCode = [];
		$partnerOtherData = $this->getPartnerOtherData();

		foreach ($partnerOtherData as $item) {
			if ($item['type'] == 'CONTRACT') {
				$contractCode[] = $item['data']['contract_code'];
			}
		}

		return $contractCode;
	}

	public function getProfileId() {
		$partnerOtherData = $this->getPartnerOtherData();

		foreach ($partnerOtherData as $item) {
			if ($item['type'] == 'CONTRACT') {
				return $item['data']['profile_id'];
			}
		}

		return -1;
	}
} // End class