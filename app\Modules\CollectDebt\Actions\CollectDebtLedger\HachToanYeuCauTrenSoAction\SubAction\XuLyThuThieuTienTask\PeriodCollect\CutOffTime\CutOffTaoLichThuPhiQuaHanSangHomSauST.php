<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class CutOffTaoLichThuPhiQuaHanSangHomSauST
{
  
  public function run(CollectDebtSchedule $lichDangHachToan, float $phiQuaHan = 0)
  {
    $otherData = [];
		$otherData[] = [
			'type' => 'OTHER',
			'time_modified' => time(),
			'data' => [
				'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_QUA_HAN,
				'fee_config' => [],
				'request_created_channel' => '',
			],
			'note' => 'Sinh phi qua han'
		];

    $scheduleParam = [
      'profile_id'           => $lichDangHachToan->profile_id,
      'contract_code'        => $lichDangHachToan->contract_code,
      'contract_type'        => $lichDangHachToan->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
      'debit_begin'          => $phiQuaHan,
      'debit_end'            => 0,
      'rundate'              => $lichDangHachToan->rundate_as_date->copy()->addDay()->format('Ymd'),
      'time_start'           => $lichDangHachToan->time_start_as_date->copy()->addDay()->timestamp,
      'time_end'             => $lichDangHachToan->time_end_as_date->copy()->addDay()->timestamp,
      'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
      'request_amount_debit' => $phiQuaHan,
      'success_amount_debit' => 0,
      'other_data'           => json_encode($otherData),
      'description'          => $lichDangHachToan->collectDebtSchedule,
      'is_settlement'        => $lichDangHachToan->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangHachToan->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangHachToan->cycle_number,
      'master_id'            => $lichDangHachToan->master_id,
    ];

		mylog(['param thu phi qua han cutoff' => $scheduleParam]);
		
    $lichThuPhiQuaHan = CollectDebtSchedule::forceCreate($scheduleParam);

		if (!$lichThuPhiQuaHan) {
			mylog(['[LOI KHONG TAO DUOC LICH THU PHI QUA HAN]' => $lichThuPhiQuaHan]);
			throw new Exception('[LOI KHONG TAO DUOC LICH THU PHI QUA HAN]');
		}

		return $lichThuPhiQuaHan;
  }
} // End class