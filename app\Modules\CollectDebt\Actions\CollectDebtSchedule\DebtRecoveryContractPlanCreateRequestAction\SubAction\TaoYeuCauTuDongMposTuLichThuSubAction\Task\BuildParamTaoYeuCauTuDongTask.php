<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task;

use App\Lib\Helper;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\ValueObjects\CutOffTimeValueObject;

class BuildParamTaoYeuCauTuDongTask
{
	public function run(Collection $listLichThuCanTaoYc)
	{
		$firstPlan = $listLichThuCanTaoYc->first()->replicate(); // Clone model đầu tiên

    $listPaymentGuide = json_decode($firstPlan->payment_guide, true);
		$paymentGuide = collect($listPaymentGuide)->where('payment_method_code', 'MPOS')->first();

    $returnData = [
      'type'                        => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
      'profile_id'                  => $firstPlan->profile_id,
      'contract_code'               => $firstPlan->contract_code,
      'plan_ids'                    => $listLichThuCanTaoYc->implode('id', ','),
      'payment_method_code'         => $paymentGuide['payment_method_code'] ?? '',
      'payment_channel_code'        => $paymentGuide['payment_channel_code'] ?? '',
      'payment_account_id'          => $paymentGuide['payment_account_id'] ?? '',
      'payment_account_holder_name' => $paymentGuide['other_data']['payment_account_holder_name'] ?? '',
      'payment_account_bank_code'   => $paymentGuide['other_data']['payment_account_bank_code'] ?? '',
      'payment_account_bank_branch' => $paymentGuide['other_data']['payment_account_bank_branch'] ?? '',
      'partner_request_id'          => '',
      'partner_transaction_id'      => '',
      'time_begin'                  => $firstPlan->rundate_as_date->copy()->timestamp,
      'time_expired'                => (new CutOffTimeValueObject($firstPlan->rundate_as_date->copy(), $firstPlan->partner_code ?? 'TNEX'))->getCutOffTimeTimestamp(),
      'is_payment'                  => CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT,
      'version'                     => CollectDebtEnum::REQUEST_VERSION_4,
      'status'                      => CollectDebtEnum::REQUEST_STT_MOI_TAO,
      'status_payment'              => CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI,
      'status_recored'              => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO,
      'currency'                    => 'VND',
      // Tổng số tiền yêu cầu thanh toán của các lịch
      'amount_request'              => $listLichThuCanTaoYc->sum('request_amount_debit'), 
      'amount_payment'              => 0, // Số tiền đã thu hồi được
      'amount_receiver'             => 0,
      'fee'                         => 0,
      'plan_data'                   => Helper::getPlanCompact($listLichThuCanTaoYc),
      'description'                 => '',
      'created_by'                  => Helper::getCronJobUser(),
      'time_created'                => now()->timestamp,
      'create_from'                 => CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG
    ];

    return $returnData;
	}
} // End class
