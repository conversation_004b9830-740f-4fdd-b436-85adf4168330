<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateAction;

use Exception;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class DebtRecoveryContractEventCreateAction
{
    public function run($request)
    {
        $params = $this->mapData($request);

        $debtRecoveryContractEvent = CollectDebtContractEvent::insertGetId($params);

        throw_if(!$debtRecoveryContractEvent, new Exception('Không thể tạo được bản ghi'));

        return [
            'id' => $debtRecoveryContractEvent
        ];
    }


    protected function mapData($request)
    {
        $dataResponse = [];

        if (isset($request['category_care_code']) && !empty($request['category_care_code'])) {
            $dataResponse['category_care_code'] = $request['category_care_code'];
        }

        if (isset($request['service_care_code']) && !empty($request['service_care_code'])) {
            $dataResponse['service_care_code'] = $request['service_care_code'];
        }

        if (isset($request['data']) && !empty($request['data'])) {
            $dataResponse['data'] = json_encode($request['data'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['description']) && !empty($request['description'])) {
            $dataResponse['description'] = $request['description'];
        }

        if (isset($request['other_data']) && !empty($request['other_data'])) {
            $dataResponse['other_data'] = json_encode($request['other_data'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['time_start']) && !empty($request['time_start'])) {
            $dataResponse['time_start'] = $request['time_start'];
        }else{
            $dataResponse['time_start'] = now()->startOfDay()->timestamp;
        }

        if (isset($request['number']) && !empty($request['number'])) {
            $dataResponse['number'] = $request['number'];
        }

        if (isset($request['time_expired']) && !empty($request['time_expired'])) {
            $dataResponse['time_expired'] = $request['time_expired'];
        }else{
            $dataResponse['time_expired'] = now()->endOfDay()->timestamp;
        }

        if(isset($request['contract_code']) && !empty($request['contract_code'])){
            $dataResponse['contract_code'] = $request['contract_code'];
        }

        $dataResponse['status'] = config('collect_debt_email_remind_config.debt_contract_event_status.new');
        $dataResponse['time_created'] = time();


        return $dataResponse;
    }
}
