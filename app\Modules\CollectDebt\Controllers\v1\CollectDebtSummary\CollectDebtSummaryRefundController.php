<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\DebtRecoverySummaryCheckRefundAction;

class CollectDebtSummaryRefundController extends Controller
{
	public function DebtRecoverySummaryCheckRefund(Request $request)
	{
		try {
			$refundInfo = app(DebtRecoverySummaryCheckRefundAction::class)->initCheckRefund($request);
			return $this->successResponse($refundInfo, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
