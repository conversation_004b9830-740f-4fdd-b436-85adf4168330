<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtContractPriority;

use DB;
use App\Lib\Helper;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtContractPriority\DebtRecoveryContractPriorityCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtContractPriority\DebtRecoveryContractPriorityDeleteRequest;
use App\Modules\CollectDebt\Actions\DebtRecoveryContractPriorityCreateAction\DebtRecoveryContractPriorityCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtContractPriority\DebtRecoveryContractPriorityDeleteAction\DebtRecoveryContractPriorityDeleteAction;

class CollectDebtContractPriorityController extends Controller
{
	public function DebtRecoveryContractPriorityCreate(DebtRecoveryContractPriorityCreateRequest $request)
	{
		DB::beginTransaction();
		try {
			$result = app(DebtRecoveryContractPriorityCreateAction::class)->run($request);
			DB::commit();
			return $this->successResponse(['id' => $result], $request);
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryContractPriorityDelete(DebtRecoveryContractPriorityDeleteRequest $request)
	{
		DB::beginTransaction();
		try {
			$result = app(DebtRecoveryContractPriorityDeleteAction::class)->run($request);
			DB::commit();
			return $this->successResponse(['id' => $result], $request);
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
