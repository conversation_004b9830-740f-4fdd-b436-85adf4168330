<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment;

use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DebtRecoveryRequestAdjustmentUpdateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:1'],
      'data.contract_code' => ['required', 'string', 'max:50'],
      'data.type' => ['required', 'numeric', 'min:0'],
      'data.reference_id' => ['required', 'numeric', 'min:1'], // type=1: th<PERSON> lấy kh<PERSON>a ch<PERSON>h của bảng partner | type=2: th<PERSON> l<PERSON>y kh<PERSON>a của bảng kh<PERSON>c (cập nhật sau)
      'data.reference_data' => ['required', 'string', 'json'], // thông tin bản ghi
      'data.amount_receiver_on_partner' => ['required', 'numeric', 'min:0'], // số tiền nhận về thực tế từ partner
      'data.amount_before' => ['required', 'numeric', 'min:0'], // số tiền trên partner trước khi điều chỉnh (trước khi MPOS thực sự bắn kết quả về)
      'data.amount_after' => ['required', 'numeric', 'min:0'], // số tiền thực tế sau khi điều chỉnh
      'data.description' => ['nullable', 'string', 'max:255'],
      'data.other_data' => ['nullable'],
      'data.status' => ['required', 'numeric'],
      'data.time_updated' => ['required', 'numeric'],
      'data.updated_by' => ['required', 'string', 'max:255'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['time_updated'] = time();
    $params['data']['updated_by'] = StandardizedDataFilter::getUserAdminStructCompact($params['data']['updated_by']);
    $this->merge($params);
  }
}
