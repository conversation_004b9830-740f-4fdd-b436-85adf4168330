<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class DanhDauEventDongBoCuVeTrangThaiCuoiTask
{
	public function run(string $contractCode, int $currentEventId)
	{
		$huyCacEventCu = CollectDebtContractEvent::query()
			->where('contract_code', $contractCode)
			->where('category_care_code', 'CONTRACT')
			->where('service_care_code', 'SYNC')
			->where('id', '<', $currentEventId)
			->where('status', '!=', CollectDebtEnum::EVENT_STT_DA_TAO_YC_THANH_CONG)
			->update([
				'status' => CollectDebtEnum::EVENT_STT_DA_TU_CHOI,
				'time_expired' => now()->timestamp
			]);

		mylog(['So luong ban ghi dong bo cu da huy' => $huyCacEventCu]);
		return $huyCacEventCu;
	}
}
