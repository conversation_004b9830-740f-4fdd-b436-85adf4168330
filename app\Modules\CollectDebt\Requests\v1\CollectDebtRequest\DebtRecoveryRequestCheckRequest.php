<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;

class DebtRecoveryRequestCheckRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * 
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.request_id' => ['required', 'numeric', 'min:1'],
      'data.users_admin_id' => ['required', 'string', 'max:255']
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['users_admin_id'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['users_admin_id']);
    $this->merge($params);
  }
} // End class
