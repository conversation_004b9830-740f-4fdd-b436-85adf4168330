<?php

namespace App\Modules\MobileApp\Controllers\Contract;

use App\Lib\Helper;
use Illuminate\Http\Request;
use  App\Modules\MobileApp\Controllers\Controller;
use App\Modules\MobileApp\Actions\MobileAppContractForListAction\MobileAppContractForListAction;
use App\Modules\MobileApp\Actions\MobileAppContractOverviewAction\MobileAppContractOverviewAction;

class MobileAppContractController extends Controller
{
	public function MobileAppContractOverview(Request $request)
	{
		try {
			$result = app(MobileAppContractOverviewAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function MobileAppContractForList(Request $request)
	{
		try {
			$result = app(MobileAppContractForListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class