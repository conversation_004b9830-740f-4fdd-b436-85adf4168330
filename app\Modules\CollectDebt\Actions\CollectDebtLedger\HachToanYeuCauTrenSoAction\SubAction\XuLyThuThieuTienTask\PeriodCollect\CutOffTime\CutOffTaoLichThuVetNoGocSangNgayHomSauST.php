<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\CutOffTime;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;

class CutOffTaoLichThuVetNoGocSangNgayHomSauST
{  
  /**
   * Tạo lịch thu vét nợ gốc sang ngày hôm sau
   *
   * @param CollectDebtSchedule $lichDangHachToan [explicite description]
   * @param float $soTienNoGocPhaiThuTiep [explicite description]
   * @param $isfee $isfee [explicite description]
   *
   * @return void
   */
  public function run(CollectDebtSchedule $lichDangHachToan, float $soTienDaTrichThanhCong = 0, float $soTienConPhaiThuTiep = 0)
  {
    $debitDegin = $lichDangHachToan->debit_begin - $soTienDaTrichThanhCong;
    $otherData = json_encode([
      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'request_created_channel' => '',
        ],
        'note' => 'Thu vet no goc sang hom sau'
      ]
    ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

    $scheduleParam = [
      'profile_id'           => $lichDangHachToan->profile_id,
      'contract_code'        => $lichDangHachToan->contract_code,
      'contract_type'        => $lichDangHachToan->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
      'debit_begin'          => $debitDegin,
      'debit_end'            => $lichDangHachToan->debit_end,
      'rundate'              => $lichDangHachToan->rundate_as_date->copy()->addDay()->format('Ymd'),
      'time_start'           => $lichDangHachToan->time_start_as_date->copy()->addDay()->startOfDay()->timestamp,
      'time_end'             => $lichDangHachToan->time_end_as_date->copy()->addDay()->endOfDay()->timestamp,
      'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
      'request_amount_debit' => $soTienConPhaiThuTiep,
      'success_amount_debit' => 0,
      'other_data'           => $otherData,
      'description'          => $lichDangHachToan->collectDebtSchedule,
      'is_settlement'        => $lichDangHachToan->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangHachToan->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangHachToan->cycle_number,
      'master_id'            => $lichDangHachToan->master_id,
    ];

		mylog(['[PARAM TAO LICH THU GOC SANG HOM SAU]' => $scheduleParam]);

    $lichThuVetNoGoc = CollectDebtSchedule::forceCreate($scheduleParam);
		if (!$lichThuVetNoGoc) {
			mylog(['[LOI]' => 'Loi tao lich thu vet no goc']);
			throw new Exception('Loi tao lich thu vet no goc');
		}

		return $lichThuVetNoGoc;
  }
}
