<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\FeeHandler;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class XuLySinhPhiChamKyFH
{
  public function run(
    CollectDebtSchedule $lichDangHachToan, 
    CollectDebtSchedule $lichThuPhiChamKyVuaTao, 
    float $phiChamKy = 0
  ) {
    $otherData = [
      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_CHAM_KY,
          'fee_config' => $lichDangHachToan->getCauHinhPhiTheoLoai(CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY),
          'request_created_channel' => '',
        ],
        'note' => 'Sinh phí chậm kỳ'
      ],
    ];

    $lichThuPhiChamKyVuaTao->other_data = json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_UNICODE);
    $lichThuPhiChamKyVuaTao->save();
    return $lichThuPhiChamKyVuaTao;
  }
} // End class