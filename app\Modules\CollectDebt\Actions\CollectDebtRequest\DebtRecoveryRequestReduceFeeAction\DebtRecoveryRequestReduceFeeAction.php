<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestReduceFeeAction;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Exception;

class DebtRecoveryRequestReduceFeeAction
{
	public function run(Request $request)
	{
		$contractCode = $request->json('data.filter.contract_code');
		throw_if(empty($contractCode), new Exception('Mã HĐ là bắt buộc'));

		$collectDebtRequestReduceFee = CollectDebtRequest::query()
			->where('contract_code', $contractCode)
			->whereHas('collectDebtRequestActions', function ($query) {
				return $query->where('type', CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI);
			})
			->orderBy('id', 'DESC')
			->get();


		return $collectDebtRequestReduceFee->transform(function (CollectDebtRequest $collectDebRequest) {
			$can[] = CollectDebtEnum::CAN_VIEW_DETAIL_REQUEST;

			if ($collectDebRequest->isTrichTayGiamPhiVaChuaDuyetBuoc1() && $collectDebRequest->isTrichNgay() != 'YES') {
				$can[] = CollectDebtEnum::CAN_APPROVE_REDUCE_FEE_STEP_1;
				$can[] = CollectDebtEnum::CAN_CANCEL_MANUAL_REQUEST;
			}

			if ($collectDebRequest->isTrichTayGiamPhiVaChuaDuyetBuoc2() && $collectDebRequest->isTrichNgay() != 'YES') {
				$can[] = CollectDebtEnum::CAN_APPROVE_REDUCE_FEE_STEP_2;
				$can[] = CollectDebtEnum::CAN_CANCEL_MANUAL_REQUEST;
			}

			if ($collectDebRequest->isRecorded()) {
				$can[] = CollectDebtEnum::CAN_VIEW_REQUEST_ON_LEDGER;
			}


			$collectDebRequest->can = $can;

			$collectDebRequest->total_amount_paid = 0;
			$collectDebRequest->total_debt_excess = 0;


			$collectDebRequest->is_trich_ngay = false;
			if ($collectDebRequest->isManualDebt() && $collectDebRequest->isTrichNgay() == 'YES') {
				$collectDebRequest->is_trich_ngay = true;
			}

			return $collectDebRequest;
		});
	}
} // End class