<?php

namespace App\Modules\CollectDebtGateway\Controllers;

use App\Modules\CollectDebtGateway\Requests\MposCollectDebtGateway\CancelDebtRequest;
use App\Modules\CollectDebtGateway\Requests\MposCollectDebtGateway\CheckBalanceRequest;
use App\Modules\CollectDebtGateway\Requests\MposCollectDebtGateway\CheckDebtRequest;
use App\Modules\CollectDebtGateway\Requests\MposCollectDebtGateway\SendDebtRequest;
use App\Modules\CollectDebtGateway\Service\MposCollectDebtGatewayService;

class MposCollectDebtGatewayController extends Controller
{
    protected MposCollectDebtGatewayService $mposCollectDebtGatewayService;

    public function __construct(MposCollectDebtGatewayService $mposCollectDebtGatewayService)
    {
        $this->mposCollectDebtGatewayService = $mposCollectDebtGatewayService;
    }

    public function sendDebt(SendDebtRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->mposCollectDebtGatewayService->sendDebt($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

    public function cancelDebt(CancelDebtRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->mposCollectDebtGatewayService->cancelDebt($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

    public function checkDebt(CheckDebtRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->mposCollectDebtGatewayService->checkDebt($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }

    public function checkBalance(CheckBalanceRequest $request)
    {
        try {
            $data = $request->json('data');
            $result = $this->mposCollectDebtGatewayService->checkBalance($data);
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), $th->getMessage());
        }
    }
}
