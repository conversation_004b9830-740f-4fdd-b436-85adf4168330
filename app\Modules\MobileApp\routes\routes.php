<?php

use Illuminate\Support\Facades\Route;

$namespace = 'App\Modules\MobileApp\Controllers';
Route::group(['middleware' => [], 'namespace' => $namespace], function () {
	Route::any('MobileAppContractOverview', [
		'uses' => 'Contract\MobileAppContractController@MobileAppContractOverview',
		'as' => 'MobileAppContractOverviewAction'
	]);

	Route::any('MobileAppContractForList', [
		'uses' => 'Contract\MobileAppContractController@MobileAppContractForList',
		'as' => 'MobileAppContractForListAction'
	]);
});
