<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiSendAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;

class DigitalNotiSendAction
{
	private array $__listNotiDaXuLy = [];

	private array $__exceptIds = [];

	public function init()
	{
		for ($i = 1; $i <= 40; $i++) {
			try {
				$digitalNoti = $this->run();

				if ($digitalNoti == 'EMPTY') {
					$this->__listNotiDaXuLy[] = 'Khong co yc nao can xu ly noti';
					break;
				}

				if ($digitalNoti && optional($digitalNoti)->id) {
					$this->__listNotiDaXuLy[] = $digitalNoti->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi dong bo giao dich' => Helper::traceError($th)]);
				@TelegramAlert::sendAccouting(Helper::traceError($th));
				continue;
				// throw $th; // sau phai bo dong nay
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $this->__listNotiDaXuLy;
	}

	public function run()
	{
		$digitalNoti = CollectDebtDigitalNoti::query()
			->where('status', CollectDebtDigitalNoti::STT_DA_BUILD_PARAM);

		if (!empty($this->__exceptIds)) {
			$digitalNoti = $digitalNoti->whereNotIn('id', $this->__exceptIds);
		}

		$digitalNoti = $digitalNoti->first();

		if (!$digitalNoti) {
			return 'EMPTY';
		}

		$this->__exceptIds[] = $digitalNoti->id;

		$wasProcessing = CollectDebtDigitalNoti::query()
																					 ->where('id', $digitalNoti->id)
																					 ->where('status', CollectDebtDigitalNoti::STT_DA_BUILD_PARAM)
																					 ->update(['status' => CollectDebtDigitalNoti::STT_DANG_GUI_NOTI]);
		if (!$wasProcessing) {
			throw new Exception('Lỗi không update noti lên thành đang xử lý');
		}

		try {
			$r = $this->pushNoti($digitalNoti);

			if (isset($r['error_code']) && $r['error_code'] == 1010) {
				Cache::store('database')->forget('mposDigitalToken');
				
				CollectDebtDigitalNoti::query()->where('id', $digitalNoti->id)
																			 ->where('status', CollectDebtDigitalNoti::STT_DANG_GUI_NOTI)
																		   ->update(['status' => CollectDebtDigitalNoti::STT_DA_BUILD_PARAM]);
			}

			$digitalNoti->time_sent = now()->timestamp;
			$digitalNoti->digital_response = json_encode($r ?? '');
			$digitalNoti->status = CollectDebtDigitalNoti::STT_GUI_NOTI_THAT_BAI;

			if (!empty($r['status'])) {
				$digitalNoti->status = CollectDebtDigitalNoti::STT_DA_GUI_NOTI;
			}

			$digitalNoti->save();

			return $digitalNoti;
		} catch (\Throwable $th) {
			throw $th;
		}
	}

	public function pushNoti(CollectDebtDigitalNoti $digitalNoti)
	{
		$p = json_decode($digitalNoti->digital_request, true);

		Log::info("Push noti param", $p);
		$response = Http::timeout(10)
			->withHeaders([
				'Content-Type' => 'application/json',
				'Authorization' => 'Bearer ' . $this->getDigitalToken()
			])
			->retry(1, 300)
			->post(env('DIGITAL_GATEWAY_URL') . '/api/request', $p);

		$result = $response->json();
		Log::info("Push noti result", $result ?? []);
		return $result;
	}

	public function getDigitalToken(): string
	{
		if (Cache::store('database')->has('mposDigitalToken')) {
			return Cache::store('database')->get('mposDigitalToken');
		}

		$checksumString = sprintf(
			'%sAppsDigitalmakeToken%s%s',
			json_encode(['deviceIdentifier' => 'TrichNo']),
			time(),
			env('DIGITAL_SECRET_KEY')
		);

		$response = Http::timeout(5)
			->withHeaders([
				'Content-Type' => 'application/json'
			])
			->retry(1, 300)
			->post(env('DIGITAL_GATEWAY_URL') . '/api/request', [
				'partner_code' => 'AppsDigital',
				'fn' => 'makeToken',
				'params' => ['deviceIdentifier' => 'TrichNo'],
				'time_request' => time(),
				'checksum' => md5($checksumString)
			]);

		$result = $response->json();

		if (empty($result['data']['token'])) {
			throw new Exception('Lỗi không lấy được token digital');
		}

		Cache::store('database')->put('mposDigitalToken', $result['data']['token'], 3000);
		return $result['data']['token'];
	}
} // End class