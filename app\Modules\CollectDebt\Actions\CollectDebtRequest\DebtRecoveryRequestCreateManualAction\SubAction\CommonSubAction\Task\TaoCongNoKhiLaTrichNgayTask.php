<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;

class TaoCongNoKhiLaTrichNgayTask
{
  public function run(CollectDebtRequest $collectDebtRequest): CollectDebtPartner
  {
    $collectDebtShare = CollectDebtShare::where('contract_code', $collectDebtRequest->contract_code)->first();

    $param = [
			'contract_code'					 => $collectDebtShare->contract_code,
      'payment_channel_code'   => $collectDebtRequest->payment_channel_code,
      'payment_method_code'    => $collectDebtRequest->payment_method_code,
      'payment_account_id'     => $collectDebtRequest->contract_code,
      'partner_request_id'     => $collectDebtRequest->partner_request_id,
      'partner_transaction_id' => $collectDebtRequest->partner_transaction_id,
      'amount_payment'         => $collectDebtRequest->amount_receiver, // số tiền mang đi thanh toán
      'amount_receiver'        => $collectDebtRequest->amount_receiver, // số tiền nhận đc
      'request_exists'         => CollectDebtEnum::PARTNER_REQUEST_DA_CO_YC_THANH_TOAN,
      'description'            => 'Công nợ cho yêu cầu trích ngay',
      'status'                 => CollectDebtEnum::PARTNER_STT_DA_XU_LY,
			
      'created_by'             => $collectDebtRequest->created_by,
      'time_created'           => $collectDebtRequest->time_created,

      'time_processing'        => time(),
			'processing_by'					 => $collectDebtRequest->created_by,

      'time_complated'         => time(),
			'complated_by'					 => $collectDebtRequest->created_by,

      'updated_by'             => $collectDebtRequest->updated_by,
      'time_updated'           => $collectDebtRequest->time_updated,

			'time_created_request'	 => time(),
			'created_request_by'		 => $collectDebtRequest->created_by,

      'other_data'             => json_encode([
        [
          'type' => 'CONTRACT',
          'time_modified' => time(),
          'data' => [
            'profile_id'          => $collectDebtShare->profile_id,
            'contract_code'       => $collectDebtShare->contract_code,
            'contract_time_start' => $collectDebtShare->contract_time_start,
            'contract_type'       => $collectDebtShare->getContractTypev1(),
            'contract_cycle'      => $collectDebtShare->contract_cycle,
            'contract_intervals'  => $collectDebtShare->contract_intervals,
            'amount'              => $collectDebtShare->amount,
            'contract_time_end'   => $collectDebtShare->contract_time_end,
            'other_data'          => '{}',
            'description'         => 'Hợp đồng v4',
            'id'                  => $collectDebtShare->getContractId()
          ],
          'note' => 'Thông tin HĐ'
        ],

        [
          'type' => 'REQUEST',
          'time_modified' => time(),
          'data' => [
            StandardizedDataFilter::getRequestCompactAttribute($collectDebtRequest),
          ],
          'note' => 'Thông tin HĐ'
        ]
      ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
    ];

    return CollectDebtPartner::forceCreate($param);
  }
} // End class