<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySyncHandleExcessAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Exception;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummarySyncHandleExcessRequest;

class DebtRecoverySummarySyncHandleExcessAction
{
	public function run(DebtRecoverySummarySyncHandleExcessRequest $request)
	{
		$collectDebtSummary = CollectDebtSummary::query()->firstWhere([
			'contract_code' => $request->json('data.contract_code')
		]);

		if (!$collectDebtSummary) {
			throw new Exception('HĐ không tồn tại');
		}

		$profileId = $collectDebtSummary->getProfileId();
		$profileIdFromRequest = trim($request->json('data.profile_id', ''));
		if ($profileId != $profileIdFromRequest) {
			throw new Exception('Lỗi ProfileId không đúng với HĐ');
		}

		if ($collectDebtSummary->total_amount_excess_revenue <= 0) {
			throw new Exception('HĐ không có tiền thu thừa');
		}

		if (!$collectDebtSummary->isHopDongDaTatToan()) {
			throw new Exception('HĐ cho tiền phải là HĐ đã tất toán');
		}

		$listHopDongNhanTien = CollectDebtSummary::query()
																						 ->whereIn('contract_code', $request->json('data.list_contract_receive_money'))
																						 ->get();

		if ($listHopDongNhanTien->isEmpty()) {
			throw new Exception('Lỗi không tìm thấy các HĐ được nhận tiền');
		}																						

		$isOk = $listHopDongNhanTien->every(function (CollectDebtSummary $smr) use ($profileIdFromRequest) {
			$profileIdFromContract = $smr->getProfileId();
			return $profileIdFromContract == $profileIdFromRequest && !$smr->isHopDongDaTatToan();
		});
		
		if (!$isOk) {
			throw new Exception('Không thể cho tiền cho các HĐ sai profileId hoặc đã được tất toán');
		}

		// Cập nhật HĐ nhận tiền vào other_data
		$otherData = $collectDebtSummary->getSummaryOtherData();
		$index = $collectDebtSummary->getSummaryOtherDataIndex('CHO_TIEN_THU_THUA');
		
		// Nếu là duyệt cho tiền
		if ($request->json('data.cashin_type') == 'APPROVED') {
			$paramUpdateChoTien = [
				'type' => 'CHO_TIEN_THU_THUA',
				'note' => 'Danh sach hop dong nhan tien',
				'data' => $request->json('data.list_contract_receive_money'),
				'time_modified' => now()->timestamp
			];
			
			if ($index == -1) {
				$otherData[] = $paramUpdateChoTien;
			}
	
			if ($index > -1) {
				$otherData[$index] = $paramUpdateChoTien;
			}
		}
		
		// Nếu là hủy duyệt cho tiền
		if ($request->json('data.cashin_type') == 'DENIED') {
			if ($index > -1) {
				unset($otherData[$index]);
			}
		}

		$r = CollectDebtSummary::query()->where('contract_code', $collectDebtSummary->contract_code)
																	  ->update([
																			'other_data' => json_encode($otherData),
																			'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO,
																			'time_updated' => now()->timestamp
																		]);
		if (!$r) {
			throw new Exception('Lỗi không cập nhật được tổng hợp');
		}

		return $collectDebtSummary;	
	}
} // End class