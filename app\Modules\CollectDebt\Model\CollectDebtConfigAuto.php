<?php

namespace App\Modules\CollectDebt\Model;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Enums\CacheEnum;

class CollectDebtConfigAuto extends Model
{

  protected $table = 'debt_recovery_request_config_auto';
  public $timestamps = false;
  protected $appends = ['time_expired_as_vn_date'];
  protected $guarded = [];
  
  public static function isPauseContractJob(string $contractCode=''): bool  {
    $isPaused = self::where('contract_code', $contractCode)
                    ->where('time_expired', '>', time())
                    ->first();

    return !empty($isPaused);
  }

  public function scopeWherePaused($query) {
    return $query->where('time_expired', '>', time());
  }

  public function getTimeExpiredAsVnDateAttribute($value) {
    return Carbon::createFromTimestamp($this->time_expired)->format('d/m/Y H:i');
  }

	public static function getHopDongDangBiDungJob(): array {
		$listHopDongDungJob = self::where('time_expired', '>', now()->timestamp)
															->pluck('contract_code')
															->toArray();
		return $listHopDongDungJob;
	}

	public static function getHopDongDungJobCache(): array {

		if (!Cache::has(CacheEnum::LIST_HOP_DONG_DUNG_JOB)) {
			$listPauseJob = self::getHopDongDangBiDungJob();
			Cache::set(CacheEnum::LIST_HOP_DONG_DUNG_JOB, $listPauseJob, now()->addMinutes(20));
			return $listPauseJob;
		}
		return Cache::get(CacheEnum::LIST_HOP_DONG_DUNG_JOB);
	}
} // End class
