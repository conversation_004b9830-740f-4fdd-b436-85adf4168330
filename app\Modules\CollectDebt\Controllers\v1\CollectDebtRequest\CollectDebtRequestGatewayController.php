<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use App\Lib\Helper;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Http\Request;

class CollectDebtRequestGatewayController extends Controller
{
	public function getByParam(Request $request)
	{
		try {
			$collectDebtRequest = CollectDebtRequest::where($request->json('data.search'))->first();
			$paramsCheckDebt = app(CheckRequestViaMposSubAction::class)->buildParams($collectDebtRequest, $request);
			
			return $this->successResponse($paramsCheckDebt, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
