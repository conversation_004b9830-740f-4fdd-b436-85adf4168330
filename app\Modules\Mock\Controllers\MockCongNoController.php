<?php

namespace App\Modules\Mock\Controllers;

use App\Lib\Helper;
use Exception;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class MockCongNoController extends \App\Http\Controllers\Controller
{
  public function index(Request $request)
  {
    $contractCode = collect([]);

    return view('partner.create', [
      'randomVA' => str_pad(mt_rand(0, 9999999), 8, '0', STR_PAD_LEFT),
      'randomMaViVA' => str_pad(mt_rand(0, 999999999), 12, '0', STR_PAD_LEFT),
      'randomMPOS' => str_pad(mt_rand(0, 9999999), 8, '0', STR_PAD_LEFT),
      'contractCode' => $contractCode,
    ]);
  }

  public function store(Request $request)
  {
    $partners = $request->input('parner');
    $partners = array_map(function ($item) {
      return array_map('trim', $item);
    }, $partners);

    $partners = collect($partners)->filter(function ($item) {
      return !empty($item['contract_code']) || !empty($item['partner_request_id']) || !empty($item['payment_transaction_id']);
    })->values()->toArray();
    
    
    throw_if(empty($partners), new Exception('Khong co cong no phu hop nao. Ban can kiem tra lai'));

    foreach ($partners as $partner) {
      if ($partner['payment_method_code'] == 'VIRTUALACCOUNT') {
        $collectDebtShare = CollectDebtShare::where('payment_guide', 'LIKE', '%'.$partner['payment_account_id'].'%')->first();
        throw_if(!$collectDebtShare, new Exception('Khong tim thay thong tin chi dan'));

        CollectDebtPartner::forceCreate([
					'contract_code' => $collectDebtShare->contract_code,
          'payment_method_code' => $partner['payment_method_code'],
          'amount_receiver' => $partner['amount_receiver'],
          'payment_account_id' => $partner['payment_account_id'],
          'partner_transaction_id' => $partner['payment_transaction_id'],
          'description' => sprintf('MOCK FAKE - %s', $partner['payment_method_code']),
          'created_by' => Helper::getCronJobUser(),
          'other_data' => json_encode([
            [
              'type' => 'CONTRACT',
              'time_modified' => time(),
              'data' => [
                'profile_id'          => $collectDebtShare->profile_id,
                'contract_code'       => $collectDebtShare->contract_code,
                'contract_time_start' => $collectDebtShare->contract_time_start,
                'contract_type'       => $collectDebtShare->getContractTypev1(),
                'contract_cycle'      => $collectDebtShare->contract_cycle,
                'contract_intervals'  => $collectDebtShare->contract_intervals,
                'amount'              => $collectDebtShare->amount,
                'contract_time_end'   => $collectDebtShare->contract_time_end,
                'other_data'          => '{}',
                'description'         => 'Hợp đồng v4',
                'id'                  => $collectDebtShare->getContractId()
              ],
              'note' => 'Thông tin HĐ'
            ]
          ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
        ]);
      } // End VA

      if ($partner['payment_method_code'] == 'MPOS') {
        $collectDebtRequest = CollectDebtRequest::where('partner_request_id', $partner['partner_request_id'])->first();
        $collectDebtShare = CollectDebtShare::where('contract_code', $collectDebtRequest->contract_code)->first();
        
        throw_if(!$collectDebtRequest, new Exception('Khong co thong tin yeu cau'));
        throw_if(
          $partner['amount_receiver'] > $collectDebtRequest->amount_request, 
          new Exception(sprintf('Số tiền trích MPOS không được vượt quá `%s`', Helper::priceFormat($collectDebtRequest->amount_request, 'đ')))
        );
        CollectDebtPartner::forceCreate([
					'contract_code' => $collectDebtShare->contract_code,
          'partner_request_id' => $partner['partner_request_id'],
          'payment_method_code' => $partner['payment_method_code'],
          'amount_payment' => $partner['amount_receiver'],
          'amount_receiver' => $partner['amount_receiver'],
          'payment_account_id' => $collectDebtShare->contract_code,
          'partner_transaction_id' => $partner['partner_transaction_id'],
          'request_exists' => 1,
          'description' => 'MOCK FAKE',
          'time_created' => time(),
          'created_by' => Helper::getCronJobUser(),
          'other_data' => json_encode([
            [
              'type' => 'CONTRACT',
              'time_modified' => time(),
              'data' => [
                'profile_id'          => $collectDebtShare->profile_id,
                'contract_code'       => $collectDebtShare->contract_code,
                'contract_time_start' => $collectDebtShare->contract_time_start,
                'contract_type'       => $collectDebtShare->getContractTypev1(),
                'contract_cycle'      => $collectDebtShare->contract_cycle,
                'contract_intervals'  => $collectDebtShare->contract_intervals,
                'amount'              => $collectDebtShare->amount,
                'contract_time_end'   => $collectDebtShare->contract_time_end,
                'other_data'          => '{}',
                'description'         => 'Hợp đồng v4',
                'id'                  => $collectDebtShare->getContractId()
              ],
              'note' => 'Thông tin HĐ'
            ],

            [
              'type' => 'REQUEST',
              'time_modified' => time(),
              'data' => [
                $collectDebtRequest->getAttributes()
              ],
              'note' => 'Thông tin HĐ'
            ]
          ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
        ]);
      } // End VA
    } // End foreach

    return redirect()->to(config('app.url') . '/mock/congno');
  }
} // End class
