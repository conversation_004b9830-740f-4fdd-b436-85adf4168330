<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class KiemTraLenhTrichNgayVaRaCanhBaoSA
{
	public function run(string $contractCode = ''): string
	{
		$collectDebtRequests = CollectDebtRequest::query()
																						->where('contract_code', $contractCode)
																						->where('payment_method_code', 'MPOS')
																						->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
																						->whereNull('time_receivered')
																						->where('status', '!=', CollectDebtEnum::REQUEST_STT_TU_CHOI)
																						->orderBy('id', 'DESC')
																						->get();
		
		if ($collectDebtRequests->isEmpty()) {
			return '';
		}

		$yeuCauTrichNgay = $collectDebtRequests->first(function (CollectDebtRequest $rq) {
			return $rq->isRutTienNhanh();
		});

		mylog(['ID yeu cau TRICH NGAY MPOS' => @optional($yeuCauTrichNgay)->id]);

		if (!$yeuCauTrichNgay) {
			return '';
		}

		$warningMessage = sprintf(
			'Bạn đang có lệnh <b>TRÍCH NGAY: <i>`%s`</i></b> (%s - Hết hạn lúc: %s) và <b>chưa được</b> đối tác MPOS báo kết quả trích mặc dù lệnh này đã được hạch toán trước. Việc bạn tạo đề xuất giảm phí khi mà <b>MPOS không thực sự trích đủ</b> <b><i>%s</i></b> có thể tiềm ẩn rủi ro về phí (bạn cho/tặng/miễn phí chậm kỳ, quá hạn cho MC). <br><br> Hệ thống <b>TỪ CHỐI</b> thao tác tất toán giảm phí của bạn cho đến khi nhận được kết quả trích từ MPOS!!!',
			
			$yeuCauTrichNgay->partner_request_id, // lenh trich
			
			Helper::priceFormat($yeuCauTrichNgay->amount_receiver), // so tien

			$yeuCauTrichNgay->time_expired_as_date->format('H:i d/m/Y'), // het han luc

			Helper::priceFormat($yeuCauTrichNgay->amount_receiver)
		);

		return $warningMessage;
	}
} // End class