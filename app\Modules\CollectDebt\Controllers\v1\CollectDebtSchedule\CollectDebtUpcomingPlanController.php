<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule;

use DB;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\CollectDebtScheduleResourceCollection;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\DebtRecoveryHandleSapToiHanAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\DebtRecoveryHandleFirstUpcomingPlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleMailDenKyDungNgayAction\DebtRecoveryHandleMailDenKyDungNgayAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleDenHanTatToanDungNgayAction\DebtRecoveryHandleDenHanTatToanDungNgayAction;

class CollectDebtUpcomingPlanController extends Controller
{
	public function DebtRecoveryHandleFirstUpcomingPlan(Request $request)
	{
		DB::beginTransaction();
		try {
			$createEventResult = app(DebtRecoveryHandleFirstUpcomingPlanAction::class)->run($request);
			DB::commit();
			return $this->successResponse($createEventResult, $request);
		}catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryHandleSapToiHan(Request $request)
	{
		try {
			$createEventResult = app(DebtRecoveryHandleSapToiHanAction::class)->run();
			return $this->successResponse($createEventResult , $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryHandleMailDenKyDungNgay(Request $request)
	{
		DB::beginTransaction();
		try {
			$createEventResult = app(DebtRecoveryHandleMailDenKyDungNgayAction::class)->run($request);
			DB::commit();
			return $this->successResponse($createEventResult, $request);
		}catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryHandleDenHanTatToanDungNgay(Request $request)
	{
		try {
			$createEventResult = app(DebtRecoveryHandleDenHanTatToanDungNgayAction::class)->run();
			return $this->successResponse($createEventResult , $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
