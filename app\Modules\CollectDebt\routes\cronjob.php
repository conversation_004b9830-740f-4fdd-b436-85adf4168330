<?php

use Illuminate\Support\Facades\Route; 
use App\Http\Middleware\EnableSettingMiddleware;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide\CollectDebtGuideController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger\CollectDebtLedgerController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CanhBaoTrichNgayController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary\CollectDebtSummaryController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger\CollectDebtLedgerReportController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule\CollectDebtUpcomingPlanController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary\CollectDebtSummaryRefundController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtWaitProcess\CollectDebtWaitProcessController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtContractEvent\CollectDebtContractEventController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowController;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\CheckRequestViaRedisAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\CutOffTimeByRedisAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction\SendNotifyViaChannelsAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoverySendBaoCaoTrichNoTnexAction\DebtRecoverySendBaoCaoTrichNoTnexAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\TaoYeuCauTuDongMposTuLichThuDoiLuongSubAction;

// CronJob: Tạo lịch thu hồi từ chỉ dẫn rồi bắn vào api /DebtRecoveryContractPlanCreate 
Route::any('/DebtRecoveryContractGuideCreatePlan', [
  'as' => 'DebtRecoveryContractGuideCreatePlanAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide\CollectDebtGuideController@createPlan'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAO_LICH_TU_CHI_DAN'));

// CronJob tạo yêu cầu từ lịch thu hồi
Route::any('/DebtRecoveryContractPlanCreateRequest', [
  'as' => TaoYeuCauTuDongMposTuLichThuDoiLuongSubAction::class,
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule\CollectDebtScheduleRequestController@createRequest'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAO_YEU_CAU_TU_LICH'));

Route::any('/DebtRecoveryContractCreateRequestViaWallet', [
  'as' => 'DebtRecoveryContractCreateRequestViaWalletAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule\CollectDebtScheduleRequestController@createRequestViaWallet'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAO_YEU_CAU_TU_LICH'));

// CronJob thực hiện việc gửi yêu cầu sang các đối tác trích nợ. Với điều kiện is_payment = 1;
Route::any('/DebtRecoveryRequestSendPayment', [
  'as' => 'DebtRecoveryRequestSendPaymentAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@sendPayment'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_GUI_LENH_SANG_DOI_TAC'));


// Cronjob kiểm tra kết quả trích nợ
Route::any('/DebtRecoveryRequestCheckPayment', [
  'as' => 'DebtRecoveryRequestCheckPaymentAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@checkPayment'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_KIEM_TRA_KET_QUA_TRICH_NO'));

Route::any('/CheckRequestViaRedisAction', function () {
	return app(CheckRequestViaRedisAction::class)->run();
});

// Cronjob thực hiện giảm trừ số tiền đóng băng và giảm số dư ví sau khi lệnh trích đã đc hạch toán
Route::any('/DebtRecoveryRequestOpenAndMinusFreeze', [
  'as' => 'DebtRecoveryRequestOpenAndMinusFreezeAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@DebtRecoveryRequestOpenAndMinusFreeze'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_GIAM_TRU_SO_DU_VI_VA_TIEN_DONG_BANG_TRONG_VI'));

// Cron job partner check
Route::any('/DebtRecoveryPartnerCheck', [
  'as' => 'DebtRecoveryPartnerCheckImproveAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtPartner\CollectDebtPartnerController@partnerCheck'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_KIEM_TRA_DOI_TAC'));


// Cronjob ghi sổ các yêu cầu đã về trạng thái cuối
Route::any('/DebtRecoveryRequestRecored', [
  'as' => 'ThucHienCaiTienGhiSoAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@recored'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_GHI_SO'));

// Cronjob thực hiện xử lý CutOff
Route::any('/DebtRecoveryRequestFinishCutOffTime', [
  'as' => 'DebtRecoveryRequestFinishCutOffTime',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestCutOffController@handlerCutOff'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_CUT_OFF_TIME'));

Route::any('/CutOffTimeByRedisAction', function () {
	return app(CutOffTimeByRedisAction::class)->initCutOff();
})->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_CUT_OFF_TIME'));

// **JOB HẠCH TOÁN
Route::any('/DebtRecoveryLedgerAccounting', [
  'as' => 'HachToanYeuCauTrenSoAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger\CollectDebtLedgerController@accouting'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_HACH_TOAN'));


Route::any('/DebtRecoveryLedgerAccountingSummary', [
  'as' => 'DebtRecoveryLedgerAccountingSummary',
  'uses' => CollectDebtLedgerController::class . '@accoutingSummary'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_HACH_TOAN'));;


// Job xu ly mail sap toi ky
Route::any('/DebtRecoveryHandleFirstUpcomingPlan', [
  'as' => 'DebtRecoveryHandleFirstUpcomingPlanAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleFirstUpcomingPlan'
]);

Route::any('/DebtRecoveryHandleSapToiHan', [
  'as' => 'DebtRecoveryHandleSapToiHanAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleSapToiHan'
]);

// Đến kỳ thanh toán
Route::any('/DebtRecoveryHandleMailDenKyDungNgay', [
  'as' => 'DebtRecoveryHandleMailDenKyDungNgayAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleMailDenKyDungNgay'
]);

// Đến hạn tất toán
Route::any('/DebtRecoveryHandleDenHanTatToanDungNgay', [
  'as' => 'DebtRecoveryHandleDenHanTatToanDungNgayAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleDenHanTatToanDungNgay'
]);

// Job duyệt tự động chỉ dẫn
Route::any('/DebtRecoveryGuideAutoApproved', [
  'as' => 'DebtRecoveryGuideAutoApproved',
  'uses' => CollectDebtGuideController::class . '@autoAprroved'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_DUYET_CHI_DAN_TU_DONG'));


// Thực hiện xử lý yêu cầu điều chỉnh và mở lại lịch
Route::any('/DebtRecoveryRequestAdjustmentProcess', [
  'as' => 'DebtRecoveryRequestAdjustmentProcessAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequestAdjustment\CollectDebtRequestAdjustmentController@process'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_XU_LY_YEU_CAU_DIEU_CHINH'));

Route::any('/DebtRecoverySummaryComplete', [
  'as' => 'DebtRecoverySummaryFinishContractAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary\CollectDebtSummaryController@complete'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAT_TOAN_HOP_DONG'));

// Thực hiện recheck yêu cầu bị cutoff, yc trích ngay
Route::any('/DebtRecoveryRequestRecheck', [
  'as' => 'DebtRecoveryRequestRecheckAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestRecheckController@handleRecheck'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_RECHECK_YEU_CAU'));

// Job turn on dong bo tong hop ve he thong hop dong
Route::any('/DebtRecoverySummaryTurnOnContractMustSyncAt23h', [
  'as' => 'DebtRecoverySummaryTurnOnContractMustSyncAt23hAction',
  'uses' => CollectDebtSummaryController::class . '@turnOnContractMustSyncAt23h'
]);

// Dong bo tong hop ve he thong hop dong
Route::any('/DebtRecoverySummaryCheckLedgerExsist', [
  'as' => 'DongBoVeHeThongHopDongAction',
  'uses' => CollectDebtSummaryController::class . '@checkLedgerExsist'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_DONG_BO_HANG_NGAY'));

// danh dau huy yc mpos khi co tien ve tu IB_OFF hoac VA
Route::any('/DebtRecoveryWaitProcessHandle', [
  'as' => 'DebtRecoveryWaitProcessHandleAction',
  'uses' => CollectDebtWaitProcessController::class . '@processHandle'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_DONG_BO_HANG_NGAY'));

// Mail qua han 3 cap do
Route::any('/DebtRecoverySummaryHandleMailOverdue', [
  'as' => 'DebtRecoverySummaryMailOverDueAction',
  'uses' => CollectDebtSummaryController::class . '@handleMailOverdue'
]);

// Mail cham ky
Route::any('/DebtRecoverySummaryHandleMailOverCycle', [
  'as' => 'DebtRecoverySummaryMailOverCycleAction',
  'uses' => CollectDebtSummaryController::class . '@handleMailOverCycle'
]);

// Xoa cac ban ghi event cu
Route::any('/DebtRecoveryContractDeleteOldEvent', [
  'as' => 'DebtRecoveryContractDeleteOldEvent',
  'uses' => CollectDebtContractEventController::class . '@deleteOldEvent'
]);

// api thuc hien check hoan tien cho: luong thu thua & luong hoan phi
Route::any('/DebtRecoverySummaryCheckRefund', [
	'as' => 'DebtRecoverySummaryCheckRefundAction',
	'uses' => CollectDebtSummaryRefundController::class . '@DebtRecoverySummaryCheckRefund'
]);

// Job kiểm tra lệnh trích ngay, nếu trích ko đủ tiền sẽ báo về email
Route::any('/DebtRecoveryRequestCanhBaoTrichNgay', [
	'as' => 'DebtRecoveryRequestCanhBaoTrichNgayAction',
	'uses' => CanhBaoTrichNgayController::class . '@handler'
]);

// Job gửi mail thông báo về tình trạng lệnh trích ngay
Route::any('/DebtRecoveryJobNotifyDebtNow', [
	'as' => SendNotifyViaChannelsAction::class,
	'uses' => CollectDebtNotifyDebtNowController::class . '@jobNotifyDebtNow'
]);

// Job báo cáo giao dịch trích nợ về cho bên HĐ làm việc với Tnex
Route::any('/DebtRecoverySendBaoCaoTrichNoTnex', [
	'as' => DebtRecoverySendBaoCaoTrichNoTnexAction::class,
	'uses' => CollectDebtLedgerReportController::class . '@sendReportTrichNoTnex'
]);