<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtPartner;

class GetPartnersCuaHopDongSubAction
{
  public function run(string $contractCode = '')
  {
    $partners = CollectDebtPartner::query()
                                  ->whereRaw("LENGTH(contract_code) > 0")
																	->where('contract_code', $contractCode)
																	->orderBy('id', 'ASC')
                                  ->get();
    return $partners;
  }
}
