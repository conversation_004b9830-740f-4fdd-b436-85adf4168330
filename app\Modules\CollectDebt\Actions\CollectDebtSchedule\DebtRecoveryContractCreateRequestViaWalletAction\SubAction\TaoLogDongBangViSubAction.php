<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class TaoLogDongBangViSubAction
{
	public function run(CollectDebtPartner $collectDebtPartner, CollectDebtSummary $collectDebtSummary)
	{
		$params = [
			'service_code'           => CollectDebtEnum::RL_DICH_VU_TRU_TIEN_VAO_VI,
			'reference_id'           => $collectDebtSummary->id,
			'order_code'             => sprintf('%s|%s', $collectDebtSummary->contract_code, $collectDebtPartner->partner_transaction_id),
			'partner_transaction_id' => $collectDebtPartner->partner_transaction_id,
			'created_by'             => Helper::getCronJobUser(),
			'time_created'           => now()->timestamp,
			'description'						 => 'Tao log de thuc hien tru tien sau khi hach toan'
		];

		mylog(['Param tao log tru tien' => $params]);

		$createResult = CollectDebtLog::query()->forceCreate($params);

		return $createResult;
	}
}
