<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryRefundFeeBuildFormAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryRefundFeeBuildFormRequest;
use Exception;

class DebtRecoverySummaryRefundFeeBuildFormAction
{

	public function run(DebtRecoverySummaryRefundFeeBuildFormRequest $request)
	{
		
		
		$contractCode = trim($request->json('data.contract_code'));
		$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();
		
		throw_if(!$collectDebtSummary, new Exception('khong tim thay thong tin hop dong'));

		$paymentBanking = $collectDebtSummary->getSummaryOtherDataItem('PAYMENT_METHOD');
		$ibOff = collect($paymentBanking['data'])->where('payment_method_code', 'IB_OFF')->first();

		$returnData = [
			'contract' => [
				'contract_code' => $collectDebtSummary->contract_code,
				'contract_time_start' => $collectDebtSummary->getTimeStartAsDate()->format('d/m/Y'),
				'contract_time_end' => $collectDebtSummary->getTimeEndAsDate()->format('d/m/Y'),
				'contract_amount' => $collectDebtSummary->contract_amount,
				'total_amount_paid' => $collectDebtSummary->total_amount_paid,
				'total_fee_paid' => $collectDebtSummary->total_fee_paid,
				'total_paid' => $collectDebtSummary->total_amount_paid + $collectDebtSummary->total_fee_paid,
				'ma_chung_tu' => 'Đang cập nhật', // ma chung tu
				'ma_yeu_cau' => 'Đang cập nhật', // ma yeu cau
				'ma_giao_dich' => 'Đang cập nhật', //
			],
		
			'banking_info' => $ibOff,

			'refund' => [
				'phi_cham_ky_co_the_hoan' => $collectDebtSummary->getOnlyPhiChamKyCoTheHoan(),
				'phi_qua_han_co_the_hoan' => $collectDebtSummary->getOnlyPhiQuaHanCoTheHoan(),
				'tong_phi_co_the_hoan' => $collectDebtSummary->getTongPhiCoTheHoan()
			]
		];

		return $returnData;
	}
} // End class