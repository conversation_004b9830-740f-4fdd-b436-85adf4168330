<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtPartner;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;

class DebtRecoveryPartnerSetStatusRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    $partnerListStatus = CollectDebtPartner::listStatus();

    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:1'],
      'data.status' => ['required', 'string', 'max:50', Rule::in(array_keys($partnerListStatus))],
      'data.user_request_id' => ['required', 'string', 'max:255'],
      'data.description' => ['nullable', 'string', 'max:255'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['user_request_id'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['user_request_id']);
    $this->merge($params);
  }
} // End class
