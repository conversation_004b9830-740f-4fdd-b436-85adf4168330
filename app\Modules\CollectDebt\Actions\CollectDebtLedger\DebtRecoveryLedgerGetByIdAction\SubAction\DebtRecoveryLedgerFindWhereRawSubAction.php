<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByIdAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtLedger;
use Exception;

class DebtRecoveryLedgerFindWhereRawSubAction
{
  public function run(string $whereRaw = '', bool $isThrowError=false): CollectDebtLedger
  {
    throw_if(empty($whereRaw), new Exception('Thiếu điều kiện truy vấn chi tiết sổ', 500));
    $collectDebtLedger = CollectDebtLedger::whereRaw($whereRaw)->first();
    throw_if(!$collectDebtLedger && $isThrowError, new Exception('Không tìm thấy thông tin sổ'));
    return $collectDebtLedger;
  }
} // End class