<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtSetting;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction\GhiSoChoYeuCauDaCoSoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\TaoYeuCauKhongCanGuiDoiTacSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\CapNhatYeuCauVeTrangThaiCuoiSubAction;

class DebtRecoveryPartnerCheckAction
{
  public $virtualAccountIds = [];

  public function run(Request $request)
  {
    $collectDebtPartners = CollectDebtPartner::where('status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
                                             ->where('status_refund', CollectDebtEnum::PARTNER_STT_RF_CHUA_HOAN)
                                             ->whereNotNull('other_data')
                                             ->whereRaw("LENGTH(payment_account_id) > 0")
                                             ->where(function ($query) {
                                                $query->where(function ($subQuery) {
                                                  $subQuery->where('payment_method_code', 'MPOS')
                                                           ->has('collectDebtRequest');
                                                })
                                                ->orWhere(function ($subQuery) {
                                                  $subQuery->whereIn('payment_method_code', ['IB_OFF', 'WALLET'])
                                                           ->whereAvailableBalance()
                                                           ->has('collectDebtSummary');
                                                })
                                                ->orWhere(function ($subQuery) {
                                                  $subQuery->where('payment_method_code', 'VIRTUALACCOUNT')
                                                           ->whereAvailableBalance()
                                                           ->whereExists(function ($query) {
                                                              $query->selectRaw("contract_code, JSON_EXTRACT(other_data, '$[*].data[*].payment_account_id')")
                                                                    ->from('debt_recovery_summary')
                                                                    ->whereRaw("LOCATE(debt_recovery_partner.payment_account_id , JSON_EXTRACT(other_data, '$[*].data[*].payment_account_id')) > 0");
                                                           });
                                                });
                                              })
																							->whereRaw("IFNULL(time_updated, time_created) + 90 < ?", [now()->timestamp]);

    if ( !empty($request->json('data.partner_request_id')) ) {
      $collectDebtPartners = $collectDebtPartners->where('partner_request_id', $request->json('data.partner_request_id'));
    }

		DB::beginTransaction();
    $collectDebtPartners = $collectDebtPartners->orderBy('id', 'ASC')
																						   ->limit(1)
																							 ->lock(" FOR UPDATE SKIP LOCKED ")
                                               ->get();
    mylog(['Công nợ cần xử lý là:' => $collectDebtPartners->pluck('id')]);

    if ($collectDebtPartners->isEmpty()) {
			DB::rollBack();
			throw new Exception('Khong co thong tin partner');
		}
    
    $collectDebtPartners->map(function (CollectDebtPartner $collectDebtPartner) {
     
      try {
        if ($collectDebtPartner->isTonTaiYeuCau()) {
          mylog(['Công nợ đã có yêu cầu' => 'YES']);
          
          $collectDebtPartnerCreditRequest = app(CapNhatYeuCauVeTrangThaiCuoiSubAction::class)->run($collectDebtPartner);
          
          /**
           * Nếu đã gọi hàm cập nhật YÊU CẦU về trạng thái cuối, và trạng thái Partner vẫn không kết thúc
           * thì Partner sẽ được đưa vào diện "CHƯA XỬ LÝ"
           */
          if ($collectDebtPartnerCreditRequest['partner']->isPartnerDangXuLy()) {
            $collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_CHUA_XU_LY;
            $collectDebtPartner->save();
          }
          
          // Nếu yc đã ghi sổ rồi và ghi nhận số tiền thừa, bắt !empty để lịch thu quá khứ ko bị ghi sổ 2 lần
          $collectDebtRequest = $collectDebtPartner->collectDebtRequest;
          if (!empty($collectDebtPartnerCreditRequest['is_excess']) && !empty($collectDebtRequest->partner_transaction_id)) {
            $inputs = [
              'data' => [
                'profile_id'    => $collectDebtRequest->profile_id,
                'contract_code' => $collectDebtRequest->contract_code,
                'plan_ids'      => $collectDebtRequest->plan_ids,
                'request_id'    => $collectDebtRequest->id,
                'currency'      => $collectDebtRequest->currency,
                'amount'        => $collectDebtPartner->amount_receiver,
                'description'   => 'TRICH_MUON',
                'status'        => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY,
                'time_record'   => time(),
                'created_by'    => Helper::getCronJobUser(),
                'time_created'  => time(),
              ]
            ];
            
            $rq = new DebtRecoveryLedgerCreateRequest();
            $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
            
            app(GhiSoChoYeuCauDaCoSoSubAction::class)->run( $collectDebtRequest, $rq, $collectDebtPartner);
          }

          DB::commit();
          return $collectDebtPartnerCreditRequest['partner'];
        } // End if is ton tai yeu cau
  
        if ($collectDebtPartner->isKhongTonTaiYeuCau()) {
          $collectDebtPartner = app(TaoYeuCauKhongCanGuiDoiTacSubAction::class)->run($collectDebtPartner);
          DB::commit();
        }
      
        return $collectDebtPartner;
      } catch (\Throwable $th) {
				CollectDebtPartner::query()->where('id', $collectDebtPartner->id)->update(['time_updated' => now()->timestamp]);
        
				DB::rollBack();

				$errorMessage = parseErr([
					'Error' => sprintf('Loi Check Partner. PartnerID: %s', $collectDebtPartner->id),
					'LogId' => request('api_request_id'),
					'ErrorMessage' => $th->getMessage()
				]);

				TelegramAlert::sendMessage($errorMessage);

        throw $th;
      }
    }); // End

    return $collectDebtPartners->pluck('id');
  }
} // End class