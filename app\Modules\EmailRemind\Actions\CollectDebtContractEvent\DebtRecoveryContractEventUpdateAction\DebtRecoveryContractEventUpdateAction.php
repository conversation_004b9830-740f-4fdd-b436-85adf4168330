<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventUpdateAction;

use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use Exception;

class DebtRecoveryContractEventUpdateAction
{
    public function run($request)
    {
        $params = $this->mapData($request);

        $debtRecoveryContractEvent = CollectDebtContractEvent::where('id', $params['id'])->first();
        if (isset($params['other_data'])) {
            $otherData = json_decode($debtRecoveryContractEvent->other_data, true);
            $otherData[] = $params['other_data'];
            unset($params['other_data']);
            $debtRecoveryContractEvent->other_data = json_encode($otherData, JSON_UNESCAPED_UNICODE);
        }

        $debtRecoveryContractEvent = $debtRecoveryContractEvent->update($params);

        throw_if(!$debtRecoveryContractEvent, new Exception('Không thể cập nhật được bản ghi'));

        return [
            'id' => $params['id']
        ];
    }


    protected function mapData($request)
    {
        $dataResponse = [];

        $dataResponse['id'] = $request['id'];

        if (isset($request['category_care_code']) && !empty($request['category_care_code'])) {
            $dataResponse['category_care_code'] = $request['category_care_code'];
        }

        if (isset($request['service_care_code']) && !empty($request['service_care_code'])) {
            $dataResponse['service_care_code'] = $request['service_care_code'];
        }

        if (isset($request['data']) && !empty($request['data'])) {
            $dataResponse['data'] = json_encode($request['data'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['description']) && !empty($request['description'])) {
            $dataResponse['description'] = $request['description'];
        }

        if (isset($request['other_data']) && !empty($request['other_data'])) {
            $dataResponse['other_data'] = json_encode($request['other_data'], JSON_UNESCAPED_UNICODE);
        }

        if (isset($request['time_start']) && !empty($request['time_start'])) {
            $dataResponse['time_start'] = $request['time_start'];
        }

        if (isset($request['number']) && !empty($request['number'])) {
            $dataResponse['number'] = $request['number'];
        }

        if (isset($request['time_expired']) && !empty($request['time_expired'])) {
            $dataResponse['time_expired'] = $request['time_expired'];
        }

        $dataResponse['status'] = config('collect_debt_email_remind_config.debt_contract_event_status.new');
        $dataResponse['time_created'] = time();


        return $dataResponse;
    }
}
