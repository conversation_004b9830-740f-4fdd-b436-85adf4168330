<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtRequest;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;

trait RequestLedgerable
{
  public function isYeuCauChuaCoSo(): bool {
		return !$this->collectDebtLedger;
	}

	public function isYeuCauDaCoSo(): bool {
		return isset($this->collectDebtLedger);
	}

	public function isTrangThaiSoDaHachToan(): bool {
		return $this->collectDebtLedger->status == CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN;
	}

	public function isYeuCauChuaHachToanTrenSo(): bool {
		return $this->isYeuCauChuaCoSo() || ($this->isYeuCauDaCoSo() && !$this->isTrangThaiSoDaHachToan());
	}

	public function isYeuCauDaHachToanTrenSo(): bool {
		if ( $this->isYeuCauChuaCoSo() ) {
			return false;
		}
		
		return $this->isYeuCauDaCoSo() && $this->isTrangThaiSoDaHachToan();
	}
} // End class
