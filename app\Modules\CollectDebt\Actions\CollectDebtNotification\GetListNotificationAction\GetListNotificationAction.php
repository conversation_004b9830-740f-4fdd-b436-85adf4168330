<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtNotification\GetListNotificationAction;

use App\Modules\CollectDebt\Enums\NotifyTypeEnum;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtNotification;
use App\Modules\CollectDebt\Resources\CollectDebtNotificationResourceCollection;

class GetListNotificationAction
{
	public function run(Request $request)
	{
		$listNotification = CollectDebtNotification::query()
			->orderBy('is_read', 'ASC')
			->orderBy('created_at', 'DESC')
			->paginate(
				$request->json('data.limit', 10),
				$request->json('data.fields', ['*']),
				'page',
				$request->json('data.page', 1)
			);

		$resource = new CollectDebtNotificationResourceCollection($listNotification);
		$response = $resource->toResponse($request)->getData(true);
		
		if (!empty($request->json('data.markAsRead'))) {
			$r = CollectDebtNotification::query()->where('is_read', 0)
																					 ->update(['is_read' => 1, 'read_at' => now()]);
		}

		return [
			'total_unread' => $this->getTotalUnread(),
			'data' => $response
		];
	}

	public function getTotalUnread(): int {
		return CollectDebtNotification::query()->where('is_read', 0)->count();
	}
} // End class
