<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;

class ThongKeTrangThaiThuHoiSA
{
	/** 
	 * Các trạng thái liên quan đến thu hồi như: 
	 * 		Đã tất toán
	 * 		Đang thu 
	 * 		Chậm kỳ - n kỳ
	 * 		Qúa hạn - n ngày
	 * 		Nợ xấu - n ngày
	 */
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$thongKeStatus = [];

		if ($collectDebtSummary->isHopDongDaTatToan()) {
			$thongKeStatus[] = [
				'class' => 'badge badge-primary',
				'name' => 'Đã tất toán'
			];
		}

		if (!$collectDebtSummary->isHopDongDaTatToan()) {
			$thongKeStatus[] = [
				'class' => 'badge badge-warning',
				'name' => '<PERSON>ang thu hồi'
			];
		}

		if ($collectDebtSummary->number_over_cycle > 0) {
			$thongKeStatus[] = [
				'class' => 'badge badge-warning',
				'name' => sprintf('Chậm kỳ - %s kỳ', $collectDebtSummary->number_over_cycle)
			];
		}

		if ($collectDebtSummary->number_day_overdue > 0) {
			if ($collectDebtSummary->number_day_overdue < 3) {
				$thongKeStatus[] = [
					'class' => 'badge badge-warning',
					'name' => sprintf('Quá hạn - %s ngày', $collectDebtSummary->number_day_overdue)
				];
			}
			
			if ($collectDebtSummary->number_day_overdue >= 3 && $collectDebtSummary->number_day_overdue < 7) {
				$thongKeStatus[] = [
					'class' => 'badge badge-warning bg-purple',
					'name' => sprintf('Quá hạn - %s ngày', $collectDebtSummary->number_day_overdue)
				];
			}

			if ($collectDebtSummary->number_day_overdue >= 7) {
				$thongKeStatus[] = [
					'class' => 'badge badge-warning bg-red',
					'name' => sprintf('Quá hạn - %s ngày', $collectDebtSummary->number_day_overdue)
				];
			}
		}

		if ($collectDebtSummary->number_day_overdue > 10) {
			$thongKeStatus[] = [
				'class' => 'badge badge-warning bg-dark',
				// 'name' => sprintf('Nợ xấu - quá %s ngày', $collectDebtSummary->number_day_overdue)
				'name' => 'Nợ xấu'
			];
		}
		
		return $thongKeStatus;
	}
}
