<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class IsToanBoYeuCauDaVeTrangThaiCuoiTask
{
  public function run(CollectDebtSchedule $plan): bool
  {
		$cacYeuCauCuaLichThuHienTai = CollectDebtRequest::query()
																										->where('contract_code', $plan->contract_code)
																										->whereRaw("FIND_IN_SET(?, `plan_ids`) > 0", [strval($plan->id)])
																										->get();

		mylog([
			'Lich can check cuoi cung' => $plan->id, 
			'Cac yeu cau check trang thai cuoi cung' => $cacYeuCauCuaLichThuHienTai
		]);

    $isCacYeuCauDaVeTrangThaiCuoi = $cacYeuCauCuaLichThuHienTai->every(function (CollectDebtRequest $collectDebtRequest) {
      return $collectDebtRequest->isCanceled() || $collectDebtRequest->isCompleted() 
                                               || $collectDebtRequest->isDaDuyetGiamPhiBuoc2()
                                               || $collectDebtRequest->isNeedCompleteAndReCheck()
																							 || $collectDebtRequest->isDaTuChoiBangTay()
																							 || $collectDebtRequest->isRecorded();
    });

    return $isCacYeuCauDaVeTrangThaiCuoi;
  }
}
