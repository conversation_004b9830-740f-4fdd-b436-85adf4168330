<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtGuide;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Contracts\Validation\Rule;

class DayDuKenhThuRule implements Rule
{
    private string  $__errorMessage;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
      $this->__errorMessage = 'Hợp đồng của bạn phải đầy đủ các kênh thu: MPOS, IB_OFF, VIRTUALACCOUNT';
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($paymentGuideField, $paymentGuideValue)
    {
      $paymentGuideFieldValueAsCollection = collect($paymentGuideValue)->groupBy('payment_method_code');
      
      if (!$paymentGuideFieldValueAsCollection->has('MPOS')) {
        return false;
      }

			if (!$paymentGuideFieldValueAsCollection->has('IB_OFF')) {
        return false;
      }

			if (!$paymentGuideFieldValueAsCollection->has('VIRTUALACCOUNT')) {
        return false;
      }

			$va = collect($paymentGuideValue)->where('payment_method_code', 'VIRTUALACCOUNT')->first();
			
			if (empty($va['other_data']['qrCode'])) {
				$this->__errorMessage = 'Lỗi Phương thức VIRTUALACCOUNT không có QrCode';
				return false;
			}

      return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
      return $this->__errorMessage;
    }
}
