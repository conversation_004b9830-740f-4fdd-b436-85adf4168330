<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;

class TaoCongNoThanhToanQuaViSubAction
{
	public function run(
		CollectDebtSummary $collectDebtSummary, 
		Collection $plans, 
		CollectDebtShare $collectDebtShare, 
		$profileAccount=[]
	): CollectDebtPartner 
	{
		/**
		 * Double Check
		 * Kiểm tra HĐ hiện tại có công nợ WALLET nào không, cần đảm bảo
		 * 1. Các partner phải có thông tin yêu cầu và trạng thái là đã xử lý
		 * 2. Đảm bảo các yêu cầu trích của partner đã hạch toán
		 * 3. Đảm bảo toàn bộ các yêu cầu đã được giảm trừ đóng băng
		 */
		$collectDebtPartners = CollectDebtPartner::query()
																						 ->where('payment_account_id', $collectDebtShare->contract_code)
																					   ->where('payment_method_code', 'WALLET')
																						 ->get();

		// 1.
		if ($collectDebtPartners->isNotEmpty()) {
			$isTonTaiCongNoChuaXuLy = $collectDebtPartners->contains(function (CollectDebtPartner $partner) {
				return empty($partner->partner_request_id) || $partner->status == CollectDebtEnum::PARTNER_STT_CHUA_XU_LY || $partner->status == CollectDebtEnum::PARTNER_STT_CHO_DUYET;
			});
	
			throw_if(
				$isTonTaiCongNoChuaXuLy, 
				new Exception(
					sprintf('Hợp đồng `%s` thanh toán qua ví đang có công nợ chưa thực hiện xong. Từ chối xử lý tiếp', $collectDebtShare->contract_code)
				)
			);
			
			// 2.
			$partnersRequestIds = $collectDebtPartners->pluck('partner_request_id')->toArray();
	
			$collectDebtRequests = CollectDebtRequest::query()
																						  ->with('ledgers')
																							->where('payment_method_code', 'WALLET')
																							->whereIn('partner_request_id', $partnersRequestIds)
																							->get();

			$isToanBoYeuCauDaHachToan = $collectDebtRequests->every(function (CollectDebtRequest $collectDebtRequest) {
				return $collectDebtRequest->ledgers->isNotEmpty() && $collectDebtRequest->ledgers->every(function (CollectDebtLedger $ledger) {
					return $ledger->status == CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN;
				});
			});

			throw_if(
				!$isToanBoYeuCauDaHachToan, 
				new Exception(sprintf('Các yêu cầu của HĐ `%s` chưa được hạch toán. Từ chối xử lý', $collectDebtShare->contract_code))
			);

			// 3.
			$isToanBoYeuCauDaDuocGiamTruVi = $collectDebtRequests->every(function (CollectDebtRequest $collectDebtRequest) {
				return $collectDebtRequest->isYeuCauDaGiamTruTienTrongVi();
			});

			throw_if(
				!$isToanBoYeuCauDaDuocGiamTruVi, 
				new Exception(sprintf('Các yêu cầu của HĐ `%s` chưa xử lý xong giảm trừ ví', $collectDebtShare->contract_code))
			);
		}
		

		// sort lich theo quy tac can tru
		$plans = app(PlanSortableCollectionByRule::class)->sortCollection($plans);

		$tongTienCanThanhToanCacLich = $plans->sum('request_amount_debit');

		$soTienTaoCongNo = min($profileAccount['data']['available_balance'], $tongTienCanThanhToanCacLich);

		// Tạo công nợ qua kênh WALLET
		$params = [
			'contract_code' => $collectDebtSummary->contract_code,
			'payment_channel_code' => 'WALLET',
			'payment_method_code' => 'WALLET',
			'payment_account_id' => $collectDebtSummary->contract_code,
			'amount_payment' => 0,
			'amount_receiver' => $soTienTaoCongNo,
			'request_exists' => CollectDebtEnum::PARTNER_REQUEST_CHUA_CO_YC_THANH_TOAN,
			'response' => '{}',
			'description' => 'Tạo công nợ từ số dư ví',

			// Luồng recheck có thể có thêm các thông tin sau:
			'created_by' => Helper::getCronJobUser(),
			'time_created' => time(),

			'updated_by' => Helper::getCronJobUser(),
			'time_updated' => time(),


			'processing_by' => Helper::getCronJobUser(),
			'time_processing' => time(),

			'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
			'other_data' => json_encode([
				[
					'type' => 'CONTRACT',
					'time_modified' => time(),
					'data' => [
						'profile_id'          => $collectDebtShare->profile_id,
						'contract_code'       => $collectDebtShare->contract_code,
						'contract_time_start' => $collectDebtShare->contract_time_start,
						'contract_type'       => $collectDebtShare->getContractTypev1(),
						'contract_cycle'      => $collectDebtShare->contract_cycle,
						'contract_intervals'  => $collectDebtShare->contract_intervals,
						'amount'              => $collectDebtShare->amount,
						'contract_time_end'   => $collectDebtShare->contract_time_end,
						'other_data'          => '{}',
						'description'         => 'Hợp đồng v4',
						'id'                  => $collectDebtShare->getContractId()
					],
					'note' => 'Thông tin hợp đồng'
				]
			], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
		];

		$collectDebtPartner = CollectDebtPartner::query()->forceCreate($params);
		throw_if(!$collectDebtPartner, new Exception('Không thể tạo được partner VÍ'));

		$collectDebtPartner->partner_transaction_id = sprintf('WP%s%s', date('ymd'), $collectDebtPartner->id);
		$collectDebtPartner->save();

		return $collectDebtPartner;
	}
} // End class
