<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class GetThongKeLenhTrichTheoHopDongSubAction
{
  public function run(string $contractCode = ''): array
  {
    $thongKe = CollectDebtRequest::query()
      ->where('contract_code', $contractCode)
      ->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA)
      ->where('status', '!=', CollectDebtEnum::REQUEST_STT_TU_CHOI)
      ->selectRaw("payment_method_code, COUNT(id) as count")
      ->groupBy('payment_method_code')
      ->get()
      ->keyBy('payment_method_code')
      ->toArray();

    return $thongKe;
  }
}
