<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\DailyCollect;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\CongTienQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\IsCoLichThuPhiQuaHanTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\FeeHandler\XuLyThanhToanPhiFH;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\Task\SwapLichThuNeuTrichTuongLaiST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime\TaoLichThuVetTatToanNoGocST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime\TaoLichThuVetNoGocTrongNgayST;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime\TaoLichThuVetSoPhiConLaiTrongNgayST;

/** ---------------------- 
 * [INSIDE-TIME] 
 *   Nếu lịch hiện tại chưa phải tất toán => Sinh lịch thu như bình thường
 *   Nếu lịch hiện tại là lịch thu tất toán => Sinh phí quá hạn ngay lập tức vì bản chất rundate đã bị muộn rồi
 * ---------------------- */   
class DailyInsideTimeTnexTask {

  public array $metaData = [];
 
  public function run(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong=0) {
    /** ------------------- THU GỐC ------------------- */
      if ( $lichDangHachToan->isLichThuGoc() ) {
        return $this->xuLyThuNoGoc($lichDangHachToan, $soTienTrichThanhCong);
      }

    /** ------------------- THU PHÍ ------------------- */
      if ($lichDangHachToan->isLichThuPhi()) {  
        return $this->xuLyThuPhi($lichDangHachToan, $soTienTrichThanhCong);
      }
  }

  public function xuLyThuNoGoc(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong=0) {
  /** ------ Không phải lịch tất toán ------ */
    if ($lichDangHachToan->isKhongPhaiLichTatToan()) {
      
      $soTienConPhaiThuCuaLich = $lichDangHachToan->request_amount_debit - $soTienTrichThanhCong;

      $lichThuVetNoGocTrongNgay = app(TaoLichThuVetNoGocTrongNgayST::class)->run($lichDangHachToan, $soTienConPhaiThuCuaLich, $soTienTrichThanhCong);
      throw_if(!$lichThuVetNoGocTrongNgay, new Exception("Daily - Lỗi không thể tạo được lịch thu vét nợ gốc trong ngày"));

      // Nếu là lịch thu tương lai thì cần hoán đổi lại loại lịch

      if ( $lichDangHachToan->isRunDateTuongLai() ) {
        app(SwapLichThuNeuTrichTuongLaiST::class)->run($lichDangHachToan, $lichThuVetNoGocTrongNgay);
      }
    }
    
  /** ------ Là lịch tất toán ------ */
    if ($lichDangHachToan->isLichTatToan()) {
			// Không được sinh phí, thay vào đó thì sẽ tạo lịch thu vét mà thôi
      $soTienNoGocConPhaiTra = $lichDangHachToan->request_amount_debit - $soTienTrichThanhCong;
      $lichThuVetTatToanNoGoc = app(TaoLichThuVetTatToanNoGocST::class)->run($lichDangHachToan, $soTienNoGocConPhaiTra);
      throw_if(!$lichThuVetTatToanNoGoc, new Exception('Không thể tạo lịch thu vét tất toán nợ gốc'));
    }

    return $this->metaData;
  }

  public function xuLyThuPhi(CollectDebtSchedule $lichDangHachToan, float $soTienTrichThanhCong=0) {
    $soPhiConLaiPhaiThu = $lichDangHachToan->request_amount_debit - $soTienTrichThanhCong;
    
    // Xử lý thanh toán phí   
    $result = app(XuLyThanhToanPhiFH::class)->run($lichDangHachToan, $soTienTrichThanhCong);
    $this->metaData = array_merge($this->metaData, $result);


    if ($soPhiConLaiPhaiThu > 0) {
      $isCoLichThuPhiQuaHan = app(IsCoLichThuPhiQuaHanTask::class)->run(
        $lichDangHachToan->contract_code,
        true
      );
      
      if ( !$isCoLichThuPhiQuaHan ) {
        $lichThuPhiVetTrongNgay = app(TaoLichThuVetSoPhiConLaiTrongNgayST::class)->run($lichDangHachToan, $soPhiConLaiPhaiThu);
        throw_if(!$lichThuPhiVetTrongNgay , new Exception('Loi khong tao duoc lich thu phi vet trong ngay'));
      }

      if ( $isCoLichThuPhiQuaHan ) {
        $lichThuPhiQuaHanDaCongGopTien = app(CongTienQuaHanTask::class)->run(
          $isCoLichThuPhiQuaHan,
          $lichDangHachToan->time_start,
          $lichDangHachToan->time_end,
          $lichDangHachToan->rundate,
          $soPhiConLaiPhaiThu
        );
      }
    }
    
    return $this->metaData;
  }
} // End class