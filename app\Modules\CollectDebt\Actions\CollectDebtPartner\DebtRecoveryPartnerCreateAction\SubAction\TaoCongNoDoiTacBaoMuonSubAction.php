<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\DebtRecoveryLedgerCreateAction;

class TaoCongNoDoiTacBaoMuonSubAction
{
  /**
   * Rơi vào case này thì yêu cầu đã đc ghi sổ rồi (do đối tác báo muộn) 
   * -> Vậy nên bước này cần thực hiện GHI SỔ LUÔN
   *
   * @param CollectDebtRequest $currentCollectDebtPartner
   * @param Request $request
   * @return void
   */
  public function run(CollectDebtPartner $currentCollectDebtPartner, CollectDebtRequest $collectDebtRequest, Request $request): CollectDebtPartner
  {
    DB::beginTransaction();
    try {
      $paramInsert = [
        'payment_channel_code' => $currentCollectDebtPartner->payment_channel_code,
        'payment_method_code' => $currentCollectDebtPartner->payment_method_code,
        'payment_account_id' => $currentCollectDebtPartner->payment_account_id,
        'partner_request_id' => $currentCollectDebtPartner->partner_request_id,
        'partner_transaction_id'  => $request->json('data.partner_transaction_id'),
        'amount_payment' => 0,
        'amount_receiver' => $request->json('data.amount_receiver'),
        'fee' => $request->json('data.fee', 0),
        'request_exists' => $currentCollectDebtPartner->request_exists,
        'response' => $request->json('data.response'),
        'description' => sprintf('Công nợ do đối tác `%s` báo muộn lúc: %s', $currentCollectDebtPartner->payment_method_code, now()->format('d/m/Y H:i:s')),
        'created_by' => $request->json('data.created_by', Helper::getCronJobUser()),
        'time_created' => time(),
        'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
      ];
      
      // Dùng guide để build model CoreContract, sau đọc từ partner sẽ không cần join sang chỉ dẫn nữa
      $collectDebtShare = CollectDebtShare::where('contract_code', $collectDebtRequest->contract_code)->first();
  
      if ($paramInsert['payment_method_code'] == 'MPOS') {
        $paramInsert['other_data'] = json_encode([
          [
            'type' => 'CONTRACT',
            'time_modified' => time(),
            'data' => [
              'profile_id'          => $collectDebtShare->profile_id,
              'contract_code'       => $collectDebtShare->contract_code,
              'contract_time_start' => $collectDebtShare->contract_time_start,
              'contract_type'       => $collectDebtShare->getContractTypev1(),
              'contract_cycle'      => $collectDebtShare->contract_cycle,
              'contract_intervals'  => $collectDebtShare->contract_intervals,
              'amount'              => $collectDebtShare->amount,
              'contract_time_end'   => $collectDebtShare->contract_time_end,
              'other_data'          => '{}',
              'description'         => 'Hợp đồng v4',
              'id'                  => $collectDebtShare->getContractId()
            ],
            'note' => 'Thông tin hợp đồng'
          ],
  
          [
            'type' => 'REQUEST',
            'time_modified' => time(),
            'data' => [
              StandardizedDataFilter::getStandardizedDataFilter('REQUEST', $collectDebtRequest)
            ],
            'note' => 'Thông tin yêu cầu'
          ]
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
      }
      
			$paramInsert['contract_code'] = $collectDebtShare->contract_code;
      $collectDebtPartner = CollectDebtPartner::forceCreate($paramInsert);
      throw_if(!$collectDebtPartner, new Exception('Không thể tạo công nợ đối tác báo muộn'));
  
      DB::commit();
      return $collectDebtPartner;
    }catch(\Throwable $th) {
      DB::rollBack();
      $message = parseErr([
        'LogID' => $request->json('api_request_id'),
        'Message' => 'Partner báo muộn',
        'Error' => traceErr($th)
      ]);

      TelegramAlert::sendNotifyPartner($message);
    }
  }
} // End class
