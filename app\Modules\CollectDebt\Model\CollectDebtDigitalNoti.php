<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;

class CollectDebtDigitalNoti extends Model
{
	const STT_MOI_TAO = 1;
	const STT_DANG_XU_LY = 2;
	const STT_DA_BUILD_PARAM = 3;
	const STT_DANG_GUI_NOTI = 4;
	const STT_DA_GUI_NOTI = 5;
	const STT_GUI_NOTI_THAT_BAI = 6;

	protected $table   = 'debt_recovery_digital_noti';
	public $timestamps = false;
	protected $appends = [];
	protected $guarded = [];

	public static function buildNotiData(string $title, string $body, string $content, string $subCategoryCode, $mposMcId, $subTitle='') {
		$otherData = [];
		
		if (!empty($subTitle)) {
			$otherData['sub_title'] = $subTitle;
		}

		$cleanHtml = preg_replace('/\s+/', ' ', $content);
		$cleanHtml = trim($cleanHtml);
		$cleanHtml = str_replace(["\r\n", "\r", "\n"], '<br>', $cleanHtml);

		$notifyData = [
			"partner_code" => 'AppsDigital',
			'fn' => 'Notification_createNotify',
			'params' => [
				'channel' => 'DIGITAL',
				'list_merchant_id' => [$mposMcId],
				'for_sub' => 2,
				'sub_category_code' => $subCategoryCode,
				'type' => 'push',
				'sent_to' => 'ONE',
				'os' => 'all',
				'title' => $title,
				'body' => $body,
				'content' => $cleanHtml,
				'time_actived' => now()->timestamp,
				'other_data' => json_encode($otherData),
				'send_now' => true
			],
			'time_request' => now()->timestamp
		];

		return $notifyData;
	}
} // End class
