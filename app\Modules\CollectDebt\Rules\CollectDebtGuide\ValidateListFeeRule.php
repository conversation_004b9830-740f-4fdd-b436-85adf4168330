<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtGuide;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Contracts\Validation\Rule;

class ValidateListFeeRule implements Rule
{
    private string $__errorMessage = '';

    private int $__contractType;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(int $contractType)
    {
      $this->__contractType = $contractType;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($listFeeKey, $listFeeValue)
    {
      $cauHinhPhiQuaHan = collect($listFeeValue)->where('type', CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN)->first();
    
      if (!$cauHinhPhiQuaHan) {
        $this->__errorMessage = 'HĐ không có cấu hình phí quá hạn';
        return false;
      }

			if ($cauHinhPhiQuaHan) {
				// phai co gia tri cau hinh de tranh loi sinh lich
				if ($cauHinhPhiQuaHan['percent_fee'] == 0 && $cauHinhPhiQuaHan['flat_fee'] == 0) {
					$this->__errorMessage = 'Cấu hình phí không đúng. Phần trăm phí QH hoặc phí cứng phải lớn hơn 0';
					return false;
				}
			}

      if ($this->__contractType == CollectDebtEnum::GUIDE_HD_TRICH_KY) {
        $cauHinhPhiChamKy = collect($listFeeValue)->where('type', CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY)->first();
        
				if (!$cauHinhPhiChamKy) {
          $this->__errorMessage = 'HĐ không có cấu hình phí chậm kỳ';
          return false;
        }

				if ($cauHinhPhiChamKy) {
					// phai co gia tri cau hinh de tranh loi sinh lich
					if ($cauHinhPhiChamKy['percent_fee'] == 0 && $cauHinhPhiChamKy['flat_fee'] == 0) {
						$this->__errorMessage = 'Cấu hình phí không đúng. Phần trăm phí CK hoặc phí cứng phải lớn hơn 0';
						return false;
					}
				}
      }

			$cauHinhPhiPhatTraCham = collect($listFeeValue)->where('type', CollectDebtEnum::GUIDE_LOAI_PHI_PHAT_TRA_CHAM)->first();
			
			if (!$cauHinhPhiPhatTraCham) {
				$this->__errorMessage = 'HĐ không có cấu hình phí phạt trả chậm';
				return false;
			}

			if (!empty($cauHinhPhiPhatTraCham['percent_fee']) && request()->json('data.partner_code') == 'TNEX') {
				$this->__errorMessage = 'Đối với đối tác TNEX, không được sét cấu hình phí phạt trả chậm';
				return false;
			}

			if (!empty($cauHinhPhiPhatTraCham['percent_fee']) && request()->json('data.partner_code') != 'TNEX') {
				if ($cauHinhPhiPhatTraCham['percent_fee'] < 3) {
					$this->__errorMessage = 'Phí phạt trả chậm tối thiểu 3%';
					return false;
				}
			}

      return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
      return $this->__errorMessage;
    }
}
