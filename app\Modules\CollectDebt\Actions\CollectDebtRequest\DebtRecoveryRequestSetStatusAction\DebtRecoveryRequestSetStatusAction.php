<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction;

use Exception;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestSetStatusRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction\DuyetYeuCauGiamPhiB1SA;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction\DuyetYeuCauTrichNgaySA;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction\DuyetYeuCauTrichTayQuaDoiTacSA;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction\DuyetYeuCauTrichTayKhongQuaDoiTacSA;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAction\DebtRecoveryRequestActionCreateAction\DebtRecoveryRequestActionCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction\DanhDauLaChuaProcessSauKhiHuyLenhGiamPhiSA;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\DebtRecoveryRequestFindRawQuerySubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\TaoCongNoKhiLaTrichNgayTask;
use App\Modules\CollectDebt\Enums\CacheEnum;

class DebtRecoveryRequestSetStatusAction
{
  private string $__apiMessage = '';

  public function run(DebtRecoveryRequestSetStatusRequest $request): CollectDebtRequest
  {
    $whereRaw = sprintf('id = %s', $request->json('data.id'));
    $collectDebtRequest = app(DebtRecoveryRequestFindRawQuerySubAction::class)->run($whereRaw);
    $flagUpdate = false;

    if ($collectDebtRequest->isManualDebt() && $request->json('data.status') != CollectDebtEnum::REQUEST_STT_TU_CHOI) {
      // Trích tay giảm phí
      if ($collectDebtRequest->isTrichTayGiamPhi()) {
        return app(DuyetYeuCauGiamPhiB1SA::class)->run($collectDebtRequest, $request);
      }

			// Nếu là duyệt lệnh trích tay, thì phải check thêm điều kiện là vẫn nằm trong thời gian dừng job
			$contractCode = $collectDebtRequest->contract_code;
			$isPausedJobTuDong = CollectDebtConfigAuto::isPauseContractJob($contractCode);
			
			if (!$isPausedJobTuDong) {
				throw new Exception('Đã hết thời gian dừng job tự động. Từ chối duyệt lệnh trích tay. Bạn cần hủy lệnh trích tay này đi để tính lại dư nợ cho đúng');
			}

			$lenhTrichDangSau = CollectDebtRequest::query()
																						->where('contract_code', $contractCode)
																						->where('id', '>', $collectDebtRequest->id)
																						->first();
			if ($lenhTrichDangSau) {
				throw new Exception('Hệ thống đã phát hiện ra có lệnh trích khác chạy ngay sau lệnh trích tay này. Bạn bắt buộc phải hủy lệnh trích này đi để hệ thống không tính sai dư nợ');
			}

      // Trích khác
      if (!$collectDebtRequest->isTrichTayGiamPhi()) {
        // Trích ngay
        if ($collectDebtRequest->isTrichNgay() == 'YES') {
          $trichNgay = app(DuyetYeuCauTrichNgaySA::class)->run($collectDebtRequest, $request);
					CacheEnum::putToListTrichNgay($collectDebtRequest->partner_request_id, $collectDebtRequest->time_expired);
					return $trichNgay;
        }

        // Trích thủ công
        if ($collectDebtRequest->isTrichNgay() == 'NO') {
          if (!$collectDebtRequest->isYeuCauPhaiGuiQuaKenhDoiTac()) {
            return app(DuyetYeuCauTrichTayKhongQuaDoiTacSA::class)->run($collectDebtRequest, $request);
          }

          if ($collectDebtRequest->isYeuCauPhaiGuiQuaKenhDoiTac()) {
            return app(DuyetYeuCauTrichTayQuaDoiTacSA::class)->run($collectDebtRequest, $request);
          }
        }
      }
    } // End trích tay


    if ($collectDebtRequest->isNew() && $request->json('data.status') == CollectDebtEnum::REQUEST_STT_DA_DUYET) {
      $this->__apiMessage = 'Đã duyệt yêu cầu trích tay';

      $collectDebtRequest->forceFill([
        'status' => CollectDebtEnum::REQUEST_STT_DA_DUYET,
        'approved_by' => $request->json('data.user_request_id'),
        'time_approved' => time()
      ])->update();

      $flagUpdate = true;
    }

    // Từ chối yêu cầu
    if (
      ($collectDebtRequest->isNew() || $collectDebtRequest->isTrichTayGiamPhiVaChuaDuyetBuoc1() || $collectDebtRequest->isTrichTayGiamPhiVaChuaDuyetBuoc2())
      && $request->json('data.status') == CollectDebtEnum::REQUEST_STT_TU_CHOI) {
      $this->__apiMessage = 'Từ chối yêu cầu thành công';

      $latestOtherData = $collectDebtRequest->putOtherData([
        'type' => 'CANCEL',
        'time_modified' => time(),
        'note' => $request->json('data.description', 'Từ chối yêu cầu'),
        'data' => [
          'user' => $request->json('data.user_request_id')
        ]
      ]);

      $collectDebtRequest->forceFill([
        'status' => CollectDebtEnum::REQUEST_STT_TU_CHOI,
        'canceled_by' => $request->json('data.user_request_id'),
        'time_canceled' => time(),
        'other_data' => $latestOtherData
      ])->update();

      // Từ chối giảm phí
      if ($collectDebtRequest->isHinhThucTrich() == "GIAM_PHI") {
        $paramAction = [
          'type' => CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI,
          'request_id' => $collectDebtRequest->id,
          'action_code' => CollectDebtEnum::REQUEST_ACTION_CODE_CANCELED,
          'created_by' => $request->json('data.user_request_id'),
          'description' => $request->json('data.description') ?? "",
          'other_data' => '{}',
          'time_created' => time(),
        ];
        $action = app(DebtRecoveryRequestActionCreateAction::class)->run($paramAction);
				app(DanhDauLaChuaProcessSauKhiHuyLenhGiamPhiSA::class)->run($action);
        $this->__apiMessage = 'Đã từ chối đề xuất giảm phí';
      }

      $collectDebtRequest->refresh();
      $collectDebtRequest->__apiMessage = $this->__apiMessage;
      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isApproved() && $request->json('data.status') == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH) {
      throw_if(!$collectDebtRequest->canMarkRequestAsCompleted(), new Exception('Không đủ điều kiện để đánh dấu yêu cầu là hoàn thành'));
      $collectDebtRequest->forceFill([
        'status' => CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
        'completed_by' => $request->json('data.user_request_id'),
        'time_completed' => time()
      ])->update();

      $flagUpdate = true;

      $this->__apiMessage = 'Đã đánh dấu yêu cầu là hoàn thành';
    }

    if ($collectDebtRequest->isApproved() && $request->json('data.status') == CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA) {
      $collectDebtRequest->forceFill([
        'status' => CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA,
        'updated_by' => $request->json('data.user_request_id'),
        'time_updated' => time()
      ])->update();

      $flagUpdate = true;
    }

    if ($collectDebtRequest->isNeedCompleteAndReCheck() && $request->json('data.status') == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH) {
      throw_if(!$collectDebtRequest->canMarkRequestAsCompleted(), new Exception('Không đủ điều kiện để đánh dấu yêu cầu là hoàn thành'));
      $collectDebtRequest->forceFill([
        'status' => CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
        'completed_by' => $request->json('data.user_request_id'),
        'time_completed' => time()
      ])->update();

      $flagUpdate = true;
    }

    $collectDebtRequest->refresh();
    $collectDebtRequest->__apiMessage = $this->__apiMessage;
    return $collectDebtRequest;
  }
} // End class