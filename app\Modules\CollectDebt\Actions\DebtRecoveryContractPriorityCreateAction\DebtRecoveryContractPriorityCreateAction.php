<?php

namespace App\Modules\CollectDebt\Actions\DebtRecoveryContractPriorityCreateAction;

use App\Modules\CollectDebt\Model\CollectDebtContractPriority;
use App\Modules\CollectDebt\Requests\v1\CollectDebtContractPriority\DebtRecoveryContractPriorityCreateRequest;

class DebtRecoveryContractPriorityCreateAction
{
	/**
	 * Đoạn này phải foreach để lấy được ids bản ghi ưu tiên, sau bên cashIn nạp tiền fail thì sẽ xóa được
	 *
	 * @param DebtRecoveryContractPriorityCreateRequest $request
	 * @return void
	 */
	public function run(DebtRecoveryContractPriorityCreateRequest $request)
	{
		$idInserted = [];
		$paramInsert = $request->json('data');
		
		foreach ($paramInsert as $p) {
			$collectDebtContractPriority = CollectDebtContractPriority::query()->forceCreate($p);
			$idInserted[] = $collectDebtContractPriority->id;
		}

		return $idInserted;
	}
} // End class