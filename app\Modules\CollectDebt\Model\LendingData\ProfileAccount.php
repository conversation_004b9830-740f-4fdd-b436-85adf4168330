<?php

namespace App\Modules\CollectDebt\Model\LendingData;

use Illuminate\Database\Eloquent\Model;

class ProfileAccount extends Model
{
	const STATUS_TAI_KHOAN_DANG_HOAT_DONG = 1;
	const STATUS_TAI_KHOAN_DANG_BI_KHOA = 2;

	protected $connection = 'lending_data';

  protected $table = 'profile_accounts';

  public $timestamps = false;
  protected $appends = [];
  protected $guarded = [];
  
	public function getSoDuKhaDung(): float {
		return $this->balance - $this->freezing_balance;
	}

	public function isSoDuKhaDungCoTheThanhToanYeuCau(float $soTienMuonTrichNo): bool {
		$soDuKhaDung = $this->getSoDuKhaDung();
		return $soDuKhaDung > 0 && $soDuKhaDung >= $soTienMuonTrichNo;
	}

	/**
	 * Điều kiện để tạo yêu cầu trích nợ qua ví:
	 * 		1. số dư khả dụng đủ thanh toán cho yêu cầu
	 * 		2. không có số tiền đóng băng (đang ko trích cho HĐ nào cả)
	 */
	public function isCoTheTaoYeuCauTrichQuaVi(float $soTienMuonTrichNo) {
		return $this->isSoDuKhaDungCoTheThanhToanYeuCau($soTienMuonTrichNo) && empty($this->freezing_balance);
	}

	public function scopeWhereTaiKhoanDangHoatDong($query) {
		return $query->where('status', self::STATUS_TAI_KHOAN_DANG_HOAT_DONG)
								 ->whereRaw('balance - freezing_balance > 0');
	}
} // End class
