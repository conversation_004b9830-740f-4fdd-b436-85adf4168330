<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\TrichNgayTask;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class DongBoTaoLichThuPhiQuaHanTrichNgayTask
{

  public function run(CollectDebtGuide $collectDebtGuide, $lichThuGoc=null, float $soPhiQuaHan=0)
  {
    $rundate   = now()->addDay()->format('Ymd');
    $timeStart = now()->startOfDay()->timestamp;
    $timeEnd   = now()->setTime(22, 0)->timestamp;
    $masterId  = 0;
    $cycleNumber = 0;

    $collectDebtShare = CollectDebtShare::where('contract_code', $collectDebtGuide->contract_code)->first();

    if ($lichThuGoc) {
      $rundate = $lichThuGoc->rundate;
      $timeStart = $lichThuGoc->time_start;
      $timeEnd = $lichThuGoc->time_end;
      $masterId = $lichThuGoc->master_id;
      $cycleNumber = $lichThuGoc->cycle_number;
    }

    $paramPlans = [
      'master_id'            => $masterId,
      'cycle_number'         => $cycleNumber,
      'profile_id'           => $collectDebtGuide->profile_id,
      'contract_code'        => $collectDebtGuide->contract_code,
      'contract_type'        => $collectDebtGuide->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
      'debit_begin'          => $soPhiQuaHan,
      'debit_end'            => 0,
      'rundate'              => $rundate,
      'time_start'           => $timeStart,
      'time_end'             => $timeEnd,
      'amount_period_debit'  => $soPhiQuaHan,
      'request_amount_debit' => $soPhiQuaHan,
      'success_amount_debit' => 0,
      'is_settlement'        => 1,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => Helper::getCronJobUser(),
      'time_created'         => time(),
      'other_data'           => json_encode([
        [
          'type' => 'OTHER',
          'data' => [
            'fee_config' => $collectDebtShare->getFeeByType(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN),
            'information_code' => 'SINH_PHI_QUA_HAN',
            'request_created_channel' => ''
          ],
          'time_modified' => time(),
          'note' => 'Thu phí quá hạn'
        ]
      ])
    ];

    $plan = CollectDebtSchedule::forceCreate($paramPlans);
    return $plan;
  }
} // End class