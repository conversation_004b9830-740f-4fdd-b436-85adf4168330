<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtWaitProcess;

class KiemTraVaDanhDauHuyLenhMposNeuCoTask
{
	public function run(string $contractCode, CollectDebtRequest $collectDebtRequest)
	{
		mylog(['kiem tra xem co lenh thu mpos nao dang chay khong' => 'ok']);

		$yeuCauMposDangChay = CollectDebtRequest::query()
			->where('contract_code', $contractCode)
			->where('payment_method_code', 'MPOS')
			->where('time_expired', '>', now()->timestamp)
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
			->first();
		
		if (!$yeuCauMposDangChay) {
			mylog(['khong co thong tin yeu cau thu mpos dang chay' => 'ok']);
			return null;
		}

		mylog([
			'dang co lenh thu mpos dang chay' => 'yes',
			'contract_code' => $yeuCauMposDangChay->contract_code,
			'ma_yc' => $yeuCauMposDangChay->partner_request_id
		]);

		// thuc hien danh dau ve trang thai cuoi
		$paramCreateProcess = [
			'source_id' => $collectDebtRequest->id,
			'obj_id' => $yeuCauMposDangChay->id,
			'obj_type' => $yeuCauMposDangChay->getTable(),
			'action_code' => 'CANCEL',
			'time_created' => now()->timestamp
		];

		mylog(['param tao process' => $paramCreateProcess]);
		
		$createWaitProcess = CollectDebtWaitProcess::query()->forceCreate($paramCreateProcess);

		if (!$createWaitProcess) {
			mylog(['loi tao process' => $createWaitProcess]);
		}

		return $createWaitProcess;
	}
} // End class
