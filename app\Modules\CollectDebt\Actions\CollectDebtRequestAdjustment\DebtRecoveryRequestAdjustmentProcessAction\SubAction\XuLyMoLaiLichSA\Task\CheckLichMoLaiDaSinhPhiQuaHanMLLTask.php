<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class CheckLichMoLaiDaSinhPhiQuaHanMLLTask
{
  public function run(CollectDebtSchedule $lichDangMoLai): bool
  {
    $listLichThuPhi = CollectDebtSchedule::query()
																				 ->where('contract_code', $lichDangMoLai->contract_code)
																				 ->where('type', CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI)
																				 ->where('rundate', date('Ymd'))
																				 ->get();

		if ($listLichThuPhi->isEmpty()) {
			return false;
		}

    return $listLichThuPhi->contains(function (CollectDebtSchedule $plan) {
      return $plan->isLichThuPhiQuaHan();
    });
  }
}
