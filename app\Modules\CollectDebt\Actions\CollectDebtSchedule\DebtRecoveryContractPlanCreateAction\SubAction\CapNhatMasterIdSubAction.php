<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CapNhatMasterIdSubAction
{
  public function run(Collection $plans)
  {
    $sql = "UPDATE `debt_recovery_contract_plan` SET `master_id` = CASE ";

    foreach ($plans as $plan) {
      $sql .= sprintf(" WHEN id = %s THEN %s ", $plan->id, $plan->id);
    }

    $sql .= sprintf("
      ELSE `master_id`
        END
      WHERE id  in (%s)
    ", $plans->implode('id', ','));

    return DB::update($sql);
  }
} // End class