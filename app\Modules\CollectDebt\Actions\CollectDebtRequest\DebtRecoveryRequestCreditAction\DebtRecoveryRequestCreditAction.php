<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreditAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreditRequest;

use function PHPUnit\Framework\throwException;

class DebtRecoveryRequestCreditAction
{
	/**
	 * Cộng tiền từ partner vào trong tổng hợp 
	 * Hàm này mục đích là để cập nhật yêu cầu về trạng thái cuối
	 * 
	 * @param DebtRecoveryRequestCreditRequest $request [Y<PERSON>u cầu cần xử lý]
	 *
	 * @return CollectDebtRequest
	 */
	public function run(DebtRecoveryRequestCreditRequest $request): CollectDebtRequest
	{
		mylog([
			'Goi vao action ket thuc yeu cau' => 'Ok',
			'Partner Request Id la:' => $request->json('data.partner_request_id')
		]);

		$collectDebtRequest = CollectDebtRequest::query()->where('partner_request_id', $request->json('data.partner_request_id'))->first();
		throw_if(!$collectDebtRequest, new Exception('YC khong ton tai'));

		mylog(['Yêu cầu cần thực hiện cập nhật Credit là:' => $collectDebtRequest->id]);

		if ($collectDebtRequest->isFinalPaymentStatus()) {
			mylog(['Yeu cau da ve trang thai THANH TOAN CUOI CUNG' => $collectDebtRequest->status_payment]);
			throw new Exception('Yeu cau da ve trang thai cuoi');

			return $collectDebtRequest;
		}

		if ($collectDebtRequest->status == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH) {
			mylog(['Yeu cau da ve trang thai HOAN THANH CUOI CUNG' => $collectDebtRequest->status]);
			throw new Exception('Yeu cau da ve trang thai cuoi');
		}

		if ($collectDebtRequest->status == CollectDebtEnum::REQUEST_STT_TU_CHOI) {
			mylog(['Yeu cau da ve trang thai TU CHOI CUOI CUNG' => $collectDebtRequest->status]);
			throw new Exception('Yeu cau da ve trang thai cuoi');
		}

		// Số tiền trích thành công
		$soTienMangDungDeThanhToan = $request->json('data.amount_payment');

		// Kiểm tra chéo xem (MPOS) có bị sai thông tin tiền bạc không
		if ($collectDebtRequest->payment_method_code == 'MPOS') {
			$collectDebtPartner = CollectDebtPartner::query()
																							->where('partner_request_id', $request->json('data.partner_request_id'))
																							->where('payment_method_code', 'MPOS')
																							->first();

			throw_if(!$collectDebtPartner, new Exception('Khong tim thay partner de cong tien'));

			mylog([
				'So tien cong vao yeu cau la: ' => $soTienMangDungDeThanhToan,
				'So tien kha dung cua partner la:' => $collectDebtPartner->getAmountBalance(),
				'Hai loai so tien nay co dang bang nhau khong? ' => $soTienMangDungDeThanhToan == $collectDebtPartner->getAmountBalance() ? 'CO' : 'KHONG'
			]);

			throw_if(
				$collectDebtPartner->getAmountBalance() != $soTienMangDungDeThanhToan,
				new Exception('So tien partner dang bi chenh lech')
			);
		}
	
		$paramUpdate = [
			'amount_receiver'        => DB::raw("amount_receiver + " . $soTienMangDungDeThanhToan),
			'amount_payment'				 => DB::raw("amount_payment + " . $soTienMangDungDeThanhToan),
			'fee'                    => $request->json('data.fee'),
			'time_receivered'        => $request->json('data.time_receivered', time()),
			'checked_by'             => $request->json('data.checked_by', Helper::getCronJobUser()),
			'status_payment'         => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA,
			'time_completed'         => time(),
			'completed_by'           => Helper::getCronJobUser()
		];

		if ($collectDebtRequest->isManualDebt() && $collectDebtRequest->isTrichNgay() == 'YES') {
			$paramUpdate['amount_receiver'] = $soTienMangDungDeThanhToan;
			$paramUpdate['amount_payment'] = $soTienMangDungDeThanhToan;
		}

		// Không đc dùng model, vì đã dùng DB::raw()
		$updatedCongTien = CollectDebtRequest::where('id', $collectDebtRequest->id)->update($paramUpdate);
		
		if ( !$updatedCongTien ) {
			$error = [
				'Loi update cong tien' => 'Ok',
				'Yeu cau cong tien' => $collectDebtRequest->id,
				'Partner thuc hien' => $collectDebtPartner
			];

			mylog($error);
			$errorMessage = parseErr($error);
			throw new Exception($errorMessage);
		}
		
		return $collectDebtRequest->refresh();
	}
}  // End class