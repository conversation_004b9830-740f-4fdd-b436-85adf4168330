<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtContractPriority\DebtRecoveryContractPriorityDeleteAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtContractPriority;
use App\Modules\CollectDebt\Requests\v1\CollectDebtContractPriority\DebtRecoveryContractPriorityDeleteRequest;
use Exception;

class DebtRecoveryContractPriorityDeleteAction
{
	
	public function run(DebtRecoveryContractPriorityDeleteRequest $request)
	{
		$ids = $request->json('data.ids');
		$contractPriorities = CollectDebtContractPriority::query()->whereIn('id', $ids)->get();
		
		throw_if(
			$contractPriorities->count() != count($ids),
			new Exception('số lượng bản ghi không chính xác')
		);

		$isAllHopDongPhaiLaTrangThaiMoi = $contractPriorities->every(function (CollectDebtContractPriority $contractPriority) {
			return $contractPriority->status == CollectDebtEnum::CONTRACT_PRIORITY_STT_MOI_TAO;
		});

		throw_if(!$isAllHopDongPhaiLaTrangThaiMoi, new Exception('Toàn bộ HĐ ưu tiên phải có trạng thái mới'));

		CollectDebtContractPriority::query()->whereIn('id', $ids)->delete();
		return $ids;
	}
} // End class