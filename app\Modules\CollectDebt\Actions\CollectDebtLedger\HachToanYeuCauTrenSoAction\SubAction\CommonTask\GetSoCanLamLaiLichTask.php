<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\GomNhomYeuCauVaoLichThuTask;

class GetSoCanLamLaiLichTask
{
	private array $__soDaHachToan = [];

	public function initHachToan()
	{
		mylog(['enter thuc hien hach toan' => 'ok']);

		for ($i = 1; $i <= 10; $i++) {
			try {
				$collectDebtRequest = $this->run();

				if ($collectDebtRequest == 'EMPTY') {
					$this->__soDaHachToan[] = 'Khong co so nao can thuc hien hach toan';
					break;
				}

				if ($collectDebtRequest && $collectDebtRequest->id) {
					$this->__soDaHachToan[] = $collectDebtRequest->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi hach toan' => Helper::traceError($th)]);
				@TelegramAlert::sendAccouting(Helper::traceError($th));
				continue;
			} finally {
				sleep(1);
			}
		}

		return $this->__soDaHachToan;
	}

  public function run()
  {
    $collectDebtLedger = CollectDebtLedger::query()
																					->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY)
																					->limit(1)
																					->first();

    if ($collectDebtLedger->isEmpty()) {
			mylog(['[EMPTY]' => 'khong co thong tin so can hach toan']);
			return 'EMPTY';
		}
    
		$updatedLenDangXuLy = CollectDebtLedger::query()
																					 ->where('id', $collectDebtLedger->id)
																					 ->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY)
																					 ->update([
																						'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN
																					 ]);
    
		if (!$updatedLenDangXuLy) {
			mylog(['[LOI]' => 'khong the cap nhat so thang DANG HACH TOAN']);
			throw new Exception('khong the cap nhat so thang DANG HACH TOAN');
		}

		$collectDebtLedger->refresh();
		if ($collectDebtLedger->status != CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN) {
			mylog(['[LOI]' => 'du lieu chua o trang thai dang hach toan, tu choi xu ly']);
			throw new Exception('du lieu chua o trang thai dang hach toan, tu choi xu ly');
		}

		mylog(['[BAN GHI SO]' => $collectDebtLedger]);

		$collectDebtLedger->schedules = $this->getLichThuCuaSo($collectDebtLedger);
		
		return $collectDebtLedger;
  }

	public function getLichThuCuaSo(CollectDebtLedger $collectDebtLedger): Collection
	{
		$planIds = $collectDebtLedger->getPlanIds();
		
		if (empty($planIds)) {
			return Collection::make();
		}

		$plans = app(GetLichThuByIdsSubAction::class)->run($planIds);

		mylog(['[DANH SACH SO CUA LICH]' => $plans]);
		
		throw_if($plans->isEmpty(), new Exception('Khong tim thay lich de hach toan'));

		return $plans;
	}
} // End class