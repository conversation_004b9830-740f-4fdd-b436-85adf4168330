<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;

class GetLichThuByIdsSubAction
{
  public function run(string $planIds = '')
  {
    $plans = CollectDebtSchedule::whereIn('id', explode(',', $planIds))
                                ->orderByRaw("FIELD(id, $planIds)")
                                ->get();

    if ($plans->isEmpty()) {
      throw new Exception('Không có lịch thu phù hợp');
    }

    return $plans;
  }
}
