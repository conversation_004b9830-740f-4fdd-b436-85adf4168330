<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySetByAccounting;

use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\GetPlanSummaryHandleAfterAccoutingAction\GetPlanSummaryHandleAfterAccoutingAction;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummarySetByAccountingRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySetByAccounting\SubAction\DebtRecoverySummaryFindWhereRawSubAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Carbon\Carbon;
use Exception;

class DebtRecoverySummarySetByAccounting {

    public function run(DebtRecoverySummarySetByAccountingRequest $request) {
        $contractCode = $request->json('data.contract_code');
        $whereRaw = sprintf('contract_code = "%s"', $contractCode);
        $debtRecoverySummary = app(DebtRecoverySummaryFindWhereRawSubAction::class)->run($whereRaw, true);
       	

        $inputs = $this->__mergeParams($request->json('data'), $debtRecoverySummary);
        $result = $debtRecoverySummary->forceFill($inputs)->update();
				
				if (!$result) {
					mylog(['Tinh tien vao tong hop that bai' => $result]);
					throw new Exception('Tinh tien vao tong hop that bai');
				}

        return $debtRecoverySummary->refresh();
    }

    protected function __mergeParams($request, $data) {
        $inputs = [
            'time_updated' => time(),
            'time_accounting' => time(),
						'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO
        ];
        $fee_paid = 0;
        $amount_paid = 0;
        $total_fee_paid = 0;
        if (isset($request['total_amount_paid']) && $request['total_amount_paid']) {
            $amount_paid = $request['total_amount_paid'];
            $inputs['total_amount_paid'] = $data->total_amount_paid + $request['total_amount_paid'];
        }
        if (isset($request['total_fee_paid']) && $request['total_fee_paid']) {
            $inputs['total_fee_paid'] = $data->total_fee_paid + $request['total_fee_paid'];
            $total_fee_paid = $request['total_fee_paid'];
        }
        if (isset($request['fee_overdue_paid']) && $request['fee_overdue_paid']) {
            $fee_paid += $request['fee_overdue_paid'];
            $inputs['fee_overdue_paid'] = $data->fee_overdue_paid + $request['fee_overdue_paid'];
        }
        if (isset($request['fee_overdue_cycle_paid']) && $request['fee_overdue_cycle_paid']) {
            $fee_paid += $request['fee_overdue_cycle_paid'];
            $inputs['fee_overdue_cycle_paid'] = $data->fee_overdue_cycle_paid + $request['fee_overdue_cycle_paid'];
        }
        if (isset($request['fee_overdue_reduction']) && $request['fee_overdue_reduction']) {
            $inputs['fee_overdue_reduction'] = $data->fee_overdue_reduction + $request['fee_overdue_reduction'];
        }
        if (isset($request['fee_overdue_cycle_reduction']) && $request['fee_overdue_cycle_reduction']) {
            $inputs['fee_overdue_cycle_reduction'] = $data->fee_overdue_cycle_reduction + $request['fee_overdue_cycle_reduction'];
        }
        if (isset($request['total_amount_refund']) && isset($request['amount_refunding'])) {
            if ($request['amount_refunding'] > 0 && $request['total_amount_refund'] > 0) {
                throw new Exception('Thông tin ko chính xác không thể có TH vừa duyệt hoàn vừa tạo yêu cầu hoàn trên một yêu cầu hoàn');
            }
        }
        if (isset($request['total_amount_refund']) && $request['total_amount_refund']) {
            $inputs['total_amount_refund'] = $data->total_amount_refund + $request['total_amount_refund'];
            $inputs['amount_refunding'] = $data->amount_refunding - $request['total_amount_refund'];
        }
        if (isset($request['fee_overdue_cycle']) && $request['fee_overdue_cycle'] > 0) {
            $inputs['is_over_cycle'] = CollectDebtEnum::SUMMARY_CO_CHAM_KY;
            $inputs['number_over_cycle'] = $data->number_over_cycle + 1;
            $inputs['fee_overdue_cycle'] = $data->fee_overdue_cycle + $request['fee_overdue_cycle'];

            $inputs['is_send_mail_slow_cycle'] = CollectDebtEnum::SUMMARY_CO_GUI_MAIL_CHAM_KY;
            $inputs['is_send_mail_overdue'] = CollectDebtEnum::SUMMARY_KHONG_GUI_MAIL_QUA_HAN;
        }
        if (isset($request['fee_overdue']) && $request['fee_overdue'] > 0) {
            $inputs['number_day_overdue'] = $data->number_day_overdue + 1;
            $inputs['is_overdue'] = CollectDebtEnum::SUMMARY_CO_QUA_HAN;
            $inputs['fee_overdue'] = $data->fee_overdue + $request['fee_overdue'];

            $inputs['is_send_mail_slow_cycle'] = CollectDebtEnum::SUMMARY_KHONG_GUI_MAIL_CHAM_KY;
            $inputs['is_send_mail_overdue'] = CollectDebtEnum::SUMMARY_CO_GUI_MAIL_QUA_HAN;
            $inputs['is_send_noti_overdue'] = CollectDebtEnum::SUMMARY_CO_GUI_NOTI_QUA_HAN;
        }
        if (isset($request['amount_refunding']) && $request['amount_refunding']) {
            $inputs['amount_refunding'] = $data->amount_refunding + $request['amount_refunding'];
        }
        if (isset($request['total_amount_receiver']) && $request['total_amount_receiver']) {
            $inputs['total_amount_receiver'] = $data->total_amount_receiver + $request['total_amount_receiver'];
        }
        if (isset($request['total_amount_excess_revenue']) && $request['total_amount_excess_revenue']) {
            $inputs['total_amount_excess_revenue'] = $data->total_amount_excess_revenue + $request['total_amount_excess_revenue'];
        }
        if (isset($request['fee_overdue']) && $request['fee_overdue'] < 0) {
            $inputs['fee_overdue'] = $data->fee_overdue + $request['fee_overdue'];
            if (isset($request['is_overdue']) && $request['is_overdue']) {
                $inputs['is_overdue'] = $request['is_overdue'];
            }
            if (isset($request['number_day_overdue'])) {
                $inputs['number_day_overdue'] = $request['number_day_overdue'];
            }
        }
        if (isset($request['fee_overdue_cycle']) && $request['fee_overdue_cycle'] < 0) {
            $inputs['fee_overdue_cycle'] = $data->fee_overdue_cycle + $request['fee_overdue_cycle'];
            if (isset($request['is_over_cycle']) && $request['is_over_cycle']) {
                $inputs['is_over_cycle'] = $request['is_over_cycle'];
            }
            if (isset($request['number_over_cycle'])) {
                $inputs['number_over_cycle'] = $request['number_over_cycle'];
            }
        }

				
        /**
				 * [Update 24.09.2024] - HĐ trong hạn hoặc quá hạn cấp 2 thì vẫn call vào 
				 * GetPlanSummaryHandleAfterAccoutingAction để tính toán
				 * 
				 * Tuy nhiên đã là nợ xấu thì thôi không call vào làm gì vì nợ xấu lâu ngày dẫn đến query nặng
				 */
				if ($data->is_overdue == CollectDebtEnum::SUMMARY_CO_QUA_HAN && $data->number_day_overdue > 15) {
					mylog([
						'Qua han nhieu ngay' => 'khong goi de tinh toan nua',
						'HopDong' => $data->contract_code
					]);
					
					return $inputs;
				}

        $other_data = $data->other_data ? json_decode($data->other_data, true) : [];
        $planData = app(GetPlanSummaryHandleAfterAccoutingAction::class)->run($request['contract_code']);
        throw_if(!$planData, new Exception('Lịch thu không tồn tại'));

        
        $typePLan = [
            'type' => 'PLAN',
            'time_modified' => time(),
            'data' => $planData,
            'note' => '',
        ];

        if (!empty($other_data)) {
            $other_data = collect($other_data)->map(function ($item) use ($typePLan) {
                        if ($item['type'] == 'PLAN') {
                            $item = $typePLan;
                        }
                        return $item;
                    })->toArray();
        } else {
            $other_data[] = $typePLan;
        }


        $inputs['other_data'] = json_encode($other_data, JSON_UNESCAPED_UNICODE);
        return $inputs;
    }

}
