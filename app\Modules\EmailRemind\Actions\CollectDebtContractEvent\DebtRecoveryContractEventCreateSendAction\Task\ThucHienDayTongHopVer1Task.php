<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task;

use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySyncContractAction\DebtRecoverySummarySyncContractAction;

class ThucHienDayTongHopVer1Task
{
	public function run(CollectDebtContractEvent $collectDebtContractEvent)
	{
		$dataResponse = [];

		$otherData = json_decode($collectDebtContractEvent->other_data, true);
		$summary = isset($otherData['summary']) ? $otherData['summary'] : [];

		if (empty($summary)) {
			return $dataResponse;
		}
		
		$summary['excess_handler'] = [
			'refund' => [
				'amount_refund' => $summary['total_amount_excess_refund']
			],
			'repayment' => [
				'amount_repayment' => $summary['total_amount_repayment_debt'],
				'list_contract' =>  []
			]
		];

		// Thanh toan cho HD khac
		if (!empty($summary['total_amount_repayment_debt'])) {
			$listHopDongNhanTien = app(GetHopDongNhanTienTuNguonThuThuaTask::class)->run($summary['contract_code']);
			if (isset($listHopDongNhanTien['data']['listMaHopDongNhanTienCashIn'])) {
				$summary['excess_handler']['repayment']['list_contract'] = $listHopDongNhanTien['data']['listMaHopDongNhanTienCashIn'];
			}
		}

		$otherData['summary'] = $summary;
		$r = @CollectDebtContractEvent::query()->where('id', $collectDebtContractEvent->id)
																		 ->update([
																			'other_data' => json_encode($otherData),
																			'time_updated' => now()->timestamp
																		 ]);

	
		$putSummaryReturn = app(DebtRecoverySummarySyncContractAction::class)->run($summary);
		
		if (!empty($putSummaryReturn)) {
			$dataResponse = $putSummaryReturn;
		}

		return $dataResponse;
	}
} // End class
