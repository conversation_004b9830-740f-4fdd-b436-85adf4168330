<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\BuildParamDigitalNotiAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\BuildParamDigitalNotiAction\SubAction\DigitalNotiBuildParamTatToanSA;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\BuildParamDigitalNotiAction\SubAction\DigitalNotiBuildParamTrichDuocTienSA;

class BuildParamDigitalNotiAction
{
	private array $__listNotiDaXuLy = [];

	private array $__exceptIds = [];

	public function init()
	{
		for ($i = 1; $i <= 40; $i++) {
			try {
				$digitalNoti = $this->run();

				if ($digitalNoti == 'EMPTY') {
					$this->__listNotiDaXuLy[] = 'Khong co yc nao can xu ly noti';
					break;
				}

				if ($digitalNoti && optional($digitalNoti)->id) {
					$this->__listNotiDaXuLy[] = $digitalNoti->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi dong bo giao dich' => Helper::traceError($th)]);
				@TelegramAlert::sendAccouting(Helper::traceError($th));
				continue;
				// throw $th; // sau phai bo dong nay
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $this->__listNotiDaXuLy;
	}

	public function run()
	{
		$digitalNoti = CollectDebtDigitalNoti::query()
																				 ->where('status', CollectDebtDigitalNoti::STT_MOI_TAO);

		if (!empty($this->__exceptIds)) {
			$digitalNoti = $digitalNoti->whereNotIn('id', $this->__exceptIds);
		}
		
		$digitalNoti = $digitalNoti->first();

		if (!$digitalNoti) {
			return 'EMPTY';
		}

		$this->__exceptIds[] = $digitalNoti->id;

		$wasProcessing = CollectDebtDigitalNoti::query()
																					 ->where('id', $digitalNoti->id)
																					 ->where('status', CollectDebtDigitalNoti::STT_MOI_TAO)
																					 ->update(['status' => CollectDebtDigitalNoti::STT_DANG_XU_LY]);
		if (!$wasProcessing) {
			throw new Exception('Lỗi không update noti lên thành đang xử lý');
		}

		DB::beginTransaction();
		try {
			switch ($digitalNoti->type) {
				case 'settled':
					$result = app(DigitalNotiBuildParamTatToanSA::class)->run($digitalNoti);
					break;

				case 'accounting':
					$result = app(DigitalNotiBuildParamTrichDuocTienSA::class)->run($digitalNoti);
					break;
			}

			DB::commit();
			return $digitalNoti;
		} catch (\Throwable $th) {
			DB::rollBack();
			throw $th;
		}
	}
} // End class