<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingAction\SubAction\XuLyThuThieuTienTask\GroupRequestIntoScheduleTask;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;

class GetLedgerNeedToAccountingSummarySA
{
	/**
	 * Bắt trạng thái sổ 
	 * và ĐẢM BẢO TOÀN BỘ LỊCH LÀ `ĐÃ HOÀN THÀNH` thì mới cho xử lý tổng hợp
	 *
	 * @return void
	 */
	public function run($exceptLedgerIds = [])
	{
		$collectDebtLedger = CollectDebtLedger::query()
			->where('status', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
			->where('status_summary', CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT)
			->first();

		// throw_if(!$collectDebtLedger, new Exception('Hach toan summary - Khong co thong tin so'));

		
		return $collectDebtLedger;
	}
}

// End class