<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideSetStatusAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideSetStatus;

class DebtRecoveryContractGuideSetStatusAction
{
  public function run(CollectDebtGuide $collectDebtGuide, DebtRecoveryContractGuideSetStatus $request)
  {
    $updatedFlag = false;

    if ($collectDebtGuide->isGuideMoiTao() && $request->json('data.status') == CollectDebtEnum::GUIDE_STT_DA_DUYET) {
      $collectDebtGuide->update([
        'status' => CollectDebtEnum::GUIDE_STT_DA_DUYET, 
        'approved_by' => $request->json('data.user_request_id'),
        'time_approved' => time()
      ]);

      $updatedFlag = true;
    }

    if ($collectDebtGuide->isGuideMoiTao() && $request->json('data.status') == CollectDebtEnum::GUIDE_STT_DA_TU_CHOI) {
      $collectDebtGuide->update([
        'status' => CollectDebtEnum::GUIDE_STT_DA_DUYET, 
        'canceled_by' => $request->json('data.user_request_id'),
        'time_canceled' => time()
      ]);

      $updatedFlag = true;
    }
      

    if ($collectDebtGuide->isGuideDaDuyet() && $request->json('data.status') == CollectDebtEnum::GUIDE_STT_DANG_TAO_LICH) {
      $collectDebtGuide->update([
        'status' => CollectDebtEnum::GUIDE_STT_DANG_TAO_LICH, 
        'updated_by' => $request->json('data.user_request_id'),
        'time_updated' => time()
      ]);

      $updatedFlag = true;
    }

    if ($collectDebtGuide->isGuideDaDuyet() && $request->json('data.status') == CollectDebtEnum::GUIDE_STT_DA_TU_CHOI) {
      $collectDebtGuide->update([
        'status' => CollectDebtEnum::GUIDE_STT_DA_TU_CHOI, 
        'canceled_by' => $request->json('data.user_request_id'),
        'time_canceled' => time()
      ]);

      $updatedFlag = true;
    }


    if ($collectDebtGuide->isGuideDangXuLy() && $request->json('data.status') == CollectDebtEnum::GUIDE_STT_DA_TAO_LICH_THANH_CONG) {
      $collectDebtGuide->update([
        'status' => CollectDebtEnum::GUIDE_STT_DA_TAO_LICH_THANH_CONG, 
        'create_calendar_by' => $request->json('data.user_request_id'),
        'time_create_calendar' => time()
      ]);

      $updatedFlag = true;
    }

    if ($collectDebtGuide->isGuideDangXuLy() && $request->json('data.status') == CollectDebtEnum::GUIDE_STT_DA_TU_CHOI) {
      $collectDebtGuide->update([
        'status' => CollectDebtEnum::GUIDE_STT_DA_TU_CHOI, 
        'canceled_by' => $request->json('data.user_request_id'),
        'time_canceled' => time()
      ]);

      $updatedFlag = true;
    }

   
    $message = sprintf(
      'Dữ liệu trạng thái không đúng luồng nghiệp vụ. Trạng thái hiện tại của chỉ dẫn là: %s. Trạng thái mà muốn cập nhật là: %s',
      $collectDebtGuide->getStatusText(),
      $collectDebtGuide->listingStatus()[$request->json('data.status')]
    );
    
    throw_if(!$updatedFlag, new Exception($message, 500));
    
    return $collectDebtGuide->refresh();
  }
} // End class
