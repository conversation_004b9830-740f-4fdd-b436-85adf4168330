<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummaryPaymentCycleReportRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
			'data.filter' => ['nullable', 'array'],
			'data.filter.time_start_from' => ['required', 'date_format:d-m-Y'],
			'data.filter.time_start_to' => ['required', 'date_format:d-m-Y'],
			'data.list_contract_code_can_access' => ['present', 'array'],
			'data.list_contract_code_can_access.*' => ['required', 'string']
    ];
  }

	public function isViewAllContract(): bool {
		return collect($this->json('data.list_contract_code_can_access'))->contains(function ($item) {
			return $item == 'ALL';
		});
	}

	public function isEmptyContract(): bool {
		return empty($this->json('data.list_contract_code_can_access'));
	}
	
	public function getTimeStartFromAsTimestamp(): int {
		return Carbon::createFromFormat('d-m-Y', $this->json('data.filter.time_start_from'))->startOfDay()->format('Ymd');
	}

	public function getTimeStartToAsTimestamp(): int {
		return Carbon::createFromFormat('d-m-Y', $this->json('data.filter.time_start_to'))->endOfDay()->format('Ymd');
	}

	protected function prepareForValidation()
	{
		$params = $this->all();
		
		if (empty($params['data']['filter']['time_start_from'])) {
			$params['data']['filter']['time_start_from'] = date('d-m-Y');
		}

		if (empty($params['data']['filter']['time_start_to'])) {
			$params['data']['filter']['time_start_to'] = date('d-m-Y');
		}

		$this->merge($params);
	}
} // End class
