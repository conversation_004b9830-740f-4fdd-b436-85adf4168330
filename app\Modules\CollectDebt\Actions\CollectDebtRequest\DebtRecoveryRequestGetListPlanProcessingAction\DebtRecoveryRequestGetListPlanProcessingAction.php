<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetListPlanProcessingAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Database\Eloquent\Collection;

class DebtRecoveryRequestGetListPlanProcessingAction
{
  public function run(): array
  {
    $processingPlanIds = [];
    $collectDebtRequests = CollectDebtRequest::where('status', CollectDebtEnum::REQUEST_STT_DA_DUYET)->get();

    $collectDebtRequests->map(function (CollectDebtRequest $collectDebtRequest) use (&$processingPlanIds) {
      $processingPlanIds = array_merge($collectDebtRequest->getPlanIds());
      return $collectDebtRequest;
    });

    return $processingPlanIds;
  }
} // End class