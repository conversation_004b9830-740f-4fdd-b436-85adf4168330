<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEmail\DebtRecoveryEmailGetDetailByIdAction;

use App\Lib\NextlendCore;

class DebtRecoveryEmailGetDetailByIdAction
{
  public function run($emailId)
  {
    $nextlendCore = app(NextlendCore::class)->callRequest([
      'id' => $emailId,
    ], 'CustomerRequestCare_getById', 'get');
    
    $result = $nextlendCore->decryptData();

    $mailContent = base64_decode($result['content']);
    $result['content'] = $mailContent;
    return $result;
  }
} // End class