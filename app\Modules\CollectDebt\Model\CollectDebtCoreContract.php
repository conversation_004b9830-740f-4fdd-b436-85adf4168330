<?php

namespace App\Modules\CollectDebt\Model;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;


class CollectDebtCoreContract extends Model
{
	public $timestamps = false;
	protected $guarded = [];
	protected $appends = [];

  /**
   * Build model trong `data`
   * 
   * {
      "type": "CONTRACT",
      "time_modified": 1700468624,
      "data": {
          "profile_id": "", 
          "contract_code": "TEST-231120-6VWD3",
          "contract_time_start": 1699981200,
          "contract_type": "3",
          "contract_cycle": "30",
          "contract_intervals": "10",
          "amount": 10000000,
          "contract_time_end": 1702659599,
          "other_data": "{}",
          "description": "Hợp đồng v4" 
          "id": 97
        },
      "note": "Thông tin HĐ"
    }
   */
  
} // End class
