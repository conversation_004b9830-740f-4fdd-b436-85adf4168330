<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction;

use Exception;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction\GhiSoChoYeuCauChuaCoSoSubAction;

class DebtRecoveryLedgerCreateAction
{  
  /**
   * Thực hiện ghi sổ, cần check điều kiện của yêu cầu muốn ghi sổ, thỏa mãn các điều kiện sau:
   * 1. <PERSON><PERSON><PERSON> cầu tồn tại
   * 2. <PERSON><PERSON><PERSON> cầu có trạng thái thanh toán về cuối cùng (3 hoặc 6)
   * 3. <PERSON><PERSON><PERSON> dữ liệu về `plan_ids`, `amount_receiver`, `currency` truyền lên phải đúng với data trong DB
   * 4. Yêu cầu đó phải có trạng thái ghi sổ là: "CHƯA GHI SỔ"
   * -- Update: 10.11.2023
   * HOẶC: Điều kiện ghi sổ:
   * + Yêu cầu tồn tại 
   * + Là yêu cầu trích tay
   * + Yêu cầu trích tay đã được duyệt bước 2
   * --
   * Phương thức này thực hiện 2 thao tác: 
   *    1: Thực hiện ghi sổ
   *    2: Đánh dấu yêu cầu là đã ghi sổ
   * 
   * @param DebtRecoveryLedgerCreateRequest $request [explicite description]
   *
   * @return CollectDebtLedger
   */
  public function run(DebtRecoveryLedgerCreateRequest $request): CollectDebtLedger
  {
    $collectDebtRequest = CollectDebtRequest::find($request->json('data.request_id'));
    mylog(['Yêu cầu muốn ghi sổ là:' => $collectDebtRequest->id]);

    try {
      throw_if(!$collectDebtRequest, new Exception('Yêu cầu thu hồi không tồn tại'));
      throw_if($collectDebtRequest->plan_ids != $request->json('data.plan_ids'), new Exception('Id Lịch truyền lên không chính xác. Id lịch chuẩn là: ' . $collectDebtRequest->plan_ids));
      throw_if($collectDebtRequest->currency != $request->json('data.currency'), new Exception('Đơn vị tiền tệ không chính xác'));
      
      if ($collectDebtRequest->isUnRecorded()) {
        throw_if($collectDebtRequest->amount_receiver != $request->json('data.amount'), new Exception('Số tiền ghi sổ ĐANG KHÁC số tiền đã trích được thực tế. Số tiền ĐÃ TRÍCH ĐƯỢC là: ' . $collectDebtRequest->amount_receiver));

        return app(GhiSoChoYeuCauChuaCoSoSubAction::class)->run(
          $collectDebtRequest,
          $request
        );
      }

      throw new Exception('Yêu cầu đã có sổ. Xem xét đi vào luồng ĐỐI TÁC BÁO MUỘN');
    }catch(\Throwable $th) {
      $errorMessage = traceErr($th);
      TelegramAlert::sendGhiSo($errorMessage);
      throw new Exception($errorMessage);
    }
  }
} // End class