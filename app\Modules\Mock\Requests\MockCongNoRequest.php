<?php
namespace App\Modules\Mock\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class MockCongNoRequest extends FormRequest
{
    /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'contract_code' => ['required', 'string', 'max:50'],
      'amount_payment' => ['required', 'numeric', 'min:1'],
      'amount_receiver' => ['required', 'numeric', 'min:1'],
    ];
  }

  protected function passedValidation()
  {
    $params = $this->all();
    
    
    $param['amount_payment'] = str_replace([',', '.'], '', $params['amount_payment']);
    $param['amount_receiver'] = str_replace([',', '.'], '', $params['amount_receiver']);
    $param['payment_account_id'] = $params['contract_code'];
   
    $this->merge($params);
  }
}