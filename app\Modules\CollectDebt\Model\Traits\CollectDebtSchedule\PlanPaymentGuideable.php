<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

use App\Modules\CollectDebt\Model\CollectDebtShare;

trait PlanPaymentGuideable
{
  /**
   * array:2 [
			0 => array:4 [
				"other_data" => array:3 [
					"payment_account_name" => "MPOS TEST"
					"payment_account_barch" => "MPOS"
					"payment_account_bank_code" => "mpos"
				]
				"payment_account_id" => "ID_MPOS"
				"payment_method_code" => "MPOS"
				"payment_channel_code" => "MPOS"
			]
			1 => array:4 [
				"other_data" => array:3 [
					"payment_account_name" => "CHỦ TÀI KHOẢN"
					"payment_account_barch" => "CHI NHÁNH"
					"payment_account_bank_code" => "VCB"
				]
				"payment_account_id" => "SO_TK_BANK_NEU_CO"
				"payment_method_code" => "IB_OFF"
				"payment_channel_code" => "BANK"
			]
		]
   */
  public function getPaymentGuide(): array
  {
    if (empty($this->collectDebtShare)) {
      return [];
    }

    return json_decode($this->collectDebtShare->payment_guide, true);
  }

  public function getSpecialPaymentGuide(string $paymentMethodCode = 'MPOS'): array
  {
    $paymentGuide = $this->getPaymentGuide();
    return collect($paymentGuide)->where('payment_method_code', $paymentMethodCode)->first();
  }
} // End class