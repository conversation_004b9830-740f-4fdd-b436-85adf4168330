<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractCreateRequestViaWalletAction\SubAction;

use App\Lib\ApiCall;
use App\Utils\CommonVar;

class DongBangSoTienTrichNoSubAction {
	public function run($profileAccount=[], $soTienTrichNo=0) {
		$params = [
      'id' => $profileAccount['data']['id'],
      'freezing_balance' => $soTienTrichNo,
      'freeze_admin_user_id' => 'cronjob'
    ];

		
    $payload = [
      'module' => CommonVar::API_PROFILE_MODULE,
      'path' => '/ProfileAccountsIncreaseFreeze',
      'params' => $params,
      'method' => 'POST'
    ];

    $profileAccountResult = (new ApiCall())->callFunctionApi($payload, true);
		return $profileAccountResult;
	}
}