<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\BuildParamTaoYeuCauTuDongTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\ThucHienTaoLenhTrichTuDongTask;

class TaoYeuCauTuDongMposTuLichThuDoiLuongSubAction
{
	private array $__listCodeCanNe = [];

	private array $__yeuCauDaTao = [];

	public function initTaoLenhTuDong()
	{
		// Lay ra HD can ne truoc
		$this->getListHopDongCanNe();

		for ($i = 1; $i <= 20; $i++) {
			try {
				$result = $this->run();

				// Truy van ma he thong tra ra "EMPTY", thi break luon
				if ($result == 'EMPTY') {
					$this->__yeuCauDaTao[] = 'khong co yeu cau can phai tao';
					break;
				}

				if ($result && $result->id) {
					$yc = [
						'request_id' => $result->id,
						'contract_code' => $result->contract_code,
						'plan_ids' => $result->plan_ids
					];

					$this->__yeuCauDaTao[] = $yc;
					var_dump($yc);
				}else {	
					dump('N/A');
				}
				
			} catch (\Throwable $th) {
				mylog(['Loi xu ly tao lich tu dong' => Helper::traceError($th)]);
				
				@TelegramAlert::sendCreateRequest('Loi xu ly tao lich tu dong: ' . Helper::traceError($th));
				continue;
			} finally {
				//sleep(1);
			}
		}

		return $this->__yeuCauDaTao;
	}

	public function run()
	{
		// 1. chi lay ra lich thu co `is_proces` la CHUA XU LY
		$plan = CollectDebtSchedule::query()
															 ->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
															 ->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
															 ->where('rundate', '<=', now()->format('Ymd'));

		if (!empty($this->__listCodeCanNe)) {
			$plan = $plan->whereNotIn('contract_code', $this->__listCodeCanNe);
		}

		$plan = $plan->orderByRaw('rundate ASC, cycle_number ASC, type ASC, is_settlement ASC')->first();

		if (!$plan) {
			return 'EMPTY';
		}

	
		mylog(['lich thu dang xu ly la' => $plan]);

		// 2. Xu ly ne HD
		if (in_array($plan->contract_code, $this->__listCodeCanNe)) {
			$error = [
				'Hop dong da vi pham cac hd can ne' => 'ok',
				'hd dang thuc hien' => $plan->contract_code,
				'cac hd phai ne' => $this->__listCodeCanNe
			];

			mylog($error);
			
			throw new Exception('Loi HD da co lich thu xu ly');
		}

		// 3. Update plan hien tai len: DANG XU LY
		$updatedPlanRow = CollectDebtSchedule::query()
			->where('contract_code', $plan->contract_code)
			->where('id', $plan->id)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->update([
				'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
			]);

		if (!$updatedPlanRow) {
			mylog(['update current row' => $updatedPlanRow]);
			throw new Exception('update current row');
		}

		// Loai tru HD hien tai luon
		$this->__listCodeCanNe[] = $plan->contract_code;

		// 3. Cap nhat cac ban ghi lich thu co cung rundate ve DANG XU LY
		// khong the biet co lich qua khu hay khong, nen chi bat catch
		try {
			$updatedPlanRows = CollectDebtSchedule::query()
				->where('contract_code', $plan->contract_code)
				->where('rundate', $plan->rundate)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
				->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
				->where('id', '!=', $plan->id)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
				]);
			
			mylog([
				'Khong the cap nhat lich thu sang trang thai dang xu ly' => 'ok',
				'Result' => $updatedPlanRows
			]);
		} catch (\Throwable $th) {
			mylog(['loi cap nhat cac lich thu cung rundate' => Helper::traceError($th)]);
			
			// Doan nay phai update plan hien tai =>  ve da chua xu ly
			$updatedVeChuaXuLy = CollectDebtSchedule::query()->where('id', $plan->id)
																	->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
																	->update([
																		'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
																	]);
			
			// ne luon neu update loi
			if (!$updatedVeChuaXuLy) {
				$this->__listCodeCanNe[] = $plan->contract_code;
			}
			throw $th;
		}


		$listLichThuRefresh = CollectDebtSchedule::query()
																						 ->with('collectDebtShare')
																						 ->where('contract_code', $plan->contract_code)
																						 ->where('rundate', $plan->rundate)
																						 ->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
																						 ->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
																						 ->get();

		mylog(['list lich thu dung de xu ly' => $listLichThuRefresh]);
																						 
		$isToanBoLichLaDangXuLy = $listLichThuRefresh->every(function (CollectDebtSchedule $p) {
			return $p->is_process == CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY;
		});

		// doan nay nen cap nhat het ve chua xu ly
		throw_if(!$isToanBoLichLaDangXuLy, new Exception('Toan bo lich chua ve dang xu ly'));

		$listLichThuTaoYeuCau = $listLichThuRefresh;

		
		$listLichThuTaoYeuCau = app(PlanSortableCollectionByRule::class)->sortCollection($listLichThuTaoYeuCau);

		// 5. Start transaction
		DB::beginTransaction();
		try {
			$buildParamTaoYeuCau = app(BuildParamTaoYeuCauTuDongTask::class)->run($listLichThuTaoYeuCau);
			$collectDebtRequest = app(ThucHienTaoLenhTrichTuDongTask::class)->run($buildParamTaoYeuCau, $listLichThuTaoYeuCau);
			$this->__listCodeCanNe[] = $collectDebtRequest->contract_code;

			DB::commit();


			return $collectDebtRequest;
		} catch (\Throwable $th) {
			DB::rollBack();
			mylog(['loi tao yc' => Helper::traceError($th)]);

			// Update toàn bộ lịch thu cần tạo yc về is_process: CHƯA XỬ LÝ
			$updated = CollectDebtSchedule::query()
																		->where('contract_code', $plan->contract_code)
																		->whereIn('id', $listLichThuTaoYeuCau->pluck('id')->toArray())
																		->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
																		->update([
																			'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
																		]);
			
			if (!$updated) {
				$this->__listCodeCanNe[] = $plan->contract_code;
				throw new Exception('Loi rollback trang thai, can sua tay');
			}															
		}
	}

	public function getListHopDongCanNe(): array
	{
		$listHopDongDungJob = CollectDebtConfigAuto::getHopDongDangBiDungJob();

		mylog(['list hop dong dang dung job la' => $listHopDongDungJob]);

		$listHopDongCoLichDangXuLy = CollectDebtSchedule::query()
																										->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
																										->pluck('contract_code')
																										->toArray();

		mylog(['list hd dang xu ly process' => $listHopDongCoLichDangXuLy]);


		$listHopDongDangCoYcThu = [];

		if ( !empty($listHopDongDungJob) ) {
			foreach ($listHopDongDungJob as $ct) {
				$this->__listCodeCanNe[] = $ct;
			}
		}

		if ( !empty($listHopDongCoLichDangXuLy) ) {
			foreach ($listHopDongCoLichDangXuLy as $ct) {
				$this->__listCodeCanNe[] = $ct;
			}
		}

		if ( !empty($listHopDongDangCoYcThu) ) {
			foreach ($listHopDongDangCoYcThu as $ct) {
				$this->__listCodeCanNe[] = $ct;
			}
		}
		
		mylog(['list hd can xu ly ne' => $this->__listCodeCanNe]);

		return $this->__listCodeCanNe;
	}
} // End class
