<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateRequest;
use Exception;

class DebtRecoveryRequestCreateAction
{
  public function run(DebtRecoveryRequestCreateRequest $request): CollectDebtRequest
  {
		try {
			$params = $request->json('data');

			$plans = app(GetLichThuByIdsSubAction::class)->run($params['plan_ids']);
			
			$plansRutGon = $plans->map(function (CollectDebtSchedule $plan) {
				$plan = $plan->makeHidden([
					'created_by',
					'other_data',
					'description',
					'time_start_as_date',
					'time_start_as_vn_date',
					'time_end_as_date',
					'time_end_as_vn_date',
				]);
				return $plan;
			});

			$params['plan_data'] = Helper::getPlanCompact($plansRutGon);

			// nen co try catch, bat ket qua tao, update
			$collectDebtRequest = CollectDebtRequest::forceCreate($params);

			$collectDebtRequest->partner_request_id = sprintf('NL%s%s', date('ymd'), $collectDebtRequest->id);
			$collectDebtRequest->other_data = $collectDebtRequest->initOtherData();
			
			$taoYeuCauResult = $collectDebtRequest->save();
			
			if (!$taoYeuCauResult) {
				throw new Exception('Loi tao yeu cau khong thanh cong');
			}

			return $collectDebtRequest;
		}catch(\Throwable $th) {
			mylog(['Loi tao yeu cau tu cong no khong co doi ta' => Helper::traceError($th)]);
			throw $th;
		}
  }
} // End class