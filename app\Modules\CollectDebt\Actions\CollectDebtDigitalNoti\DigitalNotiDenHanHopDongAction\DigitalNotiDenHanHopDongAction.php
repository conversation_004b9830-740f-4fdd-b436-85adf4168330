<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiDenHanHopDongAction;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtDigitalTmp;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;

class DigitalNotiDenHanHopDongAction
{
	public array $listContractSapDenHan = [];

	public function run()
	{
		$collectDebtGuides = CollectDebtGuide::query()
			->with([
				'collectDebtSummary' => function ($query) {
					return $query->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
				}
			])
			->whereHas('collectDebtSummary', function ($query) {
				return  $query->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
			})
			->whereDoesntHave('collectDebtDigitalTmp', function ($query) {
				return $query->where('type', 'due_deadline');
			})
			->whereBetween('contract_time_end', [
				now()->startOfDay()->timestamp,
				now()->endOfDay()->timestamp,
			])
			->get();


		if ($collectDebtGuides->isEmpty()) {
			return;
		}


		$collectDebtGuides->map(function (CollectDebtGuide $collectDebtGuide) {
			$collectDebtSummary = $collectDebtGuide->collectDebtSummary;
			$tongDuNo = $collectDebtSummary->getTongTienConPhaiTra();

			$title = sprintf('Hôm nay đến hạn thanh toán');
			$body = sprintf('Hôm nay là ngày thanh toán khoản ứng %s, vui lòng kiểm tra lại.', $collectDebtGuide->contract_code);
			
			$content = view('trichno.noti.den-han-hop-dong', [
				'collectDebtGuide' => $collectDebtGuide,
				'collectDebtSummary' => $collectDebtSummary,
				'tongDuNo' => Helper::makeVndCurrency($tongDuNo)
			])->render();


			$notifyData = CollectDebtDigitalNoti::buildNotiData(
				$title,
				$body,
				$content,
				'DIGITAL_DENHANTHANHTOAN',
				$collectDebtGuide->getMposMcId(),
				'Hợp đồng đã đến hạn'
			);

			$digitalNoti = CollectDebtDigitalNoti::query()->forceCreate([
				'contract_code' => $collectDebtGuide->contract_code,
				'type' => 'due_deadline',
				'object_model' => CollectDebtGuide::class,
				'object_id' => $collectDebtGuide->id,
				'digital_request' => json_encode($notifyData),
				'status' => CollectDebtDigitalNoti::STT_DA_BUILD_PARAM,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp
			]);

			if (!$digitalNoti) {
				throw new Exception('Lỗi không build tạo được noti');
			}

			$digitalTmp = CollectDebtDigitalTmp::query()->forceCreate([
				'contract_code' => $collectDebtGuide->contract_code,
				'type' => 'due_deadline',
				'channel' => 'NOTI',
			]);

			if (!$digitalTmp) {
				throw new Exception('Lỗi không tạo được noti tmp');
			}

			$this->listContractSapDenHan[] = $collectDebtGuide->contract_code;
			return $digitalNoti;
		});

		return $this->listContractSapDenHan;
	}
} // End class
