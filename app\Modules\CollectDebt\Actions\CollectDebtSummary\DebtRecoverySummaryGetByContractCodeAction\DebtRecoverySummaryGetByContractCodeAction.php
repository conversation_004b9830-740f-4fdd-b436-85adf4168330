<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetByContractCodeAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryGetByContractCodeRequest;
use Arr;
use Carbon\Carbon;

class DebtRecoverySummaryGetByContractCodeAction
{
  public int $phiChamKyConPhaiThuCuaCacKyTruocDo = 0;

  public function run(DebtRecoverySummaryGetByContractCodeRequest $request)
  {
    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $request->json('data.contract_code'))->first();
    
    throw_if(!$collectDebtSummary, new Exception('Không tìm thấy thông tin hợp đồng'));

    $summaryPlans = $collectDebtSummary->getSummaryOtherDataItem('PLAN');
    
    $plans = [];
    
    $lastPlan = Arr::last($summaryPlans['data']);

    /**
     * array:10 [
  0 => array:11 [
    "id" => 38139
    "fee" => 0
    "data" => array:4 [
      "id" => 38139
      "fee_overdue" => 0
      "fee_overdue_cycle" => 0
      "fee_overdue_cycle_reduction" => 0
    ]
    "note" => "Hợp đồng V4"
    "amount" => 1000000
    "status" => 3
    "fee_paid" => 0
    "time_cycle" => 1705165200
    "amount_paid" => 0
    "overdue_cycle" => 2
    "time_over_cycle" => 0
  ]
  ...
]
     */ 
    foreach ($summaryPlans['data'] as $plan) {
      $noGocPhaiThu = $plan['amount'];
      $tongPhiDuocSinhRa = $plan['fee'] + $plan['fee_paid'];

      $tongGocVaPhiPhaiThu =  $noGocPhaiThu + $tongPhiDuocSinhRa;

      $tongGocVaPhiDaThu =  $plan['fee_paid'] + $plan['amount_paid'];

      $tongPhiDuocGiam = $plan['data']['fee_overdue_cycle_reduction'];
      $tongTienCanThuTiep  = $tongGocVaPhiPhaiThu - $tongGocVaPhiDaThu;

      // Không phải kỳ tất toán, thì gom hết số phí chưa thu hết của các kỳ trước đó vào
      if ($plan['id'] != $lastPlan['id']) {
        $tongPhiConPhaiThu = $tongPhiDuocSinhRa - $tongPhiDuocGiam - $plan['fee_paid'];
        $this->phiChamKyConPhaiThuCuaCacKyTruocDo += $tongPhiConPhaiThu;
      }

      // Kỳ tất toán, check thêm lịch tương lai (time_cycle) để quyết định có cộng số tiền phí hay không
      if ($plan['id'] == $lastPlan['id'] && time() > $plan['time_cycle']) {
        $tongTienCanThuTiep += $this->phiChamKyConPhaiThuCuaCacKyTruocDo;
        $tongPhiDuocSinhRa += $this->phiChamKyConPhaiThuCuaCacKyTruocDo;
      }

     
      $plans[] = [
        'id' => $plan['id'],
        'date' => Carbon::createFromTimestamp($plan['time_cycle'])->format('d/m/Y'),
        'amount' => $plan['amount'], // nợ gốc
        'total_fee' => $tongPhiDuocSinhRa, // tổng phí
        'total_amount_paid_success' => $tongGocVaPhiDaThu + $tongPhiDuocGiam, // tổng tiền đã thu
        'total_amount_remain_debt' => $tongTienCanThuTiep,
        'currency' => $collectDebtSummary->getCurrency() // mã tiền tệ
      ];
    }

    return [
      'plans' => $plans
    ];
  }
} // End class
