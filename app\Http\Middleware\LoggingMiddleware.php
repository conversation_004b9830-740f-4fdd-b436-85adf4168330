<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class LoggingMiddleware
{
	public $timeStart;

	protected $excepts = [
		'healthcheck/liveness',
		'healthcheck/readiness',
		'GetListNotification',
		'DebtRecoveryContractGuideSearchData',
		'configs'
	];

	/**
	 * Handle an incoming request.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \Closure  $next
	 * @return mixed
	 */

	public function handle($request, Closure $next)
	{
		app()->setLocale($request->json('lang', 'vi'));

		$path = ltrim(request()->path(), '/');

		if ($this->__canWriteLog($path)) {
			Log::info($path, ['request' => $request->all()]);
		}

		return $next($request);
	}

	private function __canWriteLog($path): bool
	{
		foreach ($this->excepts as $exceptPath) {
			if (Str::contains($path, $exceptPath)) {
				return false;
			}
		}

		return true;
	}

	public function terminate($request, $response)
	{
		if ($this->isNeedWriteLogForRouting($request)) {
			app('mylog')->logging();
		}
	}

	public function isNeedWriteLogForRouting($request): bool
	{
		foreach ($this->excepts as $exceptPath) {
			$path = $request->fullUrl();
			if (Str::contains($path, $exceptPath)) {
				return false;
			}
		}

		return true;
	}
}
