<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryRequestGetByPlanIdRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.plan_id' => ['required', 'integer', 'min:1'],
      'data.fields' => ['present', 'array',]
    ];
  }
} // End class
