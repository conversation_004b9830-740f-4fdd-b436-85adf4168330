<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerUpdateAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerUpdateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByIdAction\SubAction\DebtRecoveryLedgerFindWhereRawSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\DebtRecoveryRequestFindRawQuerySubAction;

class DebtRecoveryLedgerUpdateAction
{
  public function run(DebtRecoveryLedgerUpdateRequest $request): CollectDebtLedger
  {
    $whereRaw = sprintf('id = %s', $request->json('data.request_id'));
    $collectDebtRequest = app(DebtRecoveryRequestFindRawQuerySubAction::class)->run($whereRaw);
    mylog(['Yêu cầu muốn ghi sổ là:' => $collectDebtRequest]);

    throw_if(!$collectDebtRequest, new Exception('Yêu cầu thu hồi không tồn tại'));
    throw_if(!$collectDebtRequest->isFinalPaymentStatus(), new Exception('Yêu cầu thu hồi chưa ở trạng thái cuối. Trạng thái cuối `status_payment` = 3 hoặc 6'));
    throw_if($collectDebtRequest->plan_ids != $request->json('data.plan_ids'), new Exception('Id Lịch truyền lên không chính xác. Id lịch chuẩn là: ' . $collectDebtRequest->plan_ids));
    throw_if($collectDebtRequest->amount_receiver != $request->json('data.amount'), new Exception('Số tiền ghi sổ ĐANG KHÁC số tiền đã trích được thực tế. Số tiền ĐÃ TRÍCH ĐƯỢC là: ' . $collectDebtRequest->amount_receiver));
    throw_if($collectDebtRequest->currency != $request->json('data.currency'), new Exception('Đơn vị tiền tệ không chính xác'));
    
    $currentCollectDebtLedger = app(DebtRecoveryLedgerFindWhereRawSubAction::class)->run('id = ' . $request->json('data.id'), true);
    
    $messsage = sprintf(
      'Sổ phải có trạng thái = 1 (Chưa ghi sổ) thì mới được cập nhật. Trạng thái hiện tại là: %s (%s)',
      $currentCollectDebtLedger->status,
      CollectDebtLedger::listStatus()[$currentCollectDebtLedger->status] ?? 'Không xác định'
    );

    throw_if( !$currentCollectDebtLedger->isNoProcess(), new Exception($messsage) );
    
    $paramUpdate = $request->only([
      'data.profile_id',
      'data.contract_code',
      'data.plan_ids',
      'data.request_id',
      'data.currency',
      'data.amount',
      'data.other_data',
      'data.profile_data',
      'data.description',
      'data.status',
      'data.time_record',
      'data.updated_by',
      'data.time_updated',
    ])['data'];

    $currentCollectDebtLedger->forceFill($paramUpdate)->update();
    return $currentCollectDebtLedger->refresh();
  }
} // End class