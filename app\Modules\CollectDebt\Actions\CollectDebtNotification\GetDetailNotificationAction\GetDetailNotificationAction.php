<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtNotification\GetDetailNotificationAction;

use Exception;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtNotification;

class GetDetailNotificationAction
{
	public function run(Request $request)
	{
		$id = $request->json('data.id', '');
		if (empty($id)) {
			throw new Exception('Id thông báo là bắt buộc');
		}

		$collectDebtNotification = CollectDebtNotification::query()->find($id);
		if (!$collectDebtNotification) {
			throw new Exception('Không tìm thấy thông báo của bạn');
		}

		$collectDebtNotification->is_read = 1;
		$collectDebtNotification->read_at = now();
		$collectDebtNotification->save();

		return $collectDebtNotification;
	}
} // End class
