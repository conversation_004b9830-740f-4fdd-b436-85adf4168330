<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction\CollectDebtNotifyDebtNowSendMailSubAction\CollectDebtNotifyDebtNowSendMailSubAction;

class CollectDebtNotifyDebtNow extends Model
{
	protected $table  = 'debt_recovery_notify_debt_now';
	public $timestamps = false;
	protected $guarded = [];

	public function buildMailTo(): array {
		return [
			[
				'name' => "",
				'identifier_account' => $this->to,
				'identifier_data' => ''
			]
		];
	}

	public function buildMailCC(): array {
		$listMailCc = explode(',', $this->cc);
		if (empty($listMailCc)) {
			return [];
		}

		$cc = [];
		foreach ($listMailCc as $em) {
			$cc[] = [
				'name' => "",
				'identifier_account' => $em,
				'identifier_data' => ''
			];
		}

		return $cc;
	}

	public function buildMailContent(): string {
		$otherData = json_decode($this->other_data, true);
		$content = app(CollectDebtNotifyDebtNowSendMailSubAction::class)->buildContent($otherData);
		return $content;
	}
} // End class