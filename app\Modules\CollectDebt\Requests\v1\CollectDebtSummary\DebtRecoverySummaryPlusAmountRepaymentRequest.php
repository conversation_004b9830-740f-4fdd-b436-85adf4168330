<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummaryPlusAmountRepaymentRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.contract_code' => ['required', 'string', 'max:50'],
			'data.amount' => ['required', 'numeric', 'integer', 'min:1'],
			'data.users_admin_id' => ['required', 'string', 'max:255'], // mảng session user webbackend
    ];
  }

	public function getSoTienMuonThanhToanTiep(): int {
		return $this->json('data.amount');
	}
} // End class
