<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtPartner;

use App\Lib\Helper;
use App\Lib\Security;
use App\Lib\TelegramAlert;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\HopDongChuaTatToanRule;
use App\Modules\CollectDebt\Rules\CollectDebtPartner\MaChungTuLaDuyNhatRule;

class DebtRecoveryPartnerCreateExternalTransferRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.payment_channel_code'   => ['required', 'string', 'max:50'],
      'data.payment_method_code'    => ['required', 'string', 'max:50', Rule::in(['IB_OFF'])],
      'data.contract_code'          => [
        'required', 
        'string', 
        'max:50', 
        new HopDongChuaTatToanRule()
      ],
      'data.partner_transaction_id' => [
        'required', 
        'string', 
        'max:50', 
        new MaChungTuLaDuyNhatRule()
      ], // Mã chứng từ
      'data.amount_receiver'        => ['required', 'numeric', 'min:0'],
      'data.created_by'             => ['required', 'string', 'max:255'],
      'data.description'             => ['required', 'string', 'max:255'],
    ];
  }

  public function messages() {
    return [
      'data.partner_transaction_id.required' => 'Mã chứng từ là bắt buộc',
      'data.partner_transaction_id.string' => 'Mã chứng từ phải là kiểu chữ',
      'data.partner_transaction_id.max' => 'Mã chứng từ phải có độ dài tối đa là 50 ký tự',
      'data.partner_transaction_id.unique' => 'Mã chứng từ đã tồn tại trong hệ thống',

      'data.amount_receiver.required' => 'Số tiền nhận là bắt buộc',
      'data.amount_receiver.numeric' => 'Số tiền nhận phải là kiểu số',
      'data.amount_receiver.min' => 'Số tiền nhận phải lớn hơn 0',
      'data.amount_receiver.integer' => 'Số tiền nhận phải là kiểu số nguyên',

      'data.description.required' => 'Nội dung là bắt buộc',
      'data.description.string' => 'Nội dung phải là kiểu chuỗi',
      'data.description.max' => 'Nội dung phải có độ dài tối đa là 255 ký tự',
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['time_created'] = time();
    $params['data']['status'] = CollectDebtEnum::PARTNER_STT_CHO_DUYET;
    $params['data']['description'] = Helper::cleanXss($params['data']['description']);
    $params['data']['created_by'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['created_by']);
    $params['data']['payment_account_id'] = $params['data']['contract_code']; // Tham chiếu mã hợp đồng
		$params['data']['partner_transaction_id'] = trim($params['data']['partner_transaction_id']);
    $this->merge($params);
  }
} // End class
