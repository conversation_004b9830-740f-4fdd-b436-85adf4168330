<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtLedger;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class DebtRecoveryLedgerSetStatusActionRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        $listStatus = CollectDebtLedger::listStatusAction();

        return [
            'data' => ['required', 'array'],
            'data.id' => ['required', 'numeric', 'min:1'],
            'data.status_contract' => ['numeric', Rule::in(array_keys($listStatus))],
            'data.status_excess' => ['numeric', Rule::in(array_keys($listStatus))],
            'data.status_report' => ['numeric', Rule::in(array_keys($listStatus))],
            'data.status_plan' => ['numeric', Rule::in(array_keys($listStatus))],
            'data.status_summary' => ['numeric', Rule::in(array_keys($listStatus))],
            'data.status_period' => ['numeric', Rule::in(array_keys($listStatus))],
            'data.status_email' => ['numeric', Rule::in(array_keys($listStatus))],
            'data.status_sms' => ['numeric', Rule::in(array_keys($listStatus))],
            'data.user_request_id' => ['required', 'string', 'max:255'],
            'data.description' => ['nullable', 'string', 'max:255'],
        ];
    }
    protected function passedValidation() {
        $params = $this->all();
        $data = array_map('trim', $params['data']);
        $params['data'] = $data;

        $this->merge($params);
    }

}

// End class
