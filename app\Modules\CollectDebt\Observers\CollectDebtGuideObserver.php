<?php

namespace App\Modules\CollectDebt\Observers;

use App\Modules\CollectDebt\Model\CollectDebtGuide;

class CollectDebtGuideObserver
{
  public function created(CollectDebtGuide $collectDebtGuide)
  {
    $paymentGuides = json_decode($collectDebtGuide->payment_guide, true);
		$paymentGuides[] = [
			'payment_method_code' => 'WALLET',
    	'payment_channel_code' => 'WALLET',
    	'payment_account_id' => $collectDebtGuide->contract_code,
			'other_data' => [
				"payment_account_name" => $collectDebtGuide->contract_code,
				"payment_account_number" => $collectDebtGuide->contract_code,
				"payment_account_branch" => "",
				"payment_account_bank_code" => ""
			]
		];

		$collectDebtGuide->payment_guide = json_encode($paymentGuides);
		$collectDebtGuide->save();
		return $collectDebtGuide;
  }
} // End class
