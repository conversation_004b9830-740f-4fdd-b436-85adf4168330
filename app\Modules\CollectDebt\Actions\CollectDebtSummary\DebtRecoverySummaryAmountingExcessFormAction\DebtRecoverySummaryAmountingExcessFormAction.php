<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessFormAction;


use Exception;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryAmountingExcessFormRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetAmountCanRefundAction\DebtRecoverySummaryGetAmountCanRefundAction;
use Carbon\Carbon;

class DebtRecoverySummaryAmountingExcessFormAction
{
  public function run(DebtRecoverySummaryAmountingExcessFormRequest $request): array
  {
    $collectDebtShare = CollectDebtShare::query()->where('contract_code', $request->json('data.contract_code'))->first();
    throw_if(!$collectDebtShare, new Exception('Hợp đồng không tồn tại'));

    $params['contract_code'] = $collectDebtShare->contract_code;

    $soTienCoTheHoan = app(DebtRecoverySummaryGetAmountCanRefundAction::class)->run($params);
    $paymentGuideIbOff = $collectDebtShare->getPaymentGuideItem('IB_OFF');

    return [
      'contract' => [
        ['label' => 'ma_hd', 'name' => 'Mã hợp đồng', 'value' => $collectDebtShare->contract_code],
        ['label' => 'ngay_hd', 'name' => 'Ngày hợp đồng', 'value' => sprintf('%s đến %s', $collectDebtShare->time_start_as_date->format('d/m/Y'), $collectDebtShare->time_end_as_date->format('d/m/Y'))],
        ['label' => 'so_tien_hd', 'name' => 'Số tiền hợp đồng', 'value' => $collectDebtShare->amount],
        ['label' => 'ma_gd', 'name' => 'Mã giao dịch', 'value' => 'Đang cập nhật'],
        ['label' => 'so_tien_thu_thanh_cong', 'name' => 'Số tiền thu thành công', 'value' => $soTienCoTheHoan['total_amount_can_refund'] ?? 0],
        ['label' => 'ma_yc', 'name' => 'Mã yêu cầu', 'value' => 'Đang cập nhật'],
        ['label' => 'ma_chung_tu', 'name' => 'Mã chứng từ', 'value' => 'Đang cập nhật'],
      ],
      'bank_account' => [
        ['label' => 'so_tk', 'name' => 'Số tài khoản', 'value' => $paymentGuideIbOff['payment_account_id']],
        ['label' => 'ten_tk', 'name' => 'Tên tài khoản', 'value' => $paymentGuideIbOff['payment_method_code']],
        ['label' => 'ten_nh', 'name' => 'Tên ngân hàng', 'value' => $paymentGuideIbOff['payment_channel_code']],
      ],
      'total_amount_refund' => $soTienCoTheHoan['total_amount_can_refund'] ?? 0
    ];
  }
} // End class
