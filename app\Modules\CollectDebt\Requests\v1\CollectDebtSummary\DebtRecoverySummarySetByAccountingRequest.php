<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoverySummarySetByAccountingRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data'                              => ['required', 'array'],
      'data.contract_code'                => ['required', 'string', 'max:50'],
      'data.total_amount_paid'            => ['required', 'numeric'],
      'data.total_fee_paid'               => ['nullable', 'numeric'],
      'data.fee_overdue_paid'             => ['nullable', 'numeric'],
      'data.fee_overdue_cycle_paid'       => ['nullable', 'numeric'],
      'data.fee_overdue_reduction'        => ['nullable', 'numeric'],
      'data.fee_overdue_cycle_reduction'  => ['nullable', 'numeric'],
      'data.total_amount_refund'          => ['nullable', 'numeric'],
      'data.is_overdue'                   => ['nullable', 'integer', Rule::in([1,2])],
      'data.is_over_cycle'                => ['nullable', 'integer', Rule::in([1,2])],
      'data.other_data'                   => ['nullable', 'json'],
      'data.data_over_cycle'              => ['nullable', 'json'],
      'data.status'                       => ['nullable', 'integer', Rule::in(array_keys(CollectDebtSummary::listStatus()))],

    ];
  }
} // End class
