<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyMoLaiLichSA\Task;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;

class UpdateDieuChinhOtherDataPlanMLLTask
{
	public function run(CollectDebtSchedule $plan, float $soTienGhiNhanThanhCongChoLich, float $soTienThuThanhCongTruocDo): int
	{
		$result = CollectDebtSchedule::query()->where('id', $plan->id)->update([
			'success_amount_debit' => $soTienGhiNhanThanhCongChoLich,
			'other_data' => $plan->putPlanOtherData([
				'type' => 'REQUEST_ADJUSTMENT',
				'data' => [],
				'note' => sprintf(
					'Điều chỉnh số tiền thành công từ %s xuống %s',
					Helper::priceFormat($soTienThuThanhCongTruocDo),
					Helper::priceFormat($soTienGhiNhanThanhCongChoLich)
				),
				'time_modified' => time()
			])
		]);

		if (!$result) {
			mylog(['Loi khong the cap nhat dieu chinh trong du tinh' => $result]);
			throw new Exception('Loi khong the cap nhat dieu chinh trong du tinh');
		}

		return $result;
	}
}
