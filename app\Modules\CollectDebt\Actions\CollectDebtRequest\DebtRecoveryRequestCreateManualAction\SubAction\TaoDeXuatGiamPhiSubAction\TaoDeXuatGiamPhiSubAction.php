<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoDeXuatGiamPhiSubAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtAction;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\GetReduceAndPrepayPlanAction\GetReduceAndPrepayPlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAction\DebtRecoveryRequestActionCreateAction\DebtRecoveryRequestActionCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\GetTongNoGocDaThuCuaHopDongSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\TaoYeuCauTrichTayTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoDeXuatGiamPhiSubAction\Task\TaoYeuCauGiamPhiTask;

class TaoDeXuatGiamPhiSubAction
{
  public $soTienPhiChamKyDuocGiam = 0;
  public $soTienPhiQuaHanDuocGiam = 0;

  public function run(
    CollectDebtShare $collectDebtShare,
    Collection $danhSachToanBoLichThu,
    DebtRecoveryRequestCreateManualRequest $request
  ) {
    $this->__checkDaThuDuGocHayChua($collectDebtShare);
    
    // Tạo đề xuất giảm phí, tuyệt đối không được động chạm vào công nợ
    $collectDebtRequest = app(TaoYeuCauGiamPhiTask::class)->run(
      $collectDebtShare,
      $danhSachToanBoLichThu,
      $request,
    );

    if($collectDebtRequest){
      $paramAction = [
          'type' => CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI,
          'request_id' => $collectDebtRequest->id,
          'action_code' => CollectDebtEnum::REQUEST_ACTION_CODE_CREATED,
          'created_by' => $collectDebtRequest->created_by,
          'description' => $collectDebtRequest->description,
          'other_data' => $request->json('data.attachment') ?? "{}",
          'time_created' => time(),
      ];

      $action = app(DebtRecoveryRequestActionCreateAction::class)->run($paramAction);
    }

    return $collectDebtRequest;
  }

  private function __checkDaThuDuGocHayChua(CollectDebtShare $collectDebtShare) {
    // Kiểm tra nợ gốc đã thu, nếu thu không đủ gốc -> bắn lỗi luôn
    $tongTienNoGocDaThu = app(GetTongNoGocDaThuCuaHopDongSubAction::class)->run($collectDebtShare->contract_code);
      
    $errorMessage = sprintf(
      'Bạn không thể tạo đề nghị giảm phí khi vẫn chưa thu hết nợ gốc. Số tiền ứng vốn HĐ: %s. Số tiền gốc đã thu thành công: %s',  
      Helper::priceFormat($collectDebtShare->amount, 'đ'), 
      Helper::priceFormat($tongTienNoGocDaThu, 'đ'),
    );

    throw_if($tongTienNoGocDaThu < $collectDebtShare->amount, new Exception($errorMessage));
  }
} // End class