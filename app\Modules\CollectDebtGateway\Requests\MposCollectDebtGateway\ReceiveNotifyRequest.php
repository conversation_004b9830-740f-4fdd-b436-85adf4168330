<?php

namespace App\Modules\CollectDebtGateway\Requests\MposCollectDebtGateway;

use Illuminate\Foundation\Http\FormRequest;

class ReceiveNotifyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data.deductionStatus' => 'required|string',
            'data.merchantId' => 'required|string',
            'data.withdrawBackAmount' => 'nullable|numeric',
            'data.mposRequestId' => 'required|string|max:50',
            'data.lendingReferenceId' => 'required|string|max:50',
            'data.lendingId' => 'required|string|max:255',
        ];
    }

    public function messages()
    {
        return [
            'data.deductionStatus.required' => 'Trường deductionStatus không để trống',
            'data.deductionStatus.string' => 'Trường deductionStatus phải đúng định dạng',
            'data.merchantId.required' => 'Trường merchantId không để trống',
            'data.merchantId.string' => 'Trường merchantId phải đúng định dạng',
            'data.withdrawBackAmount.required' => 'Trường withdrawBackAmount không để trống',
            'data.withdrawBackAmount.numeric' => 'Trường withdrawBackAmount phải đúng định dạng',
            'data.mposRequestId.required' => 'Trường mposRequestId không để trống',
            'data.mposRequestId.string' => 'Trường mposRequestId phải đúng định dạng',
            'data.mposRequestId.max' => 'Trường mposRequestId không được lớn hơn :max',
            'data.lendingReferenceId.required' => 'Trường lendingReferenceId không để trống',
            'data.lendingReferenceId.string' => 'Trường lendingReferenceId phải đúng định dạng',
            'data.lendingReferenceId.max' => 'Trường lendingReferenceId không được lớn hơn :max',
            'data.lendingId.required' => 'Trường lendingId không để trống',
            'data.lendingId.string' => 'Trường lendingId phải đúng định dạng',
            'data.lendingId.max' => 'Trường lendingId không được lớn hơn :max',
        ];
    }
}
