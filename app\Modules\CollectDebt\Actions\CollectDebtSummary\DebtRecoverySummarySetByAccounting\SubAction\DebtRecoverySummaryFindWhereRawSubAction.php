<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySetByAccounting\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Exception;

class DebtRecoverySummaryFindWhereRawSubAction
{
  public function run(string $whereRaw = '', bool $isThrowError=false): CollectDebtSummary
  {
    throw_if(empty($whereRaw), new Exception('Thiếu điều kiện truy vấn', 500));
    $CollectDebtSummary = CollectDebtSummary::whereRaw($whereRaw)->first();
    throw_if(!$CollectDebtSummary && $isThrowError, new Exception('Không tìm thấy thông tin'));
    return $CollectDebtSummary;
  }
} // End class