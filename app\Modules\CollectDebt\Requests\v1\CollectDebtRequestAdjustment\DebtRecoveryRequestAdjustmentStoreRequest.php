<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment;

use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryRequestAdjustmentStoreRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:0'], // Id lệnh trích
      'data.contract_code' => ['required', 'string', 'max:50'], // mã code hợp đồng
      'data.real_amount_success' => ['required', 'numeric', 'integer', 'min:0'], // số tiền THỰC TẾ mà đối tác trích được
      'data.description' => ['required', 'string', 'string:255', 'min:20'], // lý do điều chỉnh
      'data.attachments' => ['required', 'array'], // ảnh minh chứng điều chỉnh
      'data.attachments.*' => ['required', 'string'], // item ảnh là 1 string storePath upload
			'data.created_by' => ['required', 'string', 'json'], // người tạo
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['created_by'] = StandardizedDataFilter::getUserAdminStructCompact($params['data']['created_by']);

		// có option là do đầu webbackend đã convert số 0 thành string rỗng. Đây là ngoại lệ
		
		if ($params['data']['real_amount_success'] == '0.0') {
			$params['data']['real_amount_success'] = 0;
		}

    $this->merge($params);
  }
} // End class
