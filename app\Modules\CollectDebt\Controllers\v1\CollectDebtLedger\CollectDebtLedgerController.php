<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\CollectDebtLedgerResourceCollection;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerUpdateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerGetByIdRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerSetStatusRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerAccountingRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerGetByContractRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerSetStatusActionRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\HachToanYeuCauTrenSoAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\DebtRecoveryLedgerCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerUpdateAction\DebtRecoveryLedgerUpdateAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByIdAction\DebtRecoveryLedgerGetByIdAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSetStatusAction\DebtRecoveryLedgerSetStatusAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSearchDataAction\DebtRecoveryLedgerSearchDataAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSyncContractAction\DebtRecoveryLedgerSyncContractAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByContractAction\DebtRecoveryLedgerGetByContractAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccoutingEmailAction\DebtRecoveryLedgerAccoutingEmailAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingPeriodAction\DebtRecoveryLedgerAccountingPeriodAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\DebtRecoveryLedgerAccountingSummaryAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\HachToanTongHopAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\SubAction\GetLedgerNeedToAccountingSummarySA;

class CollectDebtLedgerController extends Controller {

    public function create(DebtRecoveryLedgerCreateRequest $request) {
        try {
            $collectDebtLedger = app(DebtRecoveryLedgerCreateAction::class)->run($request);
            return $this->successResponse($collectDebtLedger->only(['id']), $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function update(DebtRecoveryLedgerUpdateRequest $request) {
        try {
            $collectDebtLedger = app(DebtRecoveryLedgerUpdateAction::class)->run($request);
            return $this->successResponse($collectDebtLedger->toArray(), $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function getById(DebtRecoveryLedgerGetByIdRequest $request) {
        try {
            $collectDebtLedgers = app(DebtRecoveryLedgerGetByIdAction::class)->run($request);
            return $this->successResponse($collectDebtLedgers->toArray(), $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function getByContract(DebtRecoveryLedgerGetByContractRequest $request) {
        try {
            $collectDebtLedgers = app(DebtRecoveryLedgerGetByContractAction::class)->run($request);
            return $this->successResponse([
                        'rows' => $collectDebtLedgers->count(),
                        'data' => $collectDebtLedgers->toArray()
                            ], $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function searchData(Request $request) {
        try {
            $collectDebtLedgersPaginate = app(DebtRecoveryLedgerSearchDataAction::class)->run($request);
            $collectDebtLedgersResouce = new CollectDebtLedgerResourceCollection($collectDebtLedgersPaginate);
            $response = $collectDebtLedgersResouce->toResponse($request)->getData(true);
            return $this->successResponse($response, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    /*
     * chỉ set 1 trạng thái trong bảng
     */

    public function setStatus(DebtRecoveryLedgerSetStatusRequest $request) {
        try {
            $collectDebtLedger = app(DebtRecoveryLedgerSetStatusAction::class)->run($request);
            return $this->successResponse($collectDebtLedger->toArray(), $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    /*
     * set các trạng thái khác trong bảng
     */

    public function setStatusAction(DebtRecoveryLedgerSetStatusActionRequest $request) {
        try {
            $collectDebtLedger = app(DebtRecoveryLedgerSetStatusAction::class)->setStatusOther($request);
            return $this->successResponse($collectDebtLedger->toArray(), $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function accouting(DebtRecoveryLedgerAccountingRequest $request) {
			try {
				$ledgerIds = app(HachToanYeuCauTrenSoAction::class)->initHachToan();
				return $this->successResponse($ledgerIds, $request);
			} catch (\Throwable $th) {
				return $this->errorResponse($th->getCode(), Helper::traceError($th));
			}
    }

    public function accoutingSummary(Request $request) {
			try {
				$ledgerIds = app(HachToanTongHopAction::class)->run();
				return $this->successResponse(['list_schedule_accouting_done' => $ledgerIds], $request);
			} catch (\Throwable $th) {
				return $this->errorResponse($th->getCode(), Helper::traceError($th));
			}
    }

    public function accoutingEmail(Request $request) {
//        DB::beginTransaction();
        try {
            $collectDebtLedger = app(DebtRecoveryLedgerAccoutingEmailAction::class)->run($request);
//            DB::commit();
            return $this->successResponse(['list_schedule_accouting_done' => $collectDebtLedger], $request);
        } catch (\Throwable $th) {
//            DB::rollBack();
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function accoutingPeriod(Request $request) {
        DB::beginTransaction();

        try {
            $collectDebtLedger = app(DebtRecoveryLedgerAccountingPeriodAction::class)->run($request);
            DB::commit();
            return $this->successResponse(['list_schedule_accouting_done' => $collectDebtLedger], $request);
        } catch (\Throwable $th) {
           DB::rollBack();
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function syncContract(Request $request) {
        try {
            $collectDebtLedger = app(DebtRecoveryLedgerSyncContractAction::class)->run($request);
            return $this->successResponse($collectDebtLedger, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

} // End class

// End class
