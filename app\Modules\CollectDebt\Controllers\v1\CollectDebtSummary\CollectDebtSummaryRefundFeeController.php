<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary;

use DB;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPlusFeeRefundRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryCancelFeeRefundRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryRequestRefundFeeRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryRefundFeeBuildFormRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPlusFeeRefundAction\DebtRecoverySummaryPlusFeeRefundAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCancelFeeRefundAction\DebtRecoverySummaryCancelFeeRefundAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryRequestRefundFeeAction\DebtRecoverySummaryRequestRefundFeeAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryRefundFeeBuildFormAction\DebtRecoverySummaryRefundFeeBuildFormAction;

class CollectDebtSummaryRefundFeeController extends Controller
{
	public function DebtRecoverySummaryRefundFeeBuildForm(DebtRecoverySummaryRefundFeeBuildFormRequest $request)
	{
		try {
			$refundFeeFormResult = app(DebtRecoverySummaryRefundFeeBuildFormAction::class)->run($request);
			return $this->successResponse($refundFeeFormResult, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoverySummaryRequestRefundFee(DebtRecoverySummaryRequestRefundFeeRequest $request)
	{
		DB::beginTransaction();
		try {
			$collectDebtRecoveryLog = app(DebtRecoverySummaryRequestRefundFeeAction::class)->run($request);
			DB::commit();
			return $this->successResponse(['debt_recovery_id' => $collectDebtRecoveryLog->id], $request, 200, 'Tạo yêu cầu hoàn phí thành công');
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoverySummaryPlusFeeRefund(Request $request)
	{
		return $this->successResponse(['data' => 'ok'], $request, 200);
	}

	public function DebtRecoverySummaryCancelFeeRefund(Request $request)
	{
		return $this->successResponse(['data' => 'ok'], $request, 200);
	}
} // End class
