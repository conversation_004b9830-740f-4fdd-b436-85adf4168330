<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerUpdateAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerUpdateRequest;

class DebtRecoveryPartnerUpdateAction
{
  public function run(DebtRecoveryPartnerUpdateRequest $request): CollectDebtPartner
  {
    $partnerRequestId =  $request->json('data.partner_request_id', '');
    $collectDebtRequest = CollectDebtRequest::where('partner_request_id', $partnerRequestId)->firstOrFail();
    
    $collectDebtPartner = CollectDebtPartner::find($request->json('data.id'));
    throw_if(!$collectDebtPartner->isPartnerChuaXuLy(), new Exception('Trạng thái của parner phải = 1 (No Proces)'));

    $paramUpdate = $request->only([
      'data.payment_channel_code',
      'data.payment_method_code',
      'data.payment_account_id',
      'data.partner_request_id',
      'data.partner_transaction_id',
      'data.amount_payment',
      'data.amount_receiver',
      'data.fee',
      'data.request_exists',
      'data.response',
      // 'data.other_data',
      'data.description',
      'data.updated_by',
      'data.time_updated',
    ])['data'];
    
   
  
    $collectDebtPartner->forceFill($paramUpdate)->update();
    return $collectDebtPartner->refresh();
  }
} // End class