<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecoredAction;

use Exception;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSetting;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecoredAction\SubAction\ThucHienGhiSoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecoredAction\SubAction\SendDebtRecoveryLedgerCreate;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecoredAction\SubAction\SendDebtRecoveryLedgerCreateSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\GetRequestByRawQueryAsCollectionSubAction;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use DB;

class DebtRecoveryRequestRecoredAction
{
	private array $__exceptYeuCauIds = [];

  public array $requestIdsRecored = [
    'record_success_ids' => [],
    'record_errors_ids' => []
  ];
  
	public function initGhiSo(Request $request) {
		for ($i=1; $i<=5; $i++) {
			DB::beginTransaction();
			try {
				$this->run($request);
				DB::commit();
			}catch (\Throwable $th) {
				DB::rollBack();
				throw $th;
			}finally {
				sleep(1);
			}
		}
	}

  /**
   * Job thực hiện việc ghi sổ cho các yêu cầu đạt đủ điều kiện
   *
   * @param Request $request [explicite description]
   *
   * @return array
   */
  public function run(Request $request): array
  {
    $listYeuCauGhiSo = CollectDebtRequest::query()  
        ->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
        ->where(function ($q) {
          $q->orWhere(function ($query) {
            // Trích MPOS cho cae 2 luồng
            $query->where('payment_method_code', 'MPOS')
                  ->whereIn('status', [CollectDebtEnum::REQUEST_STT_DA_DUYET, CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA])
                  ->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA);
          })
          
          ->orWhere(function ($query) { 
            // Tự động
            $query->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
                  ->where('status', CollectDebtEnum::REQUEST_STT_DA_DUYET)
                  ->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA);
          })
          // ->orWhere(function ($query) {
          //   // Trích tay 1 phần
          //   $query->where('status', CollectDebtEnum::REQUEST_STT_DA_DUYET)
          //         ->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
          //         ->whereRaw(
          //           sprintf(
          //             "JSON_SEARCH(debt_recovery_request.other_data, 'one', '%s', NULL, '$[*]') IS NOT NULL", 
          //             'TRICH_MOT_PHAN'
          //           )
          //         )
          //         ->where('payment_method_code', '!=', 'MPOS');
          // })
          ->orWhere(function ($query) {
            // Tất toán giảm phí
            $query->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
                  ->whereHas('collectDebtRequestActions', function ($q) {
                    $q->where('action_code', 'APPROVE2')
                      ->where('type', CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI);
                  });
          });
        })
				->whereRaw("IFNULL(time_updated, time_created) + 30 < ?", [now()->timestamp]);
		
		if (!empty($this->__exceptYeuCauIds)) {
			$listYeuCauGhiSo = $listYeuCauGhiSo->whereNotIn('id', $this->__exceptYeuCauIds);
		}
				
    $listYeuCauGhiSo = $listYeuCauGhiSo->select($request->json('data.fields', ['*']))
																			 ->limit(1)
																			 ->lock(" FOR UPDATE SKIP LOCKED ")
																			 ->get();

    mylog(['Yêu cầu đạt đủ điều kiện để ghi sổ là:' => $listYeuCauGhiSo->pluck('id')]);

    throw_if($listYeuCauGhiSo->isEmpty(), new Exception('Không có yêu cầu nào đủ điều kiện ghi sổ', 404));

    $listYeuCauGhiSo->map(function (CollectDebtRequest $collectDebtRequest) use ($request) {
			$this->__exceptYeuCauIds[] = $collectDebtRequest->id;

			$collectDebtPartner = CollectDebtPartner::query()
																							->where('payment_method_code', $collectDebtRequest->payment_method_code)
																							->where('partner_request_id', $collectDebtRequest->partner_request_id)
																							->first();

			// Không có partner, thì tăng thời gian time_updated lên để né
			if ( !$collectDebtPartner && !$collectDebtRequest->isTrichTayGiamPhi() ) {
				CollectDebtRequest::query()
													->where('contract_code', $collectDebtRequest->contract_code)
													->where('id', $collectDebtRequest->id)
													->update(['time_updated' => now()->timestamp]);

				return $collectDebtRequest;
			}


      // Chưa ghi sổ => Thực hiện ghi sổ
      if ($collectDebtRequest->isUnRecorded()) {
				/**
				 * Kiểm tra với kênh thu MPOS, nếu số  tiền của partner mà khác với số tiền của yc thì sẽ set 
				 * bằng luôn số tiền của partner và cộng thời gian time_updated lên
				 */
				if ($collectDebtRequest->payment_method_code == 'MPOS') {
					if ($collectDebtRequest->amount_receiver != $collectDebtPartner->amount_receiver) {
						
						CollectDebtRequest::query()
															->where('contract_code', $collectDebtRequest->contract_code)
															->where('id', $collectDebtRequest->id)
															->update([
																'time_updated' => now()->timestamp,
																'amount_receiver' => $collectDebtPartner->amount_receiver
															]);
														
						return $collectDebtRequest;
					}
				}


        $collectDebtRequestRC = app(ThucHienGhiSoSubAction::class)->run($collectDebtRequest, $request);

        if ($collectDebtRequestRC->isRecorded()) {
          $latestOtherData = $collectDebtRequestRC->putOtherData([
            'type' => 'RECORED',
            'note' => 'Ghi sổ THÀNH CÔNG',
            'time_modified' => time(),
            'data' => []
          ]);

          $this->requestIdsRecored['record_success_ids'][] = $collectDebtRequestRC->id;
        }
  
        if ($collectDebtRequestRC->isRecordedError()) {
          $latestOtherData = $collectDebtRequestRC->putOtherData([
            'type' => 'RECORED',
            'note' => 'Ghi sổ THẤT BẠI',
            'time_modified' => time(),
            'data' => []
          ]);

          $this->requestIdsRecored['record_errors_ids'][] = $collectDebtRequestRC->id;
        }

        CollectDebtRequest::query()->where('id', $collectDebtRequestRC->id)->update(['other_data' => $latestOtherData]);
      }

      return $collectDebtRequest;
    });

    return $this->requestIdsRecored;
  }
}  // End class