<?php
namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Database\Eloquent\Collection;

class DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST
{
  public function run(Collection $plans, string $paymentMethodCode='MPOS')
  {
    if ($plans->isEmpty()) {
      return $plans;
    }

    return $plans->map(function (CollectDebtSchedule $plan) use ($paymentMethodCode) {
      $otherData = $plan->getPlanOtherData();
    
      $index = collect($otherData)->search(function ($item) {
        return $item['type'] == 'OTHER';
      });

      $typeOther = collect($otherData)->where('type', 'OTHER')->first();
      $typeOther['data']['request_created_channel'] = $paymentMethodCode;

      $otherData[$index] = $typeOther;

      $plan->other_data = json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
      $plan->save();
    });
  }
}
