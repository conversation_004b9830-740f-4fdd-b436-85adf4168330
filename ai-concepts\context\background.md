# Bối cảnh nghiệp vụ

## Hệ thống hiện tại

Quy trình xử lý khoản vay hiện tại bao gồm các bước:

1. **Nhân viên telesales** tiếp cận tập khách hàng tiềm năng và giới thiệu dịch vụ vay.
2. **Khách hàng cung cấp thông tin**, qua đó tạo hồ sơ khoản vay.
3. Hệ thống tiến hành **đ<PERSON><PERSON> gi<PERSON>, thẩm định và phê duyệt khoản vay**.
4. <PERSON>u khi được duyệt, **khoản vay được giải ngân**.
5. Giai đoạn cuối cùng là **thu hồi nợ theo kỳ hạn hoặc thỏa thuận**.

Toàn bộ quy trình này hiện đang được xử lý trên một hệ thống nội bộ đã đưa vào vậ<PERSON> hành, phục vụ trung bình khoảng **500 hợp đồng mỗi ngày**.

---

## Các vấn đề hiện tại

Dù hệ thống đang chạy thực tế, vẫn tồn tại nhiều vấn đề lớn cản trở hiệu quả mở rộng:

- ⚙️ **Hiệu suất thấp**: Một số job xử lý bị chậm, có thể do query chưa được tối ưu tốt.
- 🧩 **Logic thu hồi rời rạc, khó bảo trì**: Nhiều phần nghiệp vụ được triển khai phân tán, không theo một mô hình logic thống nhất, gây khó khăn khi cập nhật hoặc mở rộng.
- 📉 **Thiếu tracking & thống kê hiệu quả**: Rất khó đánh giá hiệu suất nhân viên thu hồi, tiến độ từng hồ sơ hoặc tổng quan hệ thống.
- 🧠 **Xử lý thủ công nhiều “case đặc biệt” (egg case)**: Ví dụ như hồ sơ lỗi, cần chỉnh sửa số liệu, sửa logic phí theo trường hợp cụ thể.

---

## Đặc thù nghiệp vụ thu hồi

Nghiệp vụ thu hồi trong hệ thống này có tính phức tạp cao, thể hiện ở:

- 📚 **Đa dạng khoản vay**: Bao gồm vay theo ngày, vay theo kỳ, vay ứng vốn qua đối tác... Mỗi loại có cấu trúc và cách tính phí riêng.
- 🧮 **Hạch toán và logic sinh phí phức tạp**: Tùy vào loại khoản vay và trạng thái khách hàng, hệ thống cần tính lãi, phí trễ, phí phát sinh, v.v... Điều này khiến phần logic xử lý rất dễ sai lệch nếu không chuẩn hóa.

---

## Động lực triển khai hệ thống mới

Công ty đang tăng trưởng nhanh về quy mô khách hàng và số lượng hồ sơ cần xử lý, kéo theo áp lực về hiệu suất, độ ổn định và khả năng mở rộng. Do đó, hệ thống mới được xây dựng nhằm:

- 🔄 **Tối ưu hiệu năng**: Hệ thống cần chạy nhanh, giảm thời gian xử lý, tránh tắc nghẽn khi số lượng hợp đồng lớn.
- 🤖 **Tăng mức độ tự động hóa**: Giảm khối lượng xử lý thủ công, tăng khả năng tự phát hiện lỗi, xử lý ngoại lệ.
- 📈 **Đáp ứng tăng trưởng quy mô**: Thiết kế có khả năng scale cả về mặt dữ liệu lẫn logic nghiệp vụ trong tương lai.

