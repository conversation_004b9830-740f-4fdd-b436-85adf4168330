<?php

namespace App\Modules\CollectDebt\Rules;

use Illuminate\Contracts\Validation\Rule;

class UserInteractiveRule implements Rule
{
    private $__errorMessage; 
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
      $userInterative = json_decode($value, true);
      if ( empty($userInterative['id']) ) {
        $this->__errorMessage = 'Thông tin người tương tác bị thiếu trường `id`'; 
        return false;
      }

      if ( empty($userInterative['username']) ) {
        $this->__errorMessage = 'Thông tin người tương tác bị thiếu trường `username`'; 
        return false;
      }

      if ( empty($userInterative['mobile']) ) {
        $this->__errorMessage = 'Thông tin người tương tác bị thiếu trường `mobile`'; 
        return false;
      }

      return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
      return $this->__errorMessage;
    }
}
