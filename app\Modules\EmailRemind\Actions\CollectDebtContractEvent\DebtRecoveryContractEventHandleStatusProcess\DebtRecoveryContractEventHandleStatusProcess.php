<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventHandleStatusProcess;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class DebtRecoveryContractEventHandleStatusProcess
{
	private array $__eventExceptIds = [];

	public function initXuLyEventBiTreo() {
		$returnData = [];

		mylog(['[ENTER]' => 'Xu ly event bi treo']);

		for ($i = 0; $i <= 40; $i++) {
			mylog(['--------------------------------------' => sprintf('%s ------------------------------', $i)]);

			try {
				$result = $this->run();

				if ($result == 'EMPTY') {
					$returnData[] = 'Khong co thong tin event can xu ly';
					break;
				}

				if ($result && optional($result)->id) {
					$returnData[] = $result->only(['id', 'contract_code']);
				}
			} catch (\Throwable $th) {
				mylog(['[LOI XY LY EVENT]' => Helper::traceError($th)]);
				@TelegramAlert::sendEmails(Helper::traceError($th));
				// throw $th;
				continue;
			} finally {
				
			}
		}

		return $returnData;
	}

	public function run()
	{
		$collectDebtEvent = CollectDebtContractEvent::query()
				->where('status', CollectDebtEnum::EVENT_STT_DANG_GUI)
				->whereBetween('time_created', [
						now()->startOfDay()->timestamp, 
						now()->endOfDay()->timestamp
				])
				->whereRaw('time_updated + (2 * 60) < ?', [ now()->timestamp ]);
		
		if (!empty($this->__eventExceptIds)) {
			$collectDebtEvent = $collectDebtEvent->whereNotIn('id', $this->__eventExceptIds);
		}

		$collectDebtEvent = $collectDebtEvent->first();

		if (!$collectDebtEvent) {
			mylog(['[EMPTY]' => 'khong co thong tin event can xu ly']);
			return 'EMPTY';
		}

		$this->__eventExceptIds[] = $collectDebtEvent->id;
		mylog(['HD can xu ly lai event la' => $collectDebtEvent->contract_code]);

		$input = [
			'status' => CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL,
			'time_updated' => now()->timestamp
		];

		$updateResult = CollectDebtContractEvent::query()
																						->where('id', $collectDebtEvent->id)
																						->where('status', CollectDebtEnum::EVENT_STT_DANG_GUI)
																						->update($input);

		if (!$updateResult) {
			mylog(['[LOI CAP NHAT STATUS BI TREO]' => 'Khong the dua status event ve trang thai cu']);
			return;
		}

		return $collectDebtEvent;
	}
}
