<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByIdAction;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\DebtRecoveryRequestFindRawQuerySubAction;

class DebtRecoveryRequestGetByIdAction
{
  public function run(int $id, array $fields=['*']): CollectDebtRequest
  {
    $collectDebtRequest = app(DebtRecoveryRequestFindRawQuerySubAction::class)->run(
      'id = ' . $id,
      $fields
    );

    $collectDebtRequest->contract_data = $collectDebtRequest->collectDebtShare->contract_data;
    return $collectDebtRequest;
  }
} // End class