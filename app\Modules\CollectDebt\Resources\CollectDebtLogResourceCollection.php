<?php

namespace App\Modules\CollectDebt\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class CollectDebtLogResourceCollection extends ResourceCollection
{
	/**
	 * Transform the resource collection into an array.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return array
	 */
	public function toArray($request)
	{
		return [
			'data' => $this->collection->toArray(),
			'links' => [],
			'meta' => []
		];
	}
}
