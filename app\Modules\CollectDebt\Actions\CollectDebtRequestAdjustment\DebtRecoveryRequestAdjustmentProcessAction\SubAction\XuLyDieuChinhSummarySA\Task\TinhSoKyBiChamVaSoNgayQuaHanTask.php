<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhSummarySA\Task;

use App\Modules\CollectDebt\Actions\CollectDebtSchedule\GetPlanSummaryHandleAfterAccoutingAction\GetPlanSummaryHandleAfterAccoutingAction;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class TinhSoKyBiChamVaSoNgayQuaHanTask
{
  const CO_CK = 1;
  const KHONG_CK = 2;
  const CO_QH = 1;
  const KHONG_QH = 2;

  public function run(CollectDebtSummary $collectDebtSummary): array
  {
    $returnData = [
      'number_over_cycle' => 0,
      'number_day_overdue' => 0,
      'is_overdue' => self::KHONG_CK,
      'is_over_cycle' => self::KHONG_QH
    ];

    $plans = app(GetPlanSummaryHandleAfterAccoutingAction::class)->run($collectDebtSummary->contract_code);

    $soKyBiCham = collect($plans)->where('overdue_cycle', GetPlanSummaryHandleAfterAccoutingAction::CO_CHAM_KY)->count();

    $returnData['number_over_cycle'] = $soKyBiCham;
    if ($soKyBiCham > 0) {
      $returnData['is_over_cycle'] = self::CO_CK;
    }

    $soNgayQuaHan = $collectDebtSummary->getSoNgayBiQuaHan();
    $returnData['number_day_overdue'] = $soNgayQuaHan;
    if ($soNgayQuaHan > 0) {
      $returnData['is_overdue'] = self::CO_QH;
    }

    return $returnData;
  }
}