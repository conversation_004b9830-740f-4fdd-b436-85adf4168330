<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentSetStatusAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use Exception;

class XuLyYeuCauDieuChinhBiHuySA
{
	/**
	 * Note: 18.03.2024
	 * Nếu là lệnh trích ngay, do con người bấm hủy thì BẮT BUỘC KHÔNG CHO HỦY YÊU CẦU ĐIỀU CHỈNH
	 * @param CollectDebtRequestAdjustment $collectDebtRequestAdjustment
	 */
	public function run(CollectDebtRequestAdjustment $collectDebtRequestAdjustment)
	{
		$collectDebtPartner = CollectDebtPartner::query()->find($collectDebtRequestAdjustment->reference_id);
		throw_if(!$collectDebtPartner, new Exception('Không tìm thấy thông tin công nợ'));

		$collectDebtRequest = CollectDebtRequest::query()
																						->where('partner_request_id', $collectDebtPartner->partner_request_id)
																						->first();

		throw_if(!$collectDebtPartner, new Exception('Không tìm thấy thông tin yêu cầu trích'));

		/**
		 * + Cố tình bấm hủy yc điều chỉnh
		 * + YC trích là loại rút tiền nhanh (trích ngay)
		 * + Tồn tại thời gian con người bấm hùy
		 */
		if (
			!empty($collectDebtRequestAdjustment->time_canceled)
			&& $collectDebtRequest->isRutTienNhanh() 
			&& !empty($collectDebtRequest->time_canceled)
		) {
			throw new Exception('Bạn không thể hủy yêu cầu điều chỉnh bởi vì bạn đã hủy lệnh RÚT TIỀN NHANH trước đó rồi');
		}

		return true;
	}
}
