<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class TaoEventMailSapToiHanTatToanSubAction
{
	public function run(CollectDebtShare $collectDebtShare, CollectDebtSummary $collectDebtSummary, $categoryCareCode='NOTIFY_CONTRACT_DUE')
	{
		$companyData = json_decode($collectDebtShare->other_data, true);
		$profileData = json_decode($collectDebtShare->profile_data, true);
		$contractData = json_decode($collectDebtShare->contract_data, true);
		$payment = json_decode($collectDebtShare->payment_guide, true);

		$param = [
			'contract_code' => $collectDebtShare->contract_code,
			'category_care_code' => $categoryCareCode,
			'service_care_code' => 'MAIL',
			'data' => json_encode([
				'company' => $companyData['company']['data'],
				'profile' => $profileData,
				'contract' => $contractData,
				'payment' => $payment
			], JSON_UNESCAPED_UNICODE),
			'description' => '',
			'content' => null,
			'other_data' => json_encode([
				'summary' => $collectDebtSummary->toArray()
			], JSON_UNESCAPED_UNICODE),
			'status' => 1,
			'number' => 1,
			'time_created' => time(),
			'time_updated' => time()
		];

		$currentEventSapToiHan = CollectDebtContractEvent::query()
			->where('contract_code', $collectDebtShare->contract_code)
			->whereBetween('time_created', [
				now()->startOfDay()->timestamp,
				now()->endOfDay()->timestamp,
			])
			->where('service_care_code', 'MAIL')
			->where('category_care_code', $categoryCareCode)
			->first();
			
		if (!$currentEventSapToiHan) {
			$collectDebtContractEvent = CollectDebtContractEvent::query()->forceCreate($param);
			return $collectDebtContractEvent;
		}

		return null;
	}
} // End class