<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\DebtRecoveryRequestFinishCutOffTimeAction;
use Illuminate\Support\Facades\DB;

class CollectDebtRequestCutOffController extends Controller
{
	public function handlerCutOff(Request $request)
	{
		try {
			$yeuCauCutOffIds = app(DebtRecoveryRequestFinishCutOffTimeAction::class)->initCutOff();
			return $this->successResponse($yeuCauCutOffIds, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
