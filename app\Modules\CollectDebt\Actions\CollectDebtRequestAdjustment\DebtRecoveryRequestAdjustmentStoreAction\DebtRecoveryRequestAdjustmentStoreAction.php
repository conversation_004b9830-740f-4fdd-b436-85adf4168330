<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentStoreAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentStoreRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentCreateAction\DebtRecoveryRequestAdjustmentCreateAction;

class DebtRecoveryRequestAdjustmentStoreAction
{
	public function run(DebtRecoveryRequestAdjustmentStoreRequest $request): CollectDebtRequestAdjustment
	{
		$id = $request->json('data.id');
		$contractCode = $request->json('data.contract_code');

		$collectDebtRequest = CollectDebtRequest::query()
			->where('id', $id)
			->where('contract_code', $contractCode)
			->where('status', CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH)
			->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
			->where('payment_method_code', 'MPOS')
			->first();

		if (!$collectDebtRequest) {
			throw new Exception('Không tìm thấy lệnh trích của bạn');
		}


		$soTienGhiNhanThanhCongTrenYeuCau = $collectDebtRequest->amount_receiver;
		$soTienDieuChinh = $request->json('data.real_amount_success');

		throw_if(
			$soTienDieuChinh >= $soTienGhiNhanThanhCongTrenYeuCau,
			new Exception('Số tiền điều chỉnh phải nhỏ hơn số tiền ghi nhận thành công của lệnh trích')
		);

		$collectDebtPartner = CollectDebtPartner::query()
			->where('partner_transaction_id', $collectDebtRequest->partner_transaction_id)
			->first();

		throw_if(!$collectDebtPartner, new Exception('Không tìm thấy bản ghi nhận tiền của yêu cầu'));

		// Kiểm tra xem partner này đã được tạo yêu cầu điều chỉnh hay chưa, nếu rồi thì bắn lỗi luôn
		$checkExistRA = CollectDebtRequestAdjustment::query()->where('reference_id', $collectDebtPartner->id)->first();
		if ($checkExistRA) {
			mylog(['ban ghi ra' => $checkExistRA->id]);
			throw new Exception('Lệnh trích của bạn đã được tạo yêu cầu điều chỉnh. Từ chối xử lý');
		}

		// Thực hiện tạo yêu cầu điều chỉnh 
		$planData = json_decode($collectDebtRequest->plan_data, true);

		$params = [
			'data' => [
				'contract_code' => $collectDebtRequest->contract_code,
				'type' => CollectDebtEnum::REQUEST_ADJUSTMENT_TYPE_DIEU_CHINH_DOI_TAC,
				'reference_id' => $collectDebtPartner->id,
				'reference_data' => $collectDebtPartner->toJson(),
				'amount_receiver_on_partner' => $soTienDieuChinh, // số tiền thực tế mà MPOS trích được
				'amount_before' => $collectDebtPartner->amount_receiver,
				'amount_after' => $soTienDieuChinh,
				'description' => $request->json('data.description'),
				'other_data' => json_encode([
					[
						'type' => 'PLAN',
						'note' => 'Thông tin lịch cần mở',
						'data' => $planData,
						'time_modified' => now()->timestamp
					],

					[
						'type' => 'ATTACHMENTS',
						'note' => 'Ảnh minh chứng',
						'data' => $request->json('data.attachments'),
						'time_modified' => now()->timestamp
					]
				]),
				'status' => CollectDebtEnum::REQUEST_ADJUSTMENT_STT_MOI_TAO,
				'time_created' => now()->timestamp,
				'created_by' => $request->json('data.created_by'),
				'profile_id' => $collectDebtRequest->profile_id,
			]
		];

		$rq = new DebtRecoveryRequestAdjustmentCreateRequest();
		$rq->setJson(new ParameterBag($params));
		$collectDebtRequestAdjustment = app(DebtRecoveryRequestAdjustmentCreateAction::class)->run($rq);

		if (!$collectDebtRequestAdjustment) {
			throw new Exception('Lỗi không tạo được yêu cầu điều chỉnh');
		}

		return $collectDebtRequestAdjustment;
	} // End method
} // End class
