<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtLedger;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class DebtRecoveryLedgerAccountingRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {

    return [
      'data' => ['array'],
    ];
  }

  // protected function passedValidation()
  // {
    
  // }
} // End class
