<?php

use Illuminate\Support\Facades\Route;

$namespace = 'App\Modules\Mock\Controllers';
Route::group(['middleware' => ['checktoken'/*,'checkip'*/], 'namespace' => $namespace], function () {
	$env = env('APP_ENV', 'production');
	
	if ($env == 'local' || $env == 'development') {
		Route::get('mock/create-guide', ['uses' => 'DefaultController@create'])->name('mock.create-guide');
		// Route::get('mock/list-guide', ['uses' => 'DefaultController@index'])->name('mock.list-guide');
		Route::post('mock/store-guide', ['uses' => 'DefaultController@store'])->name('mock.store-guide');
		Route::any('mock/detail-guide/{id}', ['uses' => 'DefaultController@detail'])->name('mock.detail-guide');

		Route::get('mock/request', ['uses' => 'MockRequestController@index'])->name('mock-request.contract');
		Route::post('mock/request/store', ['uses' => 'MockRequestController@store'])->name('mock-request.contract.store');

		Route::get('mock/congno', ['uses' => 'MockCongNoController@index'])->name('mock-congno.index');
		Route::post('mock/congno/store', ['uses' => 'MockCongNoController@store'])->name('mock-congno.store');
	}
});
