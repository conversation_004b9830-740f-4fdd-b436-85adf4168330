<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetRefundTransByContractAction;

use App\Lib\NextlendCore;
use Exception;

class DebtRecoveryRequestGetRefundTransByContractAction
{
	public function run(string $contractCode = '')
	{
		throw_if(empty($contractCode), new Exception('Mã HĐ không tồn tại'));

		$payload = [
			"condition" => 1,
			"start" => 0,
			"field" => "*",
			"orderby" => " time_created desc",
			"groupby" => "",
			"otherWhere" => "",
			"as_equal" => [
				"id" => null
			],
			"as_like" => [],
			"as_like_after" => [],
			"as_like_befor" => [],
			"as_other_search" => [
				"contract_code" => $contractCode,
				"merchant_email" => "",
				"transaction_id" => ""
			],
			"user_id_request" => 4,
			"more_than" => [
				"=" => [
					"id" => null,
					"cashout_request_id" => "",
					"status" => "",
					"partner_id" => "",
					"debt_recovery_partner_id" => ""
				],
				">=" => [
					"time_created" => "",
					"time_accepted" => ""
				],
				"<=" => [
					"time_created" => "",
					"time_accepted" => ""
				]
			],
			"between" => "",
			"as_in" => [],
			"export" => false
		];


		$refundTransactions = app(NextlendCore::class)->callRequest($payload, 'RequestRefund_getPageData', 'GET');
		$results = $refundTransactions->decryptData();
		return $results;
	}
} // End class