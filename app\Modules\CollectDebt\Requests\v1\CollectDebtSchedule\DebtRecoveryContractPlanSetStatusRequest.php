<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryContractPlanSetStatusRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:1'],
      'data.status' => [
        'required', 
        'numeric', 
        Rule::in([
          CollectDebtEnum::SCHEDULE_STT_MOI,
          CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN,
          CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH,
          CollectDebtEnum::SCHEDULE_STT_DA_HUY,
        ])
      ],
      'data.user_request_id' => ['required', 'string', 'max:255'],
      'data.description' => ['nullable', 'string', 'max:255'],
    ];
  }
} // End class
