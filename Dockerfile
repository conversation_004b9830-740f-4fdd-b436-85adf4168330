FROM yiisoftware/yii2-php:7.4-apache

WORKDIR /app/web

ARG APM_PHP_AGENT_VER=1.1
RUN curl -Lo apm-agent-php.deb https://github.com/elastic/apm-agent-php/releases/download/v${APM_PHP_AGENT_VER}/apm-agent-php_${APM_PHP_AGENT_VER}_all.deb && \
    dpkg -i apm-agent-php.deb

COPY config-devops/apache-vhost.conf /etc/apache2/sites-available/000-default.conf

# Apache configuration
ENV APACHE_DOCUMENT_ROOT=/app/web/public

# Copy code and run composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer
COPY . .

RUN composer install --no-dev && chown -R www-data:www-data /app/web