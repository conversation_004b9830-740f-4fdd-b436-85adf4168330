<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryUpdateAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSummaryEditHistory;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryUpdateRequest;
use Exception;

class DebtRecoverySummaryUpdateAction
{

	public function run(DebtRecoverySummaryUpdateRequest $request)
	{
		$contractCode = trim($request->json('data.contract_code'));
		$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();
		throw_if(!$collectDebtSummary, new Exception('Không tìm thấy tổng hợp của HĐ này'));
		throw_if($collectDebtSummary->isHopDongDaTatToan(), new Exception('Hop dong da tat toan, khong the sua tong hop'));

		
		$history = $collectDebtSummary->toArray();
		unset($history['contract_data']);
		unset($history['other_data']);

		$collectDebtSummaryEditHistory = CollectDebtSummaryEditHistory::query()
																																	->forceCreate([
																																		'contract_code' => $collectDebtSummary->contract_code,
																																		'reason' => $request->json('data.reason'),
																																		'time_edited' => now()->timestamp,
																																		'edited_by' => $request->json('data.edited_by'),
																																		'current_summary_data' => json_encode($history)
																																	]);
		$paramUpdate = $request->only([
			'data.fee_overdue', // phi qua han
			'data.fee_overdue_cycle', // phi cham ky
			'data.total_amount_receiver', // tong tien nhan
			'data.total_amount_excess_revenue', // tong tien thu thua
			'data.total_amount_paid', // tong goc da thu
			'data.total_fee_paid', // tong phi da thu
			'data.fee_overdue_paid', // phi qua han da thu
			'data.fee_overdue_cycle_paid', // phi cham ky da thu
			'data.fee_overdue_reduction', // phi qh da giam
			'data.fee_overdue_cycle_reduction', // phi cham ky da giam
			'data.is_overdue', // co qua han hay khong: 1 co | 2: khong
			'data.is_over_cycle', // co cham ky hay khong: 1 co | 2 khong
			'data.number_over_cycle', // so ky bi cham
			'data.number_day_overdue', // so ngay qua han
			'data.description', // noi dung
		])['data'];

		$collectDebtSummary->forceFill($paramUpdate)->update();
		return $collectDebtSummary;
	}
}
