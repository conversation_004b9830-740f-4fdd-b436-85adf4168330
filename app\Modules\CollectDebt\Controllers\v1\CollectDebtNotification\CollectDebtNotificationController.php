<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtNotification;

use App\Lib\Helper;
use App\Modules\CollectDebt\Actions\CollectDebtNotification\GetDetailNotificationAction\GetDetailNotificationAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotification\GetListNotificationAction\GetListNotificationAction;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;

class CollectDebtNotificationController extends Controller
{
	public function GetListNotification(Request $request)
	{
		try {
			$result = app(GetListNotificationAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function GetDetailNotification(Request $request)
	{
		try {
			$collectDebtNotification = app(GetDetailNotificationAction::class)->run($request);
			return $this->successResponse($collectDebtNotification->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
