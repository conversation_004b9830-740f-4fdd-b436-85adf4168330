<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequestAdjustment;

use DB;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Resources\CollectDebtRequestAdjustmentResourceCollection;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentStoreRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentUpdateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentSetStatusRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentSetStatusAction\SubAction\XuLyYeuCauDieuChinhBiHuySA;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentStoreAction\DebtRecoveryRequestAdjustmentStoreAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentCreateAction\DebtRecoveryRequestAdjustmentCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentUpdateAction\DebtRecoveryRequestAdjustmentUpdateAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\DebtRecoveryRequestAdjustmentProcessAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentContractAction\DebtRecoveryRequestAdjustmentContractAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentSetStatusAction\DebtRecoveryRequestAdjustmentSetStatusAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentStatisticAction\DebtRecoveryRequestAdjustmentStatisticAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentSearchDataAction\DebtRecoveryRequestAdjustmentSearchDataAction;

class CollectDebtRequestAdjustmentController extends Controller
{
  public function create(DebtRecoveryRequestAdjustmentCreateRequest $request)
  {
    try {
      $collectDebtRequest = app(DebtRecoveryRequestAdjustmentCreateAction::class)->run($request);
      return $this->successResponse($collectDebtRequest->only('id'), $request, 200, 'Tạo yêu cầu điều chỉnh thành công');
    } catch (\Throwable $th) {
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }

  public function update(DebtRecoveryRequestAdjustmentUpdateRequest $request)
  {
    try {
      $collectDebtRequest = app(DebtRecoveryRequestAdjustmentUpdateAction::class)->run($request);
      return $this->successResponse($collectDebtRequest->only('id'), $request, 200, 'Cập nhật yêu cầu điều chỉnh thành công');
    } catch (\Throwable $th) {
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }

  public function setStatus(DebtRecoveryRequestAdjustmentSetStatusRequest $request)
  {
		DB::beginTransaction();
    try {
      $collectDebtRequestAdjustment = app(DebtRecoveryRequestAdjustmentSetStatusAction::class)->run($request);
			app(XuLyYeuCauDieuChinhBiHuySA::class)->run($collectDebtRequestAdjustment);
			DB::commit();
      return $this->successResponse($collectDebtRequestAdjustment->only('id'), $request, 200, $collectDebtRequestAdjustment->__apiMessage);
    } catch (\Throwable $th) {
			DB::rollBack();
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }

  public function getByContract(Request $request)
  {
    $requestAdjustments = app(DebtRecoveryRequestAdjustmentContractAction::class)->run($request);
    $count = $requestAdjustments->filter(function (CollectDebtRequestAdjustment $ra) {
      return !$ra->isStatusTrangThaiCuoi();
    })->count();

    return $this->successResponse([
      'rows' => $count,
      'data' => $requestAdjustments->toArray()
    ], $request);
  }

  public function getById(Request $request)
  {
    $requestAdjustment = CollectDebtRequestAdjustment::query()->find($request->json('data.id'));
    return $this->successResponse($requestAdjustment->toArray(), $request);
  }

  public function searchData(Request $request) {
    try {
      $collectDebtRequestAdjustments = app(DebtRecoveryRequestAdjustmentSearchDataAction::class)->run($request);
      $collectDebtRequestResource = new CollectDebtRequestAdjustmentResourceCollection($collectDebtRequestAdjustments);
			$response = $collectDebtRequestResource->toResponse($request)->getData(true);
      return $this->successResponse($response, $request);
    } catch (\Throwable $th) {
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }

  public function statistic(Request $request) {
    try {
      $statistic = app(DebtRecoveryRequestAdjustmentStatisticAction::class)->run($request);
      return $this->successResponse($statistic, $request);
    } catch (\Throwable $th) {
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }

  public function process(Request $request) {
    try {
      $ra = app(DebtRecoveryRequestAdjustmentProcessAction::class)->run();
      return $this->successResponse(['ra_ids' => $ra->id], $request);
    } catch (\Throwable $th) {
			mylog(['Loi' => Helper::traceError($th)]);
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }

	public function store(DebtRecoveryRequestAdjustmentStoreRequest $request) {
    try {
      $ra = app(DebtRecoveryRequestAdjustmentStoreAction::class)->run($request);
      return $this->successResponse(['ra_ids' => $ra->id], $request, 200, __('Tạo yêu cầu điều chỉnh thành công'));
    } catch (\Throwable $th) {
			mylog(['Loi' => Helper::traceError($th)]);
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }
} // End class