<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtRequest;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use Illuminate\Database\Eloquent\Collection;

trait RequestPlanData
{
  public function getRequestPlanDataAsCollection(): Collection
  {
    $plans = json_decode($this->plan_data, true);
    return CollectDebtSchedule::hydrate($plans);
  }

	/**
	 * Điều kiện để kéo dài thời gian hết hạn của lệnh trích
	 * 1. Lịch có rundate là chủ nhật
	 * 		A. Là yêu cầu có chứa lịch tất toán 
	 * 		B. Nếu không có lịch tất toán, thì phải là HĐ trích kỳ và phải là lịch thu chính gốc
	 *
	 * @return boolean
	 */
	public function isDuDieuKienKeoDaiTimeExpiredSangThuHaiTiepTheo(): bool {
		$plans = $this->getRequestPlanDataAsCollection();
		
		$isCoLichRunDateLaChuNhat = $plans->contains(function (CollectDebtSchedule $plan) {
			return $plan->isRunDateLaChuNhat();
		});

		if (!$isCoLichRunDateLaChuNhat) {
			return false;
		}

		// Thỏa mãn: lịch có rundate là chủ nhật
		$isCoLichThuGocTatToan = $plans->contains(function (CollectDebtSchedule $plan) {
			return $plan->isLichThuGoc() && $plan->isLichTatToan();
		});

		// Thỏa mãn: Có lịch thu tất toán
		if ($isCoLichThuGocTatToan) {
			return true;
		}


		$collectDebtShare = CollectDebtShare::query()->where('contract_code', $this->contract_code)->first();
		
		if ($collectDebtShare->isHopDongShareTrichNgay()) {
			return false;
		}

    $isCoLichThuGocChinh = $plans->contains(function (CollectDebtSchedule $plan) {
			return $plan->isLichThuGocChinh();
		});

		// Thỏa mãn: Là HĐ trích kỳ và có lịch thu gốc chính
		return $collectDebtShare->isHopDongShareTrichKy() && $isCoLichThuGocChinh;
	}
} // End class
