<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentCreateAction\DebtRecoveryRequestAdjustmentCreateAction;
use App\Modules\CollectDebt\Model\CollectDebtLog;

class XuLyPartnerTrichNgayMposSubAction
{
  public function run(CollectDebtPartner $collectDebtPartner, CollectDebtRequest $collectDebtRequest, DebtRecoveryPartnerCreateRequest $request): bool
  {
    if ($collectDebtRequest->isTrichNgay() != 'YES') {
      return false;
    }

    /**
     * Là yêu cầu trích ngay, kiểm tra số tiền TRÍCH THỰC TẾ TỪ MPOS CÓ ĐỦ VỚI SỐ  TIỀN SET TRÍCH NGAY HAY KHÔNG
     *    Nếu ĐỦ RỒI: Thì không cần tạo yc điều chỉnh -> return false
     *    NẾU CHƯA ĐỦ: Thì tạo yc điều chỉnh -> return true
     */ 
    if ($request->json('data.amount_receiver') >= $collectDebtPartner->amount_receiver) {
			$updatedDaNhanKetQua = $this->danhDauYeuCauLaDaNhanKetQua($collectDebtRequest);
      return false;
    }

    $planData = json_decode($collectDebtRequest->plan_data, true);

    // Trích
    $params = [
      'data' => [
        'contract_code' => $collectDebtRequest->contract_code,
        'type' => CollectDebtEnum::REQUEST_ADJUSTMENT_TYPE_DIEU_CHINH_DOI_TAC,
        'reference_id' => $collectDebtPartner->id,
        'reference_data' => $collectDebtPartner->toJson(),
        'amount_receiver_on_partner' => $request->json('data.amount_receiver'), // số tiền thực tế mà MPOS trích được
        'amount_before' => $collectDebtPartner->amount_receiver,
        'amount_after' => $request->json('data.amount_receiver'),
        'description' => '',
        'other_data' => json_encode([
          [
            'type' => 'PLAN',
            'note' => 'Thông tin lịch cần mở',
            'data' => $planData,
            'time_modified' => time()
          ],
        ]),
        'status' => CollectDebtEnum::REQUEST_ADJUSTMENT_STT_MOI_TAO,
        'time_created' => time(),
        'created_by' => StandardizedDataFilter::getUserAdminStructCompact([
					'id' => $request->json('data.user_action.id', 'cronjob'),
					'username' => $request->json('data.user_action.username', 'cronjob'),
					'mobile' => $request->json('data.user_action.mobile', ''),
				]),
				'profile_id' => $collectDebtRequest->profile_id,
      ]
    ];

    
    $rq = new DebtRecoveryRequestAdjustmentCreateRequest();
    $rq->setJson(new ParameterBag($params));
    $collectDebtRequestAdjustment = app(DebtRecoveryRequestAdjustmentCreateAction::class)->run($rq);
    
		// Cap nhat trang thai yc la da nhan ket qua
		$updatedDaNhanKetQua = $this->danhDauYeuCauLaDaNhanKetQua($collectDebtRequest);

    return true;
  }

	public function danhDauYeuCauLaDaNhanKetQua(CollectDebtRequest $collectDebtRequest) {
		$updatedDaNhanKetQua = CollectDebtRequest::query()
											->where('id', $collectDebtRequest->id)
											->update([
												'time_receivered' => time(),
												'receivered_by' => Helper::getCronJobUser()
											]);

		mylog(['Danh Dau La Da Nhan Ket Qua' => $updatedDaNhanKetQua]);

		if ($updatedDaNhanKetQua) {
			// Da nhan ket qua roi thi danh dau ban ghi log ve trang thai cuoi
			$updateLog = @CollectDebtLog::query()
																 ->where('service_code', CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY)
																 ->where('partner_transaction_id', $collectDebtRequest->partner_transaction_id)
																 ->update([
																		'status' => CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG,
																		'description' => sprintf('Xu ly log luc %s', now()->format('d/m/Y H:i')),
																		'time_updated' => now()->timestamp,
																		'updated_by' => Helper::getCronJobUser()
																	]);

			mylog(['ket qua update log la' => $updateLog]);
		}
		return $updatedDaNhanKetQua;
	}
} // End class
