<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\HopDongChuaTatToanRule;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;

class DebtRecoveryRequestAddingAttachmentRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:1'],
      'data.manual_request_attachment' => ['required', 'json']
    ];
  }
} // End class
