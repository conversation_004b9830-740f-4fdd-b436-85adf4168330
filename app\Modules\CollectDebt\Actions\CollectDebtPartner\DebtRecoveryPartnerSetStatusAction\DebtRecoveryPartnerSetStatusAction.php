<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerSetStatusAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerSetStatusRequest;

class DebtRecoveryPartnerSetStatusAction
{
  private $__apiMessage = '';

  public function run(DebtRecoveryPartnerSetStatusRequest $request): CollectDebtPartner
  {
    $collectDebtPartner = CollectDebtPartner::find($request->json('data.id'));
    
    $flagUpdate = false;

    if ($collectDebtPartner->isPartnerChuaXuLy() && $request->json('data.status') == CollectDebtEnum::PARTNER_STT_DANG_XU_LY) {
      mylog(['Update tu NoProcess => Processing' => '1 => 2']);
      $collectDebtPartner->forceFill([
        'time_processing' => time(),
        'status' => CollectDebtEnum::PARTNER_STT_DANG_XU_LY,
        'processing_by' => $request->json('data.user_request_id')
      ])->update();
      
      $flagUpdate = true;
      $this->__apiMessage = 'Đã cập nhật bản ghi lịch sử thu tiền từ `CHƯA XỬ LÝ` sang `ĐANG XỬ LÝ`';
    }

    if ($collectDebtPartner->isPartnerDangXuLy() && $request->json('data.status') == CollectDebtEnum::PARTNER_STT_DA_XU_LY) {
      mylog(['Update tu Processing => Processed' => '2 => 3']);

      $collectDebtPartner->forceFill([
        'time_processing' => time(),
        'status' => CollectDebtEnum::PARTNER_STT_DA_XU_LY,
        'complated_by' => $request->json('data.user_request_id')
      ])->update();

      $flagUpdate = true;
      $this->__apiMessage = 'Đã cập nhật bản ghi lịch sử thu tiền từ `ĐANG XỬ LÝ` sang `ĐÃ XỬ LÝ`';
    }

    if ($collectDebtPartner->isPartnerDangXuLy() && $request->json('data.status') == CollectDebtEnum::PARTNER_STT_XU_LY_LOI) {
      mylog(['Update tu Processing => Process Error' => '2 => 4']);

      $collectDebtPartner->forceFill([
        'time_processing' => time(),
        'status' => CollectDebtEnum::PARTNER_STT_XU_LY_LOI,
        'processing_by' => $request->json('data.user_request_id')
      ])->update();

      $flagUpdate = true;
      $this->__apiMessage = 'Đã cập nhật bản ghi lịch sử thu tiền từ `ĐANG XỬ LÝ` sang `XỬ LÝ LỖI`';
    }

    if ($collectDebtPartner->isPartnerXuLyLoi() && $request->json('data.status') == CollectDebtEnum::PARTNER_STT_CHUA_XU_LY) {
      mylog(['Update tu Process Error => No Process' => '4 => 1']);

      $collectDebtPartner->forceFill([
        'time_processing' => time(),
        'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
        'processing_by' => $request->json('data.user_request_id')
      ])->update();

      $flagUpdate = true;
      $this->__apiMessage = 'Đã cập nhật bản ghi lịch sử thu tiền từ `XỬ LÝ LỖI` sang `CHƯA XỬ LÝ`';
    }

    // Chờ duyệt => Đã duyệt (CHƯA XỬ LÝ)
    if ($collectDebtPartner->isPartnerChoDuyet() && $request->json('data.status') == CollectDebtEnum::PARTNER_STT_CHUA_XU_LY) {
      mylog(['Update từ Đang chờ duyệt => Chưa xử lý' => '5 => 1']);

      $collectDebtPartner->forceFill([
        'time_processing' => time(),
        'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
        'approved_by' => $request->json('data.user_request_id')
      ])->update();

      $flagUpdate = true;
      $this->__apiMessage = 'Đã cập nhật bản ghi lịch sử thu tiền từ `CHỜ DUYỆT` sang `CHƯA XỬ LÝ`';
    }

    // Chờ duyệt => Từ chối
    if ($collectDebtPartner->isPartnerChoDuyet() && $request->json('data.status') == CollectDebtEnum::PARTNER_STT_DA_TU_CHOI) {
      mylog(['Update từ Đang chờ duyệt => Chưa xử lý' => '5 => 6']);

      $collectDebtPartner->forceFill([
        'time_processing' => time(),
        'status' => CollectDebtEnum::PARTNER_STT_DA_TU_CHOI,
        'canceled_by' => $request->json('data.user_request_id')
      ])->update();

      $flagUpdate = true;
			
      $this->__apiMessage = 'Đã cập nhật bản ghi lịch sử thu tiền từ `CHỜ DUYỆT` sang `ĐÃ TỪ CHỐI`';
    }

    $message = sprintf(
      'Logic set staus không đúng quy trình. Bạn đang muốn cập nhật lên trạng thái "%s" trong khi trạng thái công nợ hiện tại là "%s"',
      CollectDebtPartner::listStatus()[$request->json('data.status')],
      CollectDebtPartner::listStatus()[$collectDebtPartner->status],
    );
    throw_if(!$flagUpdate, new Exception($message));

    $collectDebtPartner = $collectDebtPartner->refresh();
    $collectDebtPartner->__apiMessage = $this->__apiMessage;
    return $collectDebtPartner;
  }
} // End class