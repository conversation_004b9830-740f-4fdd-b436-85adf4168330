<?php

namespace App\Lib;

trait ExtraCustomHelper
{
  public static function test()
  {
    return "Test Extra";
  }

  public static function makeVndCurrency($value)
  {
    if (isset($value)) {
      $value = number_format($value, 0, '.', ',');

      return $value;
    }

    return 0;
  }

  public static function jsonValidator($data)
  {
    if (!empty($data)) {
      return is_string($data) &&
        is_array(json_decode($data, true)) ? true : false;
    }
    return false;
  }
}
