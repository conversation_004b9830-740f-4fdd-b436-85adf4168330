<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentAction\SubAction;

use App\Lib\ApiCall;
use App\Utils\CommonVar;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentRequest;

class TaoYeuCauNapTienSubAction
{
	const CASHIN_NGUON_TIEN_THU_THUA = 1;
	const CASHIN_NGUON_TIEN_BANK = 2;

	/**
	 * array:6 [
			"api_request_id" => "DEBT_12251130_fpuwW7"
			"success" => true
			"checksum" => "041379273b4b3272fd7376e874b11d81"
			"result_code" => 200
			"message" => "[DEBT_12251130_fpuwW7] - Tạo yêu cầu nạp tiền thành công"
			"data" => array:1 [
				"id" => "6"
			]
		]
	 *
	 * @param CollectDebtSummary $collectDebtSummary
	 * @param DebtRecoverySummaryPlusAmountRepaymentRequest $request
	 * @return void
	 */
	public function run(CollectDebtSummary $collectDebtSummary, DebtRecoverySummaryPlusAmountRepaymentRequest $request)
	{
		$contractData = $collectDebtSummary->getContractData();

		$params = [
			'profile_id' => $contractData['profile_id'],
			'type' => self::CASHIN_NGUON_TIEN_THU_THUA,
			'request_code' => sprintf('%s_%s', $collectDebtSummary->contract_code, date('ymdHis')),
			'reference_id' =>  sprintf('%s_CT%s', $collectDebtSummary->contract_code, date('ymdHis')),
			'cashin_method_code' => 'CONTRACT_DEBT',
			'amount' => $request->getSoTienMuonThanhToanTiep(),
			'currency' => $collectDebtSummary->getCurrency(),
			'reference_data' => [
				[
					'type' => 'DEBT',
					'time_modified' => time(),
					'data' => [
						'account_type' => '',
						'account_number' => '',
						'account_fullname' => '',
						'account_branch' => '',
					],
					'note' => 'Tạo yêu cầu nạp tiền'
				]
			],
			'description' => $request->json('data.description', 'Tạo yc nạp tiền'),
			'other_data' => '[]',
			'created_by' => $request->json('data.users_admin_id'),
			'time_expired' => $request->json('data.time_expired')
		];

		$payload = [
			'module' => CommonVar::API_CASH_IN_MODULE,
			'path' => '/CashinRequestCreate',
			'params' => $params,
			'method' => 'POST'
		];

		$taoYeuCauNapTienResult = (new ApiCall())->callFunctionApi($payload, true);

		return $taoYeuCauNapTienResult;
	}
} // End class