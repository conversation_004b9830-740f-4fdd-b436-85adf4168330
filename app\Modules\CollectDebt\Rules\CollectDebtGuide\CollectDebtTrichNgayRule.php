<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtGuide;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Contracts\Validation\Rule;

class CollectDebtTrichNgayRule implements Rule
{
    private string  $__errorMessgae;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($contractTypeField, $contractTypeValue)
    {
        $contractIntervals = request()->json('data.contract_intervals');
        if ($contractTypeValue == CollectDebtEnum::GUIDE_HD_TRICH_NGAY) {
            // Nếu là trích ngày thì chu kỳ CHỈ ĐƯỢC PHÉP LÀ 1 NGÀY
            $this->__errorMessgae = 'Nếu là HĐ trích ngày thì chu kỳ trả nợ PHẢI LÀ 1 ngày/lần';
            return $contractIntervals == 1; 
        }

        if ($contractTypeValue == CollectDebtEnum::GUIDE_HD_TRICH_KY) {
            // Nếu là trích theo chu kỳ thì chu kỳ PHẢI KHÁC 1 NGÀY
            $this->__errorMessgae = 'Nếu là HĐ trích kỳ thì chu kỳ trả nợ PHẢI KHÁC 1 ngày/lần';
            return $contractIntervals != 1; 
        }

        $this->__errorMessgae = 'Hệ thống không nhận ra được loại HĐ của bạn';
        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->__errorMessgae;
    }
}
