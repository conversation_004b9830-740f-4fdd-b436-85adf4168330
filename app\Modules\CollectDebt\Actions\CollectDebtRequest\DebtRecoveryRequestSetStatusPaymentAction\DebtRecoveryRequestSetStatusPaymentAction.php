<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusPaymentAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestSetStatusPaymentRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\DebtRecoveryRequestFindRawQuerySubAction;

class DebtRecoveryRequestSetStatusPaymentAction
{
  public function run(DebtRecoveryRequestSetStatusPaymentRequest $request): CollectDebtRequest
  {
    $whereRaw = sprintf('id = %s', $request->json('data.id'));
    $collectDebtRequest = app(DebtRecoveryRequestFindRawQuerySubAction::class)->run($whereRaw);
    throw_if(!$collectDebtRequest->isApproved(), new Exception('Không thể cập nhật do yêu cầu thu hồi đang không ở trạng thái ĐÃ DUYỆT'));
    
    ccl(['Status Payment Sau khi Truy Van Xong La:' => $collectDebtRequest->status_payment]);

    $flagUpdate = false;
    
    if ($collectDebtRequest->isUnsentPayment() && $request->json('data.status_payment') == CollectDebtEnum::REQUEST_STT_PM_DANG_GUI) {
      ccl(['Update tu 1 sang 2' => 'Ok']);

      $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DANG_GUI;
      $collectDebtRequest->save();
      
      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isUnsentPayment() && $request->json('data.status_payment') == CollectDebtEnum::REQUEST_STT_PM_TU_CHOI) {
      ccl(['Update tu 1 sang 3' => 'Ok']);

      $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_TU_CHOI;
      $collectDebtRequest->time_canceled_payment = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isSendingPayment() && $request->json('data.status_payment') == CollectDebtEnum::REQUEST_STT_PM_GUI_LOI) {
      ccl(['Update tu 2 sang 4' => 'Ok']);

      $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_GUI_LOI;
      $collectDebtRequest->time_sended = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isSendingPayment() && $request->json('data.status_payment') == CollectDebtEnum::REQUEST_STT_PM_DA_GUI) {
      ccl(['Update tu 2 sang 5' => 'Ok']);
      $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_GUI;
      $collectDebtRequest->time_sended = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }


    if ($collectDebtRequest->isSentErrorPayment() && $request->json('data.status_payment') == CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI) {
      ccl(['Update tu 4 sang 1' => 'Ok']);

      $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI;
      $collectDebtRequest->time_sended = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isSentErrorPayment() && $request->json('data.status_payment') == CollectDebtEnum::REQUEST_STT_PM_TU_CHOI) {
      ccl(['Update tu 4 sang 3' => 'Ok']);

      $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_TU_CHOI;
      $collectDebtRequest->time_canceled_payment = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }

    if ($collectDebtRequest->isSentPayment() && $request->json('data.status_payment') == CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA) {
      ccl(['Update tu 5 sang 6' => 'Ok']);

      $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
      $collectDebtRequest->time_receivered = time();
      $collectDebtRequest->save();

      return $collectDebtRequest;
    }
    
    $message = sprintf(
      'Luồng cập nhật trạng thái thanh toán không đúng logic. Trạng thái thanh toán hiện tại là: "%s", trong khi bạn muốn cập nhật là: "%s"',
      CollectDebtRequest::listingStatusPayment()[$collectDebtRequest->status_payment],
      CollectDebtRequest::listingStatusPayment()[$request->json('data.status_payment')],
    );

    throw_if(!$flagUpdate, new Exception($message));
  }
} // End class