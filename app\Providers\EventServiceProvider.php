<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Observers\CollectDebtGuideObserver;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use App\Modules\CollectDebt\Observers\CollectDebtRequestObserver;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        CollectDebtRequest::observe(CollectDebtRequestObserver::class);
        CollectDebtGuide::observe(CollectDebtGuideObserver::class);
    }
}
