<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\SubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySyncContractAction\DebtRecoverySummarySyncContractAction;
use Exception;

class HandleSyncContractSubAction
{
    public function run($collectEvent)
    {

        $dataResponse = [];

        $input = [
            'time_updated' => time(),
            'status' => config('collect_debt_email_remind_config.debt_contract_event_status.sending_request'),
        ];

				mylog(['Param cap nhat len trang thai DANG GUI' => $input]);

				try {
					$r = $collectEvent->update($input);
					mylog(['Ket qua cap nhat trang thai gui event' => $r]);
        	$collectEvent = $collectEvent->refresh();

					mylog(['collectEvent' => $collectEvent]);
				}catch(\Throwable $th) {
					mylog([
						'err' => 'ERROR_SEND_EVENT',
						'message' => $th->getTrace()
					]);
				}
        
				
        throw_if(!$collectEvent, new Exception('Không thể cập nhật được bản ghi'));

        $syncContract = $this->__handleContract($collectEvent);
        if (empty($syncContract)) {
            $input = [
                'time_updated' => time(),
                'description' => sprintf('Lỗi không gửi được - %s',request('api_request_id')),
                'status' => config('collect_debt_email_remind_config.debt_contract_event_status.failed'),
            ];
            $collectEvent->update($input);
            return $dataResponse;
        }

        $input = [
            'time_updated' => time(),
            'time_sented' => time(),
            'description' => 'Đã gửi',
            'status' => config('collect_debt_email_remind_config.debt_contract_event_status.created_request'),
        ];

				mylog(['param update event' => $input]);

        $collectEvent->update($input);

        return $dataResponse;
    }

    protected function __handleContract($data)
    {
        $dataResponse = [];

        $otherData = json_decode($data->other_data, true);
        $summary = isset($otherData['summary']) ? $otherData['summary'] : [];
				mylog(['Du lieu summary' => $summary]);

        if (empty($summary)) {
            return $dataResponse;
        }

				mylog(['chuan bi goi vao v1' => 'ok']);
        $collectDebtSummarySyncCobtract = app(DebtRecoverySummarySyncContractAction::class)->run($summary);
				mylog(['ket qua khi goi vao v1' => $collectDebtSummarySyncCobtract]);
        if(!empty($collectDebtSummarySyncCobtract)){
            $dataResponse = $collectDebtSummarySyncCobtract;
        }

        return $dataResponse;
    }

}