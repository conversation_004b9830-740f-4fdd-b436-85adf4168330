<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventSetStatusAction;

use Exception;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class DebtRecoveryContractEventSetStatusAction
{
    public function run($request)
    {
        $params = $this->mapData($request);
        $debtRecoveryContractEvent = CollectDebtContractEvent::where('id', $params['id'])->get();

        throw_if(!$debtRecoveryContractEvent->isNotEmpty(), new Exception('Không tìm được bản ghi phù hợp'));

        $debtRecoveryContractEvent = $debtRecoveryContractEvent->first();

        throw_if(!in_array($params['status'],$this->checkStatus($debtRecoveryContractEvent->status)), new Exception('Trạng thái muốn cập nhật không hợp lệ'));

        $debtRecoveryContractEvent = $debtRecoveryContractEvent->update($params);

        throw_if(!$debtRecoveryContractEvent, new Exception('Cập nhật trạng thái không thất bại'));

        return [
            'id' => $params['id']
        ];
    }

    protected function mapData($request)
    {
        $dataResponse = [];

        $dataResponse['id'] = $request['id'];
        $dataResponse['status'] = $request['status'];

        if(isset($request['description']) && !empty($request['description'])){
            $dataResponse['description'] = $request['description'];
        }

        if(isset($request['user_request_id']) && !empty($request['user_request_id'])){

        }
       
        return $dataResponse;
    }

    protected function checkStatus($status)
    {
        $validStatus = [];

        switch ($status) {
            case config('collect_debt_email_remind_config.debt_contract_event_status.new'):
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.creating_content');
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.canceled');
                break;
            case config('collect_debt_email_remind_config.debt_contract_event_status.creating_content'):
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.created_content');
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.canceled');
                break;
            case config('collect_debt_email_remind_config.debt_contract_event_status.created_content'):
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.sending_request');
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.canceled');
                break;
            case config('collect_debt_email_remind_config.debt_contract_event_status.sending_request'):
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.created_request');
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.failed');

								// hồi lại về thời điểm chuẩn bị gửi yêu cầu
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.created_content');
                break;
            case config('collect_debt_email_remind_config.debt_contract_event_status.failed'):
                $validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.new');
                break;

            case config('collect_debt_email_remind_config.debt_contract_event_status.canceled'):
								// tu cancel dua ve da tao content
								$validStatus[] = config('collect_debt_email_remind_config.debt_contract_event_status.created_content');
                break;
        }

        return $validStatus;
    }
}
