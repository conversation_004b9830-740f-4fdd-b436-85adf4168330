<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtRequest;

use Illuminate\Contracts\Validation\Rule;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class PrepayPlanSameContractRule implements Rule
{
  public string $contractCode;
  /**
   * Create a new rule instance.
   *
   * @return void
   */
  public function __construct(string $contractCode='')
  {
    $this->contractCode = $contractCode;
  }

  /**
   * Undocumented function
   *
   * @param [string] $prepayPlansAttribute: Key thuộc tính
   * @param [array 1D] $prepayPlansData: Mảng các số nguyên là các id lịch thanh toán tương lai
   * @return void
   */
  public function passes($prepayPlansAttribute, $prepayPlansData)
  {
    if (empty($prepayPlansData)) {
      return true;
    }
    
    $count = CollectDebtSchedule::whereIn('id', $prepayPlansData)
                                ->where('contract_code', $this->contractCode)
                                ->where('isfee', CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC)
                                ->count();
    return $count == count($prepayPlansData);
  }

  /**
   * Get the validation error message.
   *
   * @return string
   */
  public function message()
  {
    return 'ID lịch trong PrePay Plan không nằm trong hợp đồng';
  }
}
