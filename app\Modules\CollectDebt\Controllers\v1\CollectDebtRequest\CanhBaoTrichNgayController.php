<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use App\Modules\CollectDebt\Enums\CacheEnum;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\DebtNowEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowCreateAction\CollectDebtNotifyDebtNowCreateAction;

class CanhBaoTrichNgayController extends Controller
{
	private $__limit = 30;

	public array $listLenhTrichDaCanhBao = [];

	public array $trangThaiThanhCong = ['SUCCESS', 'APPROVE', 'APPROVED'];

	public function isThanhCong($mposResult = ''): bool
	{
		return in_array($mposResult, $this->trangThaiThanhCong);
	}

	public function getListYcTrichNgay(): Collection
	{
		$now = now()->timestamp;
		$key = CacheEnum::NLV4_REDIS_TRICH_NGAY_REQUEST;

		$partnerRequestIds = Redis::zrangebyscore($key, '-inf', $now, [
			'limit' => [0, $this->__limit]
		]);

		if (empty($partnerRequestIds)) {
			return Collection::make([]);
		}

		$collectDebtRequests = CollectDebtRequest::query()->whereIn('partner_request_id', $partnerRequestIds)->get();

		return $collectDebtRequests;
	}

	public function handler(Request $request)
	{
		$listYcTrichNgay = $this->getListYcTrichNgay();

		if ($listYcTrichNgay->isEmpty()) {
			return 'khong co lenh trich ngay nao can kiem tra';
		}

		foreach ($listYcTrichNgay as $collectDebtRequest) {
			if (!$collectDebtRequest->isRutTienNhanh()) {
				continue;
			}

			
			$checkMposResult = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request, true);
			$status = $checkMposResult['data']['status'] ?? 'EMPTY';

			$soTienThanhCong = 0;
			$isThanhCong = $this->isThanhCong(@$checkMposResult['data']['status']);
			if ($isThanhCong) {
				$soTienThanhCong = $checkMposResult['data']['amount_debit'];
			}

			$subject = sprintf(
				'[%s] - Cảnh báo lệnh trích ngay: %s đang không thu hồi đủ tiền. Trạng thái: %s',
				$collectDebtRequest->contract_code,
				$collectDebtRequest->partner_request_id,
				$status
			);

			
			$params = [
				'contract_code' => $collectDebtRequest->contract_code,
				'to' => '<EMAIL>',
				'cc' => '<EMAIL>',
				'sender' => '',
				'subject' => $subject,
				'description' => '',
				'other_data' => [
					'MaLenhTrich' => $collectDebtRequest->partner_request_id,
					'MaHd' => $collectDebtRequest->contract_code,
					'TrangThai' => $status,
					'SoTienCanPhaiThu' => Helper::priceFormat($collectDebtRequest->amount_request),
					'SoTienDaThuDuocThucTe' => Helper::priceFormat($soTienThanhCong)
				],
				'request_id' => $collectDebtRequest->id
			];

			if ($soTienThanhCong == $collectDebtRequest->amount_request) {
				$params['status'] = DebtNowEnum::STT_DA_XU_LY;
			}

			$collectDebtNotifyDebtNow = app(CollectDebtNotifyDebtNowCreateAction::class)->run($params);
			@TelegramAlert::sendMessage($subject);

			$this->listLenhTrichDaCanhBao[] = $collectDebtRequest->only([
				'id',
				'partner_request_id',
				'partner_transaction_id',
				'contract_code'
			]);

			if (!in_array($status, ['TIMEOUT', 'EMPTY'])) {
				CacheEnum::removeFromListTrichNgay($collectDebtRequest->partner_request_id);
			}
		} // End foreach

		return response()->json([
			'listLenhTrichDaXuLy' => $this->listLenhTrichDaCanhBao
		], 200);
	}
} // End class
