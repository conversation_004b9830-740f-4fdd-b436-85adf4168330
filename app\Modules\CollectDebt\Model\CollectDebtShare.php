<?php

namespace App\Modules\CollectDebt\Model;

use Exception;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\CollectDebtCoreContract;
use App\Modules\CollectDebt\Model\Traits\CollectDebtShare\BuildShareAttributeInspireGuide;

class CollectDebtShare extends Model
{
	use BuildShareAttributeInspireGuide;

	protected $table   = 'debt_recovery_share';

	public $timestamps = false;

	protected $guarded = [];

	protected $appends = [
		'profile_id',
		'contract_type',
		'contract_cycle',
		'contract_intervals',
		'contract_time_start',
		'contract_time_end',
		'amount',
		'status',
		'time_start_as_date',
		'time_end_as_date',
		'time_start_as_vn_date',
		'time_end_as_vn_date',
	];

	public function getProfileDataAsArray(): array
	{
		return json_decode($this->profile_data, true);
	}

	public function getContractId(): int
	{
		$contractData = $this->getShareContractData();
		return $contractData['id'] ?? 1;
	}

	public function getContractTypev1()
	{
		$contractData = $this->getShareContractData();
		return $contractData['type'];
	}

	public function configPauseJob() {
		return $this->hasOne(CollectDebtConfigAuto::class, 'contract_code', 'contract_code');
	}

	public function getMposMerchantId(): string {
		$paymentGuide = $this->getPaymentGuide();
		$mposMerchantId = collect($paymentGuide)->firstWhere('payment_method_code', 'MPOS')['payment_account_id'] ?? '';
		return $mposMerchantId;
	}

	public function getVirtualAccountId(): string {
		$paymentGuide = $this->getPaymentGuide();
		return collect($paymentGuide)->firstWhere('payment_method_code', 'VIRTUALACCOUNT')['payment_account_id'] ?? '';
	}

	public function getPaymentGuide(): array {
		return json_decode($this->payment_guide, 1);
	}

	public function getPaymentGuideItem(string $paymentMethodCode='MPOS'): array {
		$paymentGuide = $this->getPaymentGuide();
		
		$paymentMethod = collect($paymentGuide)->first(function ($item) use ($paymentMethodCode) {
			return $item['payment_method_code'] == $paymentMethodCode;
		});

		$errorMessage = sprintf('Hợp đồng `%s` không hỗ trợ thanh toán qua kênh %s', $this->contract_code, $paymentMethodCode);
		throw_if(!$paymentMethod, new Exception($errorMessage));
		
		return $paymentMethod;
	}

	public function getCoreContractByGuide(): CollectDebtCoreContract {
    $contractData = $this->getShareContractData();
    return new CollectDebtCoreContract($contractData);
  }

	public function getFeeByType(int $feeType=0): array {
		$listFee = json_decode($this->list_fee, true);
		
		$feeConfig = Arr::first($listFee, function ($feeItem) use ($feeType)  {
			return $feeItem['type'] == $feeType;
		});	

		if ($feeConfig) {
			$feeConfig['name'] = $this->getFeeName($feeConfig['type']);
			return $feeConfig;
		}

		return $listFee;
	}

	public function getTotalFeeMoney(int $feeType, float $totalOriginalAmountMCNeedToPay): float {
		$feeConfig = $this->getFeeByType($feeType);
		$totalFee = ( $totalOriginalAmountMCNeedToPay * $feeConfig['percent_fee'] / 100 ) + $feeConfig['flat_fee'];
		
		// Áp dụng cận trên & cận dưới của cấu hình phí
		if ( !empty($feeConfig['fee_min']) && $totalFee < $feeConfig['fee_min'] ) {
			return $feeConfig['fee_min'];
		}
		
		if ( !empty($feeConfig['fee_max']) && $totalFee > $feeConfig['fee_max'] ) {
			return $feeConfig['fee_max'];
		}

		return $totalFee;
	} 

	public function getFeeName($type): string {
		$configFee =  [
			CollectDebtEnum::GUIDE_LOAI_PHI_HOP_DONG               => 'Phí hợp đồng',
			CollectDebtEnum::GUIDE_LOAI_PHI_THAM_DINH_HO_SO        => 'Phí thẩm định hồ sơ',
			CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN                => 'Phí quá hạn',
			CollectDebtEnum::GUIDE_LOAI_PHI_UU_DAI_THAM_DINH_HO_SO => 'Ưu đãi thẩm định hồ sơ',
			CollectDebtEnum::GUIDE_LOAI_PHI_GIAI_NGAN              => 'Phí giải ngân',
			CollectDebtEnum::GUIDE_LOAI_PHI_THU_HOI                => 'Phí thu hồi',
			CollectDebtEnum::GUIDE_LOAI_PHI_GIA_HAN                => 'Phí gia hạn',
			CollectDebtEnum::GUIDE_LOAI_PHI_CHAM_KY                => 'Phí chậm kỳ',
			CollectDebtEnum::GUIDE_LOAI_UU_DAI_PHI_THAM_GIA        => 'Ưu đãi phí tham gia',
			CollectDebtEnum::GUIDE_LOAI_PHI_HOAN                   => 'Phí hoàn',
		];

		return $configFee[$type] ?? 'Không xác định';
	}

	public function isHopDongShareTrichKy(): bool {
		$contractData = json_decode($this->contract_data, true);
		return $contractData['type'] == 3; // 1: Trích ngày | 3: Trích kỳ
	}

	public function isHopDongShareTrichNgay(): bool {
		$contractData = json_decode($this->contract_data, true);
		return $contractData['type'] == 1; // 1: Trích ngày | 3: Trích kỳ
	}

	public function getLoaiHopDongText(): string {
		$text = 'Gia hạn';
		if ($this->isHopDongShareTrichKy()) {
			$text = 'Trích kỳ';
		}

		if ($this->isHopDongShareTrichNgay()) {
			$text = 'Trích ngày';
		}

		return $text;
	}

	public function getCurrencyShared(): string {
		$contractData = json_decode($this->contract_data, true);
		return $contractData['currency'];
	}

	public function getMposMcId(): string {
		$profileData = $this->getProfileDataAsArray();
		return $profileData['merchant']['partner_merchant_code'];
	}
} // End class
