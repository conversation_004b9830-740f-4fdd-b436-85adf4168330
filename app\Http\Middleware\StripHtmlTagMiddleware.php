<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class StripHtmlTagMiddleware
{
  public bool $isDirty = false;

  public function handle(Request $request, Closure $next)
  {
    $params = $request->all();
    
    if (!empty($params['data']['description'])) {
      $params['data']['description'] = strip_tags($params['data']['description']);
      $this->isDirty = true;
    }

    if (!empty($params['data']['contract_code'])) {
      $params['data']['contract_code'] = trim($params['data']['contract_code']);
      $this->isDirty = true;
    }

    // form search
    if (!empty($params['data']['filter']['contract_code'])) {
      $params['data']['filter']['contract_code'] = trim($params['data']['filter']['contract_code']);
      $this->isDirty = true;
    }

    if (!empty($params['data']['text']['contract_code'])) {
      $params['data']['text']['contract_code'] = trim($params['data']['text']['contract_code']);
      $this->isDirty = true;
    }

    if ($this->isDirty) {
      $request->merge($params);
    }
    
    return $next($request);
  }
}
