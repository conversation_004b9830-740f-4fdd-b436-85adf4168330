<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessCashAction\SubAction;

use App\Lib\NextlendCore;

class TaoYeuCauHoanTienQuaVer1SubAction
{
	/**
	 * @param array $params
	 * [
	 * 		'amount' => 100000,
	 * 		'admin_user_id' => 4,
	 * 		'description' => 'yc hoan',
	 * 		'contract_code' => 'MPOS-142569-L1',
	 * 		'summary_id' => 1,
	 * 		'service_code' => 'CONTRACT_FEE_PUNISH',
	 * 		'data_source' => [
	 * 			'summary_id' => 1,
	 * 			'success_url' => 'SuccessURL',
	 * 			'cancel_url' => 'CancelURL',
	 * 			'attachments' => ['link1', 'link2'],
	 * 		],
	 * 		'order_code' => 'RFF-AMQKIEPK1',
	 * ]
	 * @return void
	 */
  public function run(array $params)
  {
    $payload = [
      'amount' => $params['amount'], // so tien yc hoan
      'admin_user_id' => $params['admin_user_id'], // user
      'description' => $params['description'],
      'contract_code' => $params['contract_code'], // mã hợp đồng bắt buộc
			'request_reference_id' => $params['summary_id'], // summary id
			'service_code' => $params['service_code'], // REFUND: Refund HD chung | CONTRACT_EXCESS: Refund Thu thua | CONTRACT_FEE_PUNISH: Refund phi
			'data_source' => [
				'summary_id' => $params['data_source']['summary_id'],
				'success_url' => $params['data_source']['success_url'],
				'cancel_url' => $params['data_source']['cancel_url'],
				'attachments' => $params['data_source']['attachments'],
				'other_data' => $params['data_source']['other_data'] ?? []
			],
      'order_code' => $params['order_code']
    ];


    $nextlendCore = app(NextlendCore::class)->callRequest($payload, 'RequestRefund_create');

    $result = $nextlendCore->decryptData();
    
		return [
			'request_data' => $payload,
			'response' => $result
		];
  }
}
