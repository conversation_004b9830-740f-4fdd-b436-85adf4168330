<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

		'discord' => [
			'tao_yc_trich_no' => 'https://discordapp.com/api/webhooks/1375655014416252959/umjmG_SfIj1MqKWMi00GU6aNxHou56J3ugOt8XMSs7YtueLH8rcESrTbCJzFBdG7Ef9v',
			'mails' => 'https://discordapp.com/api/webhooks/1375657417207058472/jsfMF7oChoKONC0zsokPmF6oeR8-GnhmpkWm4fraYy4tOu5odv-1Dwpy-fFJVze8jAoa',	
		]
];
