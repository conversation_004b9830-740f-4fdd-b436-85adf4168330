<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task;

use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Carbon\Carbon;

class InsertBatchCollectDebtRequestTask
{
  public function run(array $params = [], bool $isChuNhat=false): Collection
  {
    $returnData = Collection::make([]);

    foreach ($params as $param) {
      $collectDebtRequest = CollectDebtRequest::forceCreate($param);

      $collectDebtRequest->partner_request_id = sprintf('NL%s%s', date('ymd'), $collectDebtRequest->id);
      $collectDebtRequest->other_data = $collectDebtRequest->initOtherData();
      $collectDebtRequest->save();

      // Xử lý case chủ nhật nếu là tất toán hoặc thu gốc chính
      if ($collectDebtRequest->isDuDieuKienKeoDaiTimeExpiredSangThuHaiTiepTheo()) {
        $timeExpiredNextMonday =  Carbon::createFromTimestamp($collectDebtRequest->time_expired)
                                        ->next('Monday')
                                        ->setTime(7, 0) // Set thời gian hết hạn kéo dài đến 06:00
                                        ->timestamp;
        $collectDebtRequest->time_expired = $timeExpiredNextMonday;
        $collectDebtRequest->save();
      }
      
      $returnData->push($collectDebtRequest);
    }

    return $returnData;
  }
} // End class