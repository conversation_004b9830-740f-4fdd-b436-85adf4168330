<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtContractPriority;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryContractPriorityCreateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
			'data.*' => ['required', 'array'],
			'data.*.type' => ['required'],
      'data.*.contract_code' => ['required', 'string', 'max:50'],
      'data.*.description' => ['nullable', 'string', 'max:255'],
      'data.*.other_data' => ['nullable', 'string', 'json'],
      'data.*.created_by' => ['required', 'string', 'max:255', 'json'],
      'data.*.time_created' => ['required', 'numeric'],
      'data.*.status' => ['required', 'numeric'],
    ];
  }
} // End class
