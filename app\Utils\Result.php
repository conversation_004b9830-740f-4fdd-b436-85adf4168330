<?php

namespace App\Utils;

class Result
{
//    const OK = 200;
    const ERROR = '0002';
    const INVALID_PARAMETERS = 0003;
    const UNAUTHORIZED = 0004;
    const BAD_REQUEST = 0005;
    const VERIFICATION_CODE_EXPIRED = 1011;
    const ALREADY_VERIFIED = 1012;
    const UNKNOWN_MOBILE_NUMBER = 1013;
    const MOBILE_NUMBER_ALREADY_OCCUPIED = 1014;
    const VERIFICATION_REQUIRED = 1015;
    const VALID_PERIOD_EXPIRED = 1016;
    const MALFORMED_DATA_SUSPICOUS = 1017;
    const INVALID_VERIFICATION_CODE = 1018;
    const INVALID_ACCESS_KEY = 1019;
    const TOO_MANY_REQUESTS = 1020;
    const ACCESS_IS_EXPIRED = 1021;
    const NOT_MATCHED_USERNAME_AND_MOBILE_NUMBER = 1022;
    const ERROR_FROM_SMS_PROVIDER = 1023;
    const NO_CHANGES = 1101;
    const WRONG_PASSWORD = 1102;
    const ALREADY_REQUESTED = 1201;
    const CANNOT_PURCHASE_OWN_PRODUCT = 1202;
    const UNKNOWN_USER = 1203;
    const ALREADY_SOLD_PRODUCT = 1204;
    const SALES_STOPPED = 1205;
    const CANNOT_REPORT_OWN_PRODUCTS = 1207;
    const CANNOT_LOAN_OWN_PRODUCT = 1206;
    const USER_ALREADY_WROTE_REVIEW = 1301;
    const LOCKED_PRODUCT = 6003;
    const CANNT_ABORT = 6004;
    const PRODUCT_ARE_BEING_EVALUATED = 6005;
    const NONE_LOAN_BECAUSE_STORE_NOT_ALLOW = 6007;
    const EXCEEDED_THE_TIME_SPECIFIED = 6008;
    const BLOCKED_CONTENT = 6001;
    const USER_SUSPENDED = 6002;
    const REQUEST_SUCCESS = 200;
    const REQUEST_FALSE = 201;
    const VALIDATE_ERROR = 203;
    const TOKEN_IS_EXPIRED = 300;
    const TOKEN_IS_INVALID = 301;
    const TOKEN_IS_NOT_FOUND = 302;
    const TOKEN_BLACKLISTED_EXCEPTION = 303;
    const EVENT_DRAFT = 7001;
    const EVENT_EXPIRED = 7000;

    const GOOGLE_FIREBASE = 5010;
    const VIETTEL_SMS = 5011;
    const VN_CODE = 84;
    const INVALID_NUMBER_PHONE = 5012;
    const UPLOAD_FALSE = 8000;

    const DATA_IS_NOT_EXIST = 18000;
    const MC_ID_EMPTY = 18001;

    const PARTNER_NOTIFY_ERROR = 19000;


    public static $resultMessage = [
//        self::OK => 'Ok',
        self::ERROR => 'Error',
        self::INVALID_PARAMETERS => 'Invalid paramters.',
        self::UNAUTHORIZED => 'No authorization.',
        self::BAD_REQUEST => 'Bad request.',
        self::VERIFICATION_CODE_EXPIRED => 'Verification code has been expired.',
        self::ALREADY_VERIFIED => 'Verification code has been already verified.',
        self::UNKNOWN_MOBILE_NUMBER => 'No verification code matching mobile number.',
        self::MOBILE_NUMBER_ALREADY_OCCUPIED => 'Mobile number has been already occupied.',
        self::VERIFICATION_REQUIRED => 'Mobile number verification is required.',
        self::VALID_PERIOD_EXPIRED => 'Valid period of verification has been expired.',
        self::MALFORMED_DATA_SUSPICOUS => 'Malformed verification is suspicious.',
        self::INVALID_VERIFICATION_CODE => 'Invalid verification code.',
        self::INVALID_ACCESS_KEY => 'Invalid access key.',
        self::TOO_MANY_REQUESTS => 'Too many requests.',
        self::ACCESS_IS_EXPIRED => 'Access key is expired.',
        self::NOT_MATCHED_USERNAME_AND_MOBILE_NUMBER => 'Username doesn\'t match with mobile number.',
        self::ERROR_FROM_SMS_PROVIDER => 'Error occured from SMS provider.',
        self::NO_CHANGES => 'No changes detected.',
        self::WRONG_PASSWORD => 'Password is wrong.',
        self::ALREADY_REQUESTED => 'Already requested.',
        self::CANNOT_PURCHASE_OWN_PRODUCT => 'Cannot purchase own product.',
        self::CANNOT_REPORT_OWN_PRODUCTS => 'Cannot report own product.',
        self::UNKNOWN_USER => 'Unknown user.',
        self::ALREADY_SOLD_PRODUCT => 'Already sold product.',
        self::SALES_STOPPED => 'Sales stopped.',
        self::USER_ALREADY_WROTE_REVIEW => 'User already wrote the review',
        self::BLOCKED_CONTENT => 'This resource has been blocked for abnormal use',
        self::CANNOT_LOAN_OWN_PRODUCT => 'Cannot loan own product',
        self::USER_SUSPENDED => 'Your account has been blocked for abnormal use. If you have any objection, please contact customer service.',
        self::LOCKED_PRODUCT => 'The product has been ordered by someone',
        self::CANNT_ABORT => 'Loan already send for svfc or you already abort, Should can not abort',
        self::PRODUCT_ARE_BEING_EVALUATED => ' Products are being evaluated',
        self::NONE_LOAN_BECAUSE_STORE_NOT_ALLOW => 'none loan because store not allow',
        self::EXCEEDED_THE_TIME_SPECIFIED => 'Exceeded the time specified',
        self::REQUEST_SUCCESS => 'Request success',
        self::REQUEST_FALSE => 'Request false',
        self::VALIDATE_ERROR => 'Validate error',
        self::TOKEN_IS_EXPIRED => 'Token is expired',
        self::TOKEN_IS_INVALID => 'Token is invalid',
        self::TOKEN_BLACKLISTED_EXCEPTION => 'Token can not be used, get new one',
        self::TOKEN_IS_NOT_FOUND => 'Authorization Token not found',
        self::EVENT_DRAFT => 'Event draft',
        self::EVENT_EXPIRED => 'Event already expired',
        self::GOOGLE_FIREBASE => 'Send sms otp by google firabase',
        self::VIETTEL_SMS => 'Send sms otp by viettel',
        self::INVALID_NUMBER_PHONE => 'Invalid phone unused format',
        self::UPLOAD_FALSE => 'Upload false',

        self::DATA_IS_NOT_EXIST => 'data don\'t exist',
        self::MC_ID_EMPTY => 'merchant_id can\'t be empty',

        self::PARTNER_NOTIFY_ERROR => 'Error received notification partner',
    ];
}
