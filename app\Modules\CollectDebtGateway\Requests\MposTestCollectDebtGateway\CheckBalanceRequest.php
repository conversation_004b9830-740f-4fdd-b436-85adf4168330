<?php

namespace App\Modules\CollectDebtGateway\Requests\MposTestCollectDebtGateway;

use Illuminate\Foundation\Http\FormRequest;

class CheckBalanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data.request_id' => 'required|integer',
            'data.users_admin_id' => 'required|string|max:255',
            'data.partner_request_id' => 'required|string|max:50',
            'data.partner_merchant_id' => 'required|string',
        ];
    }

    public function messages()
    {
        return [
            'data.request_id.required' => 'Trường request_id không để trống',
            'data.request_id.integer' => 'Trường request_id phải đúng định dạng',
            'data.users_admin_id.required' => 'Trường users_admin_id không để trống',
            'data.users_admin_id.string' => 'Trường users_admin_id phải đúng định dạng',
            'data.users_admin_id.max' => 'Trường users_admin_id không được lớn hơn :max',
            'data.partner_request_id.string' => 'Trường partner_request_id phải đúng định dạng',
            'data.partner_request_id.max' => 'Trường partner_request_id không được lớn hơn :max',
            'data.partner_request_id.required' => 'Trường partner_request_id không để trống',
            'data.partner_merchant_id.required' => 'Trường partner_merchant_id không để trống',
            'data.partner_merchant_id.string' => 'Trường partner_merchant_id phải đúng định dạng',
        ];

    }
}
