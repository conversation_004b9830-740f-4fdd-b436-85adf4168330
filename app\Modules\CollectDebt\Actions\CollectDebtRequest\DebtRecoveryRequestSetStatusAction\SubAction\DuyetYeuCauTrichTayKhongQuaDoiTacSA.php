<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction;

use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\TaoCongNoKhiLaTrichNgayTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Exception;

class DuyetYeuCauTrichTayKhongQuaDoiTacSA
{
  public function run(CollectDebtRequest $collectDebtRequest, $request): CollectDebtRequest
  {
    $collectDebtRequest->load('collectDebtPartner');
    $collectDebtPartner = $collectDebtRequest->collectDebtPartner;

    $collectDebtPartner->amount_payment = $collectDebtRequest->amount_receiver;
    $collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
    $collectDebtPartner->other_data = $collectDebtPartner->putOtherData([
      'type' => 'REQUEST',
      'time_modified' => time(),
      'data' => $collectDebtRequest->only(['id', 'plan_ids', 'amount_request', 'amount_receiver', 'partner_request_id']),
      'note' => 'Trích tay không gửi đối tác'
    ]);
    $collectDebtPartner->save();

    if ($collectDebtPartner->canPaidCollectRequest()) {
      $collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_CHUA_XU_LY;
      $collectDebtPartner->save();
    }

    $collectDebtRequest->other_data = $collectDebtRequest->putOtherData([
      'type' => 'APPROVED',
      'time_modified' => time(),
      'note' => $request->json('data.description', 'Duyệt yc trích có chứng từ'),
      'data' => [
        'user' => $request->json('data.user_request_id')
      ]
    ]);

    $collectDebtRequest->status = CollectDebtEnum::REQUEST_STT_DA_DUYET;
    $collectDebtRequest->status_payment = CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA;
    $collectDebtRequest->save();

    $collectDebtRequest->__apiMessage = 'Đã duyệt yêu cầu trích tay';
    return $collectDebtRequest;
  }
}