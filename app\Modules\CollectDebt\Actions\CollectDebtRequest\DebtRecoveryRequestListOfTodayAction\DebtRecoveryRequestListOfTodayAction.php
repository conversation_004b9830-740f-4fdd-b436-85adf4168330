<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestListOfTodayAction;

use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class DebtRecoveryRequestListOfTodayAction
{
  public function run(Request $request): Collection
  {
    $collectDebtRequests = CollectDebtRequest::query()
                                             ->where('contract_code', $request->json('data.contract_code'))
                                             ->where('time_begin', '<=', time())
                                             ->where('time_expired', '>=', time())
                                             ->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT)
                                             ->whereNotIn('status', [
                                                CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
                                                CollectDebtEnum::REQUEST_STT_TU_CHOI,
                                             ])
                                             ->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
                                             ->where('status_payment', '!=', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA)
																						 ->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
																						 ->whereNull('time_canceled')
                                             ->get();
    return $collectDebtRequests;
  }
} // End class