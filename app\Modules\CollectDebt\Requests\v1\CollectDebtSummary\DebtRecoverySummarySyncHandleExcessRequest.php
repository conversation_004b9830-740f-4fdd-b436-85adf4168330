<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummarySyncHandleExcessRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.contract_code' => ['required', 'string', 'max:50'],
			'data.list_contract_receive_money' => ['required', 'array'],
			'data.list_contract_receive_money.*' => ['required', 'string', 'max:50'],
			'data.profile_id' => ['required', 'string'],
			'data.cashin_type' => ['required', 'in:APPROVED,DENIED']
    ];
  }

	public function messages()
	{
		return [
			'data.contract_code.required' => 'Mã HĐ cho là bắt buộc',
			'data.contract_code.string' => 'Mã HĐ phải là kiểu chuỗi',
			'data.contract_code.max' => 'Mã HĐ phải có độ dài tối đa là 50 ký tự',
			'data.list_contract_receive_money.required' => 'Danh sách HĐ nhận là bắt buộc',
			'data.list_contract_receive_money.array' => 'Danh sách HĐ nhận phải là kiểu mảng',
			'data.list_contract_receive_money.*.required' => 'Mã HĐ nhận là bắt buộc',
			'data.contract_code.list_contract_receive_money.*.string' => 'Mã HĐ nhận phải là kiểu chuỗi',
			'data.contract_code.list_contract_receive_money.*.max' => 'Mã HĐ nhận phải có độ dài tối đa là 50 ký tự',
			'data.profile_id.required' => 'ProfileId là bắt buộc',
			'data.profile_id.string' => 'ProfileId phải là kiểu chuỗi',
			'data.cashin_type.required' => 'Loại duyệt cho tiền là bắt buộc',
			'data.cashin_type.in' => 'Loại duyệt cho tiền phải thuộc 1 trong các giá trị: APPROVED,DENIED',
		];
	}
} // End class
