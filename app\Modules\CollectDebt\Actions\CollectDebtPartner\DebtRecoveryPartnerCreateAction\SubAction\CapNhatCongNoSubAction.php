<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtPartner;
use Illuminate\Http\Request;

class CapNhatCongNoSubAction
{
	/**
	 * Nếu lệnh trích ngay mà chui vào đây thì gần như là không cập nhật gì thêm, và kết quả sẽ check ở luồng recheck
	 *
	 * @param CollectDebtPartner $collectDebtPartner
	 * @param Request $request
	 * @return void
	 */
  public function run(CollectDebtPartner $collectDebtPartner, Request $request)
  {
    $param = $request->json('data');

    if (empty($collectDebtPartner->response)) {
      $collectDebtPartner->response = $param['response'];
    }

    if (!$collectDebtPartner->isPartnerTrangThaiCuoi()) {
      $collectDebtPartner->response = $param['response'];
      $collectDebtPartner->amount_receiver = $param['amount_receiver'];
      $collectDebtPartner->amount_payment = $param['amount_receiver'];
      
      if (!empty($param['partner_transaction_id'])) {
        $collectDebtPartner->partner_transaction_id = $param['partner_transaction_id'];
      }
    }

    $collectDebtPartner->save();

    return $collectDebtPartner;
  }
}
