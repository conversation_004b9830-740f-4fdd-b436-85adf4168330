<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\GetListRefundTransactionOnPartnerAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class GetListRefundTransactionOnPartnerAction
{
  public function run(Request $request)
  {
    $collectDebtSummary = CollectDebtSummary::query()
                                            ->where('contract_code', $request->json('data.contract_code'))
                                            ->first();

    $contractCode = $request->json('data.contract_code');
    $partners = CollectDebtPartner::query()
                                  ->whereByContractCode($contractCode)
                                  ->where('request_refund', 1);
    
    $paymentMethodCode = $request->json('data.filter.payment_method_code');
    if ( !empty($paymentMethodCode) ) {
      $partners = $partners->where('payment_method_code', $paymentMethodCode);
    }

    $partnerRequestTransactionId = $request->json('data.filter.partner_request_transaction_id');
    
    if ( !empty($partnerRequestTransactionId) ) {
      $partners = $partners->where(function ($query) use ($partnerRequestTransactionId) {
        return $query->where('partner_request_id', $partnerRequestTransactionId)
                     ->orWhere('partner_transaction_id', $partnerRequestTransactionId);
      });
    }


    $paymentAccountId = $request->json('data.filter.payment_account_id');

    if ( !empty($paymentAccountId) ) {
      $partners = $partners->where('payment_account_id', $paymentAccountId);
    }

    $status = $request->json('data.filter.status');

    if ( !empty($status) ) {
      $partners = $partners->where('status', $status);
    }
    
    $partners = $partners->orderByRaw($request->json('sortBy', 'id DESC'))
                        ->select($request->json('data.fields', ['*']))
                        ->get();

    $partners = $partners->map(function (CollectDebtPartner $partner) use ($collectDebtSummary) {
      $partner->currency = $collectDebtSummary->getCurrency();
      return $partner;
    });

    return $partners;
  }
}
