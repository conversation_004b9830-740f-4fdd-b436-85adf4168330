<?php

namespace App\Modules\EmailRemind\Actions\ExecuteSendEmailAction;

use App\Lib\NextlendCore;

class ExecuteSendEmailAction
{
  public function run(
    string $subject = '',
    string $mailContent = '', 
    array $senderInfo=[],
    array $listTo = [], 
    array $listCc,
    $categoryCareData='NOTIFY_CONTRACT_DUE',
    $requestId=''
  ) {
    $mailSendParam = [
      'request_id' => $requestId,
      'no_send_all_manager' => true,
      'source'              => 1,
      'partner_code'        => 'NEXTLEND',
      'content_type'        => 'EMAIL',
      'title'               => $subject,
      'content'             => base64_encode($mailContent),
      'sender_info'         => [],
      'receiver_list'       => [
        'to' => $listTo,
        'cc' => $listCc,
      ],
      'maximum_number_submissions' => 1,
      'description'                => 'Gửi mail nhắc thanh toán',
      'partner_data'               => [],
      'category_care_data'         => [$categoryCareData],
      'category_care_code'         => $categoryCareData,
      'attachment'                 => []
    ];

    $sendMail = app(NextlendCore::class)->callRequest($mailSendParam, 'CustomerRequestCare_create', 'POST');
    return $sendMail->decryptData(true);
  }
} // End class