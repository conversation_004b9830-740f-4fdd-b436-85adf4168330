<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class GetHopDongCoLichThuQuaKhuVanChuaXongSubAction
{
  public function run()
  {
    
    $plans = CollectDebtSchedule::query()
                                ->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
                                ->where('rundate', '<', date('Ymd'))
                                ->where('isfee', CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC)
                                ->groupBy('contract_code')
                                ->select([
                                  'id',
                                  'contract_code',
                                  'rundate'
                                ])
                                ->get();
                              
    
    if ($plans->isEmpty()) {
      return [];
    }

    $contractCodes = [];
    $plans->map(function (CollectDebtSchedule $plan) use (&$contractCodes) {
      if ($plan->isRunDateTrongTuan()) {
        $contractCodes[] = $plan->contract_code;
      }

      if ($plan->isRunDateLaChuNhat()) {
        $nextMonday = $plan->rundate_as_date->copy()->next('Monday');
        // Vẫn là quá khứ thì phải loại trừ
        if ( !$nextMonday->isSameDay(now()) ) {
          $contractCodes[] = $plan->contract_code;
        }
      }
    });

    return $contractCodes;
  }
} // End class