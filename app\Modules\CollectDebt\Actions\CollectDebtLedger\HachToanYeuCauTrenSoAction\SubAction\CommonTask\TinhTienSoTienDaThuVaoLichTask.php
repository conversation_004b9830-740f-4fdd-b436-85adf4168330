<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Lib\Helper;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;

class TinhTienSoTienDaThuVaoLichTask
{
  /**
   * Cập nhật số tiền đã thu vào lịch 
   * Lưu ý: Không được update by model, vì có object tự thêm vào -> nó sẽ gây lỗi
   * Nhớ set data để sau này còn biết được lịch thu qua những yêu cầu nào, vào số tiền thu được là bao nhiêu
   * @param $soTienTrichThanhCongChoLich: Số tiền này được tính toán bên ngoài
   */
  public function run(string $planId, float $soTienTrichThanhCongChoLichTheoKenh = 0, CollectDebtRequest $collectDebtRequest)
  {
    $plan = CollectDebtSchedule::find($planId);

    $plan->success_amount_debit += $soTienTrichThanhCongChoLichTheoKenh;
    $plan->time_updated = time();


    $plan->other_data = $plan->putPlanOtherData([
      'type' => 'REQUEST',
      'time_modified' => time(),
      'data' => [
        'request_id' => $collectDebtRequest->id,
        'amount_receiver' => $collectDebtRequest->amount_receiver,
        'amount_success_for_plan' => $soTienTrichThanhCongChoLichTheoKenh,
        'payment_method_code' => $collectDebtRequest->payment_method_code,
        'time_modified_as_date' => date('d/m/Y H:i:s')
      ],
      'note' => sprintf('Yêu cầu đã thu thành công `%s` cho lịch', Helper::priceFormat($soTienTrichThanhCongChoLichTheoKenh))
    ]);

    $result = $plan->save();

		if (!$result) {
			mylog([
				'[LOI]' => 'loi tinh tien so tien success amount vao lich',
				'result' => $result
			]);

			throw new Exception('Loi tinh tien so tien success amount vao lich');
		}
		
    return $plan;
  }
}
