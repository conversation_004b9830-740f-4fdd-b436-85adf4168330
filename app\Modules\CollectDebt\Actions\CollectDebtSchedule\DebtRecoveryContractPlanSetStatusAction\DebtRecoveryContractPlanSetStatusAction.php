<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanSetStatusAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanSetStatusRequest;

class DebtRecoveryContractPlanSetStatusAction
{  
  public function run(DebtRecoveryContractPlanSetStatusRequest $request): CollectDebtSchedule
  {
    $flagUpdate = false;

    $collectDebtSchedule = CollectDebtSchedule::find($request->json('data.id'));
    throw_if(!$collectDebtSchedule, new Exception('Không tìm thấy lịch thu hồi'));

    if ($collectDebtSchedule->isNew() && $request->json('data.status') == CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN) {
      $collectDebtSchedule->status = CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN;
      $collectDebtSchedule->create_accounting_by = $request->json('data.user_request_id');
      $collectDebtSchedule->time_accounting = time();
      $collectDebtSchedule->save();

      $flagUpdate = true;
    }

    if ($collectDebtSchedule->isNew() && $request->json('data.status') == CollectDebtEnum::SCHEDULE_STT_DA_HUY) {
      $collectDebtSchedule->status = CollectDebtEnum::SCHEDULE_STT_DA_HUY;
      $collectDebtSchedule->canceled_by = $request->json('data.user_request_id');
      $collectDebtSchedule->time_canceled = time();
      $collectDebtSchedule->save();

      $flagUpdate = true;
    }

    if ($collectDebtSchedule->isAccounting() && $request->json('data.status') == CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH) {
      $collectDebtSchedule->status = CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH;
      $collectDebtSchedule->canceled_by = $request->json('data.user_request_id');
      $collectDebtSchedule->time_approved = time();
      $collectDebtSchedule->save();

      $flagUpdate = true;
    }

    $message = sprintf('Logic cập nhật không đúng luồng. Trạng thái bạn muốn cập nhật là: "%s" trong khi lịch thu hiện tại đang có trạng thái là: "%s"',       
      CollectDebtSchedule::listStatus()[$request->json('data.status')],
      CollectDebtSchedule::listStatus()[$collectDebtSchedule->status],
    );

    throw_if(!$flagUpdate, new Exception($message));

    return $collectDebtSchedule;
  }
} // End class