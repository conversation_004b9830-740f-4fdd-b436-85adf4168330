<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\Task;

use App\Lib\TelegramAlert;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Exception;

class SwapLichThuNeuTrichTuongLaiST
{
  /**
   * Swap lịch thu:
   *  Chính -> Phụ 
   *  Phụ -> Chính 
   * khi thanh toán tương lai bằng VA hoặc IB_OFF, mục đích của việc này là để tránh không sinh phí
   */
  public function run(CollectDebtSchedule $lichThuNoGoc, CollectDebtSchedule $lichThuVet, $isHachToanCutOff=false)
  {
      $sql = sprintf('
        UPDATE `debt_recovery_contract_plan` SET `type` = CASE 
          WHEN id = %d THEN %d
          WHEN id = %d THEN %d
          ELSE `type`
          END
        WHERE id in (%d, %d)
      ', 
      $lichThuVet->id, CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH, // Dòng 22
      $lichThuNoGoc->id, CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU, // Dòng 23
      $lichThuVet->id, $lichThuNoGoc->id // Dòng 26
    );
	
		mylog(['[PARAM SQL SWAP LICH]' => $sql]);

    $result = DB::update($sql);

		if (!$result) {
			mylog(['[LOI]' => 'Khong the swap lich tuong lai']);
			throw new Exception('Loi khong the swap lich tuong lai');
		}

		// hach toan tuong lai vao thoi diem tu 22h->24h, thi khong duoc cong them 1 ngay cua lich tuong lai duoc sinh ra do
		if ($isHachToanCutOff) {
			mylog([
				'Hach toan lich tuong lai' => 'vao thoi diem cut off',
				'ID Lich tuong lai bi gach' => $lichThuNoGoc->id,
				'ID lich tuong lai duoc sinh ra' => $lichThuVet->id,
			]);

			$lichThuTuongLai = CollectDebtSchedule::find($lichThuVet->id);
			$lichThuTuongLai->time_start = $lichThuNoGoc->time_start;
			$lichThuTuongLai->time_end = $lichThuNoGoc->time_end;
			$lichThuTuongLai->rundate = $lichThuNoGoc->rundate;
			$updateMocThoiGianTuongLai = $lichThuTuongLai->save();
			
			if (!$updateMocThoiGianTuongLai) {
				mylog(['[LOI TUONG LAI]' => 'khong the cap nhat moc thoi gian tuong lai']);
				throw new Exception('khong the cap nhat moc thoi gian tuong lai');
			}
		}

		return $result;
  }
}
