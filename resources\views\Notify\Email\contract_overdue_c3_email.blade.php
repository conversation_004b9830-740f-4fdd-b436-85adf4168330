<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Email quá hạn Cấp 3</title>
    @include('Notify.Email.inc.style')
</head>
<body>
    @php($merchant = $data['merchant'])
    @php($nextlendCompany = $data['company'])
    
    <div class="mail-content" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">
        <div class="content">
            <p>
                Kính gửi: <PERSON>u<PERSON> khách
                <strong>{{ sprintf('%s - %s', $merchant['fullname'], $merchant['business_representative'])}}</strong>
            </p>
        
            <p>
                <b>{{ $nextlendCompany['company_fullname'] }}</b>
                ({{ $nextlendCompany['company_subname'] }}) xin gửi lời chào trân trọng và cảm ơn Quý khách
                hàng đã quan tâm đến dịch vụ “Hỗ trợ Ứng vốn Kinh doanh”, “Đặc biệt” dành cho đơn vị đang là đối tác của hệ sinh thái “Next360”:
            </p>
            
            <ul style="list-style-type: none; margin-left: 25px;">
                <li>Chủ thể: {{ $merchant['fullname'] }}</li>
                <li>ĐKKD số: {{ $merchant['certificate_id'] }}</li>
                <li>Đại diện: {{ $merchant['business_representative'] }} - Chức vụ: Đại diện đơn vị</li>
                <li>
                    Số CMND/CCCD: {{ $merchant['passport' ]}} - 
                    Ngày cấp: {{ $merchant['issue_date']}} - 
                    Nơi cấp: {{ $merchant['issued_by']}}
                </li>
                <li>Địa chỉ: {{ $merchant->getAddress() }}</li>
                <li>
                    Phụ lục Hợp đồng số: <strong>{{ $data['contract']['code'] }}</strong>
                </li>

                <li>Số vốn ứng: {{ $data['contract']['amount'] }} VNĐ</li>

                <li>
                    Thời hạn: 
                    {{ $data['contract']['cycle'] }} ngày, kể từ ngày
                    {{ $data['contract']['time_start'] }} đến ngày
                    {{ $data['contract']['time_end'] }}
                </li>

                <li>Sau đây gọi chung là <strong>“Quý khách”</strong>.</li>
            </ul>

            <p>
                Thời gian qua. Chúng tôi đã luôn gửi cho Quý khách tin nhắn/email/call thông báo tình trạng hoàn trả vốn ứng và phí dịch vụ quá hạn phát sinh, nhưng không nhận được sự hợp tác của Quý khách.
            </p>

            <p>
                {{ $nextlendCompany->getCompanyCode() }} xin thông báo tình trạng hoàn trả Ứng vốn của Quý khách như sau:
            </p>


            @include('Notify.Email.inc.qua_han_table')
            
            <p>
                <em>
                    * Số ngày quá hạn: số ngày thể hiện trong thông báo này chỉ tính đến ngày gửi thông báo, số ngày quá hạn thực tế được chốt tại ngày Quý khách hoàn trả vốn ứng.
                </em>
            </p>

            <p>
                Thông báo này là một phần không tách rời của Phụ lục và Hợp đồng đã ký giữa Vi Mô và Quý khách.
            </p>

            
            @include('Notify.Email.inc.hinh_thuc_thanh_toan_va')
            
            <p>
                Yêu cầu Quý khách thu xếp thực hiện thanh toán số tiền còn thiếu trên. Nếu không, Chúng tôi có quyền chuyển hồ sơ thu hồi sang bên thứ ba hoặc khởi tố hành vi lợi dụng tín nhiệm chiếm đoạt tài sản theo Điều 175 BLHS 2015 quy định.
            </p>

            <p>
                <em>
                    {Tội lừa đảo chiếm đoạt tài sản được quy định tại Điều 174 Bộ luật hình sự năm 2015 như sau: Người nào bằng thủ đoạn gian dối chiếm đoạt tài sản của người khác trị giá từ 2.000.000 đồng đến dưới 50.000.000 đồng hoặc dưới 2.000.000 đồng nhưng thuộc một trong các trường hợp sau đây, thì bị phạt cải tạo không giam giữ đến 03 năm hoặc phạt tù từ 06 tháng đến 03 năm:
                </em>
            </p>

            <p>
                <em>
                    Tội lạm dụng tín nhiệm chiếm đoạt tài sản được quy định tại Điều 175 Bộ luật hình sự năm 2015 quy định như sau: Vay, mượn, thuê tài sản của người khác hoặc nhận được tài sản của người khác bằng các hình thức hợp đồng rồi dùng thủ đoạn gian dối chiếm đoạt tài sản đó hoặc đến thời hạn trả lại tài sản mặc dù có điều kiện, khả năng nhưng cố tình không trả; hoặc Vay, mượn, thuê tài sản của người khác hoặc nhận được tài sản của người khác bằng các hình thức hợp đồng và đã sử dụng tài sản đó vào mục đích bất hợp pháp dẫn đến không có khả năng trả lại tài sản.}
                </em>
            </p>

            <p>
                Để biết thêm chi tiết hoặc cần hỗ trợ, Quý khách vui lòng liên hệ Phòng Dịch vụ và Chăm sóc Khách hàng của {{ $nextlendCompany['company_subname'] }}:
                Điện thoại: <b> {{ $nextlendCompany->getPhoneNumber() }}</b>.

                Email:
                <a href="mailto:{{ $nextlendCompany->getEmail() }}" style="text-decoration: none;">
                    {{ $nextlendCompany->getEmail() }}
                </a>
            </p>

            <p>
                {{ $nextlendCompany['company_subname'] }} xin trân trọng cảm ơn Quý khách đã quan tâm, sử dụng các sản phẩm, dịch vụ của chúng tôi và rất mong nhận được sự hợp tác của Quý khách.
            </p>

            <p>Trân trọng!</p>

            @include('Notify.Email.inc.mail_footer')
        </div>
    </div>
</body>
</html>