<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary;

use DB;
use Exception;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryDetailRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryUpdateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummarySetByAccountingRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryGetByContractCodeRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCreateAction\DebtRecoverySummaryCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryUpdateAction\DebtRecoverySummaryUpdateAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySetByAccounting\DebtRecoverySummarySetByAccounting;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction\DongBoVeHeThongHopDongAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\DebtRecoverySummaryFinishContractAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryMailOverDueAction\DebtRecoverySummaryMailOverDueAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryMailOverCycleAction\DebtRecoverySummaryMailOverCycleAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction\DebtRecoverySummaryCheckLedgerExsistAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetByContractCodeAction\DebtRecoverySummaryGetByContractCodeAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction\DebtRecoverySummaryTurnOnContractMustSyncAt23hAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class CollectDebtSummaryController extends Controller {

    public function create(DebtRecoverySummaryCreateRequest $request) {
        try {
            $collectDebtRequest = app(DebtRecoverySummaryCreateAction::class)->run($request);
            return $this->successResponse($collectDebtRequest->toArray(), $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function update(DebtRecoverySummaryUpdateRequest $request) {
			DB::beginTransaction();
			try {
				$collectDebtRequest = app(DebtRecoverySummaryUpdateAction::class)->run($request);
				DB::commit();
				return $this->successResponse($collectDebtRequest->toArray(), $request);
			} catch (\Throwable $th) {
				DB::rollBack();
				return $this->errorResponse($th->getCode(), Helper::traceError($th));
			}
    }

		public function detail(DebtRecoverySummaryDetailRequest $request) {
			try {
				$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $request->json('data.contract_code'))->first();
				throw_if(!$collectDebtSummary, new Exception('Khong tim thay thong tin tong hop'));
				
				return $this->successResponse($collectDebtSummary->toArray(), $request);
			} catch (\Throwable $th) {
				return $this->errorResponse($th->getCode(), Helper::traceError($th));
			}
    }

    public function setByAccounting(DebtRecoverySummarySetByAccountingRequest $request) {
        try {
            $collectDebtRequest = app(DebtRecoverySummarySetByAccounting::class)->run($request);
            return $this->successResponse($collectDebtRequest, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function complete(Request $request) {
        try {
            $hopDongTatToan = app(DebtRecoverySummaryFinishContractAction::class)->run($request);
            $res = !empty($hopDongTatToan) ? $hopDongTatToan : ['Cac HD khong du dieu kien tat toan'];
            return $this->successResponse($res, $request, 200);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }
    }

    public function getByContractCode(DebtRecoverySummaryGetByContractCodeRequest $request) {
			try {
				$summary = app(DebtRecoverySummaryGetByContractCodeAction::class)->run($request);
				return $this->successResponse($summary, $request, 200, 'Lấy thông tin tổng hợp thành công');
			} catch (\Throwable $th) {
				return $this->errorResponse($th->getCode(), Helper::traceError($th));
			}
    }

  public function checkLedgerExsist(Request $request)
	{
		try {
			$collectDebtContractEvent = app(DongBoVeHeThongHopDongAction::class)->initDongBoHopDong();
			return $this->successResponse($collectDebtContractEvent, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

  public function resyncContract(Request $request)
	{
		try {
			$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $request->json('data.contract_code'))->first();
			if (!$collectDebtSummary) {
				throw new Exception('Không có thông tin hợp đồng');
			}

			$updated = CollectDebtSummary::query()->where([
				'id' => $collectDebtSummary->id,
				'contract_code' => $collectDebtSummary->contract_code
			])->update(['is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO]);

			return $this->successResponse(['result' => $updated], $request, 200, 'Tái đồng bộ thành công');
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function handleMailOverdue(Request $request)
	{
		try {
			$collectDebtSummary = app(DebtRecoverySummaryMailOverDueAction::class)->initGuiMailQuaHan($request);
			return $this->successResponse($collectDebtSummary, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function handleMailOverCycle(Request $request)
	{
		try {
			$collectDebtSummary = app(DebtRecoverySummaryMailOverCycleAction::class)->initGuiMailChamKy($request);
			return $this->successResponse($collectDebtSummary, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function turnOnContractMustSyncAt23h(Request $request)
	{
		try {
			$collectDebtSummary = app(DebtRecoverySummaryTurnOnContractMustSyncAt23hAction::class)->run($request);
			return $this->successResponse($collectDebtSummary, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
