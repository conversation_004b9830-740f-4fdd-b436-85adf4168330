<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction;


class CaculateAmountSubAction
{
    public static function getTotalFeeOverdue($summary)
    {
        $feeOverdue = $summary['fee_overdue'] - $summary['fee_overdue_reduction'] - $summary['fee_overdue_paid'];
        return $feeOverdue;
    }

    public static function getTotalFeeOverdueCycle($summary)
    {
        $feeOverdueCycle = $summary['fee_overdue_cycle'] - $summary['fee_overdue_cycle_reduction'] - $summary['fee_overdue_cycle_paid'];
        return $feeOverdueCycle;
    }

    public static function getAmountRemainingPayment($summary)
    {
        $amountRemainingPayment = ($summary['contract_amount'] - $summary['total_amount_paid'] - $summary['total_fee_paid']) + ($summary['fee_overdue'] + $summary['fee_overdue_cycle']) - self::getTotalFeeReduction($summary);
        return $amountRemainingPayment;
    }

    public static function getTotalFeeReduction($summary)
    {
        $feeReduction = $summary['fee_overdue_reduction'] + $summary['fee_overdue_cycle_reduction'];
        return $feeReduction;
    }

    public static function getDebtAmountEndPeriod($summary)
    {
        $debtAmountEndPeriod = ($summary['contract_amount'] + $summary['fee_overdue'] + $summary['fee_overdue_cycle']) - (self::getTotalFeeReduction($summary) + $summary['total_amount_paid'] + $summary['total_fee_paid']);
        return $debtAmountEndPeriod;
    }
}
