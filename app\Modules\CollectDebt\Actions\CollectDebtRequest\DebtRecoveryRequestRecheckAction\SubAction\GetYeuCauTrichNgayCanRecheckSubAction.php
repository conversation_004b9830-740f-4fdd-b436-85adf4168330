<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Exception;
use Illuminate\Database\Eloquent\Collection;

class GetYeuCauTrichNgayCanRecheckSubAction
{
	public function run(): Collection
	{
		$logListYcTrich = CollectDebtLog::query()
																		->where('status', CollectDebtEnum::RL_STT_MOI_TAO)
																		->where('service_code', CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY)
																		->get();

		if ($logListYcTrich->isEmpty()) {
			return Collection::make([]);
		}
		
		$partnerTransactionIds = $logListYcTrich->pluck('partner_transaction_id')->toArray();

		$updatedLenDangXuLy = CollectDebtLog::query()
																				->whereIn('partner_transaction_id', $partnerTransactionIds)
																				->where('service_code', CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY)
																				->where('status', CollectDebtEnum::RL_STT_MOI_TAO)
																				->update(['status' => CollectDebtEnum::RL_STT_DANG_XU_LY]);

		if (!$updatedLenDangXuLy || $updatedLenDangXuLy != count($partnerTransactionIds)) {
			mylog(['Loi cap nhat ban ghi log' => $updatedLenDangXuLy]);
			throw new Exception('Loi cap nhat ban ghi log');
		}

		$ycTrichNgay = CollectDebtRequest::query()
																		 ->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
																		 ->where('status', CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH)
																		 ->where('time_expired', '<', now()->timestamp)
																		 ->whereIn('partner_transaction_id', $partnerTransactionIds)
																		 ->get();

		// khong co thong tin yc phu hop (do lenh trich ngay chua het han)
		if ($ycTrichNgay->isEmpty()) {
			$logIds = $logListYcTrich->pluck('id')->toArray(); 
			
			$updateLogVeChuaXuLy = CollectDebtLog::query()
																					 ->whereIn('id', $logIds)
																					 ->where('status', CollectDebtEnum::RL_STT_DANG_XU_LY)
																					 ->where('service_code', CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY)
																					 ->update([
																						'status' => CollectDebtEnum::RL_STT_MOI_TAO,
																						'time_updated' => now()->timestamp
																					]);
			mylog([
				'LogIds' => $logIds,
				'Ket qua cap nhat ve chua xu ly' => $updateLogVeChuaXuLy
			]);

			return Collection::make([]);
		}		

		$ycTrichNgaySeRecheck = Collection::make([]);

		foreach ($ycTrichNgay as $rq) {
			if ( !empty($rq->time_receivered) ) {
				$log = $logListYcTrich->where('partner_transaction_id', $rq->partner_transaction_id)->first();
				if ($log) {
					CollectDebtLog::where('id', $log->id)->update(['status' => CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG]);
				}
			}else {
				$ycTrichNgaySeRecheck->push($rq);
			}
		}

		return $ycTrichNgaySeRecheck;
	}
}
