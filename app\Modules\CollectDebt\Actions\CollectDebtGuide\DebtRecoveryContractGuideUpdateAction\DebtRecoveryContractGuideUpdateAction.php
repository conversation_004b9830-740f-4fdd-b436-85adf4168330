<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideUpdateAction;

use Exception;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideUpdateRequest;

class DebtRecoveryContractGuideUpdateAction
{
  public function run(CollectDebtGuide $collectDebtGuide, Request $request): CollectDebtGuide
  {
    throw_if(!$collectDebtGuide->isGuideMoiTao(), new Exception('Chỉ dẫn đang không ở trạng thái mới tạo', 500));
    $updateParams = $request->only([
      'data.profile_id',
      'data.contract_code',
      'data.contract_cycle',
      'data.contract_intervals',
      'data.contract_time_start',
      'data.contract_time_end',
      'data.amount',
      'data.payment_guide',
      'data.list_fee',
      'data.other_data',
      'data.profile_data',
      'data.updated_by',
      'data.time_updated',
      'data.description'
    ])['data'];

    $collectDebtGuide->update($updateParams);
    return $collectDebtGuide->refresh();
  }
}
