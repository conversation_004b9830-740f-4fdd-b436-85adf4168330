<?php

namespace App\Modules\CollectDebtGateway\Inter;


interface CollectDebtRecoveryGatewayLogInterface
{
    /**
     * @return string|null
     */
    public function getNameTable();
    
    /**
     * @param array $inputs
     * @return array|bool
     */
    public function createGetId(array $inputs);

    /**
     * @param $id
     * @param array $inputs
     * @return array|bool
     */
    public function updateGetId($id, array $inputs);

    /**
     * @param array $inputs
     * @return array
     */
    public function getByPartnerRequestId(array $inputs);


    /**
     * @param array $inputs
     * @return array
     */
    public function getByRequestId(array $inputs);

    /**
     * @param $data
     * @return array
     */
    public function getAllByFollow(array $inputs);
}
