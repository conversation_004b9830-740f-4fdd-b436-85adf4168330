<?php

use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\Security;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Support\Facades\DB;

if (!function_exists('mylog')) {
  function mylog(array $params=[]) {
    return app('mylog')->cc($params);
  }
}

if (!function_exists('da')) {
  function da($data) {
    dd($data->toArray());
  }
}

if (!function_exists('elog')) {
  function elog() {
    DB::enableQueryLog();
  }
}

if (!function_exists('slog')) {
  function slog() {
    dd(DB::getQueryLog());
  }
}

if (!function_exists('anti_sql')) {
  function anti_sql($requestValue) {
    Security::checkSqlCharacter($requestValue);
  }
}

if (!function_exists('traceErr')) {
  function traceErr(\Throwable $th, bool $isDumpAndDie=false) {
    $errors = Helper::traceError($th);
    if ($isDumpAndDie) {
      dd($errors);
    }
    return $errors;
  }
}


if (!function_exists('pr')) {
  function pr($data=[]) {
    echo "<pre>";
      print_r($data);
      exit;
    echo "</pre>";
  }
}

if (!function_exists('po')) {
  function po($data=[]) {
    echo "<pre>";
      print_r(json_encode($data, JSON_UNESCAPED_UNICODE|JSON_PRETTY_PRINT));
      exit;
    echo "</pre>";
  }
}

if (!function_exists('parseErr')) {
	function parseErr($data) {
		$str = '';
		$i = 0;
		$count = count($data);
		foreach ($data as $k => $v) {
			$i++;
			$str .= sprintf("<b>%s</b>: %s %s", $k, $v, $i < $count ? "\n" : "");
		}

		return $str;
	}
}

if (!function_exists('getNextMondayTimeStart')) {
	function getNextMondayTimeStart(Carbon $date): int {
		return $date->copy()->next('Monday')->setTime(
			now()->format('H'),
			now()->format('i'),
		)->timestamp;
	}
}

if (!function_exists('getNextMondayTimeEnd')) {
	function getNextMondayTimeEnd(Carbon $date): int {
		return $date->copy()->next('Monday')->setTime(22, 0)->timestamp;
	}
}


if (!function_exists('getNextMondayRundate')) {
	function getNextMondayRundate(Carbon $date): string {
		return $date->copy()->next('Monday')->format('Ymd');
	}
}

if (!function_exists('getRundateHomSau')) {
	function getRundateHomSau(CollectDebtSchedule $lichDangHachToan) {
		return $lichDangHachToan->rundate_as_date->copy()->addDay()->format('Ymd');
	}
}

if (!function_exists('getTimeStartHomSau')) {
	function getTimeStartHomSau(CollectDebtSchedule $lichDangHachToan) {
		return $lichDangHachToan->time_start_as_date->copy()->addDay()->timestamp;
	}
}

if (!function_exists('getTimeEndHomSau')) {
	function getTimeEndHomSau(CollectDebtSchedule $lichDangHachToan) {
		return $lichDangHachToan->time_end_as_date->copy()->addDay()->timestamp;
	}
}
