<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class XuLyNeuLaYeuCauGiamPhiTask
{
	public function run(CollectDebtSchedule $plan, CollectDebtRequest $yeuCauGiamPhi)
	{
		$returnData = [];

		$soTienGiamPhi = $plan->request_amount_debit;
		if ($plan->isLichThuPhiQuaHan()) {
			$returnData = [
				'label' => CollectDebtEnum::METADATA_GIAM_PHI_QUA_HAN,
				'value' => $soTienGiamPhi
			];
		}

		if ($plan->isLichThuPhiChamKy()) {
			$returnData = [
				'label' => CollectDebtEnum::METADATA_GIAM_PHI_CHAM_KY,
				'value' => $soTienGiamPhi
			];
		}

		return [
			'request_reduce_fee' => $yeuCauGiamPhi,
			'meta_data' => $returnData
		];
	}
} // End class