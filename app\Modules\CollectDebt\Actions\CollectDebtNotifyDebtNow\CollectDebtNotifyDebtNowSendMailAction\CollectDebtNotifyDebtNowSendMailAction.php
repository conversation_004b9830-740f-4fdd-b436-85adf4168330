<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction;

use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtNotifyDebtNow;
use App\Modules\EmailRemind\Actions\ExecuteSendEmailAction\ExecuteSendEmailAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction\CollectDebtNotifyDebtNowSendMailSubAction\CollectDebtNotifyDebtNowSendMailSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotification\PushNotificationAction\PushNotificationAction;
use Carbon\Carbon;

class CollectDebtNotifyDebtNowSendMailAction
{
    const STATUS_TO_SENDMAIL = 1;
    const STATUS_PENDING_SENDMAIL = 2;
    const STATUS_SUCCESS_SENDMAIL = 3;
    const PUSH_NOTIFY_MAXIMUM = 5;
    const PUSH_NOTIFY_COUNT = 1;
    const TYPE_PUSH_NOTIFY = 'LENH_TRICH_NGAY_KHONG_DU_TIEN';


    public function run($request)
    {
        $collectDebtNotify = CollectDebtNotifyDebtNow::query();
        $collectDebtNotify = $collectDebtNotify->whereIn('status', [CollectDebtNotifyDebtNowSendMailAction::STATUS_TO_SENDMAIL, CollectDebtNotifyDebtNowSendMailAction::STATUS_PENDING_SENDMAIL])
            ->where('push_notify_count', '<', CollectDebtNotifyDebtNowSendMailAction::PUSH_NOTIFY_MAXIMUM)
            ->get();
        foreach ($collectDebtNotify as $notify) {
            if ($notify->push_notify_count == 0) {
                $pushNotify = app(PushNotificationAction::class)->run(
                    $notify,
                    CollectDebtNotifyDebtNowSendMailAction::TYPE_PUSH_NOTIFY,
                    $notify->subject,
                    []
                );
            }
            $notify->push_notify_count += CollectDebtNotifyDebtNowSendMailAction::PUSH_NOTIFY_COUNT;
            $notify->save();
            $listTo = [
                [
                    'name' => "",
                    'identifier_account' => $notify->to,
                    'identifier_data' => ''
                ]
            ];
            $emails = $notify->cc;
            $emailArray = array_filter(array_map('trim', explode(",", $emails))); // Split and trim each email

            $listCc = empty($emailArray)
                ? [
                    [
                        'name' => "",
                        'identifier_account' => "",
                        'identifier_data' => ""
                    ]
                ]
                : array_map(function ($email) {
                    return [
                        'name' => "",
                        'identifier_account' => $email,
                        'identifier_data' => ""
                    ];
                }, $emailArray);
            $content = "";
            $other_data = [];
            $iDSendSuccess = [];
            if (!empty($notify->other_data)) {
                $other_data = json_decode($notify->other_data, true);
                $content = app(CollectDebtNotifyDebtNowSendMailSubAction::class)->buildContent($other_data);
            }
            if ($notify->push_notify_count != CollectDebtNotifyDebtNowSendMailAction::PUSH_NOTIFY_MAXIMUM) {
                $sendMailResult = app(ExecuteSendEmailAction::class)->run(
                    $notify->subject,
                    $content,
                    (array)$notify->sender,
                    $listTo,
                    $listCc,
                    'WARNING_DEBT_NOW',
                    'CanhBaoTrichNgay_' . $notify->id,
            );
                mylog(['data' => $sendMailResult]);
                $notify->status = CollectDebtNotifyDebtNowSendMailAction::STATUS_PENDING_SENDMAIL;
                if (!empty($sendMailResult['id'])) {
                    $notify->status = CollectDebtNotifyDebtNowSendMailAction::STATUS_SUCCESS_SENDMAIL;
                    $iDSendSuccess['id'][] = $notify->id;
                } else {
                    $notify->status = CollectDebtNotifyDebtNowSendMailAction::STATUS_TO_SENDMAIL;
                }
            } else {
                $notify->status = CollectDebtNotifyDebtNowSendMailAction::STATUS_TO_SENDMAIL;
                $errorMessage = parseErr([
                    'Error' => sprintf('Loi gui mail trich ngay . RequestId: %s', $notify->request_id),
                    'ErrorMessage' => ""
                ]);
                @TelegramAlert::sendMessage($errorMessage);
            }
            $notify->updated_at = Carbon::now();
            $notify->save();
        }
        mylog(['dataSuccess' => @$iDSendSuccess]);
        return $iDSendSuccess;
    }
}