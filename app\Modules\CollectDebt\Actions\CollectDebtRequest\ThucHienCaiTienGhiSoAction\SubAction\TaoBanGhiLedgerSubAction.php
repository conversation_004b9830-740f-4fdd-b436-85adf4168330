<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction\SubAction;

use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;

class TaoBanGhiLedgerSubAction
{
	public function run(CollectDebtRequest $collectDebtRequest): CollectDebtLedger
	{
		$otherData = [
			[
        'type' => 'REQUEST',
        'time_modified' => time(),
        'data' => StandardizedDataFilter::getRequestCompactAttribute($collectDebtRequest),
        'note' => 'Yê<PERSON> cầu ghi trên sổ'
      ],

      [
        'type' => 'SUMMARY',
        'time_modified' => time(),
        'data' => [
          'total_amount_debit_success'  => 0, // Tổng tiền thu được ghi vào sổ 
          'total_amount_paid'           => 0, // Số tiền cấn trừ thành công cho lịch
          'amount_paid'                 => 0,  // Số thiền thu thành công cho lịch thu gốc
          'fee_overdue_cycle_paid'      => 0, // Phí chậm kỳ thu được
          'fee_overdue_paid'            => 0, // Phí quá hạn thu được
          'fee_overdue_reduction'       => 0, // giảm phí QH được giảm
          'fee_overdue_cycle_reduction' => 0, // giảm phí CK được giảm
          'fee_overdue'                 => 0, // phí quá hạn
          'fee_overdue_cycle'           => 0, // phí chậm kỳ,
          'total_amount_receiver'       => $collectDebtRequest->amount_receiver, // số tiền nhận thực thế, tạm thời để bằng số tiền ghi sổ
          'total_amount_excess_revenue' => 0, // số tiền thu thừa trên sổ
        ],
        'note' => 'Bảng số liệu tổng hợp'
      ],

      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [],
				'note' => 'Dữ liệu khác'
      ]
		];

		if (!empty($collectDebtRequest->plan_ids)) {
			$listLichThu = app(GetLichThuByIdsSubAction::class)->run($collectDebtRequest->plan_ids);
			
			$otherData[] = [
        'type' => 'PLAN',
        'time_modified' => time(),
        'data' => Helper::getPlanCompact($listLichThu, true),
        'note' => 'Lịch thu'
      ];
		}

		$paramTaoSo = [
			'profile_id'    => $collectDebtRequest->profile_id,
			'contract_code' => $collectDebtRequest->contract_code,
			'plan_ids'      => $collectDebtRequest->plan_ids,
			'request_id'    => $collectDebtRequest->id,
			'currency'      => $collectDebtRequest->currency,
			'amount'        => $collectDebtRequest->amount_receiver,
			'description'   => $collectDebtRequest->description ?? '',
			'time_record'   => now()->timestamp,
			'created_by'    => request()->json('data.created_by', Helper::getCronJobUser()),
			'time_created'  => now()->timestamp,
			'time_updated'  => now()->timestamp,
			'other_data' 	  => json_encode($otherData)
		];

		mylog(['param tao so la' => $paramTaoSo]);

		$findWhere = ['request_id' => $collectDebtRequest->id];
		
		$ledger = CollectDebtLedger::query()->firstOrCreate($findWhere, $paramTaoSo);
		
		if (!$ledger) {
			mylog(['loi khong tao ban ghi ghi so' => $ledger]);
			throw new Exception('khong the tao ban ghi ghi so');
		}

		return $ledger;
	}	
} // End class
