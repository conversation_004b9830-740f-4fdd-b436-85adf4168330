<?php
namespace App\Modules\CollectDebtGateway\Requests\PartnerDebtGateway;

use Illuminate\Foundation\Http\FormRequest;

class NotifyContractFinishRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data.request_id' => 'required|integer',
            'data.users_admin_id' => 'required|string|max:255',
            'data.contract_code' => 'required|string|max:255',
            'data.amount_payment' => 'required|numeric',
            'data.amount_discount' => 'required|numeric',
            'data.amount_fee_out_of_date' => 'required|numeric',
            'data.amount_fee_cycle_out_of_date' => 'required|numeric',
        ];
    }

    public function messages()
    {
        return [
            'data.request_id.required' => 'Trường request_id không để trống',
            'data.request_id.integer' => 'Trường request_id phải đúng định dạng',
            'data.users_admin_id.required' => 'Trường users_admin_id không để trống',
            'data.users_admin_id.string' => 'Trường users_admin_id phải đúng định dạng',
            'data.users_admin_id.max' => 'Trường users_admin_id không được lớn hơn :max',
            'data.contract_code.required' => 'Trường contract_code không để trống',
            'data.contract_code.string' => 'Trường contract_code phải đúng định dạng',
            'data.contract_code.max' => 'Trường contract_code không được lớn hơn :max',
            'data.amount_payment.required' => 'Trường amount_payment không để trống',
            'data.amount_payment.numeric' => 'Trường amount_payment phải đúng định dạng',
            'data.amount_discount.required' => 'Trường amount_discount không để trống',
            'data.amount_discount.numeric' => 'Trường amount_discount phải đúng định dạng',
            'data.amount_fee_out_of_date.required' => 'Trường amount_fee_out_of_date không để trống',
            'data.amount_fee_out_of_date.numeric' => 'Trường amount_fee_out_of_date phải đúng định dạng',
            'data.amount_fee_cycle_out_of_date.required' => 'Trường amount_fee_cycle_out_of_date không để trống',
            'data.amount_fee_cycle_out_of_date.numeric' => 'Trường amount_fee_cycle_out_of_date phải đúng định dạng',
        ];

    }
}