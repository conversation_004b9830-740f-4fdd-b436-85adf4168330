<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;

trait PlanPhanLoaiLich
{
  public function isLichThuChinh(): bool
  {
    return $this->type == CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH;
  }

  public function isLichThuPhu(): bool
  {
    return $this->type == CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU;
  }

  public function isLichThuGoc(): bool
  {
    return $this->isfee == CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC;
  }

  public function isLichThuGocChinh(): bool
  {
    return $this->isLichThuGoc() && $this->isLichThuChinh();
  }

  public function isLichThuGocVet(): bool
  {
    return $this->isLichThuGoc() && $this->isLichThuPhu();
  }

  public function isLichThuPhi(): bool
  {
    return $this->isfee == CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI;
  }

  public function isLichThuPhiChinh(): bool
  {
    return $this->isLichThuPhi() && $this->isLichThuChinh();
  }

  public function isLichThuPhiVet(): bool
  {
    return $this->isLichThuPhi() && $this->isLichThuPhu();
  }

  public function isLichThuPhiChamKy(): bool
  {
    $otherData = $this->getPlanOtherData();
    $otherDataItem = collect($otherData)->where('type', 'OTHER')->first();
		return $this->isLichThuPhi() && $otherDataItem['data']['information_code'] == CollectDebtEnum::INFOCODE_SINH_PHI_CHAM_KY;
  }

  public function isLichThuPhiQuaHan(): bool
  {
    $otherData = $this->getPlanOtherData();
    $otherDataItem = collect($otherData)->where('type', 'OTHER')->first();
		return $this->isLichThuPhi() && $otherDataItem['data']['information_code'] == CollectDebtEnum::INFOCODE_SINH_PHI_QUA_HAN;
  }

  public function isLichTatToan(): bool {
    return $this->is_settlement == 1;
  }

  public function isKhongPhaiLichTatToan(): bool {
    return !$this->isLichTatToan();
  }

  public function isLichThuDuocApDungGiamPhi(): bool {
    $otherData = $this->getPlanOtherData();
    $otherDataItem = collect($otherData)->where('type', 'REDUCE_FEE')->first();
    return !empty($otherDataItem['data']);
  }

  public function getSoTienDuocPhamPhi(): float {
    $otherData = $this->getPlanOtherData();
    $otherDataItem = collect($otherData)->where('type', 'REDUCE_FEE')->first();
    return $otherDataItem['data']['total_reduce_fee'];
  }

  public function isLichTuongLai(): bool {
    return now()->lt($this->rundate_as_date) && !now()->isSameDay($this->rundate_as_date);
  }

  public function isLichHienTaiHoacQuaKhu(): bool {
    return !$this->isLichTuongLai();
  }
} // End class