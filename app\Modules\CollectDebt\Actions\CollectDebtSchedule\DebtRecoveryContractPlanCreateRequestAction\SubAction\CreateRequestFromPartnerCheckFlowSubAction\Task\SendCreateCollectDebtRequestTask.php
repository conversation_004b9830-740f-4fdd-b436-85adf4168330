<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromPartnerCheckFlowSubAction\Task;

use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Utils\CommonVar;
use App\Lib\TelegramAlert;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class SendCreateCollectDebtRequestTask
{
  public function run(Collection $collectDebtSchdules, CollectDebtPartner $collectDebtPartner, float $totalAmountReceiver)
  {
    $firstPlan = $collectDebtSchdules->first();

    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $firstPlan->contract_code)->first();

    // Tổng tiền yêu cầu thu của các lịch thu
    $totalAmoutDebitFromSchedules = $collectDebtSchdules->sum('request_amount_debit'); 

    // Số tiền mang đi thanh toán
    $totalAmountTakeOutToPay = min($totalAmountReceiver, $totalAmoutDebitFromSchedules);

    $params = [
      'module' => CommonVar::API_REQUEST_DEBT_MODULE,
      'path' => '/DebtRecoveryRequestCreate',
      'params' => [
        'type'                        => CollectDebtEnum::REQUEST_TYPE_THANH_TOAN_TRICH_NO,
        'profile_id'                  => $firstPlan->profile_id,
        'contract_code'               => $firstPlan->contract_code,
        'plan_ids'                    => $collectDebtSchdules->implode('id', ','), 
        'payment_method_code'         => $collectDebtPartner->payment_method_code,
        'payment_channel_code'        => $collectDebtPartner->payment_channel_code,
        'payment_account_id'          => $collectDebtPartner->payment_account_id,
        'payment_account_holder_name' => 'N/A',
        'payment_account_bank_code'   => 'N/A',
        'payment_account_bank_branch' => 'N/A',
        'partner_request_id'          => '', 
        'partner_transaction_id'      => $collectDebtPartner->partner_transaction_id,
        'time_begin'                  => today()->copy()->setHours(5)->timestamp,
        'time_expired'                => today()->copy()->setTimeFromTimeString(Helper::getCutOffTime())->timestamp,
        'is_payment'                  => CollectDebtEnum::REQUEST_IS_PAYMENT_KHONG_GUI_DOI_TAC_TT,
        'version'                     => CollectDebtEnum::REQUEST_VERSION_4,
        'status'                      => CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
        'status_payment'              => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA,
        'status_recored'              => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO,
        'currency'                    => $collectDebtSummary->getCurrency(),
        'amount_request'              => $totalAmountTakeOutToPay,
        'amount_payment'              => $collectDebtPartner->amount_payment,
        'amount_receiver'             => $collectDebtPartner->amount_receiver,
        'fee'                         => $collectDebtPartner->fee,
        'other_data'                  => $collectDebtPartner->other_data ?? '{}',
        'plan_data'                   => Helper::getPlanCompact($collectDebtSchdules), 
        'description'                 => $collectDebtPartner->description ?: 'Tạo yêu cầu thu từ luồng PartnerCheck',
        'time_created'                => time(),
        'time_approved'               => time(),
        'time_completed'              => time(),
        'created_by'                  => Helper::getCronJobUser(),
        'approved_by'                 => Helper::getCronJobUser(),
        'completed_by'                => Helper::getCronJobUser(),
      ],
      'method' => 'POST'
    ];
    
    $sendDebtRecoveryRequestCreditResult = (new ApiCall())->callFunctionApi($params, true);
    if ( !empty($sendDebtRecoveryRequestCreditResult['data']['id']) ) {
      $message = parseErr([
        'Flow' => 'Luồng bị động',
        'LogID' => request('api_request_id'),
        'Message' => 'Tạo yêu cầu từ luồng bị động thành công',
        'RequestID' => $sendDebtRecoveryRequestCreditResult['data']['id']
      ]);

      TelegramAlert::sendCreateRequest($message);
      
      $collectDebtPartner->partner_request_id = $sendDebtRecoveryRequestCreditResult['data']['id'];
      $collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
      $collectDebtPartner->amount_payment = $totalAmountTakeOutToPay;

      // Cập nhật lại vào lịch để biết đã tạo yêu cầu qua kênh nào
      app(DanhDauLichThuLaDaTaoYeuCauQuaKenhNaoST::class)->run($collectDebtSchdules, $collectDebtPartner->payment_method_code);
    }else {
      $message = parseErr([
        'Luồng' => 'Luồng bị động',
        'LogID' => request('api_request_id'),
        'Message' => 'Tạo yêu cầu THẤT BẠI từ luồng PartnerCheck',
        'Error' => json_encode($sendDebtRecoveryRequestCreditResult)
      ]);

      TelegramAlert::sendCreateRequest($message);
      $collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_XU_LY_LOI;
    }

    $collectDebtPartner->save();
    return $collectDebtPartner->refresh()->load('collectDebtRequest');
  }
} // End class