<?php

namespace App\Utils;

use Illuminate\Support\Facades\Log;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
class ApiAutomation {

    protected $_apiUrl;
    protected $_response;
    protected $_apiResult;
    protected $_inputs;
    protected $_checksum;
    protected $_params;
    protected $_keyAutomation;
    protected $_token;
    protected $_token_Time;
    protected $Log;


    public function __construct() {
        $this->_apiUrl = env('URL_AUTOMATION', 'https://dev-automation.nextlend.vn/');
        $this->_apiResult = false;
        $this->_inputs = array();
        $this->_token_Time = time();
        $this->_keyAutomation = env('KEY_AUTOMATION', '342423423423423423423423');
        $this->_token = md5($this->_keyAutomation.$this->_token_Time);

        $log = new Logger('api');
        $this->Log = $log->pushHandler(new StreamHandler(storage_path().'/api/core/request/'.date('Y-m-d').'-automation.log', Logger::INFO));

    }

    protected function _checkDataResponse() {
        $this->_response = $this->_apiResult;
        if (!empty($this->_response['result_code']) && $this->_response['result_code'] == 200) {
            return $this->_response;
        }

    }

    public function isJSON($string){
        return is_string($string) && is_array(json_decode($string, true))
        && (json_last_error() == JSON_ERROR_NONE) ? true : false;
    }
    public function  getProductByBusinessArea($business_areas_id ,$merchant_id = null, $contract_code = null ,$user_sale_id,$user_sale_name,$user_sale_phone,$user_sale_email){
        $url = $this->_apiUrl.'/api/nextpay/get-product-by-id/'.$business_areas_id;
        if($merchant_id){
            $url = $url.'/'.$merchant_id;
        }
        if($contract_code){
            $url = $url.'/'.$contract_code;
        }
        $this->Log->info('=========================');
        $this->Log->info('[API] url  '. $url);
        $this->Log->info('[API] user_sale_id -- '. $user_sale_id);
        $this->Log->info('[API] user_sale_name -- '. $user_sale_name);

        $this->Log->info('[API] user_sale_phone -- '. $user_sale_phone);
        $this->Log->info('[API] user_sale_email -- '. $user_sale_email);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            //CURLOPT_POSTFIELDS =>$json,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'token: '.$this->_token ,
                'time: '.$this->_token_Time ,
                'userid: '.$user_sale_id,
                'username: '.$user_sale_name,
                'phone: '.$user_sale_phone,
                'email: '.$user_sale_email
            ),
        ));
        $resultCode = curl_exec($curl);

        $this->Log->info('[API] input '. json_encode($resultCode));
        $this->Log->info('=========================');

        if ($this->isJSON($resultCode)){
            $this->_apiResult = json_decode($resultCode, true);
            //Kiểm tra dữ liệu
            $this->_checkDataResponse();
        }
        return $this->_response;
    }


    public function updateProfile($jsonData){
//        print_r($jsonData);
//        print_r($this->_apiUrl.'/api/nextpay/create-document-product');die;
        $this->Log->info('=========================');
        $url =$this->_apiUrl.'/api/nextpay/create-document-product';
        $this->Log->info('[API] url  '. $url);
        $this->Log->info('[API] input '. json_encode($jsonData));
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'token: '.$this->_token ,'time: '.$this->_token_Time
            ),
            CURLOPT_POSTFIELDS => $jsonData,
        ));


        $resultCode = curl_exec($curl);
        //print_r($resultCode);die;
        $this->Log->info('[API] [API] resultCode '. json_encode($resultCode));
        $this->Log->info('=========================');

        if ($this->isJSON($resultCode)){
            $this->_apiResult = json_decode($resultCode, true);
            //Kiểm tra dữ liệu
            $this->_checkDataResponse();
        }
        return $this->_response;
    }

    public function uploadAllDataProfile($jsonData){
//        print_r($jsonData);
//        print_r($this->_apiUrl.'/api/nextpay/create-document-product');die;
        $this->Log->info('=========================');
        $url =  $this->_apiUrl.'/api/nextpay/update-all-document-product';
        $this->Log->info('[API] url  '. $url);
        $this->Log->info('[API] input '. json_encode($jsonData));
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'token: '.$this->_token ,'time: '.$this->_token_Time
            ),
            CURLOPT_POSTFIELDS => $jsonData,
        ));


        $resultCode = curl_exec($curl);
        $this->Log->info('[API] [API] resultCode '. json_encode($resultCode));
        $this->Log->info('=========================');

        if ($this->isJSON($resultCode)){
            $this->_apiResult = json_decode($resultCode, true);
            //Kiểm tra dữ liệu
            $this->_checkDataResponse();
        }
        return $this->_response;
    }



    public function uploadRequestDocument($jsonData){
//        print_r($jsonData);
//        print_r($this->_apiUrl.'/api/nextpay/create-document-product');die;
        $this->Log->info('=========================');
        $url =  $this->_apiUrl.'/api/nextpay/update-request-document';
        $this->Log->info('[API] url  '. $url);
        $this->Log->info('[API] input '. json_encode($jsonData));
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'token: '.$this->_token ,'time: '.$this->_token_Time
            ),
            CURLOPT_POSTFIELDS => $jsonData,
        ));


        $resultCode = curl_exec($curl);
        $this->Log->info('[API] resultCode '. json_encode($resultCode));
        $this->Log->info('=========================');

        if ($this->isJSON($resultCode)){
            $this->_apiResult = json_decode($resultCode, true);
            //Kiểm tra dữ liệu
            $this->_checkDataResponse();
        }
        return $this->_response;
    }

    public function uploadContractCode($jsonData){
//        print_r($this->_apiUrl.'/api/nextpay/create-document-product');die;
        $this->Log->info('=========================');
        $url =  $this->_apiUrl.'/api/nextpay/update-request-document';
        $this->Log->info('[API] url  '. $url);

        $this->Log->info('[API] input '. json_encode($jsonData));
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'token: '.$this->_token ,'time: '.$this->_token_Time
            ),
            CURLOPT_POSTFIELDS => $jsonData,
        ));


        $resultCode = curl_exec($curl);
        $this->Log->info('[API] resultCode '. json_encode($resultCode));
        $this->Log->info('=========================');

        if ($this->isJSON($resultCode)){
            $this->_apiResult = json_decode($resultCode, true);
            //Kiểm tra dữ liệu
            $this->_checkDataResponse();
        }

        return $this->_response;
    }








}
