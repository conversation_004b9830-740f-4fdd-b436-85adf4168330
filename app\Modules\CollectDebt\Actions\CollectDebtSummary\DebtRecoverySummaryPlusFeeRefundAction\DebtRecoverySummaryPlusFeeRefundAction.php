<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPlusFeeRefundAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Request;
use Exception;
use App\Modules\CollectDebt\Model\CollectDebtRecoveryLog;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPlusFeeRefundRequest;

class DebtRecoverySummaryPlusFeeRefundAction
{
	public function run(DebtRecoverySummaryPlusFeeRefundRequest $request)
	{
		$orderCode = trim($request->json('data.order_code', ''));
		
		$collectDebtRecoveryLog = CollectDebtRecoveryLog::query()
																										->where('order_code', $orderCode)
																										->first();
		
		if (!$collectDebtRecoveryLog) {
			mylog([
				'[EMPTY]' => 'khong co thong tin yeu cau hoan phi',
				'OrderCode' => $orderCode,
			]);

			throw new Exception('khong co thong tin yeu cau hoan phi');
		};

		mylog([
			'YC Hoan Phi' => $collectDebtRecoveryLog->only(['id', 'service_code', 'order_code', 'status'])
		]);

		throw_if($collectDebtRecoveryLog->isTrangThaiCuoi(), new Exception('Yeu cau hoan phi da ve trang thai cuoi'));

		$collectDebtRecoveryLog->status = CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG;
		$collectDebtRecoveryLog->time_updated = now()->timestamp;
		$collectDebtRecoveryLog->updated_by = Helper::getCronJobUser();
		$r = $collectDebtRecoveryLog->save();

		mylog(['ket qua cap nhat ban ghi log la: ' => $r]);


		$collectDebtSummary = CollectDebtSummary::query()
																						->where('contract_code', $request->json('data.id'))
																						->sharedLock()
																						->first();
		
		mylog([
			'ID summary'                => $collectDebtSummary->reference_id,
			'ContractCode tren summary' => $collectDebtSummary->contract_code,
			'reference_id'              => $collectDebtRecoveryLog->reference_id,
			'amount_refunding'          => $collectDebtRecoveryLog->amount_refunding,
			'total_amount_refund'       => $collectDebtRecoveryLog->total_amount_refund,
		]);

		if (!$collectDebtSummary || $collectDebtSummary->id != $collectDebtRecoveryLog->reference_id) {
			throw new Exception('Loan thong tin hop dong. Tu choi xu ly');
		}

		$tongTienDaHoanThanhCong = $request->json('data.amount_refund');

		if ($tongTienDaHoanThanhCong > $collectDebtRecoveryLog->getPhiHoanTrenYeuCau()) {
			mylog(['[LOI LOGIC NGHIEM TRONG]' => 'So tien da hoan CAO HON so tien yc hoan']);
			throw new Exception('So tien da hoan CAO HON so tien yc hoan');
		}

		if ($collectDebtSummary->amount_refunding >= $collectDebtSummary->total_amount_refund) {
			mylog(['thoa man dieu kien' => 'ok']);
			$otherData = $collectDebtSummary->getSummaryOtherData();
			$otherData[] = [
				'type' => 'REFUND',
				'data' => $request->json('data'),
				'time_modified' => time(),
				'note' => $request->json('data.description', 'Hoan phi cho MC')
			];

			$collectDebtSummary->total_amount_refund += $tongTienDaHoanThanhCong;
			$collectDebtSummary->amount_refunding -= $tongTienDaHoanThanhCong;
			$collectDebtSummary->time_updated = now()->timestamp;
			$collectDebtSummary->other_data = json_encode($otherData);
			$collectDebtSummary->is_request_sync = CollectDebtEnum::SUMMARY_CO_DONG_BO;
			$result = $collectDebtSummary->save();

			if (!$result) {
				mylog(['[LOI CAP NHAT SUMMARY]' => $result]);
				throw new Exception('Loi khong cap nhat duoc so tien da hoan');
			}
		} else {
			mylog(['[LOI LOGIC]' => 'check lai amount refunding, total_emount_refund']);
			throw new Exception('check lai amount refunding, total_emount_refund');
		}

		return $collectDebtRecoveryLog;
	}
} // End class