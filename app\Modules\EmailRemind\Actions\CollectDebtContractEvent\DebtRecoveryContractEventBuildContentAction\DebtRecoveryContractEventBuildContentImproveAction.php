<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueC1SubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueC2SubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueC3SubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueCycleSubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\Task\DigitalNotiQuaHanTask;

/**
 * Với event mail: Cronjob chỉ  thực hiện build nội dung mail (chậm kỳ, quá hạn 1,2,3)
 * Với event đồng bộ: Cronjob sẽ update status từ mới tạo -> đã tạo nội dung
 */
class DebtRecoveryContractEventBuildContentImproveAction
{
	private array $__returnEventData = [];

	private array $__exceptEventIds = [];

	public function initBuildContent()
	{
		// Đồng bộ
		$this->__buildContentEventDongBo();

		for ($i = 1; $i <= 40; $i++) {
			mylog(['----------------------------' => sprintf('%s ----------------------------', $i)]);

			try {
				$result = $this->run();
				if ($result == 'EMPTY') {
					mylog(['[EMPTY]' => 'Khong co event nao can build content']);
					$this->__returnEventData[] = "EMPTY";
					break;
				}

				if ($result && optional($result)->id) {
					$this->__returnEventData[] = optional($result)->id;
				}

			} catch (\Throwable $th) {
				mylog(['Loi build content' => Helper::traceError($th)]);
				// throw $th;
				continue;
			}
		}

		return $this->__returnEventData;
	}

	public function run()
	{
		$collectDebtContractEvent = CollectDebtContractEvent::query()
			->where('status', CollectDebtEnum::EVENT_STT_MOI_TAO)
			->where('service_care_code', 'MAIL')
			->where('category_care_code', '!=', 'NOTIFY_CONTRACT_DUE');

		if (!empty($this->__exceptEventIds)) {
			$collectDebtContractEvent = $collectDebtContractEvent->whereNotIn('id', $this->__exceptEventIds);
		}
		
		$collectDebtContractEvent = $collectDebtContractEvent->first();

		if (!$collectDebtContractEvent) {
			return 'EMPTY';
		}

		// Thêm vào danh sách ngoại trừ
		$this->__exceptEventIds[] = $collectDebtContractEvent->id;

		mylog([
			'EventId dang xu ly' => $collectDebtContractEvent->id,
			'ContractCode' => $collectDebtContractEvent->contract_code
		]);

		// Update lên thành đang xử lý
		$updateThanhDangXuLy = CollectDebtContractEvent::query()
			->where('id', $collectDebtContractEvent->id)
			->where('status', CollectDebtEnum::EVENT_STT_MOI_TAO)
			->update([
				'status' => CollectDebtEnum::EVENT_STT_DANG_TAO_NOI_DUNG,
				'time_updated' => now()->timestamp
			]);

		if (!$updateThanhDangXuLy) {
			mylog([
				'Loi khong the update len trang thai dang tao noi dung' => 'ok',
				'EventId' => $collectDebtContractEvent->id,
				'ContractCode' => $collectDebtContractEvent->contract_code
			]);

			throw new Exception('Loi khong the update len trang thai dang tao noi dung');
		}

		// Bắt đầu transaction
		DB::beginTransaction();
		try {
			$mailContent = $this->__buildMailContent($collectDebtContractEvent);

			if (empty($mailContent)) {
				mylog(['Khong co noi dung mail content' => $mailContent]);
				throw new Exception('Khong co noi dung mail content');
			}

			$updateVeDaXuLy = CollectDebtContractEvent::query()
				->where('id', $collectDebtContractEvent->id)
				->update([
					'content' => $mailContent,
					'status' => CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL,
					'description' => 'Tao noi dung mail thanh cong',
					'time_updated' => now()->timestamp,
					'time_created_content' => now()->timestamp,
				]);

			if (!$updateVeDaXuLy) {
				mylog([
					'Event khong the cap nhat ve trang thai DA TAO NOI DUNG' => 'ok',
					'EventId' => $collectDebtContractEvent->id
				]);
				throw new Exception('Event khong the cap nhat ve trang thai DA TAO NOI DUNG');
			}

			DB::commit();

			return $collectDebtContractEvent;
		} catch (\Throwable $th) {
			mylog(['Loi logic build mail event' => Helper::traceError($th)]);
			DB::rollBack();

			// Update về mới tạo
			$updateVeMoiTao = CollectDebtContractEvent::query()
				->where('id', $collectDebtContractEvent->id)
				->where('status', CollectDebtEnum::EVENT_STT_DANG_TAO_NOI_DUNG)
				->update([
					'status' => CollectDebtEnum::EVENT_STT_MOI_TAO
				]);

			if (!$updateVeMoiTao) {
				mylog(['Loi update ve moi tao' => $updateVeMoiTao]);
			}

			throw new Exception(Helper::traceError($th));
		}
	}


	protected function __buildMailContent(CollectDebtContractEvent $collectDebtContractEvent)
	{
		$mailContent = '';

		switch ($collectDebtContractEvent->category_care_code) {
			case 'CONTRACT_OVERDUE_CYCLE': //Mail chậm kỳ
				$mailContent = app(EmailRemindOverdueCycleSubAction::class)->run($collectDebtContractEvent);
				break;

			case 'NOTIFY_CONTRACT_DUE1': //Mail quá hạn c1
				$mailContent = app(EmailRemindOverdueC1SubAction::class)->run($collectDebtContractEvent);
				break;

			case 'NOTIFY_CONTRACT_DUE2': //Mail quá hạn c2
				$mailContent = app(EmailRemindOverdueC2SubAction::class)->run($collectDebtContractEvent);
				break;

			case 'NOTIFY_CONTRACT_DUE3': //Mail quá hạn c3
				$mailContent = app(EmailRemindOverdueC3SubAction::class)->run($collectDebtContractEvent);
				break;

			default:
				throw new Exception('Khong thuoc case nao');
				$mailContent = '';
				break;
		}

		
		return $mailContent;
	}


	private function __buildContentEventDongBo()
	{
		$updated = CollectDebtContractEvent::query()
			->where('status', CollectDebtEnum::EVENT_STT_MOI_TAO)
			->where('service_care_code', 'SYNC')
			->where('category_care_code', 'CONTRACT')
			->update([
				'status' => CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL,
				'time_updated' => now()->timestamp,
				'time_created_content' => now()->timestamp,
			]);

		mylog(['So luong ban ghi dong bo da tao content' => $updated]);

		$this->__returnEventData[] = ['So luong ban ghi dong bo da tao content' => $updated];
		return $updated;
	}
} // End class
