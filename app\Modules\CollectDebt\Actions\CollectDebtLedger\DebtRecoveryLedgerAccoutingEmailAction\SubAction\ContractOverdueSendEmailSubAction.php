<?php
namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccoutingEmailAction\SubAction;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ContractOverdueSendEmailSubAction extends Mailable
{
    use Queueable, SerializesModels;

    public $data;
    public $email;
    /**
     * Create a new data instance.
     *
     * @return void
     */

    public function __construct($data)
    {
        $this->data = $data;
    }
 
    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('Notify.Email.contract_overdue_email')
                    ->with('data', $this->data);
        }
}