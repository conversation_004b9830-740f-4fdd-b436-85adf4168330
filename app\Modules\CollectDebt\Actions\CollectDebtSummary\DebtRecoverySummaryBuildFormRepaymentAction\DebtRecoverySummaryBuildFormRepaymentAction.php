<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryBuildFormRepaymentAction;

use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryBuildFormRepaymentAction\SubAction\GetHopDongMuonThanhToanTiepSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryBuildFormRepaymentAction\SubAction\KiemTraTinhHopLeCuaCacHopDongSubAction;
use DB;
use Exception;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryBuildFormRepaymentRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPlusAmountRepaymentRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryGetAmountCanRefundAction\DebtRecoverySummaryGetAmountCanRefundAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use Carbon\Carbon;

class DebtRecoverySummaryBuildFormRepaymentAction
{
	private array $__listHopDongRepayment = [];

	public function run(DebtRecoverySummaryBuildFormRepaymentRequest $request)
	{
		$contractCode = trim($request->json('data.contract_code'));

		$collectDebtSummary = CollectDebtSummary::query()
																						->where('contract_code', $contractCode)
																						->lock('FOR UPDATE SKIP LOCKED')
																						->first();

		throw_if(!$collectDebtSummary, new Exception("HĐ `$contractCode` không tồn tại trong hệ thống"));

		// Đây là số tiền thu thừa có thể hoàn hoặc thanh toán tiếp
		$result = app(DebtRecoverySummaryGetAmountCanRefundAction::class)->run(['contract_code' => $collectDebtSummary->contract_code]);
		$soTienDaThuThua = $result['total_amount_can_refund'] ?? 0;

		throw_if(empty($soTienDaThuThua), new Exception('HĐ của bạn không đủ số tiền thừa để chuyển ngân thanh toán tiếp'));

		$collectDebtShare = CollectDebtShare::query()->where('contract_code', $contractCode)->first();
		
		$loaiTruHopDongHienTai = $collectDebtSummary->isHopDongDaTatToan() ? [$collectDebtSummary->contract_code] : [];
		mylog(['Loai tru ho ' => $loaiTruHopDongHienTai]);

		$profileId = $collectDebtSummary->getProfileId();

		$listHopDongCuaToi = CollectDebtGuide::query()
																		     ->where('profile_id', $profileId)
																				 ->whereHas('collectDebtSummary', function ($q) {
																					return $q->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
																				 })
																				 ->get();


		mylog(['List HD V1 dang thu' => $listHopDongCuaToi]);
		
		if ($listHopDongCuaToi->isNotEmpty()) {
			foreach ($listHopDongCuaToi as $collectDebtGuide) {
				$collectDebtSummary = $collectDebtGuide->collectDebtSummary;

				$duNoCanThanhToan =  $collectDebtSummary->contract_amount - $collectDebtSummary->total_amount_paid;

				// $duNoCanThanhToan
				if ($duNoCanThanhToan > 0) { // sau chi duoc de > 0
					$this->__listHopDongRepayment[] = [
						'contract_code' => $collectDebtSummary->contract_code,
						'start_date' => $collectDebtGuide->time_start_as_date->format('d-m-Y'),
						'end_date' => $collectDebtGuide->time_end_as_date->format('d-m-Y'),
						'total_debt_amount' => $duNoCanThanhToan,
						'type'	=> $collectDebtGuide->contract_type, // loai hd
						'cycle'	=> $collectDebtGuide->contract_cycle, // so ngay ung
						'intervals'	=> $collectDebtGuide->contract_intervals, // chu ky trich
						'amount_paid'	=> $collectDebtSummary->total_amount_paid + $collectDebtSummary->total_fee_paid, // so tien da thu
						'amount_refund'	=> $collectDebtSummary->total_amount_refund, // so tien da hoan
						'status' => $collectDebtSummary->status_contract
					];
				}
			}
		}
		
		
		$profileData = $collectDebtShare->getProfileDataAsArray();

		return [
			'profile_data' => [
				'profile_id' => $profileData['id'],
				'merchant_id' => $profileData['merchant']['id'],
				'merchant_email' => $profileData['merchant']['email'],
				'merchant_mobile' => $profileData['merchant']['mobile'],
				'merchant_fullname' => $profileData['merchant']['fullname'],
			],

			'current_contract' => [
				'contract_code' => $contractCode,
				'total_amount_excess' => $soTienDaThuThua,
				'contract_amount' => $collectDebtSummary->contract_amount,
				'status' => $collectDebtSummary->isHopDongDaTatToan() ? 'Đã tất toán' : 'Chưa tất toán'
			],
			
			'list_contract_can_repayment' => $this->__listHopDongRepayment
		];
	}
} // End class