<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryMailOverCycleAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareGetContractCodeAction\DebtRecoveryShareGetContractCodeAction;

class DebtRecoverySummaryMailOverCycleAction
{
	private array $__summaryExceptIds = [];

	public function initGuiMailChamKy($request)
	{
		mylog(['Init tao event mail CHAM KY' => 'ok']);

		$returnData = [];

		$mode = $request->get('mode', 'ALL');
		switch ($mode) {
			case 'CHAN': 
				$whereRaw = 'id % 2 = 0';
				break;

			case 'LE': 
				$whereRaw = 'id % 2 != 0';
				break;

			default: 
				$whereRaw = '1';
				break;
		}

		for ($i = 0; $i <= 30; $i++) {
			mylog(['--------------------------------------' => sprintf('%s ------------------------------', $i)]);

			try {
				$result = $this->run($whereRaw);
				if ($result == 'EMPTY') {
					$returnData[] = 'khong co thong tin HD cham ky can gui mail';
					break;
				}

				if ($request && optional($result)->id) {
					$returnData[] = $result->only(['id', 'contract_code']);
				}
			} catch (\Throwable $th) {
				mylog(['[LOI XY LY]' => Helper::traceError($th)]);
				@TelegramAlert::sendEmails(Helper::traceError($th));
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $returnData;
	}

	public function run($whereRaw='1')
	{
		mylog(['Loai truy van' => $whereRaw]);
		
		$collectDebtSummary = CollectDebtSummary::query()
			->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
			->where('is_send_mail_slow_cycle', CollectDebtEnum::SUMMARY_CO_GUI_MAIL_CHAM_KY)
			->whereRaw($whereRaw);

		if (!empty($this->__summaryExceptIds)) {
			$collectDebtSummary = $collectDebtSummary->whereNotIn('id', $this->__summaryExceptIds);
		}
		
		$collectDebtSummary = $collectDebtSummary->first();
		
		if (!$collectDebtSummary) {
			mylog(['[EMPTY]' => 'khong co HD nao can gui mail ck']);
			return 'EMPTY';
		}

		mylog(['Ban ghi summary' => $collectDebtSummary]);

		$updateLenDangXuLy = CollectDebtSummary::query()
			->where('id', $collectDebtSummary->id)
			->where('is_send_mail_slow_cycle', CollectDebtEnum::SUMMARY_CO_GUI_MAIL_CHAM_KY)
			->update([
				'is_send_mail_slow_cycle' => CollectDebtEnum::SUMMARY_DANG_GUI_MAIL_CHAM_KY
			]);

		if (!$updateLenDangXuLy) {
			mylog(['[LOI]' => 'Khong the update trang thai xu ly mail cham ky thanh dang xu ly']);
			throw new Exception('Khong the update trang thai xu ly mail cham ky thanh dang xu ly');
		}

		$this->__summaryExceptIds[] = $collectDebtSummary->id;

		DB::beginTransaction();
		try {
			$createdEvent = $this->__createEvent('CONTRACT_OVERDUE_CYCLE', $collectDebtSummary);

			if (!$createdEvent) {
				mylog(['[LOI]' => 'khong the tao event mail cham ky']);
				throw new Exception('Loi khong the tao mail cham ky');
			}

			$updatedVeKhongCanGuiMailNua = CollectDebtSummary::query()
				->where('id', $collectDebtSummary->id)
				->update([
					'is_send_mail_slow_cycle' => CollectDebtEnum::SUMMARY_KHONG_GUI_MAIL_CHAM_KY
				]);

			if (!$updatedVeKhongCanGuiMailNua) {
				mylog(['[LOI]' => 'khong the update ve trang thai khong gui mail']);
			}

			DB::commit();
			return $collectDebtSummary;
		} catch (\Throwable $th) {
			mylog(['[LOI]' => Helper::traceError($th)]);
			DB::rollBack();

			$updatedVePhaiXuLy = CollectDebtSummary::query()
				->where(['id' => $collectDebtSummary->id, 'is_send_mail_slow_cycle' => CollectDebtEnum::SUMMARY_DANG_GUI_MAIL_CHAM_KY])
				->update([
					'is_send_mail_slow_cycle' => CollectDebtEnum::SUMMARY_CO_GUI_MAIL_CHAM_KY
				]);

			if (!$updatedVePhaiXuLy) {
				mylog(['[LOI]' => 'loi cap nhat ve phai xu ly summary']);
			}

			throw $th;
		}
	}

	protected function __createEvent($code, $dataSummary)
	{
		$dataShare = $this->__getDataShare($dataSummary);

		$rq = new \App\Modules\EmailRemind\Request\CollectDebtContractEvent\DebtRecoveryContractEventCreateRequest();

		$inputs = [
			'category_care_code' => $code,
			'service_care_code' => 'MAIL',
			'data' => $this->__setDataEvent($dataShare),
			'description' => 'Tao event gui mail cham ky',
			'other_data' => [
				'summary' => $dataSummary->toArray(),
			],
			'time_start' => time(),
			'contract_code' => $dataSummary->contract_code,
		];

		mylog(['Thong tin input create event' => $inputs]);

		$rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
		$create = (new \App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateAction\DebtRecoveryContractEventCreateAction())->run($inputs);

		mylog(['Thong tin create event' => $create]);

		if (isset($create['id']) && $create['id']) {
			return true;
		}

		return false;
	}


	protected function __setDataEvent($value)
	{
		$data = [];

		if ($value) {
			if (isset($value['company_data']) && $value['company_data']) {
				$data['company'] = json_decode($value['company_data'], true);
			}
			if (isset($value['contract_data']) && $value['contract_data']) {
				$data['contract'] = json_decode($value['contract_data'], true);
			}
			if (isset($value['profile_data']) && $value['profile_data']) {
				$data['profile'] = json_decode($value['profile_data'], true);
			}
			if (isset($value['payment_guide']) && $value['payment_guide']) {
				$data['payment'] = json_decode($value['payment_guide'], true);
			}
			if (isset($value['list_fee']) && $value['list_fee']) {
				$data['list_fee'] = json_decode($value['list_fee'], true);
			}
		}
		return $data;
	}

	protected function __getDataShare($dataSummary)
	{
		$inputs = [
			'contract_code' => $dataSummary->contract_code
		];

		$dataShare = app(DebtRecoveryShareGetContractCodeAction::class)->run($inputs);

		mylog(['Thong tin data share' => $dataShare]);

		throw_if(!$dataShare, new Exception("DATA SHARE EMPTY"));

		return $dataShare;
	}
}
