<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task;

use App\Lib\TelegramAlert;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST\GetHopDongDangHachToanST;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST\GetHopDongDangDungJobTuDongST;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST\GetHopDongDangCoCongNoCanXuLyST;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST\GetHopDongDangCoYeuCauTrichTayST;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST\GetHopDongCoYeuCauChuaVeTrangThaiCuoiTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST\GetHopDongKhongPhuHopDeTaoYeuCauST;

class GetLichThuDungDeTaoYeuCauLuongChuDongTask
{  
  /**
   * 1. Vì luồng bị động đã xử lý rồi, nên luồng chủ động phải né nó ra, 
   * tránh select vào các lịch mà luồng bị động đã select vào
   * 
   * 2. Việc này cũng đảm bảo, nếu luồng bị động (VA, IB_OFF) mà có tiền <có công nợ> cầng mang  
   * đi thanh toán thì sẽ cố gắng không tạo yêu cầu thu qua MPOS nữa
   * 
   */
  public function run(array $exceptPlanProcessingIds=[]): Collection
  {
    $hopDongCoCongNoCanXuLy = app(GetHopDongKhongPhuHopDeTaoYeuCauST::class)->getHopDongDangCoCongNoCanXuLy();
	
    $pauseContractCodes = app(GetHopDongKhongPhuHopDeTaoYeuCauST::class)->run();
		$pauseContractCodes = array_merge($pauseContractCodes, $hopDongCoCongNoCanXuLy);
		$pauseContractCodes = array_values(array_unique($pauseContractCodes));

		if (request()->get('is_debug')) {
			dump($pauseContractCodes);
		}
    
		
    $plans = $this->checkByRunDate(
      $pauseContractCodes, 
      $exceptPlanProcessingIds, 
      false
    );

    if ($plans->isEmpty()) {
      // he thong, k co HD qua khu
      $plans = $this->checkByRunDate(
        $pauseContractCodes, 
        $exceptPlanProcessingIds, 
        true
      );
    }else {
      // co lich thu qus khu -> chi xu ly HD qua khu, moi lan 1 lich
      $plans = $plans->groupBy('contract_code');

      $firstContractPlanCollect = $plans->first();
      
      $groups = $firstContractPlanCollect->sortBy('rundate')->groupBy('rundate');

      $firstRundateClusterPlan = $groups->first();
      $firstRundateClusterPlan = $firstRundateClusterPlan->groupBy('contract_code');


      $firstRundateClusterPlan = app(PlanSortableCollectionByRule::class)->run($firstRundateClusterPlan);
      return $firstRundateClusterPlan;
    }

    $plans = $plans->groupBy('contract_code');
		
		$plansXuLyTungHopDong = $plans->take(2);
    $plans = app(PlanSortableCollectionByRule::class)->run($plansXuLyTungHopDong);
    return $plans;
  }

  public function checkByRunDate($pauseContractCodes=[], $exceptPlanProcessingIds, bool $isToday) {
    $plans = CollectDebtSchedule::query()
                                ->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
                                ->whereChuaTaoQuaKenhThuNao()
                                ->whereNotIn('contract_code', $pauseContractCodes);
    if (!$isToday) {
      $plans = $plans->where('rundate', '<', date('Ymd'));
    }else {
      $plans = $plans->where('rundate', '=', date('Ymd'));
    }


    if ( !empty($exceptPlanProcessingIds) ) {
      $plans = $plans->whereNotIn('id', $exceptPlanProcessingIds);
    }


    if (!$isToday) {
      $plans = $plans->orderByRaw('cycle_number ASC, time_start ASC, isfee ASC')->lock(' FOR UPDATE SKIP LOCKED ')->get();
    }else {
      $plans = $plans->lock(' FOR UPDATE SKIP LOCKED ')->get();
    }
    
    return $plans;
  }
} // End class