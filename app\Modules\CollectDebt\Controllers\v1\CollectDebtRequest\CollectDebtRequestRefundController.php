<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetRefundTransByContractAction\DebtRecoveryRequestGetRefundTransByContractAction;

class CollectDebtRequestRefundController extends Controller
{
	public function getRefundTransByContract(Request $request)
	{
		try {
			$refundTrans = app(DebtRecoveryRequestGetRefundTransByContractAction::class)->run($request->json('data.contract_code'));
			return $this->successResponse($refundTrans, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
