<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\FeeHandler;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class XuLySinhPhiQuaHanFH
{
  public function run(
    CollectDebtSchedule $lichDangHachToan, 
    CollectDebtSchedule $lichThuPhiQuaHanVuaTao, 
    float $phiQuaHan = 0
  ) {
    $otherData = [
      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          'information_code' => CollectDebtEnum::INFOCODE_SINH_PHI_QUA_HAN,
          'fee_config' => $lichDangHachToan->getCauHinhPhiTheoLoai(CollectDebtEnum::GUIDE_LOAI_PHI_QUA_HAN),
          'request_created_channel' => '',
        ],
        'note' => 'Sinh phí quá hạn'
      ]
    ];

    $lichThuPhiQuaHanVuaTao->other_data = json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_UNICODE);
    $lichThuPhiQuaHanVuaTao->save();
    return $lichThuPhiQuaHanVuaTao;
  }
} // End class