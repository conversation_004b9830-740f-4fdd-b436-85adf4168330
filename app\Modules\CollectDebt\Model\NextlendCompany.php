<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

/**
   * array:16 [▼
      "company_fullname" => "Công ty Cổ phần Công nghệ Vi Mô"
      "company_subname" => "Công ty Vi Mô"
      "company_code" => "VIMO"
      "company_phone_number_1" => "************"
      "company_phone_number_2" => ""
      "company_email_1" => "<EMAIL>"
      "company_email_2" => ""
      "company_url_1" => "https://mpos.vn"
      "company_url_2" => "https://nextlend.vn"
      "company_address_1" => "Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam"
      "company_address_2" => ""
      "company_product_code_1" => "NEXTLEND"
      "company_product_group_1" => "Next360"
      "company_bank_account_1" => "************"
      "company_bank_holder_1" => "Công ty Cổ phần Công nghệ Vi Mô"
      "company_bank_name_1" => "Ngân hàng TMCP Công Thương Việt Nam (Vietinbank) – Chi nhánh Hoàng Mai"
    ]
  *
  * @return void
  */

class NextlendCompany extends Model
{
  protected $guarded = [];
  
  public function getPhoneNumber(): string {
    if ( !empty($this->company_phone_number_1) ) {
      return $this->company_phone_number_1;
    }

    if ( !empty($this->company_phone_number_2) ) {
      return $this->company_phone_number_2;
    }

    return '';
  }

  public function getWebsite(): string {
    if ( !empty($this->company_url_1) ) {
      return $this->company_url_1;
    }

    if ( !empty($this->company_url_2) ) {
      return $this->company_url_2;
    }

    return '';
  }

  public function getAddress(): string {
    if ( !empty($this->company_address_1) ) {
      return $this->company_address_1;
    }

    if ( !empty($this->company_address_2) ) {
      return $this->company_address_2;
    }

    return '';
  }

  public function getEmail(): string {
    if ( !empty($this->company_email_1) ) {
      return $this->company_email_1;
    }

    if ( !empty($this->company_email_2) ) {
      return $this->company_email_2;
    }

    return '';
  }

  public function hasWebsiteLevel1(): bool {
    return !empty($this->company_url_1);
  }

  public function getWebsiteLevel1() {
    return $this->company_url_1;
  }

  public function hasWebsiteLevel2(): bool {
    return !empty($this->company_url_2);
  }

  public function getWebsiteLevel2() {
    return $this->company_url_2;
  }

  public function getCompanyCode(): string {
    if (!empty($this->company_code)) {
      return strtoupper($this->company_code);
    }

    return 'VIMO';
  }

  public function getNoiDungChuyenKhoanMaNapTien(): string {
    // Số này có thể thay đổi trong tương lai
    return '1119326';
  }

	public function getCuPhapFullName($merchant=[]): string {
		$mc = $merchant['fullname'] ?? $merchant['short_name'] ?? 'N/A';
		return Str::of($mc)->slug(' ')->upper()->trim()->__toString();
	}

  public function getSubName(): string {
    return $this->company_subname;
  }
} // End class
