<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\QrImageTask;

use App\Lib\NextlendCore;
use Exception;

class GetQrImageFromCodeTask
{
	public function run(string $qrCode = ''): string
	{
		$nextlendCore = app(NextlendCore::class)->callRequest([
			'qrcode_string' => $qrCode
		], 'GenerateQRCode_create', 'GET');

		$decryptData = $nextlendCore->decryptData();
		
		throw_if(
			empty($decryptData['qrcode_image']),
			new Exception('Không gen được ảnh QR Code')
		);

		return $decryptData['qrcode_image'];
	}
}
