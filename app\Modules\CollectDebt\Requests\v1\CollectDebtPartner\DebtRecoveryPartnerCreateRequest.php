<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtPartner;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class DebtRecoveryPartnerCreateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.payment_channel_code' => ['required', 'string', 'max:50'],
      'data.payment_method_code' => ['required', 'string', 'max:50'],
      'data.payment_account_id' => ['required', 'string', 'max:50'], // Nếu là MPOS thì phải là Mã HĐ
      'data.partner_request_id' => ['required_if:data.payment_method_code,MPOS', 'string', 'max:50'],
      'data.partner_transaction_id' => ['nullable', 'string', 'max:50'],
      'data.amount_receiver'        => ['required', 'numeric', 'min:0'],
      'data.fee'                    => ['nullable', 'numeric', 'min:0'],
      'data.request_exists'         => ['required', Rule::in([1, 2])],
      'data.response'               => ['nullable', 'json'],
      'data.description'            => ['nullable', 'string', 'max:255'],
      'data.created_by'             => ['nullable', 'string', 'max:255'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['time_created'] = time();
    $params['data']['status'] = CollectDebtEnum::PARTNER_STT_CHUA_XU_LY;

    if (empty($params['data']['created_by'])) {
      $params['data']['created_by'] = json_encode(['id' => 'cronjob', 'username' => 'cronjob', 'mobile' => 'cronjob']);
    }

    $this->merge($params);
  }
} // End class
