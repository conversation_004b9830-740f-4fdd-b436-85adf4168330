<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtLedger;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Model\CollectDebtLedger;

class DebtRecoveryLedgerSetStatusRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    $listStatus = CollectDebtLedger::listStatus();

    return [
      'data'                 => ['required', 'array'],
      'data.id'              => ['required', 'numeric', 'min:1'],
      'data.status'          => ['required', Rule::in(array_keys($listStatus))],
      'data.user_request_id' => ['required', 'string', 'max:255'],
      'data.description'     => ['nullable', 'string', 'max:255'],
    ];
  }

  protected function passedValidation()
  {
    $params = $this->all();
    $data = array_map('trim', $params['data']);
    $params['data'] = $data;
    
    $this->merge($params);
  }
} // End class
