<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class KiemTraPhiQuaHanyRunDateHienTaiST
{  
  /**
   * Kiểm tra xem rundate hiện tại đã phát sinh phí quá hạn hay chưa
   *
   * @param $lastFeeSchedule $lastFeeSchedule [explicite description]
   *
   * @return bool
   */
  public function run(CollectDebtSchedule $lichDangHachToan, $lastFeeSchedule = null): bool
  {
    if ($lastFeeSchedule) {
      $lastOtherData = $lastFeeSchedule->getPlanOtherData();
      
      $isThuPhiQuaHanHomNay = collect($lastOtherData['list_schedule_over_due'])->first(function ($item) use ($lichDangHachToan) {
        return $item['fee_schedule']['rundate'] == $lichDangHachToan->rundate;
      });

      if ($isThuPhiQuaHanHomNay) {
        return true;
      }

      return false;
    }

    return false;
  }
}
