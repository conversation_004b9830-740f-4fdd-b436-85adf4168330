<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleDenHanTatToanDungNgayAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\ReplaceNoiDungBySummarySubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\TaoEventMailSapToiHanTatToanSubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;

class DebtRecoveryHandleDenHanTatToanDungNgayAction
{
	private array $__hopDongDaXuLySapToiHanTatToan = [];

	public function run()
	{
		$info = json_encode([
			'Type' => 'MAIL_DEN_HAN_TAT_TOAN_DUNG_NGAY',
			'Time' => now()->toDateTimeString(),
			'LogId' => request('api_request_id')
		], JSON_PRETTY_PRINT);

		@TelegramAlert::sendEmails($info);
		

		$collectDebtGuides = CollectDebtGuide::query()
																				 ->whereBetween('contract_time_end', [
																					now()->startOfDay()->timestamp,
																					now()->endOfDay()->timestamp,
																				 ])
																				 ->whereDoesntHave('collectDebtEvents', function ($query) {
																					return $query->where('service_care_code', 'MAIL')
																											 ->whereNotIn('category_care_code', ['DEN_HAN_THANH_TOAN_HD_KY', 'DEN_HAN_THANH_TOAN_HD_NGAY'])
																											 ->whereBetween('time_created', [
																													now()->startOfDay()->timestamp,
																													now()->endOfDay()->timestamp,
																											 ]);
																				 })
																				 ->whereHas('collectDebtSummary', function ($query) {
																					 $query->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
																				 })
																				 ->get();
		
		if ($collectDebtGuides->isEmpty()) {
			throw new Exception('Khong co thong tin HD sap toi han tat toan');
			return;
		}

		$collectDebtGuides =  $collectDebtGuides->load(['collectDebtSummary', 'collectDebtShare']);
		

		$emailTemplateContentTheoNgay = app(GetTemplateMailSubAction::class)->run([
			// 'customer_category_care_code' => 'NOTIFY_CONTRACT_DUE',
			'customer_category_care_code' => 'DEN_HAN_THANH_TOAN_HD_NGAY',
			'customer_service_care_code' => 'MAIL',
		]);

		$emailTemplateContentTheoKy = app(GetTemplateMailSubAction::class)->run([
			// 'customer_category_care_code' => 'NOTIFY_CONTRACT_DUE_PERIOD',
			'customer_category_care_code' => 'DEN_HAN_THANH_TOAN_HD_KY',
			'customer_service_care_code' => 'MAIL',
		]);
		
		foreach ($collectDebtGuides as $collectDebtGuide) {
			if (@$collectDebtGuide->collectDebtSummary->status_contract == CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN) {
				mylog([
					'HopDongDaTatToan' => 'ok',
					'ContractCode' => $collectDebtGuide->contract_code
				]);

				continue;
			}

			try {
				mylog([
					'-----------------------' => sprintf('%s -----------------', $collectDebtGuide->contract_code),
					'Hop Dong Sap Toi Han' => $collectDebtGuide->contract_code,
				]);
				
				// Tao event doan nay
				$collectDebtEvent = app(TaoEventMailSapToiHanTatToanSubAction::class)->run(
					$collectDebtGuide->collectDebtShare,
					$collectDebtGuide->collectDebtSummary,
					$collectDebtGuide->isChiDanHopDongTrichNgay() ? 'DEN_HAN_THANH_TOAN_HD_NGAY' : 'DEN_HAN_THANH_TOAN_HD_KY'
				);
				
				// Replace bien vao trong noi dung email
				$emailContent = '';
	
				if ($collectDebtGuide->isChiDanHopDongTrichNgay()) {
					if (empty($emailTemplateContentTheoNgay['content'])) {
						mylog(['[EMPTY MAIL CONTENT NGAY]' => 'khong co thong tin mail template content']);
						continue;
					}

					$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
						$collectDebtEvent,
						$collectDebtGuide->collectDebtSummary,
						$emailTemplateContentTheoNgay
					);
				}
	
				if ($collectDebtGuide->isChiDanHopDongTrichKy()) {
					if (empty($emailTemplateContentTheoKy['content'])) {
						mylog(['[EMPTY MAIL CONTENT KY]' => 'khong co thong tin mail template content']);
						continue;
					}

					$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
						$collectDebtEvent,
						$collectDebtGuide->collectDebtSummary,
						$emailTemplateContentTheoKy
					);
				}
				
				if (empty($emailContent)) {
					mylog(['[LOI KHONG XAC DINH]' => 'EMPTY EMAIL CONTENT']);
					continue;
				}
	
				$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
				$collectDebtEvent->content = $emailContent;
				$savedResult = $collectDebtEvent->save();
	
				if (!$savedResult) {
					mylog(['[LOI SAVED EVENT]' => $collectDebtEvent->id]);
					continue;
				}
	
				$this->__hopDongDaXuLySapToiHanTatToan[] = $collectDebtEvent->only(['id', 'contract_code']);
			}catch(\Throwable $th) {
				mylog(['[LOI VONG LAP]' => Helper::traceError($th)]);
				continue;
			}
		};

		@TelegramAlert::sendEmails("Den Han Tat Toan Dung Ngay: " . json_encode($this->__hopDongDaXuLySapToiHanTatToan, JSON_UNESCAPED_UNICODE));
		return $this->__hopDongDaXuLySapToiHanTatToan;
	}
} // End class