<?php

namespace App\Modules\CollectDebtGateway\Service;

use App\Lib\ApiCall;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtRecoveryGatewayLogRepository;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtGatewayRepository;
use App\Utils\CommonVar;

class MposCollectDebtGatewayService
{
    protected CollectDebtGatewayRepository $mposCollectDebtGatewayRepo;
    protected CollectDebtRecoveryGatewayLogRepository $collectDebtLogRepo;
    protected $_prefix = '';

    public function __construct(CollectDebtGatewayRepository $mposCollectDebtGatewayRepo, CollectDebtRecoveryGatewayLogRepository $collectDebtLogRepo)
    {
        $this->mposCollectDebtGatewayRepo = $mposCollectDebtGatewayRepo;
        $this->collectDebtLogRepo = $collectDebtLogRepo;
        // $this->_prefix = env('NEXTLEND_SERVICE_CHANNEL_CODE') . '-';
    }

    /**
     * @param $request
     * @return array
     */
    public function sendDebt($request)
    {
        // dd($request);
        $descriptionExample = config('collect_debt_gateway_config.mpos_gateway_reponse_name');

        $nextlendRequestId = $this->_prefix . $request['partner_request_id'];

        $inputs = [
            'nextlend_request_id' => $nextlendRequestId,
            'merchantId' => $request['partner_merchant_id'], //Mã merchant bên MPOS
            'lendingRequestId' => $request['partner_request_id'], // Mã giao dịch của NL (order_no)
            'debitAmount' => $request['amount_payment'], // Số tiền yêu cầu
            'requestTime' => date('YmdHis'),
            'lendingId' => $request['contract_code'], // contract_code
            'loanOriginalAmount' => $request['loan_original_amount'], //So tien giai ngan
            'deductionPerDayAmount' => $request['deduction_per_day_amount'], //So tien trich no hang ngay
            'loanBalance' => $request['loan_balance'], //Dư nợ đầu kỳ = số tiền giải ngân - số tiền trích nợ thành công
            'partner_code' => 'MPOS',
            'partnerCode' => 'NEXT_LEND',
            '_prefix' => $this->_prefix,
        ];
        if (isset($request['time_end']) && $request['time_end']) {
            $inputs['recoverEndTime'] = date('YmdHis', $request['time_end']) . '999';
        }
        if (isset($request['time_begin']) && $request['time_begin']) {
            $inputs['recoverStartTime'] = date('YmdHis', $request['time_begin']) . '000';
        }

        $dataLog = [
            'request_id' => $request['request_id'],
            'users_admin_id' => $request['users_admin_id'],
            'partner_request_id' => $request['partner_request_id'],
            'amount_payment' => $request['amount_payment'],
            'other_data' => ['func' => 'sendDebt']
        ];

        $partnerTransactionId = '';

        $idLog = $this->__createCollectDebtLog($inputs, $dataLog);

        $result = $this->mposCollectDebtGatewayRepo->sendCollectDebtCommand($inputs);
        $errorCode = $result['errorCode'];
        if ($errorCode == config('collect_debt_gateway_config.mpos_gateway_response_success')) {
            $data = $result['data'];
            if (!isset($data['status']) || !$data['status']) {
                $errorCode = isset($data['error']) && isset($data['error']['code']) ? $data['error']['code'] : $result['errorCode'];
            }
            $partnerTransactionId = isset($data['data']) && isset($data['data']['mposDebtId']) ? $data['data']['mposDebtId'] : '';
        }


        $descriptionName = isset($descriptionExample[$errorCode]) ? $descriptionExample[$errorCode] : '';

        $dataLog['partner_transaction_id'] =  $partnerTransactionId;
        $dataLog['description'] =  $descriptionName;
        $this->__updateCollectDebtLog($idLog, $result, $dataLog);

        $dataResponse = [
            'error_code' => $errorCode,
            'description' => $descriptionName,
            'partner_transaction_id' => $partnerTransactionId,
        ];

        return $dataResponse;
    }

    /**
     * @param $request
     * @return array
     */
    public function cancelDebt($request)
    {
        $descriptionExample = config('collect_debt_gateway_config.mpos_gateway_reponse_name');

        $nextlendRequestId = $this->_prefix . $request['partner_request_id'];

        $inputs = [
            'nextlend_request_id' => $nextlendRequestId,
            "merchantId" => $request['partner_merchant_id'],
            "lendingRequestId" => $request['partner_request_id'],
            'partner_code' => 'MPOS',
            '_prefix' => $this->_prefix,
        ];

        $dataLog = [
            'request_id' => $request['request_id'],
            'users_admin_id' => $request['users_admin_id'],
            'other_data' => ['func' => 'cancelDebt']
        ];

        $idLog = $this->__createCollectDebtLog($inputs, $dataLog);

        $result = $this->mposCollectDebtGatewayRepo->cancelCollectDebtCommand($inputs);
        $errorCode = $result['errorCode'];
        $descriptionName = isset($descriptionExample[$errorCode]) ? $descriptionExample[$errorCode] : '';

        if ($errorCode == config('collect_debt_gateway_config.mpos_gateway_response_success')) {
            if (isset($request['write_note']) && $request['write_note'] === 1) {
                $params = [
                    'partner_merchant_id' => $request['contract_code'],
                    'users_admin_id' => $dataLog['users_admin_id'],
                    'amount_payment' => 0,
                    'amount_receiver' => 0,
                    'partner_request_id' => $request['partner_request_id'],
                    'partner_transaction_id' => $request['partner_transaction_id'],
                    'partner_request' => $result,
                ];
                $this->__createCollectDebtPartner($params);
            }
        }

        $dataLog['description'] = $descriptionName;
        $this->__updateCollectDebtLog($idLog, $result, $dataLog);

        $dataResponse = [
            'error_code' => $errorCode,
            'description' => $descriptionName,
        ];

        return $dataResponse;
    }

    /**
     * @param $request
     * @return array
     */
    public function checkDebt($request)
    {

        $descriptionExample = config('collect_debt_gateway_config.mpos_gateway_reponse_name');

        $nextlendRequestId = $this->_prefix . $request['partner_request_id'];

        $inputs = [
            'nextlend_request_id' => $nextlendRequestId,
            'partner_code' => 'MPOS',
            'merchantId' => $request['partner_merchant_id'],
            'lendingRequestId' => $request['partner_request_id'],
            'debitAmount' => $request['amount_payment'],
            'requestTime' => date('YmdHis'),
            'lendingId' => $request['contract_code'],
            'loanOriginalAmount' => $request['loan_original_amount'], //So tien giai ngan
            'deductionPerDayAmount' => $request['deduction_per_day_amount'], //So tien trich no hang ngay
            'loanBalance' => $request['loan_balance'], //Dư nợ đầu kỳ
            '_prefix' => $this->_prefix,
        ];

        $dataLog = [
            'request_id' => $request['request_id'],
            'users_admin_id' => $request['users_admin_id'],
            'partner_request_id' => $request['partner_request_id'],
            'amount_payment' => $request['amount_payment'],
            'other_data' => ['func' => 'checkDebt']
        ];

        $checkPartnerSave = '';
        if (isset($request['partner_save']) && $request['partner_save'] == 1) {
            $checkPartnerSave = $request['partner_save'];
        }

        $idLog = $this->__createCollectDebtLog($inputs, $dataLog);

        $result = $this->mposCollectDebtGatewayRepo->checkCollectDebtCommandOnPartner($inputs);
        $errorCode = $result['errorCode'];
        $descriptionName = isset($descriptionExample[$errorCode]) ? $descriptionExample[$errorCode] : '';
        $status = 'TIMEOUT';
        $amountDebit = '';
        $partnerTransactionId = '';
        $debtRecoveryPartnerId = '';
        if ($errorCode == config('collect_debt_gateway_config.mpos_gateway_response_success')) {
            $data = $result['data'];
            if (isset($data['data']) && isset($data['data']['debtStatus']) && $data['data']['debtStatus']) {
                $debtStatus = $data['data']['debtStatus'];
                $partnerTransactionId = isset($data['data']['mposDebtId']) ? $data['data']['mposDebtId'] : '';
                switch ($debtStatus) {
                    case 'APPROVED':
                        $status = 'SUCCESS';
                        $amountDebit = isset($data['data']['debtRecoveryAmount']) ? $data['data']['debtRecoveryAmount'] : '';

                        if (empty($checkPartnerSave)) {
                            $params = [
                                'partner_merchant_id' => $inputs['lendingId'],
                                'users_admin_id' => $dataLog['users_admin_id'],
                                'amount_payment' => $inputs['debitAmount'],
                                'amount_receiver' => $amountDebit,
                                'partner_request_id' => $dataLog['partner_request_id'],
                                'partner_transaction_id' => $partnerTransactionId,
                                'partner_request' => $result,
                            ];
                            $collectDebtPartner = $this->__createCollectDebtPartner($params);
                            if (isset($collectDebtPartner['id'])) {
                                $debtRecoveryPartnerId = $collectDebtPartner['id'];
                            }
                        }
                        break;
                    case 'CANCEL':
                        $status = 'CANCEL';

                        if (empty($checkPartnerSave)) {
                            $params = [
                                'partner_merchant_id' => $inputs['lendingId'],
                                'users_admin_id' => $dataLog['users_admin_id'],
                                'amount_payment' => $inputs['debitAmount'],
                                'amount_receiver' => 0,
                                'partner_request_id' => $dataLog['partner_request_id'],
                                'partner_transaction_id' => $partnerTransactionId,
                                'partner_request' => $result,
                            ];
                            $collectDebtPartner = $this->__createCollectDebtPartner($params);
                            if (isset($collectDebtPartner['id'])) {
                                $debtRecoveryPartnerId = $collectDebtPartner['id'];
                            }
                        }

                        break;
                    case 'PENDING':
                        $status = 'PENDING';
                        break;
                    case 'EXPIRED':
                        $status = 'EXPIRED';

                        if (empty($checkPartnerSave)) {
                            $params = [
                                'partner_merchant_id' => $inputs['lendingId'],
                                'users_admin_id' => $dataLog['users_admin_id'],
                                'amount_payment' => $inputs['debitAmount'],
                                'amount_receiver' => 0,
                                'partner_request_id' => $dataLog['partner_request_id'],
                                'partner_transaction_id' => $partnerTransactionId,
                                'partner_request' => $result,
                            ];
                            $collectDebtPartner = $this->__createCollectDebtPartner($params);
                            if (isset($collectDebtPartner['id'])) {
                                $debtRecoveryPartnerId = $collectDebtPartner['id'];
                            }
                        }

                        break;
                }
            }
        } else {
            if ($errorCode == config('collect_debt_gateway_config.mpos_gateway_response_waitting')) {
                $status = 'WAITTING';
            }
        }


        $dataLog['description'] = $descriptionName;
        $dataLog['partner_transaction_id'] = $partnerTransactionId;
        $dataLog['amount_receiver'] = $amountDebit;
        $this->__updateCollectDebtLog($idLog, $result, $dataLog);

        $dataResponse = [
            'error_code' => $errorCode,
            'description' => $descriptionName,
            'status' => $status,
            'amount_debit' => $amountDebit,
            'partner_transaction_id' => $partnerTransactionId,
            'debt_recovery_partner_id' => $debtRecoveryPartnerId
        ];

        return $dataResponse;
    }

    /**
     * @param $request
     * @return array
     */
    public function checkBalance($request)
    {
        $descriptionExample = config('collect_debt_gateway_config.mpos_gateway_reponse_name');

        if (isset($request['partner_request_id']) && !empty($request['partner_request_id'])) {
            $nextlendRequestId = $this->_prefix . $request['partner_request_id'];
        } else {
            $nextlendRequestId = 'CALLCHECK_' . rand('111111111', '99999999');
        }

        $inputs = [
            'nextlend_request_id' => $nextlendRequestId,
            "merchantId" => $request['partner_merchant_id'],
            "lendingRequestId" => $nextlendRequestId,
            'partner_code' => 'MPOS',
            'recoverStartTime' => date('d-m-Y'),
            'recoverEndTime' => date('d-m-Y'),
            '_prefix' => $this->_prefix,
        ];

        $dataLog = [
            'request_id' => isset($request['request_id']) ? $request['request_id'] : '',
            'users_admin_id' => $request['users_admin_id'],
            'partner_request_id' => isset($request['partner_request_id']) ? $request['partner_request_id'] : '',
            'other_data' => ['func' => 'checkBalance']
        ];


        $idLog = $this->__createCollectDebtLog($inputs, $dataLog);

        $result = $this->mposCollectDebtGatewayRepo->checkBalanceMerchant($inputs);
        $errorCode = $result['errorCode'];
        $descriptionName = isset($descriptionExample[$errorCode]) ? $descriptionExample[$errorCode] : '';
        $amount = 0;
        if ($errorCode == config('collect_debt_gateway_config.mpos_gateway_response_success')) {
            $amount = isset($result['data']) && isset($result['data']['mcBalanceAmount']) ? $result['data']['mcBalanceAmount'] : 0;
        }

        $dataLog['description'] = $descriptionName;
        $dataLog['amount_receiver'] = $amount;
        $this->__updateCollectDebtLog($idLog, $result, $dataLog);


        $dataResponse = [
            'error_code' => $errorCode,
            'description' => $descriptionName,
            'amount' => $amount,
        ];

        return $dataResponse;
    }

    /**
     * @param $data
     * @param $dataLog
     * @return array|bool
     */
    protected function __createCollectDebtLog($data, $dataLog)
    {
        $inputs = [
            'created_by' => $dataLog['users_admin_id'],
            'request_data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'time_created' => time(),
        ];


        if (isset($dataLog['request_id']) && !empty($dataLog['request_id'])) {
            $inputs['request_id'] = $dataLog['request_id'];
        }
        if (isset($dataLog['partner_request_id']) && !empty($dataLog['partner_request_id'])) {
            $inputs['partner_request_id'] = $dataLog['partner_request_id'];
        }
        if (isset($dataLog['partner_transaction_id']) && !empty($dataLog['partner_transaction_id'])) {
            $inputs['partner_transaction_id'] = $dataLog['partner_transaction_id'];
        }
        if (isset($dataLog['amount_payment']) && !empty($dataLog['amount_payment'])) {
            $inputs['amount_payment'] = $dataLog['amount_payment'];
        }
        if (isset($dataLog['amount_receiver']) && !empty($dataLog['amount_receiver'])) {
            $inputs['amount_receiver'] = $dataLog['amount_receiver'];
        }
        if (isset($dataLog['fee']) && !empty($dataLog['fee'])) {
            $inputs['fee'] = $dataLog['fee'];
        }
        if (isset($dataLog['other_data']) && !empty($dataLog['other_data'])) {
            $inputs['other_data'] = json_encode($dataLog['other_data'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($dataLog['response']) && !empty($dataLog['response'])) {
            $inputs['response'] = json_encode($dataLog['response'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($dataLog['description']) && !empty($dataLog['description'])) {
            $inputs['description'] = $dataLog['description'];
        }


        $result = $this->collectDebtLogRepo->createGetId($inputs);

        return $result;
    }

    /**
     * @param $data
     * @param $dataLog
     * @return array|bool
     */
    protected function __updateCollectDebtLog($id, $data, $dataLog)
    {
        $inputs = [
            'response' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'updated_by' => $dataLog['users_admin_id'],
            'time_updated' => time(),
        ];

        if (isset($dataLog['request_id']) && !empty($dataLog['request_id'])) {
            $inputs['request_id'] = $dataLog['request_id'];
        }
        if (isset($dataLog['partner_request_id']) && !empty($dataLog['partner_request_id'])) {
            $inputs['partner_request_id'] = $dataLog['partner_request_id'];
        }
        if (isset($dataLog['partner_transaction_id']) && !empty($dataLog['partner_transaction_id'])) {
            $inputs['partner_transaction_id'] = $dataLog['partner_transaction_id'];
        }
        if (isset($dataLog['amount_payment']) && !empty($dataLog['amount_payment'])) {
            $inputs['amount_payment'] = $dataLog['amount_payment'];
        }
        if (isset($dataLog['amount_receiver']) && !empty($dataLog['amount_receiver'])) {
            $inputs['amount_receiver'] = $dataLog['amount_receiver'];
        }
        if (isset($dataLog['fee']) && !empty($dataLog['fee'])) {
            $inputs['fee'] = $dataLog['fee'];
        }
        if (isset($dataLog['other_data']) && !empty($dataLog['other_data'])) {
            $inputs['other_data'] = json_encode($dataLog['other_data'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($dataLog['request_data']) && !empty($dataLog['request_data'])) {
            $inputs['request_data'] = json_encode($dataLog['request_data'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($dataLog['description']) && !empty($dataLog['description'])) {
            $inputs['description'] = $dataLog['description'];
        }


        $result = $this->collectDebtLogRepo->updateGetId($id, $inputs);

        return $result;
    }

    protected function __getLogByPartnerRequestId($id)
    {

        $params = [
            'partner_request_id' => $id,
        ];

        $result = $this->collectDebtLogRepo->getByPartnerRequestId($params);
        if (!empty($result)) {
            return $result;
        }

        return false;
    }

    protected function __getLogByRequestId($id)
    {

        $params = [
            'request_id' => $id,
        ];

        $result = $this->collectDebtLogRepo->getByRequestId($params);
        if (!empty($result)) {
            return $result;
        }

        return false;
    }

    public function receiveNotify($request)
    {
        $dataResponse = [];
        $requestId = '';

        $inputs = $request;

        $partner_request_id = str_replace($this->_prefix, '', $request['lendingReferenceId']);

        $dataLog = [
            'partner_request_id' => $partner_request_id,
            'amount_receiver' => isset($request['withdrawBackAmount']) ? $request['withdrawBackAmount'] : 0,
            'partner_transaction_id' => $request['mposRequestId'],
            'users_admin_id' => $request['payment_channel'],
            'partner_merchant_id' => $request['merchantId'],
            'other_data' => ['func' => 'receiveNotify'],
            'response' => $inputs,
        ];

        $idLog = $this->__createCollectDebtLog($inputs, $dataLog);

        // when received notify partner call function check status transactions
        switch ($request['deductionStatus']) {
            case 'APPROVED':
                $params = [
                    'partner_merchant_id' => $inputs['lendingId'],
                    'users_admin_id' => $dataLog['users_admin_id'],
                    'amount_receiver' => !empty($dataLog['amount_receiver']) ? $dataLog['amount_receiver'] : 0,
                    'partner_request_id' => $dataLog['partner_request_id'],
                    'partner_transaction_id' => $dataLog['partner_transaction_id'],
                    'partner_request' => $inputs,
                ];
                $collectDebtPartner = $this->__createCollectDebtPartner($params);
                if(isset($collectDebtPartner['request_id'])){
                    $requestId = $collectDebtPartner['request_id'];
                }
                break;
            case 'EXPIRED':
                $params = [
                    'partner_merchant_id' => $inputs['lendingId'],
                    'users_admin_id' => $dataLog['users_admin_id'],
                    'amount_receiver' => 0,
                    'partner_request_id' => $dataLog['partner_request_id'],
                    'partner_transaction_id' => $dataLog['partner_transaction_id'],
                    'partner_request' => $inputs,
                ];
                $collectDebtPartner = $this->__createCollectDebtPartner($params);
                if(isset($collectDebtPartner['request_id'])){
                    $requestId = $collectDebtPartner['request_id'];
                }
                break;
            case 'CANCEL':
                $params = [
                    'partner_merchant_id' => $inputs['lendingId'],
                    'users_admin_id' => $dataLog['users_admin_id'],
                    'amount_receiver' => 0,
                    'partner_request_id' => $dataLog['partner_request_id'],
                    'partner_transaction_id' => $dataLog['partner_transaction_id'],
                    'partner_request' => $inputs,
                ];
                $collectDebtPartner = $this->__createCollectDebtPartner($params);
                if(isset($collectDebtPartner['request_id'])){
                    $requestId = $collectDebtPartner['request_id'];
                }
                break;
        }

        $dataLog['request_id'] = $requestId;
        $this->__updateCollectDebtLog($idLog, $inputs, $dataLog);


        return $dataResponse;
    }

    /**
     * @param $data
     * @return array|bool
     */
    protected function __createCollectDebtPartner($data)
    {

        $inputs = [
            'payment_channel_code' => 'MPOS',
            'payment_method_code' => 'MPOS',
            'payment_account_id' => $data['partner_merchant_id'],
            'request_exists' => 1,
            'created_by' => $data['users_admin_id'],
            'time_created' => time(),
            'amount_receiver' => 0,
        ];
        if (isset($data['partner_request_id']) && !empty($data['partner_request_id'])) {
            $inputs['partner_request_id'] = $data['partner_request_id'];
        }
        if (isset($data['partner_transaction_id']) && !empty($data['partner_transaction_id'])) {
            $inputs['partner_transaction_id'] = (string)$data['partner_transaction_id'];
        }
        
        if (isset($data['amount_receiver']) && !empty($data['amount_receiver'])) {
            $inputs['amount_receiver'] = $data['amount_receiver'];
        }
        if (isset($data['fee']) && !empty($data['fee'])) {
            $inputs['fee'] = $data['fee'];
        }
        $response = [];
        if (isset($data['response']) && !empty($data['response'])) {
            $data['response'] = json_decode($data['response'], true);
            if (!empty($data['response']) && is_array($data['response'])) {
                $response = $data['response'];
                if (isset($data['partner_request']) && !empty($data['partner_request'])) {
                    array_push($response, $data['partner_request']);
                }
            }
        } else {
            if (isset($data['partner_request']) && !empty($data['partner_request'])) {
                array_push($response, $data['partner_request']);
            }
        }
        $inputs['response'] = json_encode($response, JSON_UNESCAPED_UNICODE);

        $payload = [
            'module' => CommonVar::API_REQUEST_DEBT_MODULE,
            'path' => '/DebtRecoveryPartnerCreate',
            'params' => $inputs,
            'method' => 'POST'
        ];

        $result = (new ApiCall())->callFunctionApi($payload, false);

        return $result;
    }

    /**
     * @param $id
     * @param $data
     * @return array|bool
     */
    protected function __updateCollectDebtPartner($id, $data)
    {

        $inputs = [
            'id' => $id,
            'payment_channel_code' => 'MPOS',
            'payment_method_code' => 'MPOS',
            'payment_account_id' => $data['partner_merchant_id'],
            'request_exists' => 1,
            'updated_by' => $data['users_admin_id'],
            'time_updated' => time(),
        ];
        if (isset($data['partner_request_id']) && !empty($data['partner_request_id'])) {
            $inputs['partner_request_id'] = $data['partner_request_id'];
        }
        if (isset($data['partner_transaction_id']) && !empty($data['partner_transaction_id'])) {
            $inputs['partner_transaction_id'] = (string)$data['partner_transaction_id'];
        }
        if (isset($data['amount_payment']) && !empty($data['amount_payment'])) {
            $inputs['amount_payment'] = $data['amount_payment'];
        }
        if (isset($data['amount_receiver']) && !empty($data['amount_receiver'])) {
            $inputs['amount_receiver'] = $data['amount_receiver'];
        }
        if (isset($data['fee']) && !empty($data['fee'])) {
            $inputs['fee'] = $data['fee'];
        }

        $response = [];
        if (isset($data['response']) && !empty($data['response'])) {
            $data['response'] = json_decode($data['response'], true);
            if (!empty($data['response']) && is_array($data['response'])) {
                $response = $data['response'];
                if (isset($data['partner_request']) && !empty($data['partner_request'])) {
                    array_push($response, $data['partner_request']);
                }
            }
        } else {
            if (isset($data['partner_request']) && !empty($data['partner_request'])) {
                array_push($response, $data['partner_request']);
            }
        }
        $inputs['response'] = json_encode($response, JSON_UNESCAPED_UNICODE);

        $payload = [
            'module' => CommonVar::API_REQUEST_DEBT_MODULE,
            'path' => '/DebtRecoveryPartnerUpdate',
            'params' => $inputs,
            'method' => 'POST'
        ];

        $result = (new ApiCall())->callFunctionApi($payload, false);

        return $result;
    }

    /**
     * @param $id
     * @return array|bool
     */
    protected function __getByTransactionIdCollectDebtPartner($id)
    {

        $params = [
            'partner_transaction_id' => (string)$id,
            'payment_channel_code' => 'MPOS',
        ];


        $payload = [
            'module' => CommonVar::API_REQUEST_DEBT_MODULE,
            'path' => '/DebtRecoveryPartnerGetByPartnerTransactionId',
            'params' => $params,
            'method' => 'GET'
        ];

        $result = (new ApiCall())->callFunctionApi($payload, false);

        return $result;
    }

    /**
     * @param $id
     * @return array|bool
     */
    protected function __getByParamDebtRecoveryRequest($id)
    {

        $params = [
            'search' => [
                'partner_request_id' => $id
            ],
        ];


        $payload = [
            'module' => CommonVar::API_REQUEST_DEBT_MODULE,
            'path' => '/DebtRecoveryRequestGetByParam',
            'params' => $params,
            'method' => 'POST'
        ];

        $result = (new ApiCall())->callFunctionApi($payload, false);

        return $result;
    }

    /**
     * @param $request
     * @return array
     */
    public function reCheckDebt($request)
    {

        $descriptionExample = config('collect_debt_gateway_config.mpos_gateway_reponse_name');

        $nextlendRequestId = $this->_prefix . $request['partner_request_id'];

        $inputs = [
            'nextlend_request_id' => $nextlendRequestId,
            'partner_code' => 'MPOS',
            'merchantId' => $request['partner_merchant_id'],
            'lendingRequestId' => $request['partner_request_id'],
            'debitAmount' => $request['amount_payment'],
            'requestTime' => date('YmdHis'),
            'lendingId' => $request['contract_code'],
            'loanOriginalAmount' => $request['loan_original_amount'], //So tien giai ngan
            'deductionPerDayAmount' => $request['deduction_per_day_amount'], //So tien trich no hang ngay
            'loanBalance' => $request['loan_balance'], //Dư nợ đầu kỳ
            '_prefix' => $this->_prefix,
        ];

        $dataLog = [
            'request_id' => $request['request_id'],
            'users_admin_id' => $request['users_admin_id'],
            'partner_request_id' => $request['partner_request_id'],
            'amount_payment' => $request['amount_payment'],
            'other_data' => ['func' => 'reCheckDebt']
        ];

        $checkPartnerSave = '';
        if (isset($request['partner_save']) && $request['partner_save'] == 1) {
            $checkPartnerSave = $request['partner_save'];
        }

        $idLog = $this->__createCollectDebtLog($inputs, $dataLog);

        $result = $this->mposCollectDebtGatewayRepo->reCheckCollectDebtCommandOnPartner($inputs);
        $errorCode = $result['errorCode'];
        $descriptionName = isset($descriptionExample[$errorCode]) ? $descriptionExample[$errorCode] : '';
        $status = 'TIMEOUT';
        $amountDebit = '';
        $partnerTransactionId = '';
        $debtRecoveryPartnerId = '';
        if ($errorCode == config('collect_debt_gateway_config.mpos_gateway_response_success')) {
            $data = $result['data'];
            if (isset($data['data']) && isset($data['data']['debtStatus']) && $data['data']['debtStatus']) {
                $debtStatus = $data['data']['debtStatus'];
                $partnerTransactionId = isset($data['data']['mposDebtId']) ? $data['data']['mposDebtId'] : '';
                switch ($debtStatus) {
                    case 'APPROVED':
                        $status = 'SUCCESS';
                        $amountDebit = isset($data['data']['debtRecoveryAmount']) ? $data['data']['debtRecoveryAmount'] : '';

                        if (empty($checkPartnerSave)) {
                            $params = [
                                'partner_merchant_id' => $inputs['lendingId'],
                                'users_admin_id' => $dataLog['users_admin_id'],
                                'amount_payment' => $inputs['debitAmount'],
                                'amount_receiver' => $amountDebit,
                                'partner_request_id' => $dataLog['partner_request_id'],
                                'partner_transaction_id' => $partnerTransactionId,
                                'partner_request' => $result,
                            ];
                            $collectDebtPartner = $this->__createCollectDebtPartner($params);
                            if (isset($collectDebtPartner['id'])) {
                                $debtRecoveryPartnerId = $collectDebtPartner['id'];
                            }
                        }
                        break;
                    case 'CANCEL':
                        $status = 'CANCEL';

                        if (empty($checkPartnerSave)) {
                            $params = [
                                'partner_merchant_id' => $inputs['lendingId'],
                                'users_admin_id' => $dataLog['users_admin_id'],
                                'amount_payment' => $inputs['debitAmount'],
                                'amount_receiver' => 0,
                                'partner_request_id' => $dataLog['partner_request_id'],
                                'partner_transaction_id' => $partnerTransactionId,
                                'partner_request' => $result,
                            ];
                            $collectDebtPartner = $this->__createCollectDebtPartner($params);
                            if (isset($collectDebtPartner['id'])) {
                                $debtRecoveryPartnerId = $collectDebtPartner['id'];
                            }
                        }

                        break;
                    case 'PENDING':
                        $status = 'PENDING';
                        break;
                    case 'EXPIRED':
                        $status = 'EXPIRED';

                        if (empty($checkPartnerSave)) {
                            $params = [
                                'partner_merchant_id' => $inputs['lendingId'],
                                'users_admin_id' => $dataLog['users_admin_id'],
                                'amount_payment' => $inputs['debitAmount'],
                                'amount_receiver' => 0,
                                'partner_request_id' => $dataLog['partner_request_id'],
                                'partner_transaction_id' => $partnerTransactionId,
                                'partner_request' => $result,
                            ];
                            $collectDebtPartner = $this->__createCollectDebtPartner($params);
                            if (isset($collectDebtPartner['id'])) {
                                $debtRecoveryPartnerId = $collectDebtPartner['id'];
                            }
                        }

                        break;
                }
            }
        } else {
            if ($errorCode == config('collect_debt_gateway_config.mpos_gateway_response_waitting')) {
                $status = 'WAITTING';
            }
        }


        $dataLog['description'] = $descriptionName;
        $dataLog['partner_transaction_id'] = $partnerTransactionId;
        $dataLog['amount_receiver'] = $amountDebit;
        $this->__updateCollectDebtLog($idLog, $result, $dataLog);

        $dataResponse = [
            'error_code' => $errorCode,
            'description' => $descriptionName,
            'status' => $status,
            'amount_debit' => $amountDebit,
            'partner_transaction_id' => $partnerTransactionId,
            'debt_recovery_partner_id' => $debtRecoveryPartnerId
        ];

        return $dataResponse;
    }
}
