<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtDigitalNoti;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiSendAction\DigitalNotiSendAction;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiQuaHanAction\DigitalNotiQuaHanAction;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiClearDataAction\DigitalNotiClearDataAction;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiSapDenHanAction\DigitalNotiSapDenHanAction;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\BuildParamDigitalNotiAction\BuildParamDigitalNotiAction;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiDenHanHopDongAction\DigitalNotiDenHanHopDongAction;
use App\Modules\CollectDebt\Actions\CollectDebtDigitalNoti\DigitalNotiSapDenKyThanhToanAction\DigitalNotiSapDenKyThanhToanAction;

class CollectDebtDigitalNotiController extends Controller
{
	public function DigitalNotiBuildParam(Request $request)
	{
		try {
			$collectDebtLedger = app(BuildParamDigitalNotiAction::class)->init();
			return $this->successResponse($collectDebtLedger, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DigitalNotiSend(Request $request)
	{
		try {
			$collectDebtLedger = app(DigitalNotiSendAction::class)->init();
			return $this->successResponse($collectDebtLedger, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DigitalNotiSapDenHan(Request $request)
	{
		try {
			$notifications = app(DigitalNotiSapDenHanAction::class)->run();
			return $this->successResponse($notifications, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DigitalNotiSapDenKyThanhToan(Request $request)
	{
		try {
			$notifications = app(DigitalNotiSapDenKyThanhToanAction::class)->run();
			return $this->successResponse($notifications, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DigitalNotiDenHanHopDong(Request $request)
	{
		try {
			$notifications = app(DigitalNotiDenHanHopDongAction::class)->run();
			return $this->successResponse($notifications, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DigitalNotiClearData(Request $request)
	{
		try {
			$notifications = app(DigitalNotiClearDataAction::class)->run();
			return $this->successResponse($notifications, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DigitalNotiQuaHan(Request $request)
	{
		try {
			$notifications = app(DigitalNotiQuaHanAction::class)->run();
			return $this->successResponse($notifications, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class