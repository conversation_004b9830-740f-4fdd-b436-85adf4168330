<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Model\NextlendCompany;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class ReplaceNoiDungBySummarySubAction
{
	public function run(CollectDebtContractEvent $collectDebtContractEvent, CollectDebtSummary $collectDebtSummary, array $emailTemplateContent = []): string
	{
		$customFields = $this->__mappinCustomeFieldTemplate($collectDebtContractEvent, $collectDebtSummary);

		$emailContent = $emailTemplateContent['content'];

		foreach ($customFields as $filed => $value) {
			$emailContent = str_replace($filed, $value, $emailContent);
		}

		return $emailContent;
	} // End method

	private function __mappinCustomeFieldTemplate(CollectDebtContractEvent $collectDebtContractEvent, CollectDebtSummary $collectDebtSummary)
	{
		$eventData = $collectDebtContractEvent->getEventData();
		$merchant = $eventData['profile']['merchant'];
		$contract = $eventData['contract'];

		$eventOtherData = $collectDebtContractEvent->getEventOtherData();
		$summary = $eventOtherData['summary'];

		$paymentVa = collect($eventData['payment'])->where('other_data', '!=', '')->where('payment_method_code', 'VIRTUALACCOUNT')->map(function ($items) {
			return isset($items['other_data']) ? $items['other_data'] : '';
		})->first();

		$userAdmin = isset($contract['users_admin']) ? $contract['users_admin'] : [];

		$company = new NextlendCompany($eventData['company']);

		$params = [
			'[hop_dong_khach_hang]' => isset($merchant['fullname']) ? $merchant['fullname'] : '',
			'[hop_dong_nguoi_dai_dien]' => $merchant['business_representative'] ?? 'N/A',
			'[hop_dong_ma]' => $collectDebtContractEvent->contract_code,
			'[hop_dong_so_tien_ung]' => Helper::makeVndCurrency($summary['contract_amount']), // so tien hd
			'[hop_dong_so_ngay_vay]' => $summary['contract_cycle'], // so ngay vay
			'[hop_dong_tu_ngay]' => Carbon::createFromFormat('d-m-Y H:i:s', $contract['time_start_as_date'])->format('d/m/Y'),
			'[hop_dong_den_ngay]' => Carbon::createFromFormat('d-m-Y H:i:s', $contract['time_end_as_date'])->format('d/m/Y'),
			'[hop_dong_hinh_thuc_trich_no]' => $contract['intervals'] ?? '',
			'[hop_dong_loai]' => $summary['contract_type'] ?? '',



			'[hop_dong_stk_cong_ty]'              => isset($company['company_bank_account_1']) ? $company['company_bank_account_1'] : '',
			'[hop_dong_ten_ngan_hang_cong_ty]'    => isset($company['company_bank_name_1']) ? $company['company_bank_name_1'] : '',
			'[hop_dong_ten_chu_tk_cong_ty]'       => isset($company['company_bank_holder_1']) ? $company['company_bank_holder_1'] : '',
			'[hop_dong_nd_chuyen_khoan]'          => sprintf('NAP TIEN TK MC MA %s - %s', $company->getNoiDungChuyenKhoanMaNapTien(), $company->getCuPhapFullName($merchant)),
			'[hop_dong_ma_qr_va]'                 => isset($paymentVa['qrImage']) ? $paymentVa['qrImage'] : '',
			'[hop_dong_ten_ngan_hang_va_cong_ty]' => isset($paymentVa['payment_account_bank_code']) ? $paymentVa['payment_account_bank_code'] : '',
			'[hop_dong_stk_va_cong_ty]'           => isset($paymentVa['payment_account_number']) ? $paymentVa['payment_account_number'] : '',
			'[hop_dong_ten_chu_tk_va_cong_ty]'    => isset($paymentVa['payment_account_name']) ? $paymentVa['payment_account_name'] : '',
			'[hop_dong_nguoi_tao]'                => isset($userAdmin['fullname']) ? $userAdmin['fullname'] : '',
			'[hop_dong_sdt_nguoi_tao]'            => isset($userAdmin['mobile']) ? $userAdmin['mobile'] : '',
			'[hop_dong_email_nguoi_tao]'          => isset($userAdmin['email']) ? $userAdmin['email'] : '',
			'[hop_dong_sdt_cong_ty]'              => $company->getPhoneNumber() ?? '',
			'[hop_dong_email_cong_ty]'            => $company->getEmail() ?? '',
			'[hop_dong_dia_chi_cong_ty]'          => $company->getAddress() ?? '',
			'[hop_dong_website_1_cong_ty]'        => isset($company['company_url_1']) ? $company['company_url_1'] : '',
			'[hop_dong_website_2_cong_ty]'        => isset($company['company_url_2']) ? $company['company_url_2'] : '',
			'[hop_dong_ten_cong_ty]'              => isset($company['company_fullname']) ? $company['company_fullname'] : '',
			'[hop_dong_ten_ngan_cong_ty]'         => isset($company['company_subname']) ? $company['company_subname'] : '',
		];

		// goc, phi da tra
		$soTienDaHoanTra = $collectDebtSummary->total_amount_paid + $collectDebtSummary->total_fee_paid 
																															+ $collectDebtSummary->fee_overdue_reduction;
																															+ $collectDebtSummary->fee_overdue_cycle_reduction;

		// phi cham ky phat sinh
		$phiPhatThanhToanChamKy = $collectDebtSummary->fee_overdue_cycle;

		// phi cham ky con phai thu
		$phiChamKyConPhaiThuTiep = $collectDebtSummary->fee_overdue_cycle - $collectDebtSummary->fee_overdue_cycle_reduction
																																		  - $collectDebtSummary->fee_overdue_cycle_paid;

		// So tien HD + cac loai phi - tong tien da tra
		$thanhToanToiThieu      = $collectDebtSummary->contract_amount + $phiPhatThanhToanChamKy - $soTienDaHoanTra;
		$duNoCuoiKy             = $thanhToanToiThieu;

		$params = array_merge($params, [
			'[hop_dong_so_tien_hoan]' => Helper::makeVndCurrency($soTienDaHoanTra),
			'[hop_dong_du_no_cuoi_ky]' => Helper::makeVndCurrency($duNoCuoiKy),
			'[hop_dong_phi_cham_ky]' => Helper::makeVndCurrency($phiChamKyConPhaiThuTiep),
			'[hop_dong_so_tien_toi_thieu]' => Helper::makeVndCurrency($thanhToanToiThieu),
			'[hop_dong_ngay_chu_ky_trich_no]' => $collectDebtSummary->getTimeEndAsDate()->format('d/m/Y'),
		]);

		return $params;
	}
} // End class