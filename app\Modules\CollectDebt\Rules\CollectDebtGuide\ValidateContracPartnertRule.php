<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtGuide;

use Illuminate\Contracts\Validation\Rule;

class ValidateContracPartnertRule implements Rule
{
	private string $__errorMessage = '';


	public function passes($partnerContractField, $partnerContractValue)
	{
		$partnerCodeFromContractData = request()->json('data.contract_data.contract_partner.partner_code');
		
		if ($partnerContractValue != 'NEXTLEND') {
			if ($partnerCodeFromContractData != $partnerContractValue) {
				$this->__errorMessage = 'PartnerCode không được cấu hình đúng với ContractPartner';
				return false;
			}
		}

		return true;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return $this->__errorMessage;
	}
}
