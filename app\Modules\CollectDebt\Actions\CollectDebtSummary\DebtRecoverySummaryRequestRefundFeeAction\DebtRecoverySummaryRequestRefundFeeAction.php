<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryRequestRefundFeeAction;

use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryAmountingExcessCashAction\SubAction\TaoYeuCauHoanTienQuaVer1SubAction;
use Exception;
use Illuminate\Support\Str;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRecoveryLog;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryRequestRefundFeeRequest;

class DebtRecoverySummaryRequestRefundFeeAction
{
	public function run(DebtRecoverySummaryRequestRefundFeeRequest $request)
	{
		$contractCode = trim($request->json('data.contract_code'));
		$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();

		throw_if(!$collectDebtSummary, new Exception('khong tim thay thong tin hop dong'));

		if (!$collectDebtSummary->isHopDongDaTatToan()) {
			mylog(['Khong phai hop dong da tat toan' => $collectDebtSummary->contract_code]);
			throw new Exception('HD chua tat toan, khong the lam hoan phi');
		}

		// Kiem tra co yc hoan nao dang chua ve trang thai cuoi hay khong
		$yeuCauHoanPhiChuaVeTrangThaiCuoi = CollectDebtRecoveryLog::query()
																															->whereIn('service_code', [
																																CollectDebtEnum::RL_DICH_VU_HOAN_PHI,
																																CollectDebtEnum::RL_DICH_VU_HOAN_THU_THUA,
																															])
																															->where('reference_id', $collectDebtSummary->id)
																															->whereIn('status', [
																																CollectDebtEnum::RL_STT_MOI_TAO,
																																CollectDebtEnum::RL_STT_DANG_XU_LY
																															])
																															->first();
		if ($yeuCauHoanPhiChuaVeTrangThaiCuoi) {
			mylog(['Da co yc hoan phi dang xu ly' => $yeuCauHoanPhiChuaVeTrangThaiCuoi->order_code]);
			throw new Exception('Da co yc hoan phi dang xu ly');
		}

		$soTienPhiChamKyMuonHoan = $request->json('data.fee_over_cycle_want_refund', 0);
		$soTienPhiQuaHanMuonHoan = $request->json('data.fee_overdue_want_refund', 0);

		$soTienPhiChamKyCoTheHoan = $collectDebtSummary->getOnlyPhiChamKyCoTheHoan();
		$soTienPhiQuaHanCoTheHoan = $collectDebtSummary->getOnlyPhiQuaHanCoTheHoan();

		mylog([
			'soTienPhiChamKyMuonHoan'  => $soTienPhiChamKyMuonHoan,
			'soTienPhiChamKyCoTheHoan' => $soTienPhiChamKyCoTheHoan,
			'------------------------' => '--------------------------',
			'soTienPhiQuaHanMuonHoan'  => $soTienPhiQuaHanMuonHoan,
			'soTienPhiQuaHanCoTheHoan' => $soTienPhiQuaHanCoTheHoan,
		]);

		if ($soTienPhiChamKyMuonHoan > $soTienPhiChamKyCoTheHoan) {
			mylog(['[LOI HOAN PHI CHAM KY]' => 'So tien phi CK muon hoan bi vuot']);
			throw new Exception('So tien phi CK muon hoan bi vuot');
		}

		if ($soTienPhiQuaHanMuonHoan > $soTienPhiQuaHanCoTheHoan) {
			mylog(['[LOI HOAN PHI QUA HAN]' => 'So tien phi QH muon hoan bi vuot']);
			throw new Exception('So tien phi QH muon hoan bi vuot');
		}

		if ( empty($soTienPhiChamKyMuonHoan) && empty($soTienPhiQuaHanMuonHoan) ) {
			throw new Exception('[LOI] khong truyen phi cham ky hoac phi qua han');
		};

		$tongPhiMuonHoan = $soTienPhiChamKyMuonHoan + $soTienPhiQuaHanMuonHoan;

		$paramTaoYcHoan = [
			'service_code' => CollectDebtEnum::RL_DICH_VU_HOAN_PHI,
			'reference_id' => $collectDebtSummary->id, // ma tham chieu <summarry>
			'order_code'   => strtoupper('RFF-' . Str::random(12)),
			'created_by'   => $request->json('data.created_by'),
			'time_created' => now()->timestamp,
			'description'  => $request->json('data.reason')
		];
		
		while (
			CollectDebtRecoveryLog::query()->where('order_code', $paramTaoYcHoan['order_code'])->exists()
		) {
			$paramTaoYcHoan['order_code'] = strtoupper('RFF-' . Str::random(12));
		}

		$collectDebtRecoveryLog = CollectDebtRecoveryLog::query()->forceCreate($paramTaoYcHoan);
		
		if (!$collectDebtRecoveryLog) {
			mylog([
				'[ERROR]' => 'khong the tao yc hoan',
				'Param Tao' => $paramTaoYcHoan,
			]);

			throw new Exception('khong the tao yc hoan');
		};

		// Tao thanh cong, cap nhat summary
		$collectDebtSummary->amount_refunding += $tongPhiMuonHoan;
		$collectDebtSummary->is_request_sync = CollectDebtEnum::SUMMARY_CO_DONG_BO;
		$capNhatHoanPhiSummary = $collectDebtSummary->save();
		
		if (!$capNhatHoanPhiSummary) {
			throw new Exception('khong the cap nhat hoan phi summary');
		}

		$createdBy = json_decode($collectDebtRecoveryLog->created_by, true);

		$paramHoanSangV1 = [
			'amount'          => $tongPhiMuonHoan,
      'admin_user_id' 	=> $createdBy['id'],
      'description'   	=> $request->json('data.reason'),
      'contract_code' 	=> $collectDebtSummary->contract_code,
      'summary_id'    	=> $collectDebtSummary->id,
      'service_code'  	=> 'CONTRACT_FEE_PUNISH',
			'data_source'      => [
				'summary_id' 	=> $collectDebtSummary->id,
				'success_url' => '/DebtRecoverySummaryPlusFeeRefund',
				'cancel_url' 	=> '/DebtRecoverySummaryCancelFeeRefund',
				'attachments'	=>  $request->json('data.attachments', []),
				'other_data' => [
					'fee_over_cycle_want_refund' => $soTienPhiChamKyMuonHoan,
					'fee_overdue_want_refund' => $soTienPhiQuaHanMuonHoan,
				]
			],
      'order_code'    => $collectDebtRecoveryLog->order_code
		];


		mylog(['param hoan sang v1' => $paramHoanSangV1]);
		$ycHoanV1 = app(TaoYeuCauHoanTienQuaVer1SubAction::class)->run($paramHoanSangV1);
		mylog(['ket qua goi hoan sang v1' => $ycHoanV1]);

		$updated = $collectDebtRecoveryLog->update([
			'response' => json_encode($ycHoanV1['response']),
			'request_data' => json_encode($ycHoanV1['request_data']),
		]);

		mylog(['ket qua cap nhat debt_recovery_log cho request_data va response' => $updated]);

		return $collectDebtRecoveryLog;
	}
} // End class