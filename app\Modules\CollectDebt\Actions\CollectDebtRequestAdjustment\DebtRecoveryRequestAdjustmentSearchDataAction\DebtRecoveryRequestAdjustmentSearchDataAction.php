<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentSearchDataAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use Carbon\Carbon;

class DebtRecoveryRequestAdjustmentSearchDataAction
{
  public function run(Request $request)
  {
    $searchFilter = $request->json('data.filter');

    $results = CollectDebtRequestAdjustment::query();

    if (!empty($searchFilter['type'])) {
      $results->where('type', $searchFilter['type']);
    }

    if (!empty($searchFilter['status'])) {
      $results->where('status', $searchFilter['status']);
    }

    // Thời gian tạo
    if (!empty($searchFilter['time_created_from'])) {
      $timeCreateFrom = Carbon::createFromFormat('d-m-Y', $searchFilter['time_created_from'])->startOfDay()->timestamp;
      $results->where('time_created', '>=', $timeCreateFrom);
    }

    if (!empty($searchFilter['time_created_to'])) {
      $timeCreateTo = Carbon::createFromFormat('d-m-Y', $searchFilter['time_created_to'])->endOfDay()->timestamp;
      $results->where('time_created', '<=', $timeCreateTo);
    }

    // Thời gian duyệt
    if (!empty($searchFilter['time_approved_from'])) {
      $timeApprovedFrom = Carbon::createFromFormat('d-m-Y', $searchFilter['time_approved_from'])->startOfDay()->timestamp;
      $results->where(function ($q) use ($timeApprovedFrom) {
        $q->where('time_approved1', '>=', $timeApprovedFrom)
          ->orWhere('time_approved2', '>=', $timeApprovedFrom);
      });
    }

    if (!empty($searchFilter['time_approved_to'])) {
      $timeApprovedTo = Carbon::createFromFormat('d-m-Y', $searchFilter['time_approved_to'])->endOfDay()->timestamp;
      $results->where(function ($q) use ($timeApprovedTo) {
        $q->where('time_approved1', '<=', $timeApprovedTo)
          ->orWhere('time_approved2', '<=', $timeApprovedTo);
      });
    }

    // Thời gian cập nhật
    if (!empty($searchFilter['time_update'])) {
      $results->whereRaw("FROM_UNIXTIME(time_update, '%d-%m-%Y') = ?", array($searchFilter['time_update']));
    }

    if (!empty($searchFilter['time_canceled'])) {
      $results->whereRaw("FROM_UNIXTIME(time_canceled, '%d-%m-%Y') = ?", array($searchFilter['time_canceled']));
    }

    if (!empty($searchFilter['time_accounting'])) {
      $results->whereRaw("FROM_UNIXTIME(time_accounting, '%d-%m-%Y') = ?", array($searchFilter['time_accounting']));
    }

    if (!empty($searchFilter['time_complated'])) {
      $results->whereRaw("FROM_UNIXTIME(time_complated, '%d-%m-%Y') = ?", array($searchFilter['time_complated']));
    }

    if (!empty($searchFilter['time_overdued'])) {
      $results->whereRaw("FROM_UNIXTIME(time_overdued, '%d-%m-%Y') = ?", array($searchFilter['time_overdued']));
    }

    // Ma chung tu
    if (!empty($searchFilter['partner_transaction_id'])) {
      $results->whereHas('collectDebtPartner', function ($query) use ($searchFilter) {
				return $query->where('partner_transaction_id', trim($searchFilter['partner_transaction_id']));
			});
    }

    // Doi tac trich no
    if (!empty($searchFilter['payment_method_code'])) {
      $results->whereHas('collectDebtPartner', function ($query) use ($searchFilter) {
				return $query->where('payment_method_code', trim($searchFilter['payment_method_code']));
			});
    }

    // Profile Ids
    if (!empty($searchFilter['profile_ids'])) {
      $results->whereIn('profile_id', $searchFilter['profile_ids']);
    }

    $searchText = $request->json('data.text');

    // Mã HĐ
    if (!empty($searchText['contract_code'])) {
      $results->where('contract_code', $searchText['contract_code']);
    }

    if (!empty($searchText['reference_id'])) {
      $results->where('reference_id', $searchText['reference_id']);
    }

 

    $results = $results->latest('id')->paginate(
      $request->json('data.limit', 15),
      ['*'],
      'page',
      $request->json('data.page', 1)
    );

    $collection = $results->getCollection();
    $collection = $collection->map(function (CollectDebtRequestAdjustment  $ra) {
      $can[] = CollectDebtEnum::RA_STT_CAN_XEM_CHI_TIET;
      if ($ra->isStatusMoiTao()) {
        $can[] = CollectDebtEnum::RA_STT_CAN_DUYET_1;
        $can[] = CollectDebtEnum::RA_STT_CAN_HUY;
      }

      if ($ra->isStatusDuyet1()) {
        $can[] = CollectDebtEnum::RA_STT_CAN_DUYET_2;
        $can[] = CollectDebtEnum::RA_STT_CAN_HUY;
      }

      if ($ra->isStatusDuyet2()) {
        $can[] = CollectDebtEnum::RA_STT_CAN_HUY;
      }
      $ra->can = $can;
      return $ra;
    });
    $results->setCollection($collection);
    return $results;
  }
}
