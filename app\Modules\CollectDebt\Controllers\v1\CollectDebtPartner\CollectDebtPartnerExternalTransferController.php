<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtPartner;

use App\Lib\Helper;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateExternalTransferRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\DebtRecoveryPartnerCreateAction;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use Exception;
use Illuminate\Support\Arr;

class CollectDebtPartnerExternalTransferController extends Controller
{
  public function createExternalTransfer(DebtRecoveryPartnerCreateExternalTransferRequest $request)
  {
    try {
      $param = Arr::only($request->json('data'), [
        'payment_channel_code',
        'payment_method_code',
        'payment_account_id',
        'partner_transaction_id',
        'amount_receiver',
        'created_by',
        'time_created',
        'status',
        'description',
				'contract_code'
      ]);

      // Dùng guide để build model CoreContract, sau đọc từ partner sẽ không cần join sang chỉ dẫn nữa
      $collectDebtShare = CollectDebtShare::where('contract_code', $param['payment_account_id'])->first();
      throw_if(!$collectDebtShare, new Exception('Không tìm thấy thông tin HĐ để chuyển khoản ngoài'));

      $param['other_data'] = json_encode([
        [
          'type' => 'CONTRACT',
          'time_modified' => time(),
          'data' => [
            'profile_id'          => $collectDebtShare->profile_id,
            'contract_code'       => $collectDebtShare->contract_code,
            'contract_time_start' => $collectDebtShare->contract_time_start,
            'contract_type'       => $collectDebtShare->getContractTypev1(),
            'contract_cycle'      => $collectDebtShare->contract_cycle,
            'contract_intervals'  => $collectDebtShare->contract_intervals,
            'amount'              => $collectDebtShare->amount,
            'contract_time_end'   => $collectDebtShare->contract_time_end,
            'other_data'          => '{}',
            'description'         => 'Hợp đồng v4',
            'id'                  => $collectDebtShare->getContractId()
          ],
          'note' => 'Thông tin hợp đồng'
        ]
      ], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

      $collectDebtPartnerChuyenKhoanNgoai = CollectDebtPartner::forceCreate($param);

      return $this->successResponse($collectDebtPartnerChuyenKhoanNgoai->toArray(), $request, 200, 'Tạo chuyển khoản ngoài thành công');
    } catch (\Throwable $th) {
      return $this->errorResponse($th->getCode(), Helper::traceError($th));
    }
  }
} // End class
