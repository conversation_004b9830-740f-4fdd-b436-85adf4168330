<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\SubAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Carbon\Carbon;

class ThongKeTinhTrangThuHoiSA
{
	/** 
	 * Trả các thông tin: 
	 * 		Số tiền của HĐ (tổng ứng vốn)
	 * 		Số tiền chưa thu
	 */
	public function run(CollectDebtSummary $collectDebtSummary)
	{
		$currency = $collectDebtSummary->getCurrency();

		$returnData = [
			[
				'name' => 'Tổng ứng vốn',
				'value' => $collectDebtSummary->contract_amount,
				'currency' => $currency
			],

			[
				'name' => 'Chưa thu',
				'value' => $collectDebtSummary->contract_amount - $collectDebtSummary->total_amount_paid,
				'currency' => $currency
			],
		];

		return $returnData;
	}
}
