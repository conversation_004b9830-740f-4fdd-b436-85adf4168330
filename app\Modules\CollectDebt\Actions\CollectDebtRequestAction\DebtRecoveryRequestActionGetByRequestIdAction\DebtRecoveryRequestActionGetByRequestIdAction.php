<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAction\DebtRecoveryRequestActionGetByRequestIdAction;

use App\Modules\CollectDebt\Model\CollectDebtAction;

class DebtRecoveryRequestActionGetByRequestIdAction
{
    public function run($id)
    {
        $fields = [
            'id', 'type', 'request_id','action_code', 'created_by', 'updated_by', 'other_data', 'description', 'time_created', 'time_updated',
        ];
        $collectDebtAction = CollectDebtAction::where('request_id', $id)->select($fields)->get();
        return $collectDebtAction;
    }
}