<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanGetByContractCodeAction;

use App\Lib\Helper;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DebtRecoveryContractPlanGetByContractCodeAction
{
  public bool $hasCollapse = false;

  /**
   * Dự tính thu hồi
   * @link https://docs.google.com/document/d/1BwXVZ5_zHN8dcDNOWf_t1pZ0qq3nT_-2xzLyGP3l5Xw/edit
   * @param Request $request để là Request chung để cho chỗ khác còn gọi vào
   * @return Collection
   */
  public function run(Request $request)
  {
    $returnData = collect([]);

    $collectDebtShare = CollectDebtShare::where('contract_code', $request->json('data.contract_code'))->first();
    $plans = CollectDebtSchedule::query()
      ->where('contract_code', $request->json('data.contract_code'))
      ->orderBy('cycle_number', 'ASC')
      ->orderByRaw($request->json('data.order_by'))
      ->select([
        'id',
        'profile_id',
        'contract_code',
        'contract_type',
        'type',
        'isfee',
        'debit_begin',
        'debit_end',
        'rundate',
        'time_start',
        'time_end',
        'amount_period_debit',
        'request_amount_debit',
        'success_amount_debit',
        'is_settlement',
        'status',
        'created_by',
        'cycle_number',
        'other_data',
        'time_created',
        'time_updated',
				'is_process'
      ])
      ->get();

    $profileData = $collectDebtShare->getProfileDataAsArray();

    $plans = $plans->groupBy('cycle_number');

    $plans->transform(function (Collection $periodOfSchedules) use (&$returnData, $collectDebtShare, $profileData) {
      $totalNoGocDaThu = $periodOfSchedules->filter(function (CollectDebtSchedule $planItem) {
        return $planItem->isfee == CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC;
      })->sum('success_amount_debit');


      $tongPhiDaThu =  $periodOfSchedules->filter(function (CollectDebtSchedule $planItem) {
        return $planItem->isfee == CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI;
      })->sum('success_amount_debit');

      $cycleInfoStart = $periodOfSchedules->min('time_start');

      $periodOfSchedules->transform(function (CollectDebtSchedule $plan) use ($cycleInfoStart, $collectDebtShare, $profileData) {
        $timeEndAsDate = Carbon::createFromTimestamp($cycleInfoStart)
                               ->addDays($collectDebtShare->contract_intervals)
                               ->format('d/m/Y 23:59');

        if ($plan->isLichTatToan()) {
          $timeEndAsDate = 'Đến khi tất toán';
        }
        
        $plan->cycle_info = json_encode([
          'time_start_as_date' => Carbon::createFromTimestamp($cycleInfoStart)->format('d/m/Y H:i'),
          'time_end_as_date' => $timeEndAsDate,
        ]);

        $plan->profile_data = [
          'merchant' => [
            'id' => $profileData['merchant']['id'] ?? 'N/A',
            'fullname' => $profileData['merchant']['fullname'] ?? 'N/A',
            'email' => $profileData['merchant']['email'] ?? 'N/A',
            'mobile' => $profileData['merchant']['mobile'] ?? 'N/A',
          ]
        ];

        $plan->plan_type_name = 'Không xác định';

        if ($plan->isLichThuGoc()) {
          $plan->plan_type_name = 'Thu gốc';
        }

        if ($plan->isLichThuPhiChamKy()) {
          $plan->plan_type_name = 'Thu phí CK';
        }

        if ($plan->isLichThuPhiQuaHan()) {
          $plan->plan_type_name = 'Thu phí QH';
        }

        // Xử lý toggle
        $plan->is_open = false;

        if ($plan->isKhongPhaiLichTatToan()) {
          if (
            now()->gte(Carbon::createFromTimestamp($cycleInfoStart)) &&
            now()->lte(Carbon::createFromTimestamp($cycleInfoStart)->addDays($collectDebtShare->contract_intervals)->endOfDay())
          ) {
            $plan->is_open = true;
          }
        }

        if ($plan->isLichTatToan()) {
          if (now()->gte($plan->time_start_as_date)) {
            $plan->is_open = true;
          }
        }

        $plan->currency = $collectDebtShare->getCurrencyShared();
        return $plan;
      });

      $returnData[] = [
        'statistic' => [
          [
            'label' => 'total_original_amount_collect',
            'nme' => 'Tổng nợ gốc đã thu',
            'value' => $totalNoGocDaThu
          ],

          [
            'label' => 'total_fee_amount_collect',
            'nme' => 'Tổng phí đã thu',
            'value' => $tongPhiDaThu
          ]
        ],
        'schedules' => $periodOfSchedules,
        'is_collapse' => (int) $periodOfSchedules->contains('is_open', '=', true),
        'cycle_number' => $periodOfSchedules->first()->cycle_number,
        'cycle_info' => $periodOfSchedules->first()->cycle_info,
        'currency' => $collectDebtShare->getCurrencyShared()
      ];
    });

    $isDuyNhapMotCollapse = $returnData->where('is_collapse', 1)->count();
    if ($isDuyNhapMotCollapse > 1) {
      $returnData = $returnData->transform(function ($item) {
        if ($item['is_collapse'] == 1 && !$this->hasCollapse) {
          $this->hasCollapse = true;
          $item['is_collapse'] = 1;
        }else {
          $item['is_collapse'] = 0;
        }
  
        return $item;
      });
    }

    // Hiển thị dữ liệu hôm nay
    if (!empty($request->json('data.view_today')) && $collectDebtShare->isHopDongShareTrichKy()) {
      
      $isKhongCoGroupNaoCollapse = $returnData->every('is_collapse', '=', 0);
    
      if ($isKhongCoGroupNaoCollapse) {

        $returnData = $returnData->transform(function ($item, $index) {
          if ($index == 0) {
            $item['is_collapse'] = 1;
          }

          return $item;
        });
      }

      $groupActive = $returnData->where('is_collapse', '=', 1)->first();
      
      $lichPhaiThuKhongThuocActiveGroup = $returnData->filter(function ($item) {
        return $item['is_collapse'] == 0;
      });


      if ($lichPhaiThuKhongThuocActiveGroup->isNotEmpty()) {
        foreach ($lichPhaiThuKhongThuocActiveGroup as $groupItem) {
          foreach ($groupItem['schedules'] as $p) {
            if ($p->isLichThuMoi() && $p->time_start_as_date->lte(now()) && $p->rundate_as_date->lte(now())) {
              $replicate = $p->replicate();

              $cycleInfo = json_decode($replicate->cycle_info, true);

              $replicate->plan_type_name = sprintf(
                '%s <br><i class="text-primary">(Kỳ: %s)</i>',
                $replicate->plan_type_name,
                Carbon::createFromFormat('d/m/Y H:i', $cycleInfo['time_start_as_date'])->format('d.m')
              );

              $replicate->id = $p->id;

              $groupActive['schedules'][] = $replicate;
            }
          }
        }

        $returnData = $lichPhaiThuKhongThuocActiveGroup->push($groupActive)->sortBy('cycle_number')->values();
      }
    }

		if (!empty($request->json('data.is_hidden_crossoff_plan'))) {
			$returnData = $returnData->transform(function ($item) {
				$item['schedules'] = $item['schedules']->filter(function (CollectDebtSchedule $plan, $index) {
					return now()->isSameDay($plan->rundate_as_date) || now()->isSameDay($plan->time_end_as_date);
				})->values();

				return $item;
			});
		}

    return $returnData;
  }
} // End class