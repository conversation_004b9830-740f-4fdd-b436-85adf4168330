<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;

class JobSendMail extends Model
{
  const TYPE_THONG_BAO_QUA_HAN_CAP_1 = 1;
  const TYPE_THONG_BAO_QUA_HAN_CAP_2 = 2;
  const TYPE_THONG_BAO_QUA_HAN_CAP_3 = 3;
  const TYPE_HOP_DONG_QUA_HAN = 4;
  const TYPE_HOP_DONG_CHAM_KY = 5;
  const TYPE_NHAC_NO_TRUOC_1_NGAY = 6;

  const STT_DA_GUI = 1;
  const STT_CHUA_GUI = 0;
  
  protected $table      = 'debt_recovery_contract_job_send_mail';
  public $incrementing  = false;
  public $timestamps     = false;

  protected $guarded = [];

  public function contract() {
    return $this->belongsTo(CollectDebtGuide::class, 'contract_code', 'contract_code');
  }



  public function isQuaHanCap1(): bool {
    return $this->type == self::TYPE_THONG_BAO_QUA_HAN_CAP_1;
  }

  public function isQuaHanCap2(): bool {
    return $this->type == self::TYPE_THONG_BAO_QUA_HAN_CAP_2;
  }

  public function isQuaHanCap3(): bool {
    return $this->type == self::TYPE_THONG_BAO_QUA_HAN_CAP_3;
  }
} // End class
