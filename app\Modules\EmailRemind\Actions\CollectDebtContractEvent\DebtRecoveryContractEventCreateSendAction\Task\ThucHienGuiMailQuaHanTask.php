<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task;

use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\ExecuteSendEmailAction\ExecuteSendEmailAction;
use Exception;

class ThucHienGuiMailQuaHanTask
{
	public function run(CollectDebtContractEvent $collectDebtContractEvent, array $listEmailCc = [], $levelQuaHan = 1)
	{
		mylog([
			'Mail Qua Han Cap:' => sprintf('Cap %s', $levelQuaHan),
			'ContractCode' => $collectDebtContractEvent->contract_code
		]);

		if ($levelQuaHan == 1 || $levelQuaHan == 2) {
			$listEmailCCThem = config('collect_debt_email_remind_config.mail.NHAC_NO_THANH_TOAN.cc_all');
			
			foreach ($listEmailCCThem as $ccItem) {
				$listEmailCc[] = $ccItem;
			}
		}

		$subject = sprintf(
			'NEXTLEND THÔNG BÁO KHẨN TỚI QUÝ KHÁCH %s - HỘ KINH DOANH %s CHẬM THANH TOÁN TIỀN',
			$collectDebtContractEvent->getNguoiDaiDien('fullname'),
			$collectDebtContractEvent->getNguoiDaiDien(),
		);

		$sendMailResult = app(ExecuteSendEmailAction::class)->run(
			$subject,
			$collectDebtContractEvent->content,
			config('collect_debt_email_remind_config.mail.NHAC_NO_THANH_TOAN.sender'),
			$collectDebtContractEvent->getEmailKhachVay(),
			$listEmailCc,
			$collectDebtContractEvent->category_care_code,
			sprintf('%s|QHC%s-%s', $collectDebtContractEvent->contract_code, $levelQuaHan, time())
		);

		if (empty($sendMailResult['id'])) {
			mylog(['Loi gui mail' => $sendMailResult]);
			throw new Exception('Loi gui mail qua han cap: ' . $levelQuaHan);
		}

		return $sendMailResult;
	}
} // End class