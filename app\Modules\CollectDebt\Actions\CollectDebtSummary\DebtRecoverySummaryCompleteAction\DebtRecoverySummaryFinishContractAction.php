<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoverySummaryFinishContractAction
{
  public $hopDongDaTatToan = [];

  public function run(Request $request)
  {
    $collectDebtSummaries = CollectDebtSummary::query()
                                              ->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
																							
																							->whereColumn('total_amount_paid', '>=', 'contract_amount')
																							// phi qh da thanh toan + phi qh da giam >= phi qh da sinh ra
																							->whereRaw('fee_overdue_paid + fee_overdue_reduction >= fee_overdue')

																							// phi ck da thanh toan + phi ck da giam >= phi ck da sinh ra
																							->whereRaw('fee_overdue_cycle_reduction + fee_overdue_cycle_paid >= fee_overdue_cycle')
                                              ->orderByRaw('time_updated DESC')
                                              ->limit(20)
                                              ->get();
   
		if ($collectDebtSummaries->isEmpty()) {
			mylog(['khong co thong tin hd can tat toan' => 'ok']);
      return;
    }

		
    $collectDebtSummaries->map(function (CollectDebtSummary $collectDebtSummary) {
			mylog(['Dang xu ly cho HD' => $collectDebtSummary->contract_code]);
      try {
        $summaryTatToan = app(DebtRecoverySummaryCompleteAction::class)->run($collectDebtSummary);
				mylog(['Ket qua sau khi xu ly la' => $summaryTatToan]);
        $this->hopDongDaTatToan[] = $summaryTatToan->contract_code;
      }catch (\Throwable $th){
				@TelegramAlert::sendCancelMpos('Loi tat toan: ' . Helper::traceError($th));
				mylog(['Loi' => Helper::traceError($th)]);
				// Neu loi, tru di 1 ngay de uu tien cho HD khac
				CollectDebtSummary::query()->where('id', $collectDebtSummary->id)->update([
					'time_updated' => time()
				]);
      }
    });

    return $this->hopDongDaTatToan;
  } 
} // End class
