# Đ<PERSON><PERSON> tượng sử dụng hệ thống

Hệ thống thu hồi nợ phục vụ nhiều nhóm người dùng khác nhau trong quy trình xử lý khoản vay, mỗi nhóm có mục tiêu và hành vi sử dụng riêng.

---

## 1. Nh<PERSON> viên vận hành hệ thống

**Vai trò:**
- Quản lý dữ liệu hồ sơ, cấu hình các logic thu hồi
- <PERSON> dõi tiến trình job, log lỗi, điều chỉnh thông số xử lý
- <PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống chạy đúng nghiệp vụ và đủ tải

**Tương tác hệ thống:**
- Chủ yếu qua giao diện web nội bộ
- C<PERSON> quyền truy cập sâu vào hệ thống, log, b<PERSON><PERSON> c<PERSON>o

**Đặc điểm:**
- <PERSON><PERSON> kiến thức nghiệp vụ và hiểu cơ bản về kỹ thuật
- <PERSON><PERSON><PERSON> hệ thống ổn định, c<PERSON> cảnh báo khi lỗi

---

## 2. Nhân viên telesales / thu hồi

**Vai trò:**
- Tiếp cận danh sách khách hàng quá hạn
- Thực hiện gọi điện, nhắc nhở, ghi nhận kết quả
- Cập nhật trạng thái thanh toán, hứa trả, lý do từ chối, v.v...

**Tương tác hệ thống:**
- Qua giao diện web hoặc app nội bộ
- Nhận danh sách phân bổ tự động từ hệ thống
- Cần truy cập nhanh, không bị delay khi thao tác

**Đặc điểm:**
- Không chuyên sâu kỹ thuật
- Cần UI/UX đơn giản, thao tác nhanh
- Rất nhạy cảm với hiệu suất hệ thống (bị chậm là mất khách)

---

## 3. Khách hàng cuối (người vay)

**Vai trò:**
- Nhận thông báo về tình trạng khoản vay
- Xem chi tiết khoản nợ, lãi/phí, lịch sử thanh toán
- Có thể tương tác để cập nhật thông tin, xác nhận, hoặc yêu cầu hỗ trợ

**Tương tác hệ thống:**
- Thông qua app mobile (nếu có), tin nhắn SMS, hoặc cổng tra cứu online
- Không truy cập trực tiếp hệ thống thu hồi nội bộ

**Đặc điểm:**
- Không có kiến thức kỹ thuật
- Giao diện phải dễ hiểu, thông tin minh bạch
- Tương tác gián tiếp nhưng có ảnh hưởng lớn đến hiệu quả thu hồi

---

## Ghi chú thêm

- Hệ thống cần có **phân quyền chi tiết** để giới hạn quyền truy cập dữ liệu theo vai trò
- Có thể mở rộng trong tương lai với **đối tác xử lý nợ ngoài (đơn vị pháp lý)** hoặc **hệ thống CRM/tổng đài**
