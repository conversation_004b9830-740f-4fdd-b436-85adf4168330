<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryRequestCreditRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.partner_request_id' => ['required', 'string', 'max:50'],
      'data.partner_transaction_id' => ['nullable','string', 'max:50'],
      'data.amount_payment' => ['required', 'numeric', 'min:0'],
      'data.fee' => ['required', 'numeric', 'min:0'],
      'data.checked_by' => ['nullable', 'string', 'max:255'],
    ];
  }
} // End class
