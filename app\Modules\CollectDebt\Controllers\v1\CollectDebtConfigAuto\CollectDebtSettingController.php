<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtConfigAuto;

use Exception;
use App\Lib\Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Modules\CollectDebt\Enums\CacheEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtSetting;
use App\Modules\CollectDebt\Requests\v1\CollectDebtConfigAuto\DebtRecoveryCreateSettingRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtConfigAuto\DebtRecoveryUpdateSettingRequest;

class CollectDebtSettingController extends Controller
{
	public function allSetting(Request $request)
	{
		try {
      $collectDebtSettings = CollectDebtSetting::latest('id')->get();
			return $this->successResponse($collectDebtSettings->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function createSetting(DebtRecoveryCreateSettingRequest $request)
	{
    try {
      $params = $request->json('data');
      $collectDebtSetting = CollectDebtSetting::forceCreate($params);
			return $this->successResponse($collectDebtSetting->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function updateSetting(DebtRecoveryUpdateSettingRequest $request)
	{
    $currentSetting = CollectDebtSetting::find($request->json('data.id'));
    $params = $request->json('data');
    throw_if(!$currentSetting, new Exception('Không tìm thấy thông tin cấu hình'));

		try {
      $currentUpdatedBy = json_decode($currentSetting->updated_by, true) ?? [];
      $currentUpdatedBy[] = $params['updated_by'];
      $params['updated_by'] = json_encode($currentUpdatedBy, JSON_UNESCAPED_UNICODE);
      
      $currentSetting->forceFill($params)->update();
      $currentSetting->refresh();

			Cache::forget(CacheEnum::LIST_CAU_HINH_FIRE_JOB);
      return $this->successResponse($currentSetting->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function findSetting(Request $request)
	{
		try {
			$currentSetting = CollectDebtSetting::find($request->json('data.id'));
    	throw_if(!$currentSetting, new Exception('Không tìm thấy thông tin cấu hình'));

      return $this->successResponse($currentSetting->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
