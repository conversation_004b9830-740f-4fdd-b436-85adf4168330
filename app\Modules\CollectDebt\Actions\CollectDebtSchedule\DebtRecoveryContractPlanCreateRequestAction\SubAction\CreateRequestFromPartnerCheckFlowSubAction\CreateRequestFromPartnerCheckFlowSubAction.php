<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromPartnerCheckFlowSubAction;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanCreateRqRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromPartnerCheckFlowSubAction\Task\SendCreateCollectDebtRequestTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromPartnerCheckFlowSubAction\Task\GetLichPhuHoiTaoYeuCauLuongBiDongTask;

class CreateRequestFromPartnerCheckFlowSubAction
{
  public function run(DebtRecoveryContractPlanCreateRqRequest $request, $exceptPlanProcessingIds=[])
  {
    $collectDebtSchedulesPassedRules = app(GetLichPhuHoiTaoYeuCauLuongBiDongTask::class)->run($request, $exceptPlanProcessingIds);

    if ($collectDebtSchedulesPassedRules->isEmpty()) {
      // Không có lịch phù hợp mặc dù đã thu được tiền rồi --> Cần xử lý đi vào luồng hoàn
    }


    if ($collectDebtSchedulesPassedRules->isNotEmpty()) {
      // Có lịch thu thì cần tạo 1 y/c đại diện cho những lịch này
      $collectDebtPartner = CollectDebtPartner::find($request->json('data.partner_id'));
      
      if ( $collectDebtPartner->isPartnerTrangThaiCuoi() ) {
        if ($collectDebtPartner->hasRequest()) {
          return $collectDebtPartner->load(['collectDebtRequest']);
        }

        throw new Exception('Bản ghi công nợ đã ở trạng thái cuối và tạo yêu cầu thất bại', 500);
      }
      
      // Thực hiện tạo 1 yêu cầu đại diện cho các lịch và gọi API tạo lịch
      $collectDebtPartner = app(SendCreateCollectDebtRequestTask::class)->run(
        $collectDebtSchedulesPassedRules, 
        $collectDebtPartner,
        $request->getAmountReceiverSuccess()
      );

      return Collection::make([$collectDebtPartner->collectDebtRequest]);
    }
  }
} // End class