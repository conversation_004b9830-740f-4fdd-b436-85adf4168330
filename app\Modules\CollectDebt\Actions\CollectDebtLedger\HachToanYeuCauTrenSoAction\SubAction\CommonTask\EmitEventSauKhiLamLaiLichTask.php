<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class EmitEventSauKhiLamLaiLichTask
{
	public array $paramUpdate = [];

	public function run(bool $isCacYeuCauDaVeTrangThaiCuoi = false, bool $isSoCuoiCung = false, array $summaryData = []): array
	{
		$this->paramUpdate = [
			'status' => CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN,

			'status_plan' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,

			'status_summary' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
			'status_report' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
			'status_excess' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
			'status_contract' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT,
		];

		if ($isCacYeuCauDaVeTrangThaiCuoi && $isSoCuoiCung) {
			$phiChamKy = collect($summaryData)->where('label', CollectDebtEnum::METADATA_PHI_CHAM_KY)->sum('value');
			$phiQuaHan = collect($summaryData)->where('label', CollectDebtEnum::METADATA_PHI_QUA_HAN)->sum('value');

			if (!empty($phiChamKy) || !empty($phiQuaHan)) {
				$this->paramUpdate['status_email'] = CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT;
				$this->paramUpdate['status_sms'] = CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT;
			}
		}

		return $this->paramUpdate;
	}
} // End class
