<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetByContractCodeAction;

use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Enums\CollectDebtGuideSampleDataEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Requests\v1\CollectDebtGuide\DebtRecoveryContractGuideGetById;
use Exception;

class DebtRecoveryContractGuideGetByContractCodeAction
{
	/**
	 * API lấy chi tiết HĐ (và không dùng trường status do collectDebtShare appends ra)
	 *
	 * @param string $contract_code	[Mã hợp đồng]
	 * @return CollectDebtShare
	 */
  public function run(string $contract_code = ''): CollectDebtShare
  {
    $collectDebtShare = CollectDebtShare::query()
                                        ->with(['configPauseJob' => function ($query) {
                                          return $query->wherePaused();
                                        }])
                                        ->where('contract_code', $contract_code)
                                        ->first();
    throw_if(!$collectDebtShare, new Exception('Không tìm thấy chỉ dẫn'));

    $collectDebtSummary = CollectDebtSummary::where('contract_code', $contract_code)->first();
    $paymentMethods = $collectDebtSummary->getSummaryOtherDataItem('PAYMENT_METHOD');
    $paymentMethodStatus = collect($paymentMethods['data'])->map(function ($pm) {
      return [
        'payment_method_code' => $pm['payment_method_code'],
        'closed' => $pm['closed']
      ];
    });
  
    $collectDebtShare->payment_methods = $paymentMethodStatus;
    $collectDebtShare->time_settlement = $collectDebtSummary->time_settlement;
    $collectDebtShare->currency = $collectDebtSummary->getCurrency();
		$collectDebtShare->contract_status_before = $collectDebtSummary->isHopDongDaTatToan() ? 'DA_TAT_TOAN' : 'CHUA_TAT_TOAN';
		
		$shareContractData = $collectDebtShare->getShareContractData();
		$collectDebtShare->partner_disbursement = $shareContractData['contract_partner']['partner_code'] ?? 'NEXTLEND';
    return $collectDebtShare;
  }
}
