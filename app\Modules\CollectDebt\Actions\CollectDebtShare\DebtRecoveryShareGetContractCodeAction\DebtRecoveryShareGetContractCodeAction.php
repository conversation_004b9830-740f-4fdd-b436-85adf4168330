<?php
namespace App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareGetContractCodeAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class DebtRecoveryShareGetContractCodeAction
{
    public function run($request)
    {

        $debtRecoveryShare = CollectDebtShare::where('contract_code', $request['contract_code'])->get();
        throw_if(!$debtRecoveryShare->isNotEmpty(), new Exception('Không tìm được bản ghi phù hợp'));
        
        $debtRecoveryShare = $debtRecoveryShare->first()->toArray();
        
        return $debtRecoveryShare;
    }
}