<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction;

use Exception;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtDigitalNoti;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class DigitalNotiBuildParamSapDenKySubAction
{
	public function run(CollectDebtSchedule $plan, CollectDebtContractEvent $event)
	{
		return '';
	}
}
