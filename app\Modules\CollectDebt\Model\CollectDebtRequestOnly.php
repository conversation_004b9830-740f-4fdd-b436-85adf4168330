<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CollectDebtRequestOnly extends Model
{
	protected $table 	 = 'debt_recovery_request';
	public $timestamps = false;
	protected $guarded = [];

	public function collectDebtSummary(): BelongsTo
	{
		return $this->belongsTo(CollectDebtSummary::class, 'contract_code', 'contract_code');
	}
} // End class
