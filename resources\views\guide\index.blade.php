@extends('layouts.master')

@section('page_name', 'Danh sách chỉ dẫn')

@section('content')
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                <div class="table-responsive">
                  <table class="table table-hover table-bordered">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Mã HĐ</th>
                        <th>Số tiền vay</th>
                        <th>Số ngày vay</th>
                        <th>Loại trích nợ</th>
                        <th>Chu kỳ trích</th>
                        <th>Xem chi tiết</th>
                      </tr>
                    </thead>

                    <tbody>
                      @forelse ($guides as $guide)
                        <tr>
                          <td>{{ $guide->id }}</td>
                          <td>
                            <span class="d-block font-weight-bold">{{ $guide->contract_code }}</span>
                            <code class="d-block">{{ $guide->time_start_as_vn_date }}</code>
                            <code class="d-block">{{ $guide->time_end_as_vn_date }}</code>
                          </td>
                         
                          <td>{{ \App\Lib\Helper::priceFormat($guide->amount) }}</td>
                          <td>{{ $guide->contract_cycle }}</td>
                          <td>{{ $guide->isChiDanHopDongTrichNgay() ? 'Trích ngày' : 'Trích kỳ' }}</td>
                          <td>{{ $guide->contract_intervals }} ngày/lần</td>
                          <td><a target"_blank" href="{{ config('app.url') . '/mock/detail-guide/' . $guide->id }}">Chi tiết</a></td>
                        </tr>
                      @empty 
                        <tr>
                          <td colspan="7">No data</td>
                        </tr>
                      @endforelse
                    </tbody>
                  </table>
                </div>

                 {!! $guides->links() !!}
                </div>
            </div>
        </div>
    </div>
@endsection
