<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhGhiSoSA;

use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use Exception;

class XuLyDieuChinhGhiSoSA
{
  public $phiQuaHan = 0;

  public $phiChamKy = 0;

  public $soTienHachToanThucTe = 0;

  /**
   * [
      'total_amount_debit_success'  => 0, // Tổng tiền thu được ghi vào sổ  -> DONE
      'total_amount_paid'           => 0, // Số tiền cấn trừ thành công cho lịch -> DONE
      'amount_paid'                 => 0,  // Số thiền thu thành công cho lịch thu gốc -> DONE
      'fee_overdue_cycle_paid'      => 0, // Phí chậm kỳ thu được
      'fee_overdue_paid'            => 0, // Phí quá hạn thu được
      'fee_overdue_reduction'       => 0, // giảm phí QH được giảm 
      'fee_overdue_cycle_reduction' => 0, // giảm phí CK được giảm 
      'fee_overdue'                 => 0, // phí quá hạn -> DONE
      'fee_overdue_cycle'           => 0, // phí chậm kỳ -> DONE
      'total_amount_receiver'       => $paramInsert['amount'], // số tiền nhận thực thế, tạm thời để bằng số tiền ghi sổ -> DONE
      'total_amount_excess_revenue' => 0, // số tiền thu thừa trên sổ -> DONE
    ],
   *
   * @param CollectDebtRequestAdjustment $ra
   * @param CollectDebtRequest $collectDebtRequest
   * @param array $metaData
   * @return void
   */
  public function run(CollectDebtRequestAdjustment $ra, CollectDebtRequest $collectDebtRequest, array $metaData = [])
  {
    $ledgers = CollectDebtLedger::query()
      ->where('request_id', $collectDebtRequest->id)
      ->get();

    $collectDebtLedger = $ledgers->first(function (CollectDebtLedger $lg) {
      return !$lg->isSoDoiTacBaoMuon();
    });

    $this->soTienHachToanThucTe = $ra->getSoTienTrichThanhCongThucTe();

    $summary = $collectDebtLedger->getOtherDataItem('SUMMARY')['data'];
    $offsetDebts = $collectDebtLedger->getOtherDataItem('OFFSET_DEBT')['data'];
    $offsetDebtsArray = collect($offsetDebts)->keyBy('plan_id')->toArray(); 
    
    $plans = app(GetLichThuByIdsSubAction::class)->run($collectDebtLedger->plan_ids);
    $plans = app(PlanSortableCollectionByRule::class)->sortCollection($plans);
    
    $plans->map(function (CollectDebtSchedule $plan) use ($offsetDebtsArray) {

      $soTienCanTruMLL = min($this->soTienHachToanThucTe, $offsetDebtsArray[$plan->id]['amount_offset_debt']);
      $this->soTienHachToanThucTe -= $soTienCanTruMLL;

      $plan->soTienCanTruMLL = $soTienCanTruMLL;
      $plan->soTienConThieu = $soTienCanTruMLL - $offsetDebtsArray[$plan->id]['amount_offset_debt'];
      return $plan;
    });

    // total_amount_paid: lấy số tiền âm để trừ ngược
    $soTienThuDuocThanhCongChoLichThuGoc = $plans->sum(function (CollectDebtSchedule $plan) {
      if ($plan->isLichThuGoc()) {
        return $plan->soTienConThieu;
      }

      return 0;
    }); 

    // amount_paid: So tien thu thanh cong cho lich thu goc
    $soTienThuThucTeChoLichThuGoc = $plans->sum(function (CollectDebtSchedule $plan) {
      if ($plan->isLichThuGoc()) {
        return $plan->soTienConThieu;
      }

      return 0;
    });

    $phiChamKyThuDuoc = $plans->sum(function (CollectDebtSchedule $plan) {
      if ($plan->isLichThuPhiChamKy()) {
        return $plan->soTienConThieu;
      }

      return 0;
    });

    $phiQuaHanThuDuoc = $plans->sum(function (CollectDebtSchedule $plan) {
      if ($plan->isLichThuPhiQuaHan()) {
        return $plan->soTienConThieu;
      }

      return 0;
    });

    $tongTienGhiSo = $ra->getSoTienTrichThanhCongThucTe() - $summary['total_amount_debit_success'];

    $summaryOtherData = [
      // Tổng tiền thu được ghi vào sổ = Số tiền trích thực tế - Số tiền ghi sổ cũ
      'total_amount_debit_success' => $tongTienGhiSo,

      // Số tiền cấn trừ cho lịch
      'total_amount_paid' => $soTienThuDuocThanhCongChoLichThuGoc,

      // số tiền thu thành công cho lịch thu gốc
      'amount_paid' => $soTienThuThucTeChoLichThuGoc,


      // phí ck thu được
      'fee_overdue_cycle_paid' => $phiChamKyThuDuoc,

      // phí qh thu được
      'fee_overdue_paid' => $phiQuaHanThuDuoc,

      // giảm phí qh
      'fee_overdue_reduction' => 0,

      // giảm phí ck
      'fee_overdue_cycle_reduction' => 0,

      // **phí ck, phí qh được sinh ra  -> được xử lý bên dưới
      
      // tổng tiền nhận: Số tiền ghi sổ LLL - tổng tiền nhận cũ
      'total_amount_receiver' => $tongTienGhiSo,

      // số tiền thu thừa
      'total_amount_excess_revenue' => 0 - $summary['total_amount_excess_revenue']
    ];

  /* --------------------------- Xử lý phí chậm kỳ --------------------------- */
    $phiChamKyCu = $summary['fee_overdue_cycle'];
    $phiChamKyMoi = collect($metaData)->where('label', CollectDebtEnum::METADATA_PHI_CHAM_KY)->sum('value');
    $summaryOtherData['fee_overdue_cycle'] = $phiChamKyMoi - $phiChamKyCu;
  /* --------------------------- Xử lý phí quá hạn --------------------------- */
    $phiQuaHanCu = $summary['fee_overdue'];
    $phiQuaHanMoi = collect($metaData)->where('label', CollectDebtEnum::METADATA_PHI_QUA_HAN)->sum('value');
    $summaryOtherData['fee_overdue'] = $phiQuaHanMoi - $phiQuaHanCu;

  /* --------------------------- Xử lý số liệu giảm phí (Cập nhật sau) --------------------------- */
    $ledgerOtherData = $collectDebtLedger->getLedgerOtherData();
    $ledgerOtherData[] = [
      'type' => 'REQUEST_ADJUSTMENT',
      'data' => [
        'summary_adjustment' => $summaryOtherData
      ],
      'note' => sprintf(
        'Điều chỉnh số tiền từ %s thành %s', 
        Helper::priceFormat($collectDebtLedger->amount), 
        Helper::priceFormat($ra->getSoTienTrichThanhCongThucTe())
      ),
      'time_modified' => time()
    ];

    $collectDebtLedger->amount = $ra->getSoTienTrichThanhCongThucTe();
    $collectDebtLedger->other_data = json_encode($ledgerOtherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
    $resultSaved = $collectDebtLedger->save();

		if (!$resultSaved) {
			mylog(['Loi dieu chinh so' => $resultSaved]);
			throw new Exception('Loi dieu chinh so');
		}
		
    return $collectDebtLedger;
  } // End method
} // End clas
