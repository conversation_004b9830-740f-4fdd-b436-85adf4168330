<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtLog;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\CollectDebtLog\DebtRecoveryLogSearchDataAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class CollectDebtLogController extends Controller
{
	public function DebtRecoveryLogSearchData(Request $request)
	{
		try {
			$logs = app(DebtRecoveryLogSearchDataAction::class)->run($request);
			$resouce = new \App\Modules\CollectDebt\Resources\CollectDebtLogResourceCollection($logs);
			$response = $resouce->toResponse($request)->getData(true);


			$summaryIds = $logs->pluck('reference_id')->toArray();
			
			$collectDebtSummaries = CollectDebtSummary::query()
																								->whereIn('id', $summaryIds)
																								->select(['id', 'contract_code'])
																								->get()
																								->keyBy('id');

			$response['mapping'] = [
				'service_code' => [
					CollectDebtEnum::RL_DICH_VU_HOAN_PHI => 'Hoàn phí',
					CollectDebtEnum::RL_DICH_VU_HOAN_THU_THUA => 'Hoàn thu thừa',
					CollectDebtEnum::RL_DICH_VU_TRU_TIEN_VAO_VI => 'Trừ tiền trong ví',
					CollectDebtEnum::RL_DICH_VU_KIEM_TRA_TRICH_NGAY => 'Kiểm tra lệnh trích ngay đã hết hạn',
				],

				'status' => [
					CollectDebtEnum::RL_STT_MOI_TAO => 'Mới tạo',
					CollectDebtEnum::RL_STT_DANG_XU_LY => 'Đang xử lý',
					CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG => 'Thành công',
					CollectDebtEnum::RL_STT_DA_XU_LY_THAT_BAI => 'Thất bại',
				]
			];

			foreach ($response['data'] as &$l) {
				$l['contract_code'] = '';
				
				if ($collectDebtSummaries->has($l['reference_id'])) {
					$l['contract_code'] = $collectDebtSummaries->get($l['reference_id'])->contract_code;
				}
			}

			return $this->successResponse($response, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryLogGetById(Request $request)
	{
		try {
			$log = CollectDebtLog::query()->find($request->json('data.id'));
			return $this->successResponse($log->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
