<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestReduceFeeAction;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class DebtRecoveryRequestReduceFeeSearchDataAction
{
	public function run(Request $request)
	{
		$collectDebtRequestReduceFee = CollectDebtRequest::query()
																										 ->whereHas('collectDebtRequestActions', function ($query) {
																												return $query->where('type', CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI);
																										});

		$contractCode = $request->json('data.filter.contract_code');

		if (!empty($contractCode)) {
			$collectDebtRequestReduceFee->where('contract_code', trim($contractCode));
		}

		$status = $request->json('data.filter.status');

		if (!empty($status)) {
			switch ($status) {
				case CollectDebtEnum::REQUEST_STT_DA_DUYET_GIAM_PHI_CAP_1: 
					$collectDebtRequestReduceFee->whereHas('collectDebtRequestActions', function ($query) {
						return $query->where('type', CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI)
												 ->where('action_code', 'APPROVE1');
					});
					break;

				case CollectDebtEnum::REQUEST_STT_DA_DUYET_GIAM_PHI_CAP_2: 
					$collectDebtRequestReduceFee->whereHas('collectDebtRequestActions', function ($query) {
						return $query->where('type', CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI)
												 ->where('action_code', 'APPROVE2');
					});
					break;

				default: 
					$collectDebtRequestReduceFee->where('status', trim($status));
					break;

			}
			
		}

		$profileIds = $request->json('data.filter.profile_ids');

		if (!empty($profileIds)) {
			$collectDebtRequestReduceFee->whereIn('profile_id', $profileIds);
		}

		$timeCreateFrom = $request->json('data.filter.time_created_from');
		if (!empty($timeCreateFrom)) {
			$t = Carbon::createFromFormat('d-m-Y', $timeCreateFrom)->startOfDay()->timestamp;
			$collectDebtRequestReduceFee->where('time_created', '>=', $t);
		}

		$timeCreateTo = $request->json('data.filter.time_created_to');
		if (!empty($timeCreateTo)) {
			$t = Carbon::createFromFormat('d-m-Y', $timeCreateTo)->endOfDay()->timestamp;
			$collectDebtRequestReduceFee->where('time_created', '<=', $t);
		}

		$collectDebtRequestReduceFee = $collectDebtRequestReduceFee->orderBy('id', 'DESC')
																															 ->paginate( 
																																$request->json('data.limit', 10), 
																																['*'], 
																																'page', 
																																$request->json('data.page', 1) 
																															);
														
		$collection = $collectDebtRequestReduceFee->getCollection();

		$collection = $collection->transform(function (CollectDebtRequest $collectDebRequest) {
			$can[] = CollectDebtEnum::CAN_VIEW_DETAIL_REQUEST;

			if ($collectDebRequest->isTrichTayGiamPhiVaChuaDuyetBuoc1() && $collectDebRequest->isTrichNgay() != 'YES') {
				$can[] = CollectDebtEnum::CAN_APPROVE_REDUCE_FEE_STEP_1;
				$can[] = CollectDebtEnum::CAN_CANCEL_MANUAL_REQUEST;
			}

			if ($collectDebRequest->isTrichTayGiamPhiVaChuaDuyetBuoc2() && $collectDebRequest->isTrichNgay() != 'YES') {
				$can[] = CollectDebtEnum::CAN_APPROVE_REDUCE_FEE_STEP_2;
				$can[] = CollectDebtEnum::CAN_CANCEL_MANUAL_REQUEST;
			}

			if ($collectDebRequest->isRecorded()) {
				$can[] = CollectDebtEnum::CAN_VIEW_REQUEST_ON_LEDGER;
			}


			$collectDebRequest->can = $can;

			$collectDebRequest->total_amount_paid = 0;
			$collectDebRequest->total_debt_excess = 0;


			$collectDebRequest->is_trich_ngay = false;
			if ($collectDebRequest->isManualDebt() && $collectDebRequest->isTrichNgay() == 'YES') {
				$collectDebRequest->is_trich_ngay = true;
			}

			return $collectDebRequest;
		});

		$collectDebtRequestReduceFee->setCollection($collection);

		return $collectDebtRequestReduceFee;
	}
} // End class