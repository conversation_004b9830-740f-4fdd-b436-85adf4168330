<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Model\CollectDebtLedger;

class PushSoTienTrichNoThanhCongTheoKenhTask
{
  public function run(CollectDebtLedger $ledger, string $paymentMethodCode, float $soTienTrichThanhCongTheoKenh)
  {
    $ledgerOtherData = $ledger->getLedgerOtherData();

    $typeOrtherIndex = $ledger->getOtherDataTypeIndex('OTHER');

    $otherDataItem = $ledger->getOtherDataItem('OTHER');

    $isChuCoKenhThanhToan = collect($otherDataItem['data'])->where('information_code', 'PAYMENT')
                                                            ->where('payment_method_code', $paymentMethodCode)
                                                            ->count();
    if ($isChuCoKenhThanhToan == 0) {
      $otherDataItem['data'][] = [
        'information_code' => 'PAYMENT',
        'payment_method_code' => $paymentMethodCode,
        'amount' => $soTienTrichThanhCongTheoKenh // Số tiền đã trích nợ thành công theo kênh
      ];
  
      $ledgerOtherData[$typeOrtherIndex] = $otherDataItem;
      CollectDebtLedger::where('id', $ledger->id)
                       ->update(['other_data' => json_encode($ledgerOtherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)]);
    }

    return $ledger;
  }
}
