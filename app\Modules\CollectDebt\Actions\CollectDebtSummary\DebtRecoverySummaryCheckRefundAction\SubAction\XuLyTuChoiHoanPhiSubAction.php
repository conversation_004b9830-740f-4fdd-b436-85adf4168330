<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\SubAction;

use DB;
use Exception;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class XuLyTuChoiHoanPhiSubAction
{
	public function run(CollectDebtLog $collectDebtLog, CollectDebtSummary $collectDebtSummary)
	{
		$soTienYeuCauHoan = $collectDebtLog->getSoTienYeuCauHoan();
		$phiCkMuonHoan = $collectDebtLog->getPhiChamKyMuonHoan();
		$phiQhMuonHoan = $collectDebtLog->getPhiQuaHanMuonHoan();

		mylog([
			'soTienYeuCauHoan' => $soTienYeuCauHoan,
			'phiCkMuonHoan' => $phiCkMuonHoan,
			'phiQhMuonHoan' => $phiQhMuonHoan,
		]);

		$otherData = $collectDebtSummary->getSummaryOtherData();
		$otherData[] = [
			'type' => 'REFUND',
			'data' => [
				'soTienYeuCauHoan' => $soTienYeuCauHoan
			],
			'time_modified' => now()->timestamp,
			'note' => 'Từ chối hoàn phí'
		];

		/**
		 * Các công việc cần làm:
		 * 	1. giảm số tiền trên amount_refunding
		 * 	2. giảm số tiền phí ck muốn hoàn trên tổng hợp xuống
		 * 	3. giảm số tiền phí qh muốn hoàn trên tổng hợp xuống
		 */
		$r = $collectDebtSummary->update([
			'amount_refunding' => DB::raw('amount_refunding - ' . $soTienYeuCauHoan),
			'other_data' => json_encode($otherData),
			'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO
		]);
		
		if (!$r) {
			mylog(['Loi cap nhat summary' => 'Loi']);
			throw new Exception('Loi cap nhat summary');
		}

		return $collectDebtSummary;
	}
}
