<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtContractEvent;

use App\Lib\Helper;

use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class CollectDebtContractEventController extends Controller
{
	public function deleteOldEvent(Request $request)
	{
		$timeline = now()->subDays(7)->timestamp;
		try {
			$deleted = CollectDebtContractEvent::query()->where('time_created', '<', $timeline)->delete();
			
			if (!$deleted) {
				mylog(['[LOI DELETE]' => 'khong the delete cac event cu']);
			}

			mylog(['So ban ghi event cu bi delete' => $deleted]);
			
			return $this->successResponse(['result' => $deleted], $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
