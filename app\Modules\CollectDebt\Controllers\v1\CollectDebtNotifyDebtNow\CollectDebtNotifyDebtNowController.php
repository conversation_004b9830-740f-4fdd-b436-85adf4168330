<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtNotifyDebtNow;

use App\Lib\Helper;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowCreateAction\CollectDebtNotifyDebtNowCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction\CollectDebtNotifyDebtNowSendMailAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction\SendNotifyViaChannelsAction;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Requests\v1\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowRequest;
use Illuminate\Http\Request;

class CollectDebtNotifyDebtNowController extends Controller
{
    public function createNotifyDebtNow(CollectDebtNotifyDebtNowRequest $request)
    {
        try {
            $collectNotifyDebtNow = app(CollectDebtNotifyDebtNowCreateAction::class)->run($request);
            return $this->successResponse($collectNotifyDebtNow->toArray(), $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }

    }

    public function jobNotifyDebtNow(Request $request)
    {
        try {
            $result = app(SendNotifyViaChannelsAction::class)->run();
            return $this->successResponse($result, $request);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getCode(), Helper::traceError($th));
        }

    }


}