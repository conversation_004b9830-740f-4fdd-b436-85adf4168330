<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtRequest;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Contracts\Validation\Rule;

class HopDongKhongCoLichTuDongDangChayRule implements Rule
{
  /**
   * Create a new rule instance.
   *
   * @return void
   */
  public function __construct()
  {
    //
  }

  /**
   * Tất cả yêu cầu của HĐ đang chạy đã về trạng thái cuối                    
   *
   * @param  string  $contractField
   * @param  mixed  $contractCode
   * @return bool
   */
  public function passes($contractField, $contractCode)
  {
    $count = CollectDebtRequest::query()
                              ->where('contract_code', $contractCode)
                              ->whereNotIn('status', [
                                CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
                                CollectDebtEnum::REQUEST_STT_TU_CHOI,
																CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA
                              ])
                              ->count();
  
    if ($count > 0) {
      return false;
    }

    return true;
  }

  /**
   * Get the validation error message.
   *
   * @return string
   */
  public function message()
  {
    return 'Hợp đồng này vẫn có yêu cầu tự động đang chạy, bạn cần phải hủy các yêu cầu thu tự động đó đi';
  }
}
