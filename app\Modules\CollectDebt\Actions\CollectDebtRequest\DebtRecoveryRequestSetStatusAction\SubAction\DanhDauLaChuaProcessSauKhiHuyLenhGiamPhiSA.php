<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\SubAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtAction;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class DanhDauLaChuaProcessSauKhiHuyLenhGiamPhiSA
{
	/**
	 * Nếu hủy lệnh giảm phí (created bản ghi action là CANCLED thì bỏ process)
	 *
	 * @return void
	 */
	public function run(CollectDebtAction $collectDebtRequestAction)
	{
		if (
			$collectDebtRequestAction->type == CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI
			&& $collectDebtRequestAction->action_code == CollectDebtEnum::REQUEST_ACTION_CODE_CANCELED
		) {
			$collectDebtRequest = CollectDebtRequest::query()->find($collectDebtRequestAction->request_id);
			throw_if(!$collectDebtRequest, new Exception('Không có thông tin yêu cầu giảm phí'));

			$planIds = $collectDebtRequest->getPlanIds();
			$updatePlanVeChuaProcess = CollectDebtSchedule::query()
																										->where('contract_code', $collectDebtRequest->contract_code)
																										->whereIn('id', $planIds)
																										->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
																										->update([
																											'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY,
																											'time_updated' => now()->timestamp
																										]);

			if (!$updatePlanVeChuaProcess) {
				throw new Exception('Không thể update các lịch của yc giảm phí về CHƯA PROCESS');
			}
		}
	}
} // End class
