<?php

namespace App\Modules\CollectDebt\Model;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Model\JobSendMail;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanScope;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanOtherData;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanStatusable;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanRunDateable;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanPhanLoaiLich;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanPhanLoaiHopDong;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanPaymentGuideable;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanXuLyKetQuaTrichNo;

class CollectDebtSchedule extends Model
{
	use PlanScope,
		PlanStatusable,
		PlanPhanLoaiLich,
		PlanPaymentGuideable,
		PlanXuLyKetQuaTrichNo,
		PlanPhanLoaiHopDong,
		PlanRunDateable,
		PlanOtherData;

	protected $table 	 = 'debt_recovery_contract_plan';
	public $timestamps = false;
	protected $guarded = [];

	protected $appends = [
		'time_start_as_date',
		'time_end_as_date',
		'rundate_as_date',

		'time_start_as_vn_date',
		'time_end_as_vn_date',
		'rundate_as_vn_date',
	];

	/* -------------------- Relationship ----------------- */
	public function pauseContract()
	{
		return $this->hasMany(CollectDebtConfigAuto::class, 'contract_code', 'contract_code');
	}

	public function mails()
	{
		return $this->hasMany(JobSendMail::class, 'plan_id', 'id');
	}

	public function collectDebtShare()
	{
		return $this->belongsTo(CollectDebtShare::class, 'contract_code', 'contract_code');
	}

	public function collectDebtSummary() {
		return $this->belongsTo(CollectDebtSummary::class, 'contract_code', 'contract_code');
	}

	public function collectDebtProcessing() {
		return $this->hasOne(CollectDebtProcessing::class, 'contract_code', 'contract_code');
	}
	
	/* -------------------- Appends ----------------- */
	public function getTimeStartAsDateAttribute()
	{
		return Carbon::createFromTimestamp($this->time_start)->copy()->startOfDay();
	}

	public function getTimeEndAsDateAttribute()
	{
		return Carbon::createFromTimestamp($this->time_end)->copy()->endOfDay();
	}

	public function getRundateAsDateAttribute()
	{
		$runDateHours = 5;
		$runDateMinutes = 0;

		$timeStartHour = (int) date('H', $this->time_start);

		if ($timeStartHour != 0) {
			$runDateHours = $timeStartHour;
			$runDateMinutes = (int) date('i', $this->time_start);
		};

		return Carbon::createFromFormat('Ymd', $this->rundate)->copy()->setTime($runDateHours, $runDateMinutes);
	}

	public function getTimeStartAsVnDateAttribute()
	{
		return Carbon::createFromTimestamp($this->time_start)->format('H:i, d/m/Y');
	}

	public function getTimeEndAsVnDateAttribute()
	{
		return Carbon::createFromTimestamp($this->time_end)->format('H:i, d/m/Y');
	}

	public function getRundateAsVnDateAttribute()
	{
		$runDateHours = 5;
		$runDateMinutes = 0;

		$timeStartHour = (int) date('H', $this->time_start);

		if ($timeStartHour != 0) {
			$runDateHours = $timeStartHour;
			$runDateMinutes = (int) date('i', $this->time_start);
		};

		return Carbon::createFromFormat('Ymd', $this->rundate)->copy()->setTime($runDateHours, $runDateMinutes)->format('H:i, d/m/Y');
	}


	/* -------------------- Method ----------------- */
	public function isThoiDiemHienTaiLonHonRunDate(Carbon $dateRunRealTime): bool
	{
		return $dateRunRealTime->gt($this->rundate_as_date) && !$dateRunRealTime->isSameDay($this->rundate_as_date);
	}

	public function getSoTienConPhaiThanhToan(): float
	{
		return $this->request_amount_debit - $this->success_amount_debit;
	}

	public function getSoTienMoLaiLichPhaiThu(float $soTienCanTruSauKhiDieuChinh=0): float {
		return $this->success_amount_debit - $soTienCanTruSauKhiDieuChinh;
	}

	public function isLichThuNextlend(): bool {
		$this->loadMissing('collectDebtShare');
		return $this->collectDebtShare->partner_code == 'NEXTLEND';
	}

	public function isLichThuTnex(): bool {
		$this->loadMissing('collectDebtShare');
		return $this->collectDebtShare->partner_code == 'TNEX';
	}
} // End class
