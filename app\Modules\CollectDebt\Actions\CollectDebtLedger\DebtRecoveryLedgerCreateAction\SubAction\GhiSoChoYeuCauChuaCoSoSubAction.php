<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Support\Arr;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction\Task\BuildThongTinLichGhiTrenSoTask;
use Illuminate\Database\Eloquent\Collection;

class GhiSoChoYeuCauChuaCoSoSubAction
{
  // CHƯA CÓ SỔ
  public function run(CollectDebtRequest $collectDebtRequest, DebtRecoveryLedgerCreateRequest $request): CollectDebtLedger
  {
    // Bắt điều kiện throw lỗi nếu là thu gốc hoặc thu tự động
    if ($collectDebtRequest->isAutoDebt() || $collectDebtRequest->isTrichNgay() != 'YES') {
      throw_if(!$collectDebtRequest->isFinalPaymentStatus(), new Exception('Yêu cầu thu hồi chưa ở trạng thái cuối. Trạng thái cuối `status_payment` = 3 hoặc 6'));
    }


    if ($collectDebtRequest->isTrichTayGiamPhi()) {
      throw_if(!$collectDebtRequest->isDaDuyetGiamPhiBuoc2(), new Exception('Từ chối thao tác. Lý do: Yêu cầu giảm phí chưa được duyệt bước 2'));
    }

    $collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO;

    $paramInsert = Arr::only($request->json('data'), [
      'profile_id',
      'contract_code',
      'plan_ids',
      'request_id',
      'currency',
      'amount',
      'description',
      'status',
      'time_record',
      'created_by',
      'time_created',
    ]);

		// khởi tạo rỗng, nếu yêu cầu k có lịch -> thu thừa
		if ( empty($collectDebtRequest->plan_ids) ) {
			$plansCollection = Collection::make([]); 
		}
		

		if (!empty($collectDebtRequest->plan_ids)) {
			$plansCollection = app(GetLichThuByIdsSubAction::class)->run($collectDebtRequest->plan_ids);
		}
    
    $paramInsert['plan_ids'] = $plansCollection->implode('id', ',');
		$ledgerOtherDataParams = [
      [
        'type' => 'REQUEST',
        'time_modified' => time(),
        'data' => StandardizedDataFilter::getRequestCompactAttribute($collectDebtRequest),
        'note' => 'Yêu cầu ghi trên sổ'
      ],

      [
        'type' => 'SUMMARY',
        'time_modified' => time(),
        'data' => [
          'total_amount_debit_success'  => 0, // Tổng tiền thu được ghi vào sổ 
          'total_amount_paid'           => 0, // Số tiền cấn trừ thành công cho lịch
          'amount_paid'                 => 0,  // Số thiền thu thành công cho lịch thu gốc
          'fee_overdue_cycle_paid'      => 0, // Phí chậm kỳ thu được
          'fee_overdue_paid'            => 0, // Phí quá hạn thu được
          'fee_overdue_reduction'       => 0, // giảm phí QH được giảm
          'fee_overdue_cycle_reduction' => 0, // giảm phí CK được giảm
          'fee_overdue'                 => 0, // phí quá hạn
          'fee_overdue_cycle'           => 0, // phí chậm kỳ,
          'total_amount_receiver'       => $paramInsert['amount'], // số tiền nhận thực thế, tạm thời để bằng số tiền ghi sổ
          'total_amount_excess_revenue' => 0, // số tiền thu thừa trên sổ
        ],
        'note' => 'Bảng số liệu tổng hợp'
      ],

      [
        'type' => 'OTHER',
        'time_modified' => time(),
        'data' => [
          // Sẽ nhét các option vào đây
        ]
      ]
		];

		if ($plansCollection->isNotEmpty()) {
			$ledgerOtherDataParams[] = [
        'type' => 'PLAN',
        'time_modified' => time(),
        'data' => Helper::getPlanCompact($plansCollection, true),
        'note' => 'Lịch thu'
      ];
		}

		$paramInsert['time_updated'] = time();
    $paramInsert['other_data'] = json_encode($ledgerOtherDataParams, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

		$collectDebtLedger = CollectDebtLedger::query()
																					->where('contract_code', $collectDebtRequest->contract_code)
																					->where('request_id', $collectDebtRequest->id)
																					->first();
		
		// Kiểm tra không có sổ mới cho tạo sổ
		if ( !$collectDebtLedger ) {
			$collectDebtLedger = CollectDebtLedger::forceCreate($paramInsert);
		}
    
    throw_if(!$collectDebtLedger, new Exception('Không thể tạo được sổ cho yêu cầu [CHƯA CÓ SỔ]: ' . $collectDebtRequest->id));

    // Đánh dấu yêu cầu là đã ghi sổ
    $requestOtherData = $collectDebtRequest->getRequestOtherData();
    $requestOtherData[] = [
      'type' => 'RECORED',
      'time_modified' => time(),
      'data' => $collectDebtRequest->only(['id', 'status', 'status_payment', 'status_recored', 'partner_request_id', 'partner_transaction_id']),
      'note' => 'Yêu cầu đã được ghi sổ thành công'
    ];

    $collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO;
    $collectDebtRequest->other_data = json_encode($requestOtherData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    $collectDebtRequest->save();

    // Return bản ghi sổ
    return $collectDebtLedger;
  }
} // End class