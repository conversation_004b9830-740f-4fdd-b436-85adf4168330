<?php

namespace App\Modules\CollectDebtGateway\Repositories\Connections;

use App\Lib\Encryption;
use Illuminate\Support\Str;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;

class NextLendServiceConnection
{
  public $_API_URL            = '';
  public $_API_APP_SECRET_KEY = '';
  public $_API_KET_ENCRYPTION_DATA = '';
  public $_version = '';
  public $_channelCode = '';

  public $_log;
  public $uniqueKeyLog = '';

  public $timeStart;
  public $apiDuring;
  public $errorCode;

  private $__coreResponseResult;

  public function __construct()
  {
    $this->timeStart = now();
    $this->apiDuring = microtime(true);

    $this->_API_URL = config('nextlend.NEXTLEND_SERVICE_REQUEST_URL');
    $this->_API_APP_SECRET_KEY = config('nextlend.NEXTLEND_SERVICE_SECRET_KEY');
    $this->_API_KET_ENCRYPTION_DATA = config('nextlend.NEXTLEND_SERVICE_ENCRYPT_KEY');
    $this->_version = config('nextlend.NEXTLEND_SERVICE_VERSION');
    $this->_channelCode = config('nextlend.NEXTLEND_SERVICE_CHANNEL_CODE', 'NEXTLENDV4');
  }


  public function callRequest(array $inputs = [], string $functionName = '', string $method = 'POST')
  {
    $this->uniqueKeyLog = sprintf('[API-SERVICE-NEXTLEND]--%s--|%s-%s', $functionName, Str::random(48), $method);
    $this->_log[$this->uniqueKeyLog]['api_url'] = $this->_API_URL;
    $this->_log[$this->uniqueKeyLog]['time_start_at'] = $this->timeStart;
    $this->_log[$this->uniqueKeyLog]['func']       = $functionName;
    $this->_log[$this->uniqueKeyLog]['method']     = $method;

    $requestInput = $this->_makeRequestInput($inputs, $functionName);

    $dataPost = json_encode($requestInput, JSON_UNESCAPED_UNICODE);
    $this->_log[$this->uniqueKeyLog]['dataPost'] = $dataPost;


    $headerArray = array(
      "cache-control: no-cache",
      "content-type: application/json; charset=UTF-8",
    );

    $curl = curl_init();

    curl_setopt_array($curl, array(
      CURLOPT_URL => $this->_API_URL,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_HTTPAUTH => CURLAUTH_ANY,
      CURLOPT_SSL_VERIFYPEER => false,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_CUSTOMREQUEST => $method,
      CURLOPT_POSTFIELDS => $dataPost,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_HTTPHEADER => $headerArray
    ));

    $response = curl_exec($curl);
    $resultStatus = curl_getinfo($curl);

    curl_close($curl);
    $this->_log[$this->uniqueKeyLog]['api_response_http_code'] = $resultStatus['http_code'];
    $this->_log[$this->uniqueKeyLog]['api_response'] = $response;
    $this->_log[$this->uniqueKeyLog]['api_time_end'] = time();

    if ($resultStatus['http_code'] == 200) {
      $this->__coreResponseResult = json_decode($response, true);
      $this->_log[$this->uniqueKeyLog]['decrypt_data'] = $this->__coreResponseResult;
      if (gettype($this->__coreResponseResult['data']) == 'string') {
        $decode = json_decode($this->__coreResponseResult['data'], true);
        $this->_log[$this->uniqueKeyLog]['decrypt_data_parse'] = $decode;
      }
      
      app('mylog')->cc($this->_log);
      $this->errorCode = isset($this->__coreResponseResult['RespCode']) ? $this->__coreResponseResult['RespCode'] : '';
      // dd($this->__coreResponseResult);
      return $this->__coreResponseResult;
    }

    app('mylog')->cc($this->_log);
    return false;
  }

  public function _makeRequestInput(array $params = [], string $func = ''): array
  {
    $hash          = $this->_buildHash($params);

    $chuoiTruocMd5 = $func . $this->_version . $this->_channelCode . $hash . $this->_API_APP_SECRET_KEY;
    $this->_log[$this->uniqueKeyLog]['before_md5'] = $chuoiTruocMd5;
    $checkSum      = md5($chuoiTruocMd5);

    $this->_log[$this->uniqueKeyLog]['params_raw'] = $params;

    return [
      'Fnc'             => $func,
      'Checksum'         => $checkSum,
      'EncData'           => $hash,
      'ChannelCode' => $this->_channelCode,
      'Version' => $this->_version,
    ];
  }

  protected function _buildHash(array $params = []): string
  {

    $paramsJsonEncode = json_encode($params, JSON_UNESCAPED_UNICODE);

    $paramsEncrypt = Encryption::Encrypt($paramsJsonEncode, $this->_API_KET_ENCRYPTION_DATA);
    return $paramsEncrypt;
  }

	public function buildHttpRequest(array $input, string $functionName = '', string $method="POST"): Request
	{
		$this->uniqueKeyLog = sprintf('[API-SERVICE-NEXTLEND]--%s--|%s-%s', $functionName, Str::random(48), 'POST');
		$this->_log[$this->uniqueKeyLog]['func'] = $functionName;

		$requestInput = $this->_makeRequestInput($input, $functionName);
		$dataPost = json_encode($requestInput, JSON_UNESCAPED_UNICODE);
		$this->_log[$this->uniqueKeyLog]['dataPost'] = $dataPost;

		$headers = [
			'Content-Type' => 'application/json; charset=UTF-8',
			'Cache-Control' => 'no-cache',
		];

		return new Request($method, $this->_API_URL, $headers, $dataPost);
	}
} // End class