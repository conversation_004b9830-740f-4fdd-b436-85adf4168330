<?php

namespace App\Modules\MobileApp\Actions\MobileAppContractForListAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Carbon\Carbon;

class MobileAppContractForListAction
{
	public array $response = [];

	public function run(Request $request)
	{
		$listConatractCode = $request->json('data.listContractCode', []);
		$collectDebtSummaries = CollectDebtSummary::query()
			->whereIn('contract_code', $listConatractCode)
			->select([
				'contract_code',
				'other_data',
				'contract_amount',
				'fee_overdue',
				'fee_overdue_cycle',
				'total_amount_paid',
				'total_fee_paid',
				'fee_overdue_paid',
				'fee_overdue_cycle_paid',
				'fee_overdue_reduction',
				'fee_overdue_cycle_reduction',
				'status_contract'
			])
			->get();

		if ($collectDebtSummaries->isEmpty()) {
			throw new Exception('Lỗi không tìm thấy thông tin HĐ');
		}

		$collectDebtSummaries->each(function (CollectDebtSummary $collectDebtSummary) {
			$planOtherData = $collectDebtSummary->getSummaryOtherDataItem('PLAN');

			$overviewData = [
				'contractCode' => $collectDebtSummary->contract_code,
				'settlementLoanPaymentAmount' => Helper::priceFormat($collectDebtSummary->getTongTienConPhaiTra()),
				'nextLoanPaymentDate' => $this->getOnlyNextPaymentDate($planOtherData),
				'totalMoneyPaid' => Helper::priceFormat($collectDebtSummary->getTongTienTrichThanhCong())
			];


			$this->response[] = $overviewData;
		});

		return $this->response;
	}

	public function getOnlyNextPaymentDate($planOtherData)
	{
		$firstSchedule = Carbon::createFromTimestamp($planOtherData['data'][0]['time_cycle']);
		if ($firstSchedule->isFuture()) {
			return $firstSchedule->format('d/m/Y');
		} 

		
		$nextPaymentDate = collect($planOtherData['data'])->first(function ($it) {
			$timeCycle = Carbon::createFromTimestamp($it['time_cycle']);
			return $timeCycle->gte(now());
		});
		
		if (!$nextPaymentDate) {
			return '';
		}

		return Carbon::createFromTimestamp($nextPaymentDate['time_cycle'])->format('d/m/Y');
	}
}
