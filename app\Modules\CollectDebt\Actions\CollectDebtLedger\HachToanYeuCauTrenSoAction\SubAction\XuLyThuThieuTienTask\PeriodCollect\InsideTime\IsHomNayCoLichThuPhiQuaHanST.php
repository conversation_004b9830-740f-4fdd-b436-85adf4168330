<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\InsideTime;

use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class IsHomNayCoLichThuPhiQuaHanST
{  
  // IS CẦN PHẢI thu phí quá hạn

  // 1. Chưa có lịch thu phí
  // 2. Hoặc có 1 lịch thu vét gốc
  public function run(CollectDebtSchedule $lichDangHachToan): bool
  {
    $lichThuPhiHomNay = CollectDebtSchedule::where('contract_code', $lichDangHachToan->contract_code)
                                           ->where('isfee', 1)
                                           ->where('rundate', $lichDangHachToan->rundate)
                                           ->where('type', CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH)
                                           ->orderBy('id', 'DESC')
                                           ->first();
    if (!$lichThuPhiHomNay) {
      return false;
    }

    $soLuongLichThuVetGocTrongNgay = CollectDebtSchedule::where('contract_code', $lichDangHachToan->contract_code)
                                                        ->where('rundate', $lichDangHachToan->rundate)
                                                        ->where('type', CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU)
                                                        ->where('isfee', CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC)
                                                        ->orderBy('id', 'DESC')
                                                        ->count();
                                                        
    // Đã có nhiều hơn 01 lịch thu gốc trong ngày -> đã có phát sinh lịch thu phí quá hạn
    if ($soLuongLichThuVetGocTrongNgay > 1) {
      return true;
    }

    return false;
  }
} // End class
