<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayMposSubAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\GetReduceAndPrepayPlanAction\GetReduceAndPrepayPlanAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction\GetNoTheoLoaiPhaiThuHomNaySA;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\CommonSubAction\Task\TaoYeuCauTrichTayTask;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class TrichMotPhanMposSubAction
{
  public $soTienPhiChamKyDuocGiam = 0;
  public $soTienPhiQuaHanDuocGiam = 0;

  public function run(
    CollectDebtShare $collectDebtShare,
    Collection $danhSachToanBoLichThu,
    DebtRecoveryRequestCreateManualRequest $request
  ) {
    
    $tongTienCanPhaiThuCacLich = $danhSachToanBoLichThu->sum('request_amount_debit');
    $soTienMongMuonTrichThanhCong = $request->json('data.amount_request');

    $errorMessage = 'Số tiền trích một phần qua MPOS phải lớn hơn 0đ';

    if ( !empty($soTienMongMuonTrichThanhCong) ) {
      $errorMessage = sprintf(
        'Thao tác bị từ chối. Số tiền ghi nhận trích đang bị LỚN HƠN SO VỚI số tiền cần thu của các lịch. Số tiền bạn đã nhập là: %s. Số tiền cần phải trích là: %s',
        Helper::priceFormat($soTienMongMuonTrichThanhCong, 'đ'),
        Helper::priceFormat($tongTienCanPhaiThuCacLich, 'đ')
      );
    }
    
    throw_if(
      empty($soTienMongMuonTrichThanhCong) || $soTienMongMuonTrichThanhCong > $tongTienCanPhaiThuCacLich, 
      new Exception($errorMessage)
    );

    $collectDebtRequest = app(TaoYeuCauTrichTayTask::class)->run(
      $danhSachToanBoLichThu, 
      $collectDebtShare,
      $request, 
      'MPOS'
    );

    return $collectDebtRequest;
  }
} // End class