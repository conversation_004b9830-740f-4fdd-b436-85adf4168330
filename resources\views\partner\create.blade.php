@extends('layouts.master')
@section('page_name', '<PERSON><PERSON><PERSON> lập Partner')
@section('content')
<!-- Main content -->
<div class="content">
	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="card">
					<form action="{{ config('app.url') . '/mock/congno/store' }}" method="POST">
						<div class="card-body">
							<fieldset class="scheduler-border">
								<legend class="scheduler-border">VIRTUAL ACCOUNT (áp dụng với HĐ TEST v4):</legend>
								<div class="d-flex justify-content-between">
									<div class="control-group mx-2">
										<label>Mã VA:</label>
										<input type="text" class="form-control" name="parner[VA][payment_account_id]" value="" placeholder="Mã QR VA">
									</div>

									<div class="control-group mx-2">
										<label>Kênh nhận:</label>
										<input readonly type="text" class="form-control" name="parner[VA][payment_method_code]" value="VIRTUALACCOUNT">
									</div>

									<div class="control-group mx-2">
										<label>Số tiền nhận:</label>
										<input type="text" class="form-control" name="parner[VA][amount_receiver]" value="1000000">
									</div>


									<div class="control-group mx-2">
										<label>Mã giao dịch:</label>
										<input type="text" class="form-control" name="parner[VA][payment_transaction_id]" value="{{ $randomVA }}">
									</div>
								</div>
							</fieldset>


							{{-- <fieldset class="scheduler-border">
								<legend class="scheduler-border">MPOS</legend>
								<div class="d-flex justify-content-between">
									<div class="control-group">
										<label>Mã yêu cầu:</label>
										<input type="text" class="form-control" name="parner[MPOS][partner_request_id]" value="">
									</div>

									<div class="control-group mx-2">
										<label>Kênh nhận:</label>
										<input readonly type="text" class="form-control" name="parner[MPOS][payment_method_code]" value="MPOS">
									</div>

									<div class="control-group mx-2">
										<label>Số tiền nhận:</label>
										<input type="text" class="form-control" name="parner[MPOS][amount_receiver]" value="1000000">
									</div>

									<div class="control-group mx-2">
										<label>Mã MC:</label>
										<input type="text" class="form-control" name="parner[MPOS][payment_account_id]" value="{{ env('PARTNER_MPOS_SAMPLE_MERCHANT_ID') }}">
									</div>

									<div class="control-group mx-2">
										<label>Mã giao dịch:</label>
										<input type="text" class="form-control" name="parner[MPOS][partner_transaction_id]" value="{{ $randomMPOS }}">
									</div>
								</div>
							</fieldset> --}}

							<button class="btn btn-primary" type="submit">Tạo công nợ</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- /.row -->
	</div><!-- /.container-fluid -->
</div>
<!-- /.content -->
@endsection

@push('js_bot')
<script>
	function onChangeMaHd(element) {
		let currentMaHopDong = element.value.trim();
		$('#hd_ib_off_1').val(currentMaHopDong);
		$('#hd_ib_off_2').val(currentMaHopDong);
	}
</script>
@endpush