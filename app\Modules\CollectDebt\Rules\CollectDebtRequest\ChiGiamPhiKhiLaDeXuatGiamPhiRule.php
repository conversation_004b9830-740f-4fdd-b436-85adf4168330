<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtRequest;

use Illuminate\Contracts\Validation\Rule;

class ChiGiamPhiKhiLaDeXuatGiamPhiRule implements Rule
{
  public string $debtType;
  public string $loaiGiamPhi;
  /**
   * Create a new rule instance.
   * TRICH_MOT_PHAN | TAT_TOAN
   * @return void
   */
  public function __construct(string $debtType, string $loaiGiamPhi)
  {
    $this->debtType = $debtType;
    $this->loaiGiamPhi = $loaiGiamPhi;
  }

  /**
   *
   * @param string $prepayPlansAttribute
   * @param mix array  $reducePlan[] 
   *            number $reduceDataValue
   * @return void
   */
  public function passes($reduceFeeAttrs, $reduceDataValue)
  {
    if ($this->debtType == 'GIAM_PHI') {
      return true;
    }

    return false;
  }

  /**
   * Get the validation error message.
   *
   * @return string
   */
  public function message()
  {
    return 'Bạn chỉ được phép nhập số tiền giảm phí khi và chỉ khi chọn loại yêu cầu là `ĐỀ XUẤT GIẢM PHÍ`';
  }
}
