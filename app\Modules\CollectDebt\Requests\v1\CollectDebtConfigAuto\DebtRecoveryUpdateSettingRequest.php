<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtConfigAuto;

use App\Modules\CollectDebt\Model\CollectDebtSetting;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;
use Illuminate\Validation\Rule;

class DebtRecoveryUpdateSettingRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {

    return [
      'data' => ['array'],
      'data.id'  => ['required', 'numeric', 'min:0'],
      'data.type' => ['required', 'numeric', Rule::in([CollectDebtSetting::TYPE_ON_OFF, CollectDebtSetting::TYPE_VALUE])],
      'data.code' => ['required', 'string', 'max:255', 'unique:debt_recovery_setting,code,' . $this->json('data.id')],
      'data.description' => ['present', 'string', 'max:255'],
      'data.time_start' => ['present', 'string', 'date_format:H:i:s'],
      'data.time_end' => ['present', 'string', 'date_format:H:i:s'],
      'data.status' => ['required', 'numeric', Rule::in([CollectDebtSetting::STOPED, CollectDebtSetting::RUNNING])],
      'data.daily_run' => ['required', 'array'],
      'data.daily_run.*' => ['required', 'string', Rule::in($this->sevenDayOfWeek())],
      'data.value' => ['present', 'string'],
      'data.time_updated' => ['present', 'numeric'],
      'data.updated_by' => ['required']
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['time_updated'] = time();
    $this->merge($params);
  }

  protected function passedValidation()
  {
    $params = $this->all();
    $params['data']['daily_run'] = implode(',', $params['data']['daily_run']);
    $params['data']['updated_by'] = StandardizedDataFilter::getUserAdminStructCompact($params['data']['updated_by']);
    $this->merge($params);
  }

  public function sevenDayOfWeek(): array {
    $dailyRun = 'Sun,Mon,Tue,Wed,Thu,Fri,Sat';
    return explode(',', $dailyRun);
  }
} // End class
