<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtGuide;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Contracts\Validation\Rule;

class PreventDuplicatePaymentMethodRule implements Rule
{
    private string  $__errorMessgae;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($paymentGuideField, $paymentGuideValue)
    {
      $paymentGuideFieldValueAsCollection = collect($paymentGuideValue)->groupBy('payment_method_code');
      foreach ($paymentGuideFieldValueAsCollection as $paymentMethod => $paymentGuides) {
        if ($paymentGuides->count() > 1) {
          $this->__errorMessgae = sprintf('Trường `payment_guide` đã có `%s` phương thức thanh toán qua `%s`', $paymentGuides->count(), $paymentMethod);
          return false;
        }
      }
      
      return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->__errorMessgae;
    }
}
