<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use Illuminate\Database\Eloquent\Collection;

class GetSoTienThuThuaTrenSoTask
{
  public float $soTienThanhCongGhiTrenSo = 0;

  public function run(Collection $plans, float $soTienThanhCongGhiTrenSo = 0): array
  {
    $this->soTienThanhCongGhiTrenSo = $soTienThanhCongGhiTrenSo;

    $plans = $plans->transform(function (CollectDebtSchedule $plan) {
      if ($this->soTienThanhCongGhiTrenSo <= 0) {
        return $plan;
      }

      $soTienConPhaiThanhToanCuaLich = $plan->getSoTienConPhaiThanhToan();

      $soTienPhanBoVaoLichThu = min($this->soTienThanhCongGhiTrenSo, $soTienConPhaiThanhToanCuaLich);

      $plan->success_amount_debit  = $soTienPhanBoVaoLichThu;

      $this->soTienThanhCongGhiTrenSo -= $soTienPhanBoVaoLichThu;

      return $plan;
    });

    return [
      'plans' => $plans,

      // Số tiền này đã bị trừ dần qua các lịch -> ra thu thừa
      'total_amount_excess' => $this->soTienThanhCongGhiTrenSo, 
    ];
  }
} // End class