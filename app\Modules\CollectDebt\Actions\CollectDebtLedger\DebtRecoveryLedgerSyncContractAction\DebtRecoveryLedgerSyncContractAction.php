<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSyncContractAction;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerSetStatusActionRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerSetStatusAction\DebtRecoveryLedgerSetStatusAction;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareGetContractCodeAction\DebtRecoveryShareGetContractCodeAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction\DebtRecoverySummaryCheckLedgerExsistAction;

class DebtRecoveryLedgerSyncContractAction
{
    public function run($request)
    {
        $dataResponse = [];

        $debtRecoveryLedger = CollectDebtLedger::where(['status_summary' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT, 'status_contract' => CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT])->first();
        throw_if(!$debtRecoveryLedger, new Exception('Không tìm thấy bản ghi'));

        $process = $this->__process($debtRecoveryLedger);
        if (!$process) {
            $this->__ReturnError('PROCESS ERROR');
        }

        $dataSummary = $this->__getDataSummary($debtRecoveryLedger);
        if (!$dataSummary) {
            $this->__ReturnError('DATA SUMMARY ERROR');
        }

        $dataShare = $this->__getDataShare($debtRecoveryLedger);
        if (!$dataShare) {
            $this->__ReturnError('DATA SHARE EMPTY');
        }

        $createEvent = $this->__createEvent($debtRecoveryLedger, $dataShare, $dataSummary);
        if ($createEvent) {
            $this->__complete($debtRecoveryLedger);
        } else {
            $this->__fail($debtRecoveryLedger);
        }


        return $dataResponse;
    }

    protected function __ReturnError($code)
    {
        throw new Exception($code);
    }

    /**
     * Undocumented function
     *
     * @param [CollectDebtLedger] $ledgersWithTheirSchedules
     * @param [model to array] $dataShare
     * @param [CollecyDebtSummary] $dataSummary
     * @return void
     */
    public function __createEvent($ledgersWithTheirSchedules, $dataShare, $dataSummary)
    {
        $dataSyncSummary = app(DebtRecoverySummaryCheckLedgerExsistAction::class)->__caculateTime($dataSummary);
				mylog(['param 2 thong tin' => $dataSyncSummary]);

        $mergeSummary = array_merge($dataSummary->toArray(), $dataSyncSummary);
				mylog(['ket qua sau khi merge 2 thong tin' => $mergeSummary]);

        $inputs = [
            'category_care_code' => 'CONTRACT',
            'service_care_code' => 'SYNC',
            'data' => $this->__setDataEvent($dataShare),
            'description' => 'Tạo Event',
            'other_data' => [
                'summary' => $mergeSummary,
                'ledger' => $ledgersWithTheirSchedules->id
            ],
            'time_start' => time(),
            'contract_code' => $dataSummary->contract_code,
        ];
        $create = (new \App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateAction\DebtRecoveryContractEventCreateAction())->run($inputs);
        if (isset($create['id']) && $create['id']) {
            return true;
        }
        return false;
    }

    protected function __getDataShare($ledgersWithTheirSchedules)
    {
        $inputs = [
            'contract_code' => $ledgersWithTheirSchedules->contract_code
        ];
        return app(DebtRecoveryShareGetContractCodeAction::class)->run($inputs);
    }

    protected function __getDataSummary($ledgersWithTheirSchedules)
    {
        return \App\Modules\CollectDebt\Model\CollectDebtSummary::where(['contract_code' => $ledgersWithTheirSchedules->contract_code])->first();
    }

    protected function __setDataEvent($value)
    {
        $data = [
            //            'contract'=> [],
            //            'profile'=> [],
            //            'list_fee'=> [],
            //            'company'=> [],
            //            'payment'=> [],
            //            'overdue'=> [],
            //            'overdue_period'=> [],
        ];
        if ($value) {
            if (isset($value['company_data']) && $value['company_data']) {
                $data['company'] = json_decode($value['company_data'], true);
            }
            if (isset($value['contract_data']) && $value['contract_data']) {
                $data['contract'] = json_decode($value['contract_data'], true);
            }
            if (isset($value['profile_data']) && $value['profile_data']) {
                $data['profile'] = json_decode($value['profile_data'], true);
            }
            if (isset($value['payment_guide']) && $value['payment_guide']) {
                $data['payment'] = json_decode($value['payment_guide'], true);
            }
            if (isset($value['list_fee']) && $value['list_fee']) {
                $data['list_fee'] = json_decode($value['list_fee'], true);
            }
        }
        return $data;
    }

    protected function __process($ledgersWithTheirSchedules)
    {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_contract' => CollectDebtEnum::LEDGER_STT_ACTION_DANG_CAP_NHAT,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

    protected function __complete($ledgersWithTheirSchedules)
    {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_contract' => CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }

    protected function __fail($ledgersWithTheirSchedules)
    {
        $rq = new DebtRecoveryLedgerSetStatusActionRequest();
        $inputs['data'] = [
            'id' => $ledgersWithTheirSchedules->id,
            'status_contract' => CollectDebtEnum::LEDGER_STT_ACTION_LOI,
        ];
        $rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
        $update = (new DebtRecoveryLedgerSetStatusAction())->setStatusOther($rq);
        if (get_class($update) == CollectDebtLedger::class) {
            return true;
        }
        return false;
    }
}
