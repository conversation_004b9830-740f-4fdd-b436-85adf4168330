<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction;

use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoDeXuatGiamPhiSubAction\TaoDeXuatGiamPhiDoiLuongSubAction;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateManualRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoDeXuatGiamPhiSubAction\TaoDeXuatGiamPhiSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayMposSubAction\TaoYeuCauTrichTayMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateManualAction\SubAction\TaoYeuCauTrichTayCoChungTuSubAction\TaoYeuCauTrichTayCoChungTuSubAction;

class DebtRecoveryRequestCreateManualAction
{
  public function run(DebtRecoveryRequestCreateManualRequest $request)
  {
    $contractCode = $request->json('data.contract_code');
    $collectDebtShare = CollectDebtShare::where('contract_code', $contractCode)->first();

    $danhSachToanBoLichThu = $this->getLichThuHomNayVaTuongLai($contractCode);
    $danhSachToanBoLichThu = app(PlanSortableCollectionByRule::class)->sortCollection($danhSachToanBoLichThu);

    // Đề xuất giảm phí
    if ($request->isDeXuatGiamPhi()) {
      return app(TaoDeXuatGiamPhiDoiLuongSubAction::class)->run(
        $collectDebtShare, 
        $danhSachToanBoLichThu,
        $request
      );
    }

    DB::beginTransaction();
    try {
      switch ($request->json('data.payment_method_code')) {
        case 'MPOS': 
          $collectDebtPartner = app(TaoYeuCauTrichTayMposSubAction::class)->run(
            $collectDebtShare,
            $danhSachToanBoLichThu,
            $request,
          );
  
          break;
  
        case 'VIRTUALACCOUNT': 
          $collectDebtPartner = app(TaoYeuCauTrichTayCoChungTuSubAction::class, ['paymentMethodCode' => 'VIRTUALACCOUNT'])->run(
            $collectDebtShare,
            $danhSachToanBoLichThu,
            $request,
          );
          break;
  
        case 'IB_OFF': 
          $collectDebtPartner = app(TaoYeuCauTrichTayCoChungTuSubAction::class, ['paymentMethodCode' => 'IB_OFF'])->run(
            $collectDebtShare,
            $danhSachToanBoLichThu,
            $request,
          );
          break;
        
        default: 
          throw new Exception('Hệ thống không nhận diện được phương thức thanh toán của bạn');
          break;
      }

      DB::commit();
    }catch(\Throwable $th) {
      DB::rollBack();
      throw $th;
    }

    return $collectDebtPartner;
  } // End method


  public function getLichThuHomNayVaTuongLai(string $contractCode=''): Collection {
    $runDateHopLe = now()->subDay()->format('Ymd');

    $plans = CollectDebtSchedule::query()
                                ->where('contract_code', $contractCode)
                                ->where('rundate', '>=', $runDateHopLe)
                                ->whereIn('status', [CollectDebtEnum::SCHEDULE_STT_MOI, CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN])
                                ->orderByRaw('rundate ASC, isfee ASC, is_settlement ASC')
                                ->get();
    
    throw_if($plans->isEmpty(), new Exception('Không có lịch thu nào để thực hiện tạo yêu cầu'));
    return $plans;
  }

}  // End class