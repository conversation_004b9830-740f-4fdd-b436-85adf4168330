<body style="margin: 0;">
	<style>
		.container { font-size: 14px; line-height: 1.8; }
		.row { display: block; margin-bottom: 10px; justify-content: space-between;  }
		.label { width: 115px; color: gray; display: inline-block; }
		.value { font-weight: 500; text-align: right;}
		.badge { display: inline-block; padding: 2px 8px; font-size: 12px; border-radius: 12px; color: white; font-weight: 500; line-height: 1; }
	  .badge-success { background-color:rgb(0, 206, 148); }
	</style>

	<div class="container">
		<div class="row">
			<span class="label">Số tiền vay:</span>
			<span class="value">{{ \App\Lib\Helper::makeVndCurrency($collectDebtGuide->amount) }}</span>
		</div>

		<div class="row">
			<span class="label">Thời gian:</span>
			<span class="value">{{ date('H:i d/m/Y', $collectDebtLedger->time_accounting) }}</span>
		</div>

		<div class="row">
			<span class="label">Mã hợp đồng:</span>
			<span class="value">{{ $collectDebtGuide->contract_code }}</span>
		</div>

		<div class="row">
			<span class="label">Thời hạn:</span>
			<span class="value">Đã cập nhật</span>
		</div>

		<div class="row">
			<span class="label">Số tiền đã trích:</span>
			<span class="value">{{ \App\Lib\Helper::makeVndCurrency($collectDebtLedger->amount) }}</span>
		</div>

		<div class="row">
			<span class="label">Phương thức:</span>
			<span class="value">{{ \App\Lib\NotiHelper::getKenhThanhToanNoti($collectDebtLedger->getOtherDataItem('REQUEST')['data']['payment_method_code']) }}</span>
		</div>

		<div class="row">
			<span class="label">Tình trạng:</span>
			<span class="value badge badge-success">Đã thanh toán</span>
		</div>
	</div>
</body>