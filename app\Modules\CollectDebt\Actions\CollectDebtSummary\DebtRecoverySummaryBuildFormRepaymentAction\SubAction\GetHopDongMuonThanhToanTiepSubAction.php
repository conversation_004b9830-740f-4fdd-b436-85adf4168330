<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryBuildFormRepaymentAction\SubAction;

use App\Lib\NextlendCore;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;

class GetHopDongMuonThanhToanTiepSubAction
{
	/**
	 * array:2 [
			"data" => array:3 [
				0 => array:12 [
					"contract_code" => "MPOS-2401181354517-L1"
					"amount" => 10000000
					"type" => 3
					"cycle" => 10
					"intervals" => 5
					"status" => 6
					"finished" => 1
					"time_start" => 1704560400
					"time_end" => 1705510799
					"required_amount" => 10735000
					"amount_paid" => 0
					"amount_refund" => 0
				]
				...
			]

			"other_data" => array:3 [
				"type_name" => array:3 [
					1 => "Trích nợ theo ngày"
					2 => "Gia hạn"
					3 => "Trích nợ theo chu kỳ"
				]
				"status_name" => array:9 [
					1 => "Chờ duyệt"
					2 => "Đã duyệt"
					3 => "Từ chối"
					4 => "Đã tạo yêu cầu giải ngân"
					5 => "Đã duyệt yêu cầu giải ngân"
					6 => "Đã giải ngân"
					7 => "Chờ thẩm định"
					8 => "Hồ sơ nháp"
					9 => "Hồ sơ sai thông tin"
				]
				"finished_name" => array:3 [
					1 => "Chưa tất toán"
					2 => "Đã tất toán"
					3 => "Đã gia hạn"
				]
			]
		]
	 *
	 * @param CollectDebtShare $collectDebtShare
	 * @param array $exceptContractCodes
	 * @return void
	 */
	public function run(CollectDebtShare $collectDebtShare, array $exceptContractCodes = [])
	{
		$profileData = $collectDebtShare->getProfileDataAsArray();

		$nextlendCore = app(NextlendCore::class)->callRequest([
			'not_in_contract_code' => $exceptContractCodes,
			'merchant_id' => $profileData['merchant']['id'],
			'version' => CollectDebtEnum::REQUEST_VERSION_4
		], 'ContractV4_getListNotFinished', 'GET');

		$listHopDongDangThanhToanCuaMerchant = $nextlendCore->decryptData();
		return $listHopDongDangThanhToanCuaMerchant;
	}
}
