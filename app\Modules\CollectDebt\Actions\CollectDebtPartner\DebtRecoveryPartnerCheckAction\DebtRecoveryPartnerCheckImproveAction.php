<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction\GhiSoChoYeuCauDaCoSoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\TaoYeuCauKhongCanGuiDoiTacSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\CapNhatYeuCauVeTrangThaiCuoiSubAction;

class DebtRecoveryPartnerCheckImproveAction
{
	public $virtualAccountIds = [];

	private array $__exceptIds = [];

	private array $__partnerIdsDaXuLy = [];

	public function initPartnerCheck(Request $request)
	{
		$params = $request->all();

		$evenNumberValid = [];

		if (!empty($params['number'])) {
			for ($i = 1; $i <= $params['number']; $i++) {
					$rMax[$i] = $i;
			}

			$evenNumberValid = $rMax;
		}

		$evenNumber = $params['even_number'] ?? null;

		for ($i = 1; $i <= 30; $i++) {
			try {
				$result = $this->run($request, $evenNumber, $evenNumberValid);
				

				// Truy van ma he thong tra ra "EMPTY", thi break luon
				if ($result == 'EMPTY') {
					break;
				}

			} catch (\Throwable $th) {
				mylog(['Loi Partner Check' => Helper::traceError($th)]);
				// throw $th;
				@TelegramAlert::sendMessagePartner('Loi PartnerCheck: ' . Helper::traceError($th));
				continue;
			} finally {
				usleep(300000);
			}
		}
		
		return $this->__partnerIdsDaXuLy;
	}

	public function run(Request $request, $evenNumber, $evenNumberValid)
	{
		$collectDebtPartners = CollectDebtPartner::query()
			->join('debt_recovery_summary', 'debt_recovery_summary.contract_code', '=', 'debt_recovery_partner.contract_code')
			->when($evenNumber && in_array($evenNumber, $evenNumberValid), function($query) use ($evenNumber, $evenNumberValid) {
				$condition = "SUBSTRING(`debt_recovery_partner.id` % ".count($evenNumberValid).", -1,1) = ". ($evenNumber == count($evenNumberValid) ? "0" : $evenNumber);
				$query->whereRaw($condition);
			})
			->whereRaw("LENGTH(debt_recovery_partner.contract_code) > 0")
			->where('debt_recovery_partner.status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
			->where('debt_recovery_partner.status_refund', CollectDebtEnum::PARTNER_STT_RF_CHUA_HOAN)
			->where(function ($query) {
					$query->where('payment_method_code', 'MPOS')
								->orWhere(function ($sub1) {
									$sub1->whereIn('payment_method_code', ['IB_OFF', 'WALLET', 'VIRTUALACCOUNT'])
											 ->whereAvailableBalance();
								});
			});

		if (!empty($this->__exceptIds)) {
			$collectDebtPartners = $collectDebtPartners->whereNotIn('debt_recovery_partner.id', $this->__exceptIds);
		}

		// Chi lay 1 ban ghi thoi
		$collectDebtPartners = $collectDebtPartners->orderByRaw('debt_recovery_partner.number_perform ASC, debt_recovery_partner.id ASC')
																							 ->limit(1)
																							 ->selectRaw('debt_recovery_partner.*')
																							 ->get();

		// Khong co partner -> ghi log va return EMPTY
		if ($collectDebtPartners->isEmpty()) {
			mylog([
				'LogId' => request('api_request_id'),
				'Error' => 'Khong co du lieu partner check',
				'Time' => now()->toDateTimeString()
			]);

			return 'EMPTY';
		}

		// Có thông tin bản ghi partner -> update sang đang xử lý cái  đã (update theo id, status)
		$collectDebtPartner = $collectDebtPartners->first();

		$this->__exceptIds[] = $collectDebtPartner->id;

		if ($collectDebtPartner) {
			$updatePartnerVeDangXuLy = CollectDebtPartner::query()
				->where('id', $collectDebtPartner->id)
				->where('status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
				->update([
					'status' => CollectDebtEnum::PARTNER_STT_DANG_XU_LY,
					'number_perform' => 1
				]);

			if ( !$updatePartnerVeDangXuLy ) {
				mylog(['loi khong cap nhat duoc partner sang dang xu ly' => $collectDebtPartner->id]);
				throw new Exception('Chuyen doi partner sang trang thai dang xu ly bi loi');
			}
		}

		$collectDebtPartner->refresh();

		if ($collectDebtPartner->status != CollectDebtEnum::PARTNER_STT_DANG_XU_LY) {
			$error = ['Error Cosistant' => sprintf('Loi xung dot du lieu partner: %s', $collectDebtPartner->id)];
			throw new Exception(json_encode($error));
		}


		DB::beginTransaction();
		try {
			if ($collectDebtPartner->isTonTaiYeuCau()) {
				mylog([
					'Công nợ đã có yêu cầu' => 'YES',
					'Cong no dang xu ly' => $collectDebtPartner->partner_request_id
				]);

				$collectDebtPartnerCreditRequest = app(CapNhatYeuCauVeTrangThaiCuoiSubAction::class)->run($collectDebtPartner);

				/**
				 * Xuống đến đây mà partner vẫn còn `ĐANG XỬ LÝ`, chứng tỏ bị lỗi gì đó 
				 * -> UPDATE lại thành CHƯA XỬ LÝ và break;
				 */
				if ($collectDebtPartnerCreditRequest['partner']->isPartnerDangXuLy()) {
					mylog(['Da bi loi gi do ma khong the cap nhat ve trang thai cuoi' => 'ok']);
					throw new Exception('Loi khong cap nhat duoc yeu cau ve trang thai cuoi');
				}

				// Nếu yc đã ghi sổ rồi và ghi nhận số tiền thừa, bắt !empty để lịch thu quá khứ ko bị ghi sổ 2 lần
				$collectDebtRequest = $collectDebtPartner->collectDebtRequest;
				if (!empty($collectDebtPartnerCreditRequest['is_excess']) && !empty($collectDebtRequest->partner_transaction_id)) {
					$inputs = [
						'data' => [
							'profile_id'    => $collectDebtRequest->profile_id,
							'contract_code' => $collectDebtRequest->contract_code,
							'plan_ids'      => $collectDebtRequest->plan_ids,
							'request_id'    => $collectDebtRequest->id,
							'currency'      => $collectDebtRequest->currency,
							'amount'        => $collectDebtPartner->amount_receiver,
							'description'   => 'TRICH_MUON',
							'status'        => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY,
							'time_record'   => time(),
							'created_by'    => Helper::getCronJobUser(),
							'time_created'  => time(),
						]
					];

					$rq = new DebtRecoveryLedgerCreateRequest();
					$rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));

					app(GhiSoChoYeuCauDaCoSoSubAction::class)->run($collectDebtRequest, $rq, $collectDebtPartner);
				}

				DB::commit();

				$this->__partnerIdsDaXuLy[] = $collectDebtPartner->id;	

				return $collectDebtPartnerCreditRequest['partner'];
			} // End if is ton tai yeu cau

			if ($collectDebtPartner->isKhongTonTaiYeuCau()) {
				mylog([
					'Cong no KHONG CO yêu cầu' => 'YES',
					'Cong no dang xu ly, ma chung tu la:' => $collectDebtPartner->partner_transaction_id
				]);

				$collectDebtPartnerResult = app(TaoYeuCauKhongCanGuiDoiTacSubAction::class)->run($collectDebtPartner);
				DB::commit();

				$this->__partnerIdsDaXuLy[] = $collectDebtPartner->id;	
			}

			return $collectDebtPartner;
		} catch (\Throwable $th) {
			// Thuc hien ghi nhat ky log
			$err = [
				'LogId' => request('api_request_id'),
				'Partner Bi Loi' => @optional($collectDebtPartner)->id,
				'Err' => Helper::traceError($th)
			];

			mylog($err);

			DB::rollBack();

			// Khi ban ghi bi loi, thi can tra lai trang ban dau (CHUA XU LY)
			$updateChuaXuLy = CollectDebtPartner::query()->where('id', $collectDebtPartner->id)
																 ->where('status', CollectDebtEnum::PARTNER_STT_DANG_XU_LY)
																 ->update([
																	'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
																	'number_perform' => 0,
																 ]);

			if (!$updateChuaXuLy) {
				mylog(['[LOI UPDATE VE CHUA XU LY]' => $updateChuaXuLy]);
			}

			throw new Exception('Loi xu ly partner: ' . json_encode($err, JSON_PRETTY_PRINT));
		}
	}
} // End class