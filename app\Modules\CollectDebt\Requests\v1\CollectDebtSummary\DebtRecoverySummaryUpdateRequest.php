<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtSummary;

use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoverySummaryUpdateRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data'                              => ['required', 'array'],
      'data.contract_code'                => ['required', 'string', 'max:50'],
			
      'data.fee_overdue' => ['required', 'integer', 'min:0'], // phi qua han
			'data.fee_overdue_cycle' => ['required', 'integer', 'min:0'], // phi cham ky
			'data.total_amount_receiver' => ['required', 'integer', 'min:0'], // tong tien nhan
			'data.total_amount_excess_revenue' => ['required', 'integer', 'min:0'], // tong tien thu thua
			'data.total_amount_paid' => ['required', 'integer', 'min:0'], // tong goc da thu
			'data.total_fee_paid' => ['required', 'integer', 'min:0'], // tong phi da thu
			'data.fee_overdue_paid' => ['required', 'integer', 'min:0'], // phi qua han da thu
			'data.fee_overdue_cycle_paid' => ['required', 'integer', 'min:0'], // phi cham ky da thu
			'data.fee_overdue_reduction' => ['required', 'integer', 'min:0'], // phi qh da giam
			'data.fee_overdue_cycle_reduction' => ['required', 'integer', 'min:0'], // phi cham ky da giam
			'data.is_overdue' => ['required', 'integer', Rule::in([1, 2])], // co qua han hay khong: 1 co | 2: khong
			'data.is_over_cycle' => ['required', 'integer', Rule::in([1, 2])], // co cham ky hay khong: 1 co | 2 khong
			'data.number_over_cycle' => ['required', 'integer', 'min:0'], // so ky bi cham
			'data.number_day_overdue' => ['required', 'integer', 'min:0'], // so ngay qua han
			'data.description' => ['required', 'string', 'max:255'], // noi dung,

			// lưu vết ai thao tác
			'data.reason' => ['required', 'string', 'max:300'],
			'data.edited_by' => ['required', 'string', 'json']
    ];
  }

	protected function prepareForValidation()
	{
		$params = $this->all();
		$params['data']['edited_by'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['edited_by']);
		$this->merge($params);
	}
} // End class
