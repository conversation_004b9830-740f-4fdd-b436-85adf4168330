<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanSearchDataAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequest;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class DebtRecoveryContractPlanSearchDataAction
{  
  public function run(Request $request): LengthAwarePaginator
  {
    $collectDebtSchedulePagination = CollectDebtSchedule::query();
    $collectDebtSchedulePagination = $collectDebtSchedulePagination->latest('id')->paginate(
      $request->json('data.limit', 10),
      ['*'],
      'pahe',
      $request->json('data.page', 1)
    );
    
    return $collectDebtSchedulePagination;
  }
} // End class