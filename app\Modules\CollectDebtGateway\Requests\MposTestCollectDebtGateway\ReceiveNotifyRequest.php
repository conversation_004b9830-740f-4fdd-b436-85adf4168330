<?php

namespace App\Modules\CollectDebtGateway\Requests\MposTestCollectDebtGateway;

use Illuminate\Foundation\Http\FormRequest;

class ReceiveNotifyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data.request_payment_id' => 'required|string|max:50',
            'data.merchant_id' => 'required|string',
            'data.amount_debit' => 'required|numeric',
            'data.partner_transaction_id' => 'required|string|max:50',
        ];
    }

    public function messages()
    {
        return [
            'data.request_payment_id.required' => 'Trường request_payment_id không để trống',
            'data.request_payment_id.string' => 'Trường request_payment_id phải đúng định dạng',
            'data.request_payment_id.max' => 'Trường request_payment_id không được lớn hơn :max',
            'data.merchant_id.required' => 'Trường merchant_id không để trống',
            'data.merchant_id.string' => 'Trường merchant_id phải đúng định dạng',
            'data.amount_debit.required' => 'Trường amount_debit không để trống',
            'data.amount_debit.numeric' => 'Trường amount_debit phải đúng định dạng',
            'data.partner_transaction_id.required' => 'Trường partner_transaction_id không để trống',
            'data.partner_transaction_id.string' => 'Trường partner_transaction_id phải đúng định dạng',
            'data.partner_transaction_id.max' => 'Trường partner_transaction_id không được lớn hơn :max',
        ];
    }
}
