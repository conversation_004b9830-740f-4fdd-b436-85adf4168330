<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtWaitProcess;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtWaitProcess;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCancelPaymentAction\DebtRecoveryRequestCancelPaymentAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction\CreatePartnerKhongDongDoChuDongHuySubAction;

class DebtRecoveryWaitProcessHandleAction
{
	private array $__processDaXuLy = [];

	private array $__processIdsExcept = [];

	public function initWaitProcess()
	{
		for ($i = 1; $i <= 15; $i++) {
			try {
				$result = $this->run();

				// Truy van ma he thong tra ra "EMPTY", thi break luon
				if ($result == 'EMPTY') {
					$this->__processDaXuLy[] = 'khong co thong tin process de xu ly';
					break;
				}
				
				var_dump("ok");
			} catch (\Throwable $th) {
				mylog(['Loi Wait Process' => Helper::traceError($th)]);
				// throw $th;
				@TelegramAlert::sendWaitProcess('Loi Xu Ly Wait Process: ' . Helper::traceError($th));
				continue;
			} finally {
				usleep(300000);
			}
		}

		mylog(['cac ban ghi wait process' => $this->__processDaXuLy]);
		return $this->__processDaXuLy;
	}


	public function run()
	{
		$waitProcesses = CollectDebtWaitProcess::query()
																					->where('status', CollectDebtEnum::WAIT_PROCESS_STT_CHUA_XU_LY);

		if (!empty($this->__processIdsExcept)) {
			$waitProcesses = $waitProcesses->whereNotIn('id', $this->__processIdsExcept);
		}

		$waitProcesses = $waitProcesses->limit(1)->get();
													
		if ($waitProcesses->isEmpty()) {
			return 'EMPTY';
		}

		$collectDebtWaitProcess = $waitProcesses->first();
		mylog(['ban ghi process' => $collectDebtWaitProcess]);

		// update len dang xu ly
		$updatedLenDangXuLy = CollectDebtWaitProcess::query()
																								->where('id', $collectDebtWaitProcess->id)
																								->where('status', CollectDebtEnum::WAIT_PROCESS_STT_CHUA_XU_LY)
																								->update([
																									'status' => CollectDebtEnum::WAIT_PROCESS_STT_DANG_XU_LY
																								]);
		
		if (!$updatedLenDangXuLy) {
			mylog(['loi cap nhat len dang xu ly' => 'ok']);
			throw new Exception('loi cap nhat len dang xu ly');
		}

		$this->__processIdsExcept[] = $collectDebtWaitProcess->id;

		$collectDebtWaitProcess->refresh();
		if ($collectDebtWaitProcess->status != CollectDebtEnum::WAIT_PROCESS_STT_DANG_XU_LY) {
			mylog(['Loi trang thai khong phai dang xu ly, trang thai moi nhat la' => $collectDebtWaitProcess->status]);
			throw new Exception('Loi trang thai khong phai dang xu ly, trang thai moi nhat la');
		}

		DB::beginTransaction();
		try {
			if ($collectDebtWaitProcess->isSeHuyYeuCauMpos()) {
				$collectDebtRequest = CollectDebtRequest::query()->with(['collectDebtShare', 'collectDebtPartner'])->find($collectDebtWaitProcess->obj_id);
				
				if (!$collectDebtRequest) {
					mylog(['Khong co thong tin yeu cau voi ID la:' => $collectDebtWaitProcess->obj_id]);
					$updatedVeDaXuLy = $collectDebtWaitProcess->forceFill([
						'status' => CollectDebtEnum::WAIT_PROCESS_STT_DA_XU_LY,
						'time_updated' => now()->timestamp
					])->update();

					if (!$updatedVeDaXuLy) {
						mylog(['Loi cap nhat update da xu ly' => 'ok']);
						throw new Exception('Khong tim duoc yeu cau va update waitprocess khong thanh cong');
					}

					DB::commit();
					return $collectDebtWaitProcess;
				}

				if ($collectDebtRequest->collectDebtPartner) {
					mylog([
						'[LOI] - yeu cau da co partner' => @optional($collectDebtRequest)->collectDebtPartner
					]);
					
					$updateProcessVeTrangThaiDuocHuy = CollectDebtWaitProcess::query()
																																	 ->where('id', $collectDebtWaitProcess->id)
																																	 ->update(['status' => CollectDebtEnum::WAIT_PROCESS_STT_DUOC_HUY]);

					mylog(['Ket qua cap nhat wait process la huy, ko dung nua' => $updateProcessVeTrangThaiDuocHuy]);
					DB::commit();
					return $collectDebtWaitProcess;
				}

				// kiem tra lenh thu IB_OFF, VA da hach toan chua
				$yeuCauThuBiDong = CollectDebtRequest::query()->with('collectDebtLedger')->find($collectDebtWaitProcess->source_id);
				
				// khong co so, hoac co so nhung chua hach toan
				if (!$yeuCauThuBiDong->collectDebtLedger || $yeuCauThuBiDong->collectDebtLedger->status != CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN) {
					mylog(['khong the thuc hien do yc thu VA, IBOFF chua hach toan' => 'ok']);
					throw new Exception('Process chua the thuc hien do loi logic');
				}
				
				if (empty($collectDebtRequest->partner_transaction_id)) {
					mylog([
						'Yeu cau bi huy la' => $collectDebtRequest->partner_request_id,
						'Yeu cau muon huy khong co ma chung tu' => 'ok'
					]);

					throw new Exception('Yeu cau muon huy khong co ma chung tu, ma yc la: ' . $collectDebtRequest->partner_request_id);
				}

				$cancelMposRequest = app(DebtRecoveryRequestCancelPaymentAction::class)->run($collectDebtRequest);
				// co the huy lenh 
				if (
					!empty($cancelMposRequest['data']['error_code']) && 
					( $cancelMposRequest['data']['error_code'] == '00' || $cancelMposRequest['data']['error_code'] == '-1' )
				) {
					// tao partner 0d
					$coreContract = $collectDebtRequest->collectDebtShare->getCoreContractByGuide();
					$partnerKhongDong = app(CreatePartnerKhongDongDoChuDongHuySubAction::class)->run($collectDebtRequest, $coreContract);
					mylog(['partner khong dong' => $partnerKhongDong]);

					if ($partnerKhongDong) {
						$collectDebtWaitProcess->status = CollectDebtEnum::WAIT_PROCESS_STT_DA_XU_LY;
						$collectDebtWaitProcess->time_updated = now()->timestamp;
						$saveResult = $collectDebtWaitProcess->save();

						if (!$saveResult) {
							throw new Exception('khong cap nhat duoc wait process');
						}

						$updateYeuCauMpos = CollectDebtRequest::query()->where('id', $collectDebtRequest->id)->update([
							'time_canceled' => now()->timestamp,
							'canceled_by' => Helper::getCronJobUser(),
						]);

						if (!$updateYeuCauMpos) {
							mylog(['khong cap nhat duoc yc ve trang thai da huy' => $collectDebtRequest->id]);
							throw new Exception('khong cap nhat duoc yeu cau ve trang thai da huy');
						}

						$this->__processDaXuLy[] = $collectDebtWaitProcess->id;
						DB::commit();
					}
				}

 				// khong the huy lenh -> danh dau la that bai
				if(
					empty($cancelMposRequest['data']['error_code']) || $cancelMposRequest['data']['error_code'] != '00'
				) {
					$collectDebtWaitProcess->status = CollectDebtEnum::WAIT_PROCESS_STT_XU_LY_LOI;
					$collectDebtWaitProcess->time_updated = now()->timestamp;
					$saveResult = $collectDebtWaitProcess->save();

					if (!$saveResult) {
						throw new Exception('khong cap nhat duoc wait process');
					}

					$this->__processDaXuLy[] = $collectDebtWaitProcess->id;
					DB::commit();
				}
			}

			return $collectDebtWaitProcess;
		} catch (\Throwable $th) {
			$error = [
				'Loi xu ly wait process' => Helper::traceError($th), 
				'LogId' => request('api_request_id'),
			];

			mylog($error);
			
			DB::rollBack();

			$updatedVeChuaXuLy = CollectDebtWaitProcess::query()
												->where('id', $collectDebtWaitProcess->id)
												->where('status', CollectDebtEnum::WAIT_PROCESS_STT_DANG_XU_LY)
												->update(['status' => CollectDebtEnum::WAIT_PROCESS_STT_CHUA_XU_LY]);

			if (!$updatedVeChuaXuLy) {
				mylog(['[LOI] Update ve chua xu ly' => 'ok']);
			}

			throw new Exception(Helper::traceError($th));
		}
	}
} // End class
