<?php

namespace App\Modules\CollectDebt\Model\Traits\Common;

use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

/**
 * Trait thực hiện rút gọn data trước khi lưu vào DB cho phần `dữ liệu liên quan`
 */
class StandardizedDataFilter
{
  // CollectDebtRequest Instance
  public static function getRequestCompactAttribute($instance): array
  {
    return $instance->only([
      'id',
      'type',
      'profile_id',
      'plan_ids',
      'contract_code',
      'payment_method_code',
      'payment_channel_code',
      'payment_account_id',
      'payment_account_holder_name',
      'payment_account_bank_code',
      'payment_account_bank_branch',
      'partner_request_id',
      'partner_transaction_id',
      'description',
      'time_begin',
      'time_expired',
      'is_payment',
      'version',
      'status',
      'status_payment',
      'status_recored',
      'status_action',
      'create_from',
      'currency',
      'fee',
      'amount_receiver',
      'amount_payment',
      'amount_request',
      'time_created',
      'time_completed',
    ]);
  }

  // CollectDebtSchedule Instance
  public static function getPlanCompactAttribute($instance): array
  {
    return $instance->only([
      'id',
      'master_id',
      'profile_id',
      'contract_code',
      'contract_type',
      'type',
      'isfee',
      'cycle_number',
      'debit_begin',
      'debit_end',
      'rundate',
      'time_start',
      'time_end',
      'amount_period_debit',
      'request_amount_debit',
      'success_amount_debit',
      'description',
      'is_settlement',
      'other_data',
      'status',
    ]);
  }

  public static function getUserAdminStructCompact($data=[], $merge=[]): string {
    $fields = [
      'id' => $data['id'] ?? 'cronjob',
      'username' => $data['username'] ?? 'cronjob',
      'mobile' => $data['mobile'] ?? 'cronjob',
    ];

    $fields = array_merge($fields, $merge);

    return json_encode($fields);
  }

  /**
   * Lấy dữ liệu rút gọn từ model
   *
   * @param string $type
   * @param [type] $data
   * @return void
   */
  public static function getStandardizedDataFilter(string $type='REQUEST', $data) {
    if ($type == 'REQUEST') {
      return self::getRequestCompactAttribute($data);
    }

    if ($type == 'PLAN') {
      return self::getPlanCompactAttribute($data);
    }

    if ($type == 'USER_ADMIN') {
      return self::getUserAdminStructCompact($data);
    }
  }

  
} // End class