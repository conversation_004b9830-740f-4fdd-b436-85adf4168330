<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetPaymentStatisticAction;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCaculateAmountAction\DebtRecoverySummaryCaculateAmountAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerStatisticByChannelAction\DebtRecoveryPartnerStatisticByChannelAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLateFee;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

use function Symfony\Component\String\b;

class DebtRecoveryContractGuideGetPaymentStatisticAction
{
  public function run(Request $request)
  {
    $data = [
      'contract_code' => $request->json('data.contract_code'),
    ];

    $thongTinThanhToan = app(DebtRecoverySummaryCaculateAmountAction::class)->run($data);
    $collectDebtSummary = CollectDebtSummary::query()->where($data)->first();
    $currency = $collectDebtSummary->getCurrency();

		// tong thu sau giam tru = Tong tien goc da thu + tong tien phi da thu - tien phi da hoan
    $tongTienConPhaiThuSauKhiGiamTru = $collectDebtSummary->total_amount_paid 
																				+ $collectDebtSummary->total_fee_paid
																				- ( $collectDebtSummary->fee_overdue_cycle_refund + $collectDebtSummary->fee_overdue_refund);
		$desc = '';

		if (!$collectDebtSummary->isHopDongDaTatToan()) {
			if ($tongTienConPhaiThuSauKhiGiamTru >= $collectDebtSummary->contract_amount) {
				$desc = '<b class="text-success"><i class="fa fa-check"></i> HĐ đã đủ điều kiện <br>làm tất toán</b>';
			}else {
				$desc = sprintf(
					'<b>Cần phải thu tối thiểu: <br><span class="text-danger">%s</span> để tất toán HĐ</b>', 
					Helper::priceFormat($collectDebtSummary->contract_amount - $tongTienConPhaiThuSauKhiGiamTru)
				);
			}
		}
    
		$descDaHoan = '';
		if (!empty($collectDebtSummary->amount_refunding)) {
			$descDaHoan = sprintf(
				'Đang có yc hoàn <span class="text-danger">%s</span> <br> cần được xử lý', 
				Helper::priceFormat($collectDebtSummary->amount_refunding)
			);
		}
    $statistic = [
      [
        'label' => 'tong_du_no',
        'name' =>  'Tổng dư nợ',
        'value' => $thongTinThanhToan['total_amount_debt'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['total_amount_debt'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      [
        'label' => 'no_goc',
        'name' =>  'Nợ gốc',
        'value' => $thongTinThanhToan['amount_original_debt'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['amount_original_debt'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      [
        'label' => 'tong_phi_cham_ky',
        'name' => 'Phí chậm kỳ phát sinh',
        'value' => $thongTinThanhToan['fee_overdue_cycle'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['fee_overdue_cycle'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      [
        'label' => 'tong_phi_qua_han',
        'name' =>  'Phí quá hạn phát sinh',
        'value' => $thongTinThanhToan['fee_overdue'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['fee_overdue'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'tong_phi_cham_ky_da_thu',
        'name' => 'Phí chậm kỳ NL đã thu',
        'value' => $thongTinThanhToan['fee_overdue_cycle_paid'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['fee_overdue_cycle_paid'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      [
        'label' => 'tong_phi_qua_han_da_thu',
        'name' =>  'Phí quá hạn NL đã thu',
        'value' => $thongTinThanhToan['fee_overdue_paid'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['fee_overdue_paid'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      
      [
        'label' => 'tong_phi_qua_han_da_giam',
        'name' => 'Giảm phí quá hạn',
        'value' => $thongTinThanhToan['fee_overdue_reduction'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['fee_overdue_reduction'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      [
        'label' => 'tong_phi_cham_ky_da_giam',
        'name' => 'Giảm phí chậm kỳ',
        'value' => $thongTinThanhToan['fee_overdue_cycle_reduction'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['fee_overdue_cycle_reduction'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      [
        'label' => 'tong_tien_nhan',
        'name' => 'Tổng tiền nhận',
        'value' => $thongTinThanhToan['total_amount_receiver'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['total_amount_receiver'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'tong_tien_thu_dua',
        'name' => 'Tổng tiền đã thu thừa',
        'value' => $collectDebtSummary->total_amount_excess_revenue,
        'value_as_price' => Helper::priceFormat($collectDebtSummary->total_amount_excess_revenue, 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'tien_thu_thua_duoc_hoan',
        'name' => 'Tiền thu thừa được hoàn',
        'value' => $collectDebtSummary->total_amount_excess_refund,
        'value_as_price' => Helper::priceFormat($collectDebtSummary->total_amount_excess_refund, 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'phi_ck_duoc_hoan',
        'name' => 'Phí chậm kỳ được hoàn',
        'value' => $collectDebtSummary->fee_overdue_cycle_refund,
        'value_as_price' => Helper::priceFormat($collectDebtSummary->fee_overdue_cycle_refund, 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'phi_qh_duoc_hoan',
        'name' => 'Phí quá hạn được hoàn',
        'value' => $collectDebtSummary->fee_overdue_refund,
        'value_as_price' => Helper::priceFormat($collectDebtSummary->fee_overdue_refund, 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      [
        'label' => 'da_hoan',
        'name' => 'Đã hoàn',
        'value' => $thongTinThanhToan['total_amount_refund'],
        'value_as_price' => Helper::priceFormat($thongTinThanhToan['total_amount_refund'], 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => $descDaHoan,
      ],

			[
        'label' => 'tong_tien_thu_thua_da_su_dung_de_thanh_toan_tiep',
        'name' => 'Tổng tiền thu thừa đã được dùng để thanh toán tiếp',
        'value' => $collectDebtSummary->total_amount_repayment_debt,
        'value_as_price' => Helper::priceFormat($collectDebtSummary->total_amount_repayment_debt, 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

      [
        'label' => 'so_ky_cham',
        'name' => 'Số kỳ chậm',
        'value' => $collectDebtSummary->number_over_cycle,
        'value_as_price' => $collectDebtSummary->number_over_cycle,
        'display' => 1,
        'currency' => 'Kỳ',
				'desc' => '',
      ],

      [
        'label' => 'so_ngay_qua_han',
        'name' => 'Số ngày quá hạn',
        'value' => $collectDebtSummary->number_day_overdue,
        'value_as_price' => $collectDebtSummary->number_day_overdue,
        'display' => 1,
        'currency' => 'Ngày',
				'desc' => '',
      ],

      [
        'label' => 'tong_thu_sau_giam_tru',
        'name' => 'Tổng thu sau giảm trừ',
        'value' => $tongTienConPhaiThuSauKhiGiamTru,
        'value_as_price' => Helper::priceFormat($tongTienConPhaiThuSauKhiGiamTru, 'đ'),
        'display' => 1,
        'currency' => $currency,
				'desc' => $desc,
      ],

			[
        'label' => 'profile_id',
        'name' => 'ProfileID',
        'value' => $collectDebtSummary->getProfileId(),
        'value_as_price' => '',
        'display' => 0,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'is_send_mail_slow_cycle',
        'name' => 'Cần phải gửi mail chậm kỳ',
        'value' => $collectDebtSummary->is_send_mail_slow_cycle == 1 ? 'Có' : 'Không',
        'value_as_price' => '',
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'is_send_mail_overdue',
        'name' => 'Cần phải gửi mail quá hạn',
        'value' => $collectDebtSummary->is_send_mail_overdue == 1 ? 'Có' : 'Không',
        'value_as_price' => '',
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'is_send_noti_overdue',
        'name' => 'Cần phải gửi noti quá hạn qua app',
        'value' => $collectDebtSummary->is_send_noti_overdue == 1 ? 'Có' : 'Không',
        'value_as_price' => '',
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],

			[
        'label' => 'is_request_sync',
        'name' => 'Cần phải đồng bộ',
        'value' => $collectDebtSummary->is_request_sync == 1 ? 'Có' : 'Không',
        'value_as_price' => '',
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
      ],
    ];

   
    $statistic[] = [
      'label' => 'tinh_trang_thanh_toan',
      'name' => 'Tình trạng thanh toán',
      'value' => $collectDebtSummary->isHopDongDaTatToan() ? 'Đã tất toán' : 'Đang thanh toán',
      'value_as_price' => $collectDebtSummary->isHopDongDaTatToan() ? 'badge-success' : 'badge-warning',
      'display' => 0,
      'currency' => $currency,
			'desc' => '',
    ]; 

		// Check thêm điều kiện đã áp dụng phí phạt trả chậm
		$collectDebtLateFee = CollectDebtLateFee::query()->where('contract_code', $collectDebtSummary->contract_code)
																										 ->where('type', CollectDebtEnum::TYPE_PHI_PHAT_TRA_CHAM)
																										 ->first();
																										 
		if ($collectDebtLateFee) {
			$m = 'Áp dụng phí phạt trả chậm ('.$collectDebtLateFee->percent_late_fee.'%)';
			array_unshift($statistic, [
				'label' => 'phat_tra_cham',
        'name' => $m,
        'value' => $collectDebtLateFee->total_fee,
        'value_as_price' => Helper::priceFormat($collectDebtLateFee->total_fee),
        'display' => 1,
        'currency' => $currency,
				'desc' => '',
			]);
		}
    return $statistic;
  }
 } // End class