<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestOpenAndMinusFreezeAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestOpenAndMinusFreezeAction\SubAction\GiamTruSoDuVaDongBangViSubAction;

class DebtRecoveryRequestOpenAndMinusFreezeAction
{
	// Job thuc hien giam tru so du vi, doc vao bang log chu khong doc vao, json nhu cu nua
	public function run()
	{
		$collectDebtLog = CollectDebtLog::query()
																		->where('service_code', CollectDebtEnum::RL_DICH_VU_TRU_TIEN_VAO_VI)
																		->where('status', CollectDebtEnum::RL_STT_MOI_TAO)
																		->first();

		if (!$collectDebtLog) {
			mylog(['EMPTY' => 'khong co thong tin xu ly vi']);
			throw new Exception('khong co thong tin xu ly vi');
		}

		// lay thong tin yeu cau ra truoc
		$collectDebtRequest = CollectDebtRequest::query()
																							->where('payment_method_code', 'WALLET')
																							->where('partner_transaction_id', $collectDebtLog->partner_transaction_id)
																							->whereHas('collectDebtLedger', function ($query) {
																								return $query->where('status', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN);
																							})
																							->first();

		if (!$collectDebtRequest) {
			mylog(['[EMPTY]' => 'khong tim thay yeu cau trich qua vi']);
			throw new Exception('khong tim thay yeu cau trich qua vi');
		}

		mylog(['ID yeu cau tru vi' => $collectDebtRequest->id]);

		// update len dang xu ly
		$updateLenDangXuLy = CollectDebtLog::query()
																			 ->where('id', $collectDebtLog->id)
																			 ->where('status', CollectDebtEnum::RL_STT_MOI_TAO)
																			 ->update([
																				'status' => CollectDebtEnum::RL_STT_DANG_XU_LY
																			 ]);

		if (!$updateLenDangXuLy) {
			mylog(['LOI UPDATE DANG XU LY' => 'khong the cap nhat len dang xu ly']);
			throw new Exception('khong the cap nhat len dang xu ly');
		}

		mylog(['Ban ghi log thuc hien tru tien la' => $collectDebtLog]);

		DB::beginTransaction();
		try {
			// Update ve thanh cong truoc, neu call sang ben Profile ma loi thi huy luon
			$updateLogVeDaXuLy = CollectDebtLog::query()
																				 ->where('id', $collectDebtLog->id)
																				 ->update(['status' => CollectDebtEnum::RL_STT_DA_XU_LY_THANH_CONG]);

			if (!$updateLogVeDaXuLy) {
				mylog(['[LOI CAP NHAT DA XU LY]' => $updateLogVeDaXuLy]);
				throw new Exception('LOI CAP NHAT DA XU LY');
			}

			// thuc hien giam tru dong bang 
			$giamTruSoDuVaTienDongBang = app(GiamTruSoDuVaDongBangViSubAction::class)->run($collectDebtRequest);
			mylog(['Log giam tru dong bang' => $giamTruSoDuVaTienDongBang]);

			// giam tru thanh cong
			if (!empty($giamTruSoDuVaTienDongBang['data']['id'])) {
				mylog(['Goi giam tru thanh cong' => 'ok']);

				$requestOtherData = $collectDebtRequest->getRequestOtherData();
				$requestOtherData[] = [
					'type' => 'OPEN_AND_MINUS_FREEZE',
					'note' => 'Giảm trừ số dư ví và tiền đóng băng',
					'data' => $giamTruSoDuVaTienDongBang,
					'time_modified' => time()
				];

				$collectDebtRequest->other_data = json_encode($requestOtherData);
				$collectDebtRequest->save();

				DB::commit();
				return $collectDebtRequest;
			}

			// giam tru that bai
			mylog(['[LOI GIAM TRU DONG BANG]' => 'LOI']);
			throw new Exception('LOI GIAM TRU DONG BANG');

		} catch (\Throwable $th) {
			mylog(['[Error]' => Helper::traceError($th)]);
			DB::rollBack();

			$updateVeChuaXuLy = CollectDebtLog::query()->where('id', $collectDebtLog->id)
																				->where('status', CollectDebtEnum::RL_STT_DANG_XU_LY)
																				->update(['status' => CollectDebtEnum::RL_STT_MOI_TAO]);

			mylog(['Ket qua update ve chua xu ly la:' => $updateVeChuaXuLy]);

			@TelegramAlert::sendGhiSo(Helper::traceError($th));
			throw $th;
		}
	}
}  // End class