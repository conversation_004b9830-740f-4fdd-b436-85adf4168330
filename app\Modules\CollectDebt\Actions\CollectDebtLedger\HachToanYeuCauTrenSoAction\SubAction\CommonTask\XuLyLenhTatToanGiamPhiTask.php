<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class XuLyLenhTatToanGiamPhiTask
{
	public function run(CollectDebtRequest $yeuCauGiamPhi, Collection $plans)
	{
		mylog(['[INFO]' => 'La yc tat toan giam phi']);
		$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $yeuCauGiamPhi->contract_code)->first();
		throw_if(!$collectDebtSummary, new Exception('Khong tim duoc thong tin HD'));

		$returnData = [];

		// TH1: Da thu het goc, con phi
		if ($collectDebtSummary->total_amount_paid >= $collectDebtSummary->contract_amount) {
			mylog(['TH1' => 'Da thu het goc, con phi']);

			foreach ($plans as $plan) {
				$soTienGiamPhi = $plan->getSoTienConPhaiThanhToan();
				if ($plan->isLichThuPhiQuaHan()) {
					$returnData[] = [
						'label' => CollectDebtEnum::METADATA_GIAM_PHI_QUA_HAN,
						'value' => $soTienGiamPhi
					];
				}

				if ($plan->isLichThuPhiChamKy()) {
					$returnData[] = [
						'label' => CollectDebtEnum::METADATA_GIAM_PHI_CHAM_KY,
						'value' => $soTienGiamPhi
					];
				}
			}
		}

		// TH2: Chua het goc
		if ($collectDebtSummary->total_amount_paid < $collectDebtSummary->contract_amount) { 
			/**
		 * Update 06.06.2024 đoạn này có thể đang giảm phí cho lịch thu gốc, nên cần chọc vào summary để tính toán
			* Đọc dữ liệu ở bảng tổng hợp hiện tại thì lấy được thông tin sau: 
					Số tiền nợ gốc còn phải thu (A)
					Số tiền phí CK đã thu (B) 
					Số tiền phí QH đã thu ( C) 
				
				Tính toán lại số trên sổ mới: 
					Số tiền nợ gốc đã thu A1: 
					Số tiền phí CK đã thu B1: 
					Số tiền phí QH đã thu C1: 

				Nếu B < = A thì: A1 = A, B1 =  - B , C1 = B - A
				Nếu B > A, thì : A1 = A, B1 =  - A,  C1 =  0. 
		 */
		
			$A = $collectDebtSummary->contract_amount - $collectDebtSummary->total_amount_paid; // no goc con phai thu
			$B = $collectDebtSummary->fee_overdue_cycle_paid; // phi ck da thu
			$C = $collectDebtSummary->fee_overdue_paid; // phi qh da thu

			mylog([
				'Hien Tai - Truoc Khi Xu Ly' => 'ok',
				'No goc con phai thu' => $A, 
				'Phi Cham Ky Da Thu' => $B, 
				'Phi Qua Han Da Thu' => $C
			]);

			if ($B <= $A) {
				mylog(['Hien tai' => 'Phi CK da thu <= No goc phai thu']);
				$A1 = $A;
				$B1 = -$B;
				$C1 = $B-$A;
			}

			if ($B > $A) {
				mylog(['Hien tai' => 'Phi CK da thu > No goc phai thu']);

				$A1 = $A;
				$B1 = -$A;
				$C1 = 0;
			}

			mylog([
				'No goc se tinh tien vao tong hop' => $A1,
				'Phi CK da thanh toan tinh tien vao tong hop' => $B1,
				'Phi QH da thanh toan duoc tinh tien vao tong hop' => $C1,
			]);

			$phiChamKyDuocGiam = abs($B1);
			$phiQuaHanDuocGiam = abs($C1);

			if (empty($phiQuaHanDuocGiam)) {
				$phiQuaHanDuocGiam = $yeuCauGiamPhi->getSoTienDuocGiamPhi() - $phiChamKyDuocGiam;
				if ($phiQuaHanDuocGiam < 0) {
					$phiQuaHanDuocGiam = 0;
				}
			}

			$returnData = [
				[ 'label' => CollectDebtEnum::METADATA_THU_GOC, 'value' => $A1 ],

				[ 'label' => CollectDebtEnum::METADATA_THANH_TOAN_PHI_CK, 'value' => $B1 ],

				[ 'label' => CollectDebtEnum::METADATA_THANH_TOAN_PHI_QH, 'value' => $C1 ],

				[ 'label' => CollectDebtEnum::METADATA_GIAM_PHI_CHAM_KY, 'value' => $phiChamKyDuocGiam ],

				[ 'label' => CollectDebtEnum::METADATA_GIAM_PHI_QUA_HAN, 'value' => $phiQuaHanDuocGiam ],
			];

			mylog($returnData);
		}
	
		return [
			'request_reduce_fee' => $yeuCauGiamPhi,
			'meta_data' => $returnData
		];
	}
} // End class