<?php


use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Route;
use GuzzleHttp\Exception\RequestException;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebtGateway\Repositories\Connections\NextLendServiceConnection;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;

Route::any('/CheckRedis', function () {
	$collectDebtRequests = CollectDebtRequest::query()->where('payment_method_code', 'MPOS')
	->where('status_payment', 5)
	->latest('id')
	->get();
	
	$inputs = [];

	foreach ($collectDebtRequests as $rq) {
		$inputs[] = app(CheckRequestViaMposSubAction::class)->buildParamsDirect($rq);
	}


	// 2. Khởi tạo service
	$nextLend = new NextLendServiceConnection();
	$client = new Client([
		'base_uri' => "https://dev-service.nextlend.vn/nextlend-v1-service-v2/request.php",
		'timeout' => 10, 
		'verify' => false
	]);

	// 3. Generator tạo từng request
	$requests = function () use ($inputs, $nextLend) {
		foreach ($inputs as $input) {
			yield $nextLend->buildHttpRequest($input, 'checkDebt');
		}
	};

	// 4. Pool chạy song song
	$pool = new Pool($client, $requests(), [
		'concurrency' => 10,
		'fulfilled' => function ($response, $index) {
			$body = (string)$response->getBody();
			$decoded = json_decode($body, true);
			dump($decoded);
		},
		'rejected' => function (RequestException $reason, $index) {
			echo "✗ #$index failed: " . $reason->getMessage() . "\n";
		},
	]);

	$promise = $pool->promise();
	$promise->wait();

	dd("end");
});

Route::any('/SetRedis', function () {
	$r = Cache::put("ListTrichNo", "ok", now()->addMinutes(5));
	dump($r);

	if (!$r) {
		throw new Exception('Loi set redis');
	}
});

Route::any('/RemoveCache', function () {
	Cache::forget("ListTrichNo");
});
