<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\CreateRequestFromProactiveFlowSubAction\Task\ST;

use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;

class GetHopDongKhongPhuHopDeTaoYeuCauST
{
	private array $__contractCodeCoPartnerChuaXuLy = [];

  public function run()
  {
    $hopDongCoYeuCauTrichTay = CollectDebtRequest::query()
      ->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
      ->where('status', CollectDebtEnum::REQUEST_STT_MOI_TAO)
      ->select(['id', 'contract_code']);



    $hopDongDangDungJobTuDong =  CollectDebtConfigAuto::query()
																											->where('time_expired', '>', time())
																											->select(['id', 'contract_code']);

		// Dùng query builder để không "append" các trường thừa, tránh lỗi câu lệnh union
    $hopDongDangHachToan = DB::table('debt_recovery_contract_plan')
														 ->where('status', CollectDebtEnum::SCHEDULE_STT_DANG_HACH_TOAN)
														 ->orWhere(function ($q) {
																return $q->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
																				 ->where('rundate', '<', now()->format('Ymd'));
														 })
														 ->selectRaw("id, contract_code");

    $hopDongCoYeuCauChuaVeTrangThaiCuoi = $this->getHopDongCoYeuCauChuaVeTrangThaiCuoi();


		$hopDongChuaHachToanXong = CollectDebtLedger::query()
																								->where('status', '!=', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
																								->select(['id', 'contract_code']);
																								
		if (request()->get('is_debug')) {
			mylog([
				'hopDongCoYeuCauTrichTay' => (clone $hopDongCoYeuCauTrichTay)->get()->toArray(),
				'hopDongDangDungJobTuDong' => (clone $hopDongDangDungJobTuDong)->get()->toArray(),
				'hopDongDangHachToan' => (clone $hopDongDangHachToan)->get()->toArray(),
				'hopDongCoYeuCauChuaVeTrangThaiCuoi' => (clone $hopDongCoYeuCauChuaVeTrangThaiCuoi)->get()->toArray(),
				'hopDongChuaHachToanXong' => (clone $hopDongChuaHachToanXong)->get()->toArray(),
			]);
		}
   
    $results = $hopDongCoYeuCauTrichTay->unionAll($hopDongDangDungJobTuDong)
                                       ->unionAll($hopDongDangHachToan)
                                       ->unionAll($hopDongCoYeuCauChuaVeTrangThaiCuoi)
                                       ->unionAll($hopDongChuaHachToanXong)
                                       ->get()
                                       ->pluck('contract_code')
                                       ->toArray();
    return $results;
  }

  public function getHopDongCoYeuCauChuaVeTrangThaiCuoi()
  {
    $hopDongCoYeuCauChuaVeTrangThaiCuoi = CollectDebtRequest::query()
      // Chưa về trạng thái cuối
      ->whereNotIn('status', [
        CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
        CollectDebtEnum::REQUEST_STT_TU_CHOI,
        CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA
      ])
      ->orWhere(function ($q) {
        // Đã về trạng thái cuối nhưng chưa ghi sổ
        $q->where('status', CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH)
          ->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO);
      })
      ->select(['id', 'contract_code']);


    return $hopDongCoYeuCauChuaVeTrangThaiCuoi;
  }

  /**
	 * Điều kiện này như sau: 
	 * 		+ Nếu như đã có partner, thì ở job PartnerCheck sẽ thực hiện tạo yêu cầu và ốp partner vào yêu cầu đó
	 * 			-> do vậy, cần lấy ra các HĐ trên các partner này và né nó ra và luồng này sẽ không tạo yêu cầu cho các HĐ đó nữa 
	 * @return void
	 */
	public function getHopDongDangCoCongNoCanXuLy()
  {
    $collectDebtPartners = CollectDebtPartner::query()
																							->whereIn('status', [
																								CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
																								CollectDebtEnum::PARTNER_STT_CHO_DUYET,
																								CollectDebtEnum::PARTNER_STT_DANG_XU_LY
																							])
																							->whereNotNull('other_data')
																							->select(['id', 'other_data'])
																							->get();

    mylog(['Danh sach partner CHUA XU LY, can phai ne' => $collectDebtPartners->pluck('id')]);
		
		$collectDebtPartners->each(function (CollectDebtPartner $partner) {
			$this->__contractCodeCoPartnerChuaXuLy = array_merge($this->__contractCodeCoPartnerChuaXuLy, $partner->getContractCodeOtherData());
		});

		return $this->__contractCodeCoPartnerChuaXuLy;
  }
} // End class
