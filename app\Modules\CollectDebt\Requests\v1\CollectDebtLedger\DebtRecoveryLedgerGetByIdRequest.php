<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtLedger;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryLedgerGetByIdRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data'               => ['required', 'array'],
      'data.id'            => ['required_without:data.request_id', 'numeric', 'min:1'], 
      'data.request_id'            => ['required_without:data.id', 'numeric', 'min:1'], 
      'data.user_request_id' => ['nullable', 'string', 'max:255'],
    ];
  }

  public function hasId(): bool {
    return !empty($this->json('data.id'));
  }

  public function hasRequestId(): bool {
    return !empty($this->json('data.request_id'));
  }
} // End class
