<?php
namespace App\Modules\EmailRemind\Controllers;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\EmailRemind\Resources\CollectDebtContractEventResourceCollection;
use App\Modules\EmailRemind\Request\CollectDebtContractEvent\DebtRecoveryContractEventCreateRequest;
use App\Modules\EmailRemind\Request\CollectDebtContractEvent\DebtRecoveryContractEventUpdateRequest;
use App\Modules\EmailRemind\Request\CollectDebtContractEvent\DebtRecoveryContractEventGetByIdRequest;
use App\Modules\EmailRemind\Request\CollectDebtContractEvent\DebtRecoveryContractEventSetStatusRequest;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateAction\DebtRecoveryContractEventCreateAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventUpdateAction\DebtRecoveryContractEventUpdateAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventGetByIdAction\DebtRecoveryContractEventGetByIdAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventSetStatusAction\DebtRecoveryContractEventSetStatusAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\DebtRecoveryContractEventCreateSendAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventSearchDataAction\DebtRecoveryContractEventSearchDataAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\DebtRecoveryContractEventBuildContentAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventGetByContractAction\DebtRecoveryContractEventGetByContractAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventHandleStatusProcess\DebtRecoveryContractEventHandleStatusProcess;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\DebtRecoveryContractEventBuildContentImproveAction;

class DebtRecoveryContractEventController extends Controller
{
    public function create(DebtRecoveryContractEventCreateRequest $request)
	{
		try {
			$data = $request->json('data');
			$collectDebtContractEvent = app(DebtRecoveryContractEventCreateAction::class)->run($data);
			return $this->successResponse($collectDebtContractEvent, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

    public function update(DebtRecoveryContractEventUpdateRequest $request)
	{
		try {
			$data = $request->json('data');
			$collectDebtContractEvent = app(DebtRecoveryContractEventUpdateAction::class)->run($data);
			return $this->successResponse($collectDebtContractEvent, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

    public function getById(DebtRecoveryContractEventGetByIdRequest $request)
	{
		try {
			$data = $request->json('data');
			$collectDebtContractEvent = app(DebtRecoveryContractEventGetByIdAction::class)->run($data);
			return $this->successResponse($collectDebtContractEvent, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

    public function setStatus(DebtRecoveryContractEventSetStatusRequest $request)
	{
		try {
			$data = $request->json('data');
			$collectDebtContractEvent = app(DebtRecoveryContractEventSetStatusAction::class)->run($data);
			return $this->successResponse($collectDebtContractEvent, $request, 200, 'Đổi trạng thái thành công');
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function searchData(Request $request)
	{
		try {
			$collectDebtContractEventPaginate = app(DebtRecoveryContractEventSearchDataAction::class)->run($request);
			$collectDebtContractEventResource = new CollectDebtContractEventResourceCollection($collectDebtContractEventPaginate);
			$response = $collectDebtContractEventResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function buildContent(Request $request)
	{
		try {
			$data = $request->json('data');
			$collectDebtContractEvent = app(DebtRecoveryContractEventBuildContentImproveAction::class)->initBuildContent();
			return $this->successResponse($collectDebtContractEvent, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function createSend(Request $request)
	{
		try {
			$collectDebtContractEvent = app(DebtRecoveryContractEventCreateSendAction::class)->initEventCreateSend();
			return $this->successResponse($collectDebtContractEvent, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getByContract(Request $request)
	{
		try {
			$collectDebtContractEventPaginate = app(DebtRecoveryContractEventGetByContractAction::class)->run($request);
			$resouce = new \App\Modules\CollectDebt\Resources\CollectDebtContractEventResourceCollection($collectDebtContractEventPaginate);
			$response = $resouce->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function handleStatusProcess(Request $request)
	{
		try {
			$data = $request->json('data');
			$collectDebtContractEvent = app(DebtRecoveryContractEventHandleStatusProcess::class)->initXuLyEventBiTreo();
			return $this->successResponse($collectDebtContractEvent, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class