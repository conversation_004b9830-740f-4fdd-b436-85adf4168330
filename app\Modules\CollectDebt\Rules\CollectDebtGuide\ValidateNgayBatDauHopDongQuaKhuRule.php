<?php

namespace App\Modules\CollectDebt\Rules\CollectDebtGuide;

use Carbon\Carbon;
use Illuminate\Contracts\Validation\Rule;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSetting;

class ValidateNgayBatDauHopDongQuaKhuRule implements Rule
{
		public int $soNgayQuaKhuGioiHan = 15;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
			$isEnableSetting = CollectDebtSetting::isEnableSetting('IS_ALLOW_HOP_DONG_QUA_KHU');
			
			if ($isEnableSetting) {
				return true;
			}

      $ngayBatDauHopDong = Carbon::createFromFormat('d-m-Y H:i:s', $value);
			$expression = now()->diffInDays($ngayBatDauHopDong) <= $this->soNgayQuaKhuGioiHan;
			return $expression;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
      return sprintf('Ngày bắt đầu hợp đồng không được nhỏ hơn thời điểm hiện tại quá %s ngày', $this->soNgayQuaKhuGioiHan);
    }
}
