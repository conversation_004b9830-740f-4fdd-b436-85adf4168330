<?php

namespace App\Modules\CollectDebtGateway\Repositories;

use App\Lib\Helper;
use App\Modules\CollectDebtGateway\Inter\MposCollectDebtGatewayInterface;
use App\Modules\CollectDebtGateway\Model\CollectDebtRecoveryGatewayLog;
use App\Modules\CollectDebtGateway\Repositories\Connections\NextLendServiceConnection;
use Illuminate\Support\Facades\DB;

class CollectDebtGatewayRepository implements MposCollectDebtGatewayInterface
{
  protected CollectDebtRecoveryGatewayLog $collectDebtLog;

  public function __construct(CollectDebtRecoveryGatewayLog $collectDebtLog)
  {
    $this->collectDebtLog = $collectDebtLog;
  }

  /**
   * @param $data
   * @return array
   */
  public function sendCollectDebtCommand($data)
  {

    $result = app(NextLendServiceConnection::class)->callRequest(
      $data,
      'sendDebt',
    );

    $errorCode = isset($result['RespCode']) ? $result['RespCode'] : '';
    if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
      $data = json_decode($result['data'], true);
      if (!empty($data) && is_array($data)) {
        $result = $data;
        if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
          $data = json_decode($result['data'], true);
          if (!empty($data) && is_array($data)) {
            $result = $data;
          }
        }
      }
    }

    $dataResponse = [
      'errorCode' => $errorCode,
      'data' => $result
    ];

    return $dataResponse;
  }

  /**
   * @param $data
   * @return array
   */
  public function checkBalanceMerchant($data)
  {

    $result = app(NextLendServiceConnection::class)->callRequest(
      $data,
      'getBalance',
    );

    $errorCode = isset($result['RespCode']) ? $result['RespCode'] : '';
    if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
      $data = json_decode($result['data'], true);
      if (!empty($data) && is_array($data)) {
        $result = $data;
        if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
          $data = json_decode($result['data'], true);
          if (!empty($data) && is_array($data)) {
            $result = $data;
          }
        }
      }
    }

    $dataResponse = [
      'errorCode' => $errorCode,
      'data' => $result
    ];

    return $dataResponse;
  }

  /**
   * @param $data
   * @return array
   */
  public function checkCollectDebtCommandOnPartner($data)
  {
    $result = app(NextLendServiceConnection::class)->callRequest(
      $data,
      'checkDebt',
    );

    $errorCode = isset($result['RespCode']) ? $result['RespCode'] : '';
    if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
      $data = json_decode($result['data'], true);
      if (!empty($data) && is_array($data)) {
        $result = $data;
        if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
          $data = json_decode($result['data'], true);
          if (!empty($data) && is_array($data)) {
            $result = $data;
          }
        }
      }
    }

    $dataResponse = [
      'errorCode' => $errorCode,
      'data' => $result
    ];

    return $dataResponse;
  }


  /**
   * @param $data
   * @return array
   */
  public function cancelCollectDebtCommand($data)
  {
    $result = app(NextLendServiceConnection::class)->callRequest(
      $data,
      'cancelDebt',
    );

    $errorCode = isset($result['RespCode']) ? $result['RespCode'] : '';
    if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
      $data = json_decode($result['data'], true);
      if (!empty($data) && is_array($data)) {
        $result = $data;
        if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
          $data = json_decode($result['data'], true);
          if (!empty($data) && is_array($data)) {
            $result = $data;
          }
        }
      }
    }

    $dataResponse = [
      'errorCode' => $errorCode,
      'data' => $result
    ];

    return $dataResponse;
  }

  public function notifyContractFinish($data)
  {
    $result = app(NextLendServiceConnection::class)->callRequest(
      $data,
      'notifyContractFinish',
    );

    $errorCode = isset($result['RespCode']) ? $result['RespCode'] : '';
    if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
      $data = json_decode($result['data'], true);
      if (!empty($data) && is_array($data)) {
        $result = $data;
        if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
          $data = json_decode($result['data'], true);
          if (!empty($data) && is_array($data)) {
            $result = $data;
          }
        }
      }
    }

    $dataResponse = [
      'errorCode' => $errorCode,
      'data' => $result
    ];

    return $dataResponse;
  }

  /**
   * @param $data
   * @return array
   */
  public function reCheckCollectDebtCommandOnPartner($data)
  {
    $result = app(NextLendServiceConnection::class)->callRequest(
      $data,
      'recheckDebt',
    );

    $errorCode = isset($result['RespCode']) ? $result['RespCode'] : '';
    if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
      $data = json_decode($result['data'], true);
      if (!empty($data) && is_array($data)) {
        $result = $data;
        if (isset($result['data']) && Helper::jsonValidator($result['data'])) {
          $data = json_decode($result['data'], true);
          if (!empty($data) && is_array($data)) {
            $result = $data;
          }
        }
      }
    }

    $dataResponse = [
      'errorCode' => $errorCode,
      'data' => $result
    ];

    return $dataResponse;
  }
} // End class