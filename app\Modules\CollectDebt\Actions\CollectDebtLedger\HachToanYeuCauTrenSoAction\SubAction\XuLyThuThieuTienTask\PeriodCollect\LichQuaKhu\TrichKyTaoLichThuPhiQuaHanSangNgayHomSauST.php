<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\LichQuaKhu;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TrichKyTaoLichThuPhiQuaHanSangNgayHomSauST
{
  public function run(CollectDebtSchedule $lichDangHachToan, float $phiQuaHan = 0)
  {
    $scheduleParam = [
      'profile_id'           => $lichDangHachToan->profile_id,
      'contract_code'        => $lichDangHachToan->contract_code,
      'contract_type'        => $lichDangHachToan->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
      'debit_begin'          => $phiQuaHan,
      'debit_end'            => 0,
      'rundate'              => getRundateHomSau($lichDangHachToan),
      'time_start'           => getTimeStartHomSau($lichDangHachToan),
      'time_end'             => getTimeEndHomSau($lichDangHachToan),
      'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
      'request_amount_debit' => $phiQuaHan,
      'success_amount_debit' => 0,
      'other_data'           => $lichDangHachToan->other_data,
      'description'          => $lichDangHachToan->collectDebtSchedule,
      'is_settlement'        => $lichDangHachToan->is_settlement,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => $lichDangHachToan->created_by,
      'time_created'         => time(),
      'cycle_number'         => $lichDangHachToan->cycle_number,
      'master_id'         => $lichDangHachToan->master_id,
    ];

    return CollectDebtSchedule::forceCreate($scheduleParam);
  }
} // End class