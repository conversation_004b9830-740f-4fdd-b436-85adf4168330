<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByContractAction;

use App\Lib\Security;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerGetByContractRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerGetByIdAction\SubAction\DebtRecoveryLedgerGetWhereRawSubAction;
use Exception;

class DebtRecoveryLedgerGetByContractAction
{
  public function run(DebtRecoveryLedgerGetByContractRequest $request): Collection
  {
    $contractCode = $request->json('data.contract_code');
    
    Security::checkSqlCharacter($contractCode);

    $whereRaw = sprintf("contract_code = '%s'", $contractCode);
    $collectDebtLedgers = app(DebtRecoveryLedgerGetWhereRawSubAction::class)->run($whereRaw);
    
    return $collectDebtLedgers;
  }
} // End class