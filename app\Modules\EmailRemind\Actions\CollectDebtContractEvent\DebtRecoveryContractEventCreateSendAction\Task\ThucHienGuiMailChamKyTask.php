<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task;

use Exception;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\ExecuteSendEmailAction\ExecuteSendEmailAction;

class ThucHienGuiMailChamKyTask
{
	public function run(CollectDebtContractEvent $collectDebtContractEvent, array $listEmailCc = [])
	{
		mylog([
			'Mail Cham Ky' => 'Yes',
			'ContractCode' => $collectDebtContractEvent->contract_code
		]);


		$subject = sprintf(
			'NextLend Thông Báo đến `%s` Tình Trạng Hoàn Tiền Ứng Vốn Kinh <PERSON>h <PERSON>ày %s',
			$collectDebtContractEvent->getNguoiDaiDien(),
			date('d-m-Y')
		);

		$sendMailResult = app(ExecuteSendEmailAction::class)->run(
			$subject,
			$collectDebtContractEvent->content,
			config('collect_debt_email_remind_config.mail.NHAC_NO_THANH_TOAN.sender'),
			$collectDebtContractEvent->getEmailKhachVay(),
			$listEmailCc,
			$collectDebtContractEvent->category_care_code,
			sprintf('%s|CK-%s', $collectDebtContractEvent->contract_code, time())
		);

		if (empty($sendMailResult['id'])) {
			mylog(['Loi gui mail cham ky' => $sendMailResult]);
			throw new Exception('Loi gui mail cham ky');
		}

		return $sendMailResult;
	}
} // End class