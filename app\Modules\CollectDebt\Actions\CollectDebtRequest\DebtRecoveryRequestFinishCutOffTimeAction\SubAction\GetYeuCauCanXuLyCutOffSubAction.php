<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use Illuminate\Database\Eloquent\Collection;

class GetYeuCauCanXuLyCutOffSubAction
{
	public function run(): Collection
	{
		$collectDebtRequests = CollectDebtRequest::query()
			->where('time_expired', '<', now()->timestamp)
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->whereNotIn('status', [
				CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH,
				CollectDebtEnum::REQUEST_STT_TU_CHOI,
			])
			->where('status_payment', '!=', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA)
			->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT)
			->where('status_cutoff', CollectDebtEnum::REQUEST_CHUA_CUT_OFF)
			->where(function ($q) {
				return $q->whereRaw("LENGTH(partner_transaction_id) = 0")
							 	 ->orDoesntHave('collectDebtPartner');
			})
			->limit(1)
			->get();
			
		return $collectDebtRequests;
	}
}
