<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhSummarySA;

use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhSummarySA\Task\TinhSoKyBiChamVaSoNgayQuaHanTask;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtRequestAdjustment;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummarySetByAccountingRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySetByAccounting\DebtRecoverySummarySetByAccounting;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhSummarySA\Task\MoLaiHopDongVer1Task;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAdjustment\DebtRecoveryRequestAdjustmentProcessAction\SubAction\XuLyDieuChinhSummarySA\Task\MoLaiPhuongThucThanhToanVer4Task;

class XuLyDieuChinhSummarySA
{
  public function run(CollectDebtRequestAdjustment $ra, CollectDebtLedger $collectDebtLedger): CollectDebtSummary
  {
    $collectDebtSummary = $ra->collectDebtSummary;

    if ($collectDebtSummary->isHopDongDaTatToan()) {
      $moLaiHopDongVer1 = app(MoLaiHopDongVer1Task::class)->run($collectDebtSummary);
    }

    $moLaiPhuongThucThanhToan = app(MoLaiPhuongThucThanhToanVer4Task::class)->run($collectDebtSummary);

    // Gọi lại hàm tịnh tiến số âm
    $summaryLedger = $collectDebtLedger->getOtherDataItem('REQUEST_ADJUSTMENT')['data']['summary_adjustment'];
    $rq = new DebtRecoverySummarySetByAccountingRequest();

    $soKyBiChamVaSoNgayQuaHan = app(TinhSoKyBiChamVaSoNgayQuaHanTask::class)->run($collectDebtSummary);

    $payload = [
      'data' => [
        'contract_code'               => $collectDebtSummary->contract_code,
        
        'plan'                        => $collectDebtLedger->getPlanIds(),
        'is_overdue'                  => $soKyBiChamVaSoNgayQuaHan['is_overdue'], // co ck hay khong
        'is_over_cycle'               => $soKyBiChamVaSoNgayQuaHan['is_over_cycle'], // co qh hay khong
        'number_over_cycle'           => $soKyBiChamVaSoNgayQuaHan['number_over_cycle'], // so ky bi cham,
        'number_day_overdue'           => $soKyBiChamVaSoNgayQuaHan['number_day_overdue'], // so ky bi cham,
        
        // Toan bo summary mo lai lich tren so
        'total_amount_debit_success' => $summaryLedger['total_amount_debit_success'],
        'total_amount_paid'           => $summaryLedger['total_amount_paid'],
        'total_amount_excess_revenue'   => $summaryLedger['total_amount_excess_revenue'],
        'fee_overdue_cycle'           => $summaryLedger['fee_overdue_cycle'],
        'fee_overdue'                 => $summaryLedger['fee_overdue'],
        'total_amount_receiver'       => $summaryLedger['total_amount_receiver'],
        'fee_overdue_reduction'       => 0,
        'fee_overdue_cycle_reduction' => 0,
        'amount_paid'       => $summaryLedger['amount_paid'],
        'fee_overdue_paid'            => $summaryLedger['fee_overdue_paid'],
        'fee_overdue_cycle_paid'      => $summaryLedger['fee_overdue_cycle_paid'],
        'total_fee_paid'              => $summaryLedger['fee_overdue_cycle_paid'] + $summaryLedger['fee_overdue_paid'],
      ]
    ];

    $rq->setJson(new ParameterBag((array) $payload));

    $tinhTienSummary = app(DebtRecoverySummarySetByAccounting::class)->run($rq);

    return $tinhTienSummary;
  }
}
