<?php 
namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyThuThieuTienTask\PeriodCollect\LichQuaKhu;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class TaoLichThuQuaHanSangNgayHomSauST {

  public function run(CollectDebtSchedule $lichDangHachToan, float $noGocConPhaiTra = 0, float $tongPhi = 0)
  {
    $returnScheduleCollection = Collection::make([]);

    if ($noGocConPhaiTra > 0) {
      $scheduleParam = [
        'profile_id'           => $lichDangHachToan->profile_id,
        'contract_code'        => $lichDangHachToan->contract_code,
        'contract_type'        => $lichDangHachToan->contract_type,
        'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
        'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
        'debit_begin'          => $noGocConPhaiTra,
        'debit_end'            => 0,
        'rundate'              => $lichDangHachToan->rundate_as_date->copy()->addDay()->format('Ymd'),
        'time_start'           => $lichDangHachToan->time_start_as_date->copy()->addDay()->startOfDay()->timestamp,
        'time_end'             => $lichDangHachToan->time_end_as_date->copy()->addDay()->endOfDay()->timestamp,
        'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
        'request_amount_debit' => $noGocConPhaiTra,
        'success_amount_debit' => 0,
        'other_data'           => $lichDangHachToan->other_data,
        'description'          => $lichDangHachToan->collectDebtSchedule,
        'is_settlement'        => $lichDangHachToan->is_settlement,
        'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
        'created_by'           => $lichDangHachToan->created_by,
        'time_created'         => time(),
        'cycle_number'         => $lichDangHachToan->cycle_number,
        'master_id'         => $lichDangHachToan->master_id,
      ];

      $loanOriginSchedule = CollectDebtSchedule::forceCreate($scheduleParam);
      $returnScheduleCollection->push($loanOriginSchedule);
    }

    if ($tongPhi > 0) {
      $feeScheduleParam = [
        'profile_id'           => $lichDangHachToan->profile_id,
        'contract_code'        => $lichDangHachToan->contract_code,
        'contract_type'        => $lichDangHachToan->contract_type,
        'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_PHU,
        'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_PHI,
        'debit_begin'          => $tongPhi,
        'debit_end'            => 0,
        'rundate'              => $lichDangHachToan->rundate_as_date->copy()->addDay()->format('Ymd'),
        'time_start'           => $lichDangHachToan->time_start_as_date->copy()->addDay()->startOfDay()->timestamp,
        'time_end'             => $lichDangHachToan->time_end_as_date->copy()->addDay()->endOfDay()->timestamp,
        'amount_period_debit'  => $lichDangHachToan->amount_period_debit,
        'request_amount_debit' => $tongPhi,
        'success_amount_debit' => 0,
        'other_data'           => $lichDangHachToan->other_data,
        'description'          => $lichDangHachToan->collectDebtSchedule,
        'is_settlement'        => $lichDangHachToan->is_settlement,
        'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
        'created_by'           => $lichDangHachToan->created_by,
        'time_created'         => time(),
        'cycle_number'        => $lichDangHachToan->cycle_number,
        'master_id'           => $lichDangHachToan->master_id
      ];

      $feeSchedule = CollectDebtSchedule::forceCreate($feeScheduleParam);
      $returnScheduleCollection->push($feeSchedule);
    }

    return $returnScheduleCollection;
  }
} // End class