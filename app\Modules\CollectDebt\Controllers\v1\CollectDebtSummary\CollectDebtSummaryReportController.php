<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary;

use App\Lib\Helper;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\CollectDebtSummaryResourceCollection;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryDebitReportRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtSummary\DebtRecoverySummaryPaymentCycleReportRequest;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryDebitReportAction\DebtRecoverySummaryDebitReportAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryPaymentCycleReportAction\DebtRecoverySummaryBaoCaoDenKyAction;

class CollectDebtSummaryReportController extends Controller
{
	public function debitReport(DebtRecoverySummaryDebitReportRequest $request)
	{
		try {
			$summaryReports = app(DebtRecoverySummaryDebitReportAction::class)->run($request);
			$resources = new CollectDebtSummaryResourceCollection($summaryReports);
			$response = $resources->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function paymentCycleReport(DebtRecoverySummaryPaymentCycleReportRequest $request)
	{
		try {
			$summaryReports = app(DebtRecoverySummaryBaoCaoDenKyAction::class)->run($request);
			$resources = new CollectDebtSummaryResourceCollection($summaryReports);
			$response = $resources->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
