<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckRefundAction\SubAction;

use DB;
use Exception;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class XuLyTuHoanThuThuaThanhCongSubAction
{
	public function run(CollectDebtLog $collectDebtLog, CollectDebtSummary $collectDebtSummary, $soTienDaThucHienHoan=0)
	{
		$soTienYeuCauHoan = $collectDebtLog->getSoTienYeuCauHoan();

		mylog([
			'soTienYeuCauHoan' => $soTienYeuCauHoan,
			'soTienDaThucHienHoan' => $soTienDaThucHienHoan
		]);

		if ($soTienDaThucHienHoan > $soTienYeuCauHoan){
			mylog(['LOI LOGIC HOAN TIEN THU THUA' => 'ok']);
			throw new Exception('LOI LOGIC HOAN TIEN THU THUA');
		}

		// giảm hết số tiền đã yc hoàn
		// tăng số tiền đã thực hiện hoàn thực tế
		$otherData = $collectDebtSummary->getSummaryOtherData();
		$otherData[] = [
			'type' => 'REFUND',
			'data' => [
				'soTienDaThucHienHoan' => $soTienDaThucHienHoan
			],
			'time_modified' => now()->timestamp,
			'note' => 'Hoàn thu thừa thành công'
		];

		$r = $collectDebtSummary->update([
			'amount_refunding' => DB::raw('amount_refunding - ' . $soTienYeuCauHoan),
			'total_amount_refund' => DB::raw('total_amount_refund + ' . $soTienDaThucHienHoan),
			'total_amount_excess_refund' => DB::raw('total_amount_excess_refund + ' . $soTienDaThucHienHoan),
			'other_data' => json_encode($otherData),
			'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO
		]);

		if (!$r) {
			mylog([
				'LOI CAP NHAT REFUNDING' => 'ok',
				'CASE' => 'Hoan tien 100%'
			]);
			throw new Exception('LOI CAP NHAT REFUNDINF');
		}

		return $collectDebtSummary;
	}
} // End class
