<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequestAdjustment;

use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryRequestAdjustmentSetStatusRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric'], // mã yc điều chỉnh
      'data.status' => ['required', 'numeric'], // status muốn cập nhật
      'data.user_request_id' => ['required', 'string', 'max:255'], // status muốn cập nhật
      'data.description' => ['nullable', 'string', 'string:255'], // status muốn cập nhật
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['user_request_id'] = StandardizedDataFilter::getUserAdminStructCompact($params['data']['user_request_id']);
    $this->merge($params);
  }
}
