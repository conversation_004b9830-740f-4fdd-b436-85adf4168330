<?php
namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCaculateAmountAction;

use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Exception;

class DebtRecoverySummaryCaculateAmountAction
{
    public function run($data)
    {
        $dataResponse = [];
        throw_if(!isset($data['contract_code']), new Exception('Trường contract_code không được để trống'));

        $debtRecoverySummary = CollectDebtSummary::query()
                                ->where('contract_code',$data['contract_code'])->first()->toArray();

        throw_if(!$debtRecoverySummary, new Exception('Không tìm thấy bản ghi summary phù hợp'));

        // Nợ gốc
        $amount_original_debt = $debtRecoverySummary['contract_amount'] - $debtRecoverySummary['total_amount_paid'];
        // <PERSON><PERSON> chậm kỳ
        $fee_overdue_cycle = $debtRecoverySummary['fee_overdue_cycle'];
        // Phí quá hạn 
        $fee_overdue = $debtRecoverySummary['fee_overdue'];
        // Giảm phí Chậm kỳ
        $fee_overdue_cycle_reduction = $debtRecoverySummary['fee_overdue_cycle_reduction'];
        // Giảm phí quá hạn
        $fee_overdue_reduction = $debtRecoverySummary['fee_overdue_reduction'];
        //Số tiền đã hoàn
        $total_amount_refund = $debtRecoverySummary['total_amount_refund'];
        //Tổng nợ
        $total_amount_debt = ($debtRecoverySummary['contract_amount'] + $debtRecoverySummary['fee_overdue_cycle'] + $debtRecoverySummary['fee_overdue']) - ($debtRecoverySummary['total_amount_paid'] + $debtRecoverySummary['fee_overdue_reduction'] + $debtRecoverySummary['fee_overdue_cycle_reduction'] + $debtRecoverySummary['total_fee_paid']);

        $dataResponse = [
            'amount_original_debt' => (float)$amount_original_debt,
            'fee_overdue_cycle' => (float)$fee_overdue_cycle,
            'fee_overdue' => (float)$fee_overdue,
            'fee_overdue_cycle_reduction' => (float)$fee_overdue_cycle_reduction,
            'fee_overdue_reduction' => (float)$fee_overdue_reduction,
            'total_amount_refund' => (float)$total_amount_refund,
            'total_amount_debt' => (float)$total_amount_debt,
            'other_data' => $debtRecoverySummary,
            'total_amount_receiver' => (float) $debtRecoverySummary['total_amount_receiver'],
            'fee_overdue_paid' => (float) $debtRecoverySummary['fee_overdue_paid'],
            'fee_overdue_cycle_paid' => (float) $debtRecoverySummary['fee_overdue_cycle_paid'],
        ];

        // dd($dataResponse);

        return $dataResponse;
    }
}