<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtGuide;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;

class DebtRecoveryContractGuideSetStatus extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    $statusList = app(CollectDebtGuide::class)->listingStatus();
    $statusKey = array_keys($statusList);
    
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:0'],
      'data.status' => ['required', Rule::in($statusKey)],
      'data.user_request_id' => ['required', 'string', 'max:255', new UserInteractiveRule()],
      'data.description  ' => ['nullable', 'max:255'],
    ];
  }
} // End class
