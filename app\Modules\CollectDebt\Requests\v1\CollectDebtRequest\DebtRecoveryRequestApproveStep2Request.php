<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtRequest;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\Rules\UserInteractiveRule;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use App\Modules\CollectDebt\Rules\CollectDebtGuide\HopDongChuaTatToanRule;

class DebtRecoveryRequestApproveStep2Request extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.id' => ['required', 'numeric', 'min:1'],
      'data.contract_code' => ['required', 'string', 'max:50', new HopDongChuaTatToanRule()],
      'data.approved_step2_by' => ['required', 'string', 'max:255'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['approved_step2_by'] = StandardizedDataFilter::getStandardizedDataFilter('USER_ADMIN', $params['data']['approved_step2_by']);
    $this->merge($params);
  }
} // End class
