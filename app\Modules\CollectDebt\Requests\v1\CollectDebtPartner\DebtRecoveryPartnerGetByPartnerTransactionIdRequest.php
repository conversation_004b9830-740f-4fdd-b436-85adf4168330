<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtPartner;

use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryPartnerGetByPartnerTransactionIdRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.partner_transaction_id' => ['required', 'string', 'max:50'],
      'data.payment_channel_code' => ['required', 'string', 'max:50'],
    ];
  }
} // End class
