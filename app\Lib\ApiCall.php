<?php

namespace App\Lib;

use App\Utils\ApiConnect;
use App\Utils\CommonVar;

class ApiCall
{
    /**
     * @param $data
     * @return array
     */
    public function callFunctionApi($data, $isNeedFullData=false) {
        $module = $data['module'];
        $path = $data['path'];
        $apiConnect = new ApiConnect($module, $path);
        $apiParams = empty($data['params']) ? [] : $data['params'];
        $method = !empty($data['method']) ? $data['method'] : CommonVar::API_METHOD_GET;
        $result = $apiConnect->callApi($apiParams, $method);
        $apiData = json_decode($result, true);
        
        
        $retData = [];
       
        if ($apiData && $apiData['success'] && !empty($apiData['data'])) {
            $retData = $apiData['data'];
        }

        if ($isNeedFullData) {
            $retData = $apiData;
        }
        return $retData;
    }
}