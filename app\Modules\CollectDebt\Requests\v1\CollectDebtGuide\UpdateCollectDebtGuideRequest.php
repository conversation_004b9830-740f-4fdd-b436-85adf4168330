<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtGuide;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCollectDebtGuideRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.profile_id' => ['required', 'string', 'max:25'],
      'data.contract_code' => ['required', 'string', 'max:50'],
      'data.contract_cycle' => ['required', 'numeric', 'min:1'],
      'data.contract_intervals' => ['required', 'numeric', 'min:1'],
      'data.contract_time_start' => ['required', 'numeric'],
      'data.contract_time_end' => ['required', 'numeric'],
      'data.amount' => ['required', 'numeric', 'min:1000000'],
      'data.payment_guide' => ['required', 'json'],
      'data.list_fee' => ['required', 'json'],
      'data.other_data' => ['required', 'json'],
      'data.profile_data' => ['required', 'json'],
      
      'data.created_by' => ['required', 'array'],
      'data.created_by.username' => ['required', 'string', 'max:20', ],
      'data.created_by.id' => ['required', 'string', 'max:20'],
      'data.created_by.mobile' => ['required', 'string', 'max:20'],

      // 'data.updated_by' => ['required', 'array'],
      // 'data.updated_by.username' => ['required', 'string', 'max:20'],
      // 'data.updated_by.id' => ['required', 'string', 'max:20'],
      // 'data.updated_by.mobile' => ['required', 'string', 'max:20'],

      // 'data.approved_by' => ['required', 'array'],
      // 'data.approved_by.username' => ['required', 'string', 'max:20'],
      // 'data.approved_by.id' => ['required', 'string', 'max:20'],
      // 'data.approved_by.mobile' => ['required', 'string', 'max:20'],

      // 'data.canceled_by' => ['required', 'array'],
      // 'data.canceled_by.username' => ['required', 'string', 'max:20'],
      // 'data.canceled_by.id' => ['required', 'string', 'max:20'],
      // 'data.canceled_by.mobile' => ['required', 'string', 'max:20'],

      // 'data.create_calendar_by' => ['required', 'array'],
      // 'data.create_calendar_by.username' => ['required', 'string', 'max:20'],
      // 'data.create_calendar_by.id' => ['required', 'string', 'max:20'],
      // 'data.create_calendar_by.mobile' => ['required', 'string', 'max:20'],


      'data.time_created' => ['required', 'numeric', 'min:0'],
      // 'data.time_approved' => ['required', 'numeric', 'min:0'],
      // 'data.time_canceled' => ['required', 'numeric', 'min:0'],
      // 'data.time_create_calendar' => ['required', 'numeric', 'min:0'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    $params['data']['status'] = CollectDebtEnum::GUIDE_STT_MOI_TAO;

    $params['data']['updated_by'] = '';
    $params['data']['approved_by'] = '';
    $params['data']['canceled_by'] = '';
    $params['data']['create_calendar_by'] = '';

    $params['data']['time_created'] = time();
    $params['data']['time_approved'] = 0;
    $params['data']['time_canceled'] = 0;
    $params['data']['time_create_calendar'] = 0;

    $this->merge($params);
  }

  protected function passedValidation()
  {
    $params = $this->all();
    $params['data']['created_by'] = json_encode($params['data']['created_by'], JSON_UNESCAPED_UNICODE);
    $this->merge($params);
  }
} // End class
