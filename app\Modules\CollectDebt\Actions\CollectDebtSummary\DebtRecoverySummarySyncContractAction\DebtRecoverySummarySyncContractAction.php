<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummarySyncContractAction;

use App\Lib\NextlendCore;

class DebtRecoverySummarySyncContractAction
{
    public function run($request)
    {
        $dataResponse = [];
        $setupData = $this->__setInput($request);
        $send = $this->__sendContractV4($setupData);
        if(!empty($send)){
            $dataResponse = $send;
        }

        return $dataResponse;
    }

    protected function __setInput($request)
    {
        $dataResponse = $request;
				unset($dataResponse['other_data']);
				unset($dataResponse['contract_data']);
				
        return $dataResponse;
    }

    protected function __sendContractV4($data)
    {

        $dataResponse = [];
        $sendService = app(NextlendCore::class)->callRequest($data, 'ContractV4_summaryData', 'POST');
        $sendService = $sendService->decryptData(true);

				mylog(['v1 response' => $sendService]);
				
        if(isset($sendService['errorCode'])){
            return $dataResponse;
        }

        $dataResponse = $sendService;

        return $dataResponse;
    }
}