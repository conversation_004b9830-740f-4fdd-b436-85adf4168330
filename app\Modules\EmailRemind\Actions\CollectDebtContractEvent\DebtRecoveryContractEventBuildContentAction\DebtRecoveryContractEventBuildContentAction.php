<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Support\Facades\DB;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindChamKySubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindDueDateSubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueC1SubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueC2SubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueC3SubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\EmailRemindOverdueCycleSubAction;

class DebtRecoveryContractEventBuildContentAction
{
    public function run($request)
    {
        $dataResponse = [];

        for ($i = 1; $i <= 30; $i++) {

            try {
                $debtRecoveryContractEvent = CollectDebtContractEvent::where('status', config('collect_debt_email_remind_config.debt_contract_event_status.new'))->first();
                if (!$debtRecoveryContractEvent) {
                    break;
                }

                switch ($debtRecoveryContractEvent->service_care_code) {
                    case 'MAIL':
                        $dataResponse = $this->__runMail($debtRecoveryContractEvent);
                        break;
                    case 'SYNC':
                        $dataResponse = $this->__runSync($debtRecoveryContractEvent);
                        break;
                    default:
                        $dataResponse = [];
                        break;
                }
            } catch (\Throwable $th) {
                mylog(['tao noi dung email: ' => Helper::traceError($th)]);
                continue;
            }
        }

        return $dataResponse;
    }

    protected function __runMail($data)
    {
        $dataResponse = [];

        $input = [
            'time_updated' => time(),
            'status' => config('collect_debt_email_remind_config.debt_contract_event_status.creating_content'),
        ];
        $data->update($input);
        $data = $data->refresh();
        throw_if(!$data, new Exception('Không thể cập nhật được bản ghi'));

        // build content template
        $content = $this->__buildMailContent($data);
        if (empty($content)) {
            $input = [
                'time_updated' => time(),
                'description' => sprintf('Lỗi không tạo được nội dung mail - %s', request('api_request_id')),
                'status' => config('collect_debt_email_remind_config.debt_contract_event_status.canceled'),
            ];
            $data->update($input);
            return $dataResponse;
        }

        $input = [
            'content' => $content,
            'status' => config('collect_debt_email_remind_config.debt_contract_event_status.created_content'),
            'description' => 'Đã tạo nội dung mail thành công',
            'time_updated' => time(),
            'time_created_content' => time(),
        ];
        $data->update($input);

        return $dataResponse;
    }

    protected function __buildMailContent($data)
    {
        $dataResponse = '';

        switch ($data->category_care_code) {
                // case 'NOTIFY_CONTRACT_DUE': //Mail sắp tới hạn
                //     $dataResponse = app(EmailRemindDueDateSubAction::class)->run($data);
                //     break;
            case 'CONTRACT_OVERDUE_CYCLE': //Mail quá kỳ
                $dataResponse = app(EmailRemindOverdueCycleSubAction::class)->run($data);
                break;
            case 'NOTIFY_CONTRACT_DUE1': //Mail quá hạn c1
                $dataResponse = app(EmailRemindOverdueC1SubAction::class)->run($data);
                break;
            case 'NOTIFY_CONTRACT_DUE2': //Mail quá hạn c2
                $dataResponse = app(EmailRemindOverdueC2SubAction::class)->run($data);
                break;
            case 'NOTIFY_CONTRACT_DUE3': //Mail quá hạn c3
                $dataResponse = app(EmailRemindOverdueC3SubAction::class)->run($data);
                break;
            default:
                $dataResponse = '';
                break;
        }

        return $dataResponse;
    }

    protected function __runSync($data)
    {
        $dataResponse = [];

        switch ($data->category_care_code) {
            case 'CONTRACT':
                $dataResponse = $this->__handleContract($data);
                break;
            default:
                $dataResponse = [];
        }


        return $dataResponse;
    }

    protected function __handleContract($data)
    {
        $dataResponse = [];

        $input = [
            'status' => config('collect_debt_email_remind_config.debt_contract_event_status.created_content'),
            'time_updated' => time(),
            'time_created_content' => time(),
        ];
        $data->update($input);

        return $dataResponse;
    }
}
