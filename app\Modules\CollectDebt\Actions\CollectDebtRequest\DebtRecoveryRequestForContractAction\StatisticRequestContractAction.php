<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestForContractAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateRequest;

class StatisticRequestContractAction
{
  public function run(string $contractCode='')
  {
    $statistic = CollectDebtRequest::query()
                                   ->where('contract_code', $contractCode)
                                   ->selectRaw("
                                      COUNT(id) as total_transaction, 
                                      SUM(amount_request) as total_amount_request,
                                      SUM(amount_receiver) as total_amount_receiver
                                    ")
                                    ->first();

		$thongKeSoTienThuHoiTheoKenh = CollectDebtRequest::query()
																										 ->where('contract_code', $contractCode)
																										 ->selectRaw("
																										 		SUM(amount_receiver) as so_tien_da_trich,
																												payment_method_code
																										 ")
																										 ->groupBy('payment_method_code')
																										 ->get();
    
    $collectDebtSummary = CollectDebtSummary::query()->where('contract_code', $contractCode)->first();

		$currency = $collectDebtSummary->getCurrency();
		
    $returnData = [
      [
        'label' => 'total_transaction',
        'value' => $statistic->total_transaction,
        'name' => 'Số GD',
        'currency' => $currency,
				'class' => 'text-primary',
      ],
      
      [
        'label' => 'total_amount_request',
        'value' => Helper::priceFormat($statistic->total_amount_request),
        'name' => 'Đã gửi trích',
        'currency' => $currency,
				'class' => 'text-danger',
      ],

      [
        'label' => 'total_amount_receiver',
        'value' =>  Helper::priceFormat($statistic->total_amount_receiver),
        'name' => 'Trích thành công',
        'currency' => $currency,
				'class' => 'text-primary',
      ],
    ];

		if ($thongKeSoTienThuHoiTheoKenh->isNotEmpty()) {
			foreach ($thongKeSoTienThuHoiTheoKenh as $rq) {
				$returnData[] = [
					'label' => sprintf('so_tien_trich_thanh_cong_%s', strtolower($rq->payment_method_code)),
        	'value' =>  Helper::priceFormat($rq->so_tien_da_trich),
        	'name' => sprintf('Trích %s', $rq->payment_method_code),
        	'currency' => $currency,
					'class' => 'text-success',
				];
			}
		}

		return $returnData;
  }
} // End class