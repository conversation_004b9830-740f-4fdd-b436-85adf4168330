<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtTest;

use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use ReflectionClass;

class CollectDebtTelegramController extends Controller
{
    public function checkTele(Request $request)
    {
        $telegram = new TelegramAlert();
        return $this->successResponse([
            'data_tele' => $telegram->checkConstants(),
            'result' => TelegramAlert::sendMessage("This is test message")
        ], $request);
    }
} // End class
