<?php

namespace App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventGetByIdAction;

use Exception;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class DebtRecoveryContractEventGetByIdAction
{
    public function run($request)
    {

        $debtRecoveryContractEvent = CollectDebtContractEvent::where('id', $request['id'])->get();

        throw_if(!$debtRecoveryContractEvent->isNotEmpty(), new Exception('Không tìm được bản ghi phù hợp'));

        $debtRecoveryContractEvent = $debtRecoveryContractEvent->first()->toArray();

        return $debtRecoveryContractEvent;
    }
}
