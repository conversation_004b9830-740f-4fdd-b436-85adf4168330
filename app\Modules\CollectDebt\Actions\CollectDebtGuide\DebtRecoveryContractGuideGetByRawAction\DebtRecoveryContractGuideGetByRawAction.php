<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideGetByRawAction;

use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtGuide;

class DebtRecoveryContractGuideGetByRawAction
{  
  /**
   * $statements = [
   *  'where' => 'string',
   *  'orderBy' => 'string',
   *  'limit' => 'string'
   * ]
   *
   * @param string $statements [explicite description]
   *
   * @return Collection
   */
  public function run(array $statements=[], array $with=[], array $fields=['*']): Collection
  {
    return CollectDebtGuide::query()
                           ->with($with)
                           ->whereRaw($statements['where'])
                           ->limit($statements['limit'] ?? 1)
                           ->orderByRaw($statements['orderBy'] ?? 'id ASC')
                           ->select($fields)
                           ->get();
  }
} // End class