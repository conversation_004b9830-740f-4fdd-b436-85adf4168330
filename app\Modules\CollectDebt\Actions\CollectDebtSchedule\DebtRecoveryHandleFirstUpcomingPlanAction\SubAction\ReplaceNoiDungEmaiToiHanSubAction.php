<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\NextlendCompany;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;

class ReplaceNoiDungEmaiToiHanSubAction
{
	/**
  "id" => "71"
  "customer_category_id" => "7"
  "customer_category_care_code" => "NOTIFY_CONTRACT_DUE"
  "customer_service_care_code" => "MAIL"
  "name" => "Thông báo sắp tới hạn"
  "description" => ""
  "content" => """
    <div class="mail-content" style="font-family:'Times New Roman', Times, serif;font-size:16px;text-align:left;">
        <div class="content">
            <p><PERSON><PERSON><PERSON> gửi: <PERSON><PERSON><PERSON> khách <strong>[hop_dong_khach_hang] - [hop_dong_nguoi_dai_dien]</strong></p>
            <p><b>[hop_dong_ten_cong_ty]</b>([hop_dong_ten_ngan_cong_ty]) xin gửi lời chào trân trọng và cảm ơn Quý khách hàng
                đã quan tâm đến dịch vụ “Hỗ trợ Ứng vốn Kinh doanh”, “Đặc biệt” dành cho đơn vị đang là đối tác
                của hệ sinh thái “Next360”:</p>
            <ul style="list-style-type: none; margin-left: 25px;">
                <li><strong>Phụ lục Hợp đồng số: [hop_dong_ma]</strong></li>
                <li>Số vốn ứng: [hop_dong_so_tien_ung] VNĐ</li>
                <li>Thời hạn:[hop_dong_so_ngay_vay] ngày, kể từ ngày [hop_dong_tu_ngay] đến ngày [hop_dong_den_ngay]</li>
                <li>Hình thức trích nợ: [hop_dong_hinh_thuc_trich_no] ngày/lần</li>
            </ul>
            <p>Tình trạng hoàn trả khoản ứng vốn và thời hạn hoàn trả của Quý khách như sau:</p>
    
            <table width="100%">
                <tbody>
                    <tr>
                        <td style="border:1px solid #000000;padding:5px;"><strong>Số tiền đã hoàn trả</strong></td>
                        <td style="border:1px solid #000000;padding:5px;">[hop_dong_so_tien_hoan] VNĐ</td>
                    </tr>
                    <tr>
                        <td style="border:1px solid #000000;padding:5px;"><strong>Dư nợ cuối kỳ</strong></td>
                        <td style="border:1px solid #000000;padding:5px;">[hop_dong_du_no_cuoi_ky] VNĐ</td>
                    </tr>
                    <tr>
                        <td style="border:1px solid #000000;padding:5px;"><strong>Thanh toán tối thiểu</strong></td>
                        <td style="border:1px solid #000000;padding:5px;">[hop_dong_so_tien_toi_thieu] VNĐ</td>
                    </tr>
                    <tr>
                        <td style="border:1px solid #000000;padding:5px;"><strong>Thanh toán trước ngày</strong></td>
                        <td style="border:1px solid #000000;padding:5px;">[hop_dong_ngay_chu_ky_trich_no]</td>
                    </tr>
                </tbody>
            </table>
            <p>Chi tiết lịch sử thanh toán được đính kèm theo email này tại thời điểm thông báo là ngày T-1 (T: ngày)</p>
            <p><strong>Hình thức hỗ trợ thanh toán hoàn trả:</strong></p>
            <div>
                <p style="margin-left: 5px;"><span>1. Hoàn tiền đã ứng bằng giao dịch thanh toán thẻ trên MPOS.VN sẽ được cấn trừ khoản tiền đã ứng theo chu kỳ ngày ▶
                <div style="margin-top: 2px;margin-left: 5px;">2. Hoàn tiền đã ứng bằng hình thức chuyển khoản về tài khoản nhận thanh toán của [hop_dong_ten_ngan_c ▶
                    <div style="margin-left:30px;">
                        <p>• Số tài khoản nhận tiền: [hop_dong_stk_cong_ty]</p>
                        <p>• Ngân hàng: [hop_dong_ten_ngan_hang_cong_ty]</p>
                        <p>• Tên chủ tài khoản: [hop_dong_ten_chu_tk_cong_ty]</p>
                        <p>• Nội dung chuyển khoản: [hop_dong_nd_chuyen_khoan]</p>
                    </div>
                </div>
                <div style="margin-left: 5px;">
                    <p>3. Hoàn tiền đã ứng bằng việc Quét QR: Mã QR thanh toán được dành riêng cho khoản ứng của đơn vị. Ngay sau khi Quý khách quét QR thanh toán t ▶
    
                    <div style="margin:auto;width:50%;text-align:center;"><img alt="QR Image" src="[hop_dong_ma_qr_va]" /> <span style="text-align:center;display:bl ▶
                </div>
            </div>
    
            <p><i>Lưu ý:</i></p>
    
            <ul style="padding-left:25px;margin-bottom:20px;list-style:none;">
                <li><i>- Quý khách có thể thanh toán trước hạn, đúng hạn sẽ không những “<b>KHÔNG</b>” phát sinh thêm bất kỳ khoản phí nào mà còn được [hop_dong_ten ▶
                <li style="margin-top: 1px;"><i>- Trường hợp Quý khách thanh toán chậm, không đủ hoặc không thanh toán vào ngày đến hạn thanh toán có thể bị phát si ▶
            </ul>
    
            <p>Mọi vướng mắc Quý khách vui lòng liên hệ Chuyên viên: [hop_dong_nguoi_tao], Số điện thoại/zalo: [hop_dong_sdt_nguoi_tao], Email: [hop_dong_email_nguo ▶
    
            <p>Hoặc Phòng Dịch vụ và Chăm sóc Khách hàng của [hop_dong_ten_ngan_cong_ty]: Tel: [hop_dong_sdt_cong_ty]. Email: <a href="mailto:[hop_dong_email_cong_t ▶
    
            <p>Trân trọng!</p>
    
            <div style="font-family:'Times New Romans', serif;font-size:16px;text-align:justify;">
                <p><b style="color:#0A8CC3;text-align:left;">SẴN SÀNG TĂNG TỐC KINH DOANH, THAM GIA ỨNG VỐN NEXTLEND!</b></p>
                <p>A: [hop_dong_dia_chi_cong_ty]</p>
                <p>Hotline: [hop_dong_sdt_cong_ty]</p>
                <p><span>Email: </span> <a href="mailto:[hop_dong_email_cong_ty]" style="text-decoration:none;"> [hop_dong_email_cong_ty] </a></p>
                <p><span>W: </span> <a href="[hop_dong_website_1_cong_ty]" style="text-decoration:none;"> [hop_dong_website_1_cong_ty] </a></p>
                <p><span>W: </span> <a href="[hop_dong_website_2_cong_ty]" style="text-decoration:none;"> [hop_dong_website_2_cong_ty] </a></p>
            </div>
        </div>
    </div>
    """
  "status" => "2"
  "time_created" => "1700713961"
  "time_updated" => ""
  "time_deleted" => ""
  "time_locked" => ""
  "time_unlocked" => ""
  "time_actived" => ""
  "other_data" => "{"create": [{"action_at": "2023-11-23 11:32:41", "action_by": "Vận hành", "action_info": {"name": "Administrator", "email": "<EMAIL>", "mobile": "035687 ▶"
]
	 *
	 * @return void
	 */
	public function run(CollectDebtSchedule $plan, CollectDebtContractEvent $collectDebtContractEvent, array $emailTemplateContent = []): string
	{
		$customFields = $this->__mappinCustomeFieldTemplate($collectDebtContractEvent, $plan);

		$emailContent = $emailTemplateContent['content'];

		foreach ($customFields as $filed => $value) {
			$emailContent = str_replace($filed, $value, $emailContent);
		}

		return $emailContent;
	} // End method

	public function __mappinCustomeFieldTemplate(CollectDebtContractEvent $collectDebtContractEvent, CollectDebtSchedule $plan)
	{
		$eventData = $collectDebtContractEvent->getEventData();
		$merchant = $eventData['profile']['merchant'];
		$contract = $eventData['contract'];

		$eventOtherData = $collectDebtContractEvent->getEventOtherData();
		$summary = $eventOtherData['summary'];

		$paymentVa = collect($eventData['payment'])->where('other_data', '!=', '')->where('payment_method_code', 'VIRTUALACCOUNT')->map(function ($items) {
			return isset($items['other_data']) ? $items['other_data'] : '';
		})->first();

		$userAdmin = isset($contract['users_admin']) ? $contract['users_admin'] : [];

		$company = new NextlendCompany($eventData['company']);

		$params = [
			'[hop_dong_khach_hang]' => isset($merchant['fullname']) ? $merchant['fullname'] : '',
			'[hop_dong_nguoi_dai_dien]' => $merchant['business_representative'] ?? 'N/A',
			'[hop_dong_ma]' => $collectDebtContractEvent->contract_code,
			'[hop_dong_so_tien_ung]' => Helper::makeVndCurrency($summary['contract_amount']), // so tien hd
			'[hop_dong_so_ngay_vay]' => $summary['contract_cycle'], // so ngay vay
			'[hop_dong_tu_ngay]' => Carbon::createFromFormat('d-m-Y H:i:s', $contract['time_start_as_date'])->format('d/m/Y'),
			'[hop_dong_den_ngay]' => Carbon::createFromFormat('d-m-Y H:i:s', $contract['time_end_as_date'])->format('d/m/Y'),
			'[hop_dong_hinh_thuc_trich_no]' => $contract['intervals'] ?? '',
			'[hop_dong_loai]' => $summary['contract_type'] ?? '',



			'[hop_dong_stk_cong_ty]'              => isset($company['company_bank_account_1']) ? $company['company_bank_account_1'] : '',
			'[hop_dong_ten_ngan_hang_cong_ty]'    => isset($company['company_bank_name_1']) ? $company['company_bank_name_1'] : '',
			'[hop_dong_ten_chu_tk_cong_ty]'       => isset($company['company_bank_holder_1']) ? $company['company_bank_holder_1'] : '',
			'[hop_dong_nd_chuyen_khoan]'          => sprintf('NAP TIEN TK MC MA %s - %s', $company->getNoiDungChuyenKhoanMaNapTien(), $company->getCuPhapFullName($merchant)),
			'[hop_dong_ma_qr_va]'                 => isset($paymentVa['qrImage']) ? $paymentVa['qrImage'] : '',
			'[hop_dong_ten_ngan_hang_va_cong_ty]' => isset($paymentVa['payment_account_bank_code']) ? $paymentVa['payment_account_bank_code'] : '',
			'[hop_dong_stk_va_cong_ty]'           => isset($paymentVa['payment_account_number']) ? $paymentVa['payment_account_number'] : '',
			'[hop_dong_ten_chu_tk_va_cong_ty]'    => isset($paymentVa['payment_account_name']) ? $paymentVa['payment_account_name'] : '',
			'[hop_dong_nguoi_tao]'                => isset($userAdmin['fullname']) ? $userAdmin['fullname'] : '',
			'[hop_dong_sdt_nguoi_tao]'            => isset($userAdmin['mobile']) ? $userAdmin['mobile'] : '',
			'[hop_dong_email_nguoi_tao]'          => isset($userAdmin['email']) ? $userAdmin['email'] : '',
			'[hop_dong_sdt_cong_ty]'              => $company->getPhoneNumber() ?? '',
			'[hop_dong_email_cong_ty]'            => $company->getEmail() ?? '',
			'[hop_dong_dia_chi_cong_ty]'          => $company->getAddress() ?? '',
			'[hop_dong_website_1_cong_ty]'        => isset($company['company_url_1']) ? $company['company_url_1'] : '',
			'[hop_dong_website_2_cong_ty]'        => isset($company['company_url_2']) ? $company['company_url_2'] : '',
			'[hop_dong_ten_cong_ty]'              => isset($company['company_fullname']) ? $company['company_fullname'] : '',
			'[hop_dong_ten_ngan_cong_ty]'         => isset($company['company_subname']) ? $company['company_subname'] : '',
		];

		$collectDebtSummary = $plan->collectDebtSummary;

		// [NGÀY] - Chỉ làm trên lịch (C) tất toán
		if ($plan->contract_type == CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_TRICH_NGAY) {
			$soTienDaHoanTra = $collectDebtSummary->total_amount_paid + $collectDebtSummary->total_fee_paid;

			// Số tiền HĐ + các loại phí - số tiền đã hoàn trả
			$duNoCuoiKy = $collectDebtSummary->contract_amount + $collectDebtSummary->fee_overdue
																												 + $collectDebtSummary->fee_overdue_cycle
																												 - $soTienDaHoanTra;

			$phiPhatThanhToanChamKy = 0;

			$thanhToanToiThieu = $duNoCuoiKy;
			$thanhToanTruocNgay = $plan->time_start_as_date->format('d/m/Y');
		}

		// [CHU KỲ] - Làm trên các lịch (C)
		if ($plan->contract_type == CollectDebtEnum::SCHEDULE_LOAI_HD_KHOAN_UNG_CHU_KY) {
			if ($plan->isLichTatToan()) {
				$soTienDaHoanTra = $collectDebtSummary->total_amount_paid + $collectDebtSummary->total_fee_paid;

				// Số tiền HĐ + các loại phí - số tiền đã hoàn trả
				$duNoCuoiKy = $collectDebtSummary->contract_amount + $collectDebtSummary->fee_overdue
																													 + $collectDebtSummary->fee_overdue_cycle
																													 - $soTienDaHoanTra;

				$phiPhatThanhToanChamKy = $collectDebtSummary->fee_overdue_cycle - $collectDebtSummary->fee_overdue_cycle_paid;;

				$thanhToanToiThieu = $duNoCuoiKy;
				$thanhToanTruocNgay = $plan->time_start_as_date->format('d/m/Y');
			}

			if (!$plan->isLichTatToan()) {
				$plansSummaryOtherData = $collectDebtSummary->getSummaryOtherDataItem('PLAN');

				/**
				 * array:11 [▼
						"id" => 51529
						"time_cycle" => 1714237200
						"time_over_cycle" => 1714323600
						"amount" => 3333333
						"fee" => 1666  -> Số phí còn lại phải thanh toán tiếp
						"amount_paid" => 3333333
						"fee_paid" => 1667  -> Số phí đã thanh toán | Muốn tính tổng phí: Thì cộng giá trị này với `fee`
						"overdue_cycle" => 1
						"note" => "Hợp đồng v4"
						"status" => 1
						"data" => array:4 [▶]
					]
				 */	
					
				$soTienDaHoanTra = $collectDebtSummary->total_amount_paid + $collectDebtSummary->total_fee_paid;

				$tongSoTienChuaThanhCacKyTruocVaKySapToiHan = collect($plansSummaryOtherData['data'])->sum(function ($item) use ($plan) {
					$tongPhiBaoGomPhiChuaThanhToanVaDaThanhToan = $item['fee'] + $item['fee_paid'];

					if ($item['id'] <= $plan->id) {
						return ($item['amount'] + $tongPhiBaoGomPhiChuaThanhToanVaDaThanhToan) - ( $item['amount_paid'] + $item['fee_paid']);
					}

					return 0;
				});

				
				// Số tiền HĐ + các loại phí - số tiền đã hoàn trả
				$duNoCuoiKy = $tongSoTienChuaThanhCacKyTruocVaKySapToiHan;

				// Tổng phí chậm kỳ đã sinh ra - Tổng phí chậm kỳ đã thnh toán																													 
				$phiPhatThanhToanChamKy = $collectDebtSummary->fee_overdue_cycle - $collectDebtSummary->fee_overdue_cycle_paid;


				$thanhToanToiThieu = $tongSoTienChuaThanhCacKyTruocVaKySapToiHan;

				$thanhToanTruocNgay = $plan->time_start_as_date->format('d/m/Y');
			}
		}

		$params = array_merge($params, [
			'[hop_dong_so_tien_hoan]' => Helper::makeVndCurrency($soTienDaHoanTra ?? 0),
			'[hop_dong_du_no_cuoi_ky]' => Helper::makeVndCurrency($duNoCuoiKy ?? 0),
			'[hop_dong_phi_cham_ky]' => Helper::makeVndCurrency($phiPhatThanhToanChamKy ?? 0),
			'[hop_dong_so_tien_toi_thieu]' => Helper::makeVndCurrency($thanhToanToiThieu ?? 0),
			'[hop_dong_ngay_chu_ky_trich_no]' => $thanhToanTruocNgay,
		]);

		return $params;
	}
} // End class