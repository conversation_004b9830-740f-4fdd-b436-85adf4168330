<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLog;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class DebtRecoveryLogSearchDataAction
{
	public function run(Request $request)
	{
		$logs = CollectDebtLog::query();

		$serviceCode = $request->json('data.filter.service_code');
		if (!empty($serviceCode)) {
			$logs->where('service_code', $serviceCode);
		}

		$contractCode = $request->json('data.filter.contract_code');
		if (!empty($contractCode)) {
			$collectDebtSummary = CollectDebtSummary::query()->where('contract_code', trim($contractCode))->first();
			$logs->where('reference_id', optional($collectDebtSummary)->id);
		}

		$orderCode = $request->json('data.filter.order_code');
		if (!empty($orderCode)) {
			$logs->where('order_code', $orderCode);
		}

		$partnerTransactionId = $request->json('data.filter.partner_transaction_id');
		if (!empty($partnerTransactionId)) {
			$logs->where('partner_transaction_id', $partnerTransactionId);
		}

		$status = $request->json('data.filter.status');
		if (!empty($status)) {
			$logs->where('status', $status);
		}

		if ( !empty($request->json('data.filter.from_date')) ) {
			$fromDate = Carbon::createFromFormat('d-m-Y', $request->json('data.filter.from_date'))->startOfDay()->timestamp;
			$logs = $logs->where('time_created', '>=', $fromDate);
		}

		if ( !empty($request->json('data.filter.to_date')) ) {
			$toDate = Carbon::createFromFormat('d-m-Y', $request->json('data.filter.to_date'))->endOfDay()->timestamp;
			$logs = $logs->where('time_created', '<=', $toDate);
		}

		$logs = $logs->orderBy('id', 'DESC')
								->paginate(
									$request->json('data.limit', 20),
									['*'],
									'page',
									$request->json('data.page', 1)
								);
			
		return $logs;
	}
} // End class