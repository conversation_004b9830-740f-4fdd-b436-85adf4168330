<?php

namespace App\Modules\CollectDebtGateway\Service;

use App\Modules\CollectDebtGateway\Repositories\CollectDebtRecoveryGatewayLogRepository;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtGatewayRepository;
use App\Lib\ApiCall;
use App\Utils\CommonVar;

class PartnerCollectDebtGatewayService
{
    protected CollectDebtGatewayRepository $collectDebtGatewayRepo;
    protected CollectDebtRecoveryGatewayLogRepository $collectDebtLogRepo;

    public function __construct(CollectDebtRecoveryGatewayLogRepository $collectDebtLogRepo, CollectDebtGatewayRepository $collectDebtGatewayRepo)
    {
        $this->collectDebtLogRepo = $collectDebtLogRepo;
        $this->collectDebtGatewayRepo = $collectDebtGatewayRepo;
    }

    /**
     * @param $request
     * @return array
     */
    public function sendDebt($request)
    {
        $dataResponse = [];
        $paymentChannel = $request['payment_channel'];
        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataResponse = app(MposCollectDebtGatewayService::class)->sendDebt($request);
                break;
            case config('collect_debt_gateway_config.payment_channel.mpos_test'):
                $dataResponse = app(MposTestCollectDebtGatewayService::class)->sendDebt($request);
                break;
        }

        return $dataResponse;
    }

    /**
     * @param $request
     * @return array
     */
    public function checkDebt($request)
    {
        $dataResponse = [];
        $paymentChannel = $request['payment_channel'];
        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataResponse = app(MposCollectDebtGatewayService::class)->checkDebt($request);
                break;
            case config('collect_debt_gateway_config.payment_channel.mpos_test'):
                $dataResponse = app(MposTestCollectDebtGatewayService::class)->checkDebt($request);
                break;
        }

        return $dataResponse;
    }

    /**
     * @param $request
     * @return array
     */
    public function checkBalance($request)
    {
        $dataResponse = [];
        $paymentChannel = $request['payment_channel'];
        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataResponse = app(MposCollectDebtGatewayService::class)->checkBalance($request);
                break;
            case config('collect_debt_gateway_config.payment_channel.mpos_test'):
                $dataResponse = app(MposTestCollectDebtGatewayService::class)->checkBalance($request);
                break;
        }

        return $dataResponse;
    }

    /**
     * @param $request
     * @return array
     */
    public function cancelDebt($request)
    {
        $dataResponse = [];
        $paymentChannel = $request['payment_channel'];
        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataResponse = app(MposCollectDebtGatewayService::class)->cancelDebt($request);
                break;
            case config('collect_debt_gateway_config.payment_channel.mpos_test'):
                $dataResponse = app(MposTestCollectDebtGatewayService::class)->cancelDebt($request);
                break;
        }

        return $dataResponse;
    }

    /**
     * @param $request
     * @return array
     */
    public function notifyContractFinish($request)
    {
        // dd($request);
        $dataResponse = array();

        $descriptionExample = config('collect_debt_gateway_config.mpos_gateway_reponse_name');

        $inputs = [
            'contract_code' => $request['contract_code'],
            'amount_payment' => $request['amount_payment'],
            'amount_discount' => $request['amount_discount'], // Số tiền được giảm
            'amount_fee_out_of_date' => $request['amount_fee_out_of_date'],
            'amount_fee_cycle_out_of_date' => $request['amount_fee_cycle_out_of_date'],
        ];


        $dataLog = [
            'request_id' => $request['request_id'],
            'users_admin_id' => $request['users_admin_id'],
            'amount_payment' => $request['amount_payment'],
        ];


        $result = $this->collectDebtGatewayRepo->notifyContractFinish($inputs);
        $errorCode = $result['errorCode'];
        if ($errorCode == config('collect_debt_gateway_config.mpos_gateway_response_success')) {
            // $data = $result['data'];
        }


        $descriptionName = isset($descriptionExample[$errorCode]) ? $descriptionExample[$errorCode] : $errorCode;

        $dataLog['description'] =  $descriptionName;
        $this->__createCollectDebtLog($inputs, $dataLog, $result);

        $dataResponse = [
            'error_code' => $errorCode,
            'description' => $descriptionName,
        ];

        return $dataResponse;
    }

    /**
     * @param $data
     * @param $dataLog
     * @return array|bool
     */
    protected function __createCollectDebtLog($data, $dataLog, $responseCurrent = [])
    {
        $inputs = [
            'created_by' => $dataLog['users_admin_id'],
            'request_data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'time_created' => time(),
        ];

        $requestData = array();
        array_push($requestData, $data);
        $inputs['request_data'] = json_encode($requestData, JSON_UNESCAPED_UNICODE);

        if (isset($dataLog['request_id']) && !empty($dataLog['request_id'])) {
            $inputs['request_id'] = $dataLog['request_id'];
        }
        if (isset($dataLog['partner_request_id']) && !empty($dataLog['partner_request_id'])) {
            $inputs['partner_request_id'] = $dataLog['partner_request_id'];
        }
        if (isset($dataLog['partner_transaction_id']) && !empty($dataLog['partner_transaction_id'])) {
            $inputs['partner_transaction_id'] = $dataLog['partner_transaction_id'];
        }
        if (isset($dataLog['amount_payment']) && !empty($dataLog['amount_payment'])) {
            $inputs['amount_payment'] = $dataLog['amount_payment'];
        }
        if (isset($dataLog['amount_receiver']) && !empty($dataLog['amount_receiver'])) {
            $inputs['amount_receiver'] = $dataLog['amount_receiver'];
        }
        if (isset($dataLog['fee']) && !empty($dataLog['fee'])) {
            $inputs['fee'] = $dataLog['fee'];
        }
        if (isset($dataLog['response']) && !empty($dataLog['response'])) {
            $requestData = array();
            array_push($requestData, $dataLog['response']);
            $inputs['response'] = json_encode($requestData, JSON_UNESCAPED_UNICODE);
        }
        if (isset($dataLog['other_data']) && !empty($dataLog['other_data'])) {
            $inputs['other_data'] = json_encode($dataLog['other_data'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($dataLog['description']) && !empty($dataLog['description'])) {
            $inputs['description'] = $dataLog['description'];
        }
        if (!empty($responseCurrent)) {
            $inputs['response'] = json_encode($responseCurrent, JSON_UNESCAPED_UNICODE);
        }


        $result = $this->collectDebtLogRepo->createGetId($inputs);

        return $result;
    }

    /**
     * @param $data
     * @param $dataLog
     * @return array|bool
     */
    protected function __updateCollectDebtLog($id, $data, $dataLog)
    {
        $inputs = [
            'response' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'updated_by' => $dataLog['users_admin_id'],
            'time_updated' => time(),
        ];

        if (isset($dataLog['request_id']) && !empty($dataLog['request_id'])) {
            $inputs['request_id'] = $dataLog['request_id'];
        }
        if (isset($dataLog['partner_request_id']) && !empty($dataLog['partner_request_id'])) {
            $inputs['partner_request_id'] = $dataLog['partner_request_id'];
        }
        if (isset($dataLog['partner_transaction_id']) && !empty($dataLog['partner_transaction_id'])) {
            $inputs['partner_transaction_id'] = $dataLog['partner_transaction_id'];
        }
        if (isset($dataLog['amount_payment']) && !empty($dataLog['amount_payment'])) {
            $inputs['amount_payment'] = $dataLog['amount_payment'];
        }
        if (isset($dataLog['amount_receiver']) && !empty($dataLog['amount_receiver'])) {
            $inputs['amount_receiver'] = $dataLog['amount_receiver'];
        }
        if (isset($dataLog['fee']) && !empty($dataLog['fee'])) {
            $inputs['fee'] = $dataLog['fee'];
        }
        if (isset($dataLog['response']) && !empty($dataLog['response'])) {
            $response = json_decode($dataLog['response'], true);
            if (!empty($response) && is_array($response)) {
                array_push($response, $data);
                $inputs['response'] = json_encode($response, JSON_UNESCAPED_UNICODE);
            }
        }
        if (isset($dataLog['other_data']) && !empty($dataLog['other_data'])) {
            $inputs['other_data'] = json_encode($dataLog['other_data'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($dataLog['description']) && !empty($dataLog['description'])) {
            $inputs['description'] = $dataLog['description'];
        }
        if (isset($dataLog['request']) && !empty($dataLog['request'])) {
            if (isset($dataLog['request_data']) && !empty($dataLog['request_data'])) {
                $requestData = json_decode($dataLog['request_data'], true);
                if (!empty($requestData) && is_array($requestData)) {
                    array_push($requestData, $dataLog['request']);
                    $inputs['request_data'] = json_encode($requestData, JSON_UNESCAPED_UNICODE);
                }
            }
        }


        $result = $this->collectDebtLogRepo->updateGetId($id, $inputs);

        return $result;
    }

    /**
     * @param $request
     * @return array
     */
    public function reCheckDebt($request)
    {
        $dataResponse = [];
        $paymentChannel = $request['payment_channel'];
        switch ($paymentChannel) {
            case config('collect_debt_gateway_config.payment_channel.mpos'):
                $dataResponse = app(MposCollectDebtGatewayService::class)->reCheckDebt($request);
                break;
        }

        return $dataResponse;
    }
}
