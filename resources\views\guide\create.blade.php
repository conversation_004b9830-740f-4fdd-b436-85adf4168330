@extends('layouts.master')

@section('content')
    <!-- Main content -->
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="POST" action="{{ config('app.url') . '/mock/store-guide' }}">
														<div class="form-group">
																<label for="">Đối tác giải ngân:</label>
																<div class="form-check">
																		<input class="form-check-input" type="radio" name="contract_partner" id="partner_nextlend" value="" checked>
																		<label class="form-check-label" for="partner_nextlend">
																				NEXTLEND
																		</label>
																</div>
																<div class="form-check">
																		<input class="form-check-input" type="radio" name="contract_partner" id="partner_tnex" value="TNEX" {{ old('currency') == 'TNEX' ? 'checked' : '' }}>
																		<label class="form-check-label" for="partner_tnex">
																				TNEX
																		</label>
																</div>
														</div>

														<div class="form-group">
																<label for="" class="text-danger">*Profile ID:</label>
																 <input type="text" class="form-control profile_id" placeholder="Nhập mã Profile ID để tạo HĐ cho nhiều MC"
                                         name="profile_id"
                                        value="" />
														</div>

                                <div class="form-group">
                                    <label for="">Loại tiền tệ:</label>
                                    <select name="currency" id="currency" class="form-control">
                                        <option value="VND" @if (old('currency') == 'VND') selected @endif>VND</option>
                                        <option value="USD" @if (old('currency') == 'USD') selected @endif>USD</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="">Mã hợp đồng</label>
                                    <input type="text" class="form-control" name="contract_code"
                                        value="{{ old('contract_code', $randomContractCode) }}">
                                </div>

                                <div class="form-group">
                                    <label for="">Ngày bắt đầu hợp đồng (ngày/tháng/năm)</label>
                                    <input type="text" class="form-control datepicker" placeholder="ngày/tháng/năm"
                                        data-date-format="dd/mm/yyyy" name="contract_time_start"
                                        value="{{ old('contract_time_start', now()->format('d/m/Y')) }}" />
                                </div>


                                <div class="form-group">
                                    <label for="">Loại HĐ trích nợ</label>
                                    <select name="contract_type" id="contract_type" class="form-control">
                                        <option value="">---Chọn---</option>
                                        <option value="1" @if (old('contract_type') == 1) selected @endif>Trích ngày
                                        </option>
                                        <option value="3" @if (old('contract_type', 3) == 3) selected @endif>Trích kỳ
                                        </option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="">Số ngày vay</label>
                                    <select name="contract_cycle" id="contract_cycle" class="form-control">
                                        <option value="2" @if (old('contract_cycle', 2) == 2) selected @endif>2 ngày</option>
                                        <option value="3" @if (old('contract_cycle') == 3) selected @endif>3 ngày</option>
                                        <option value="4" @if (old('contract_cycle') == 4) selected @endif>4 ngày</option>
                                        <option value="6" @if (old('contract_cycle') == 6) selected @endif>6 ngày</option>
                                        <option value="8" @if (old('contract_cycle') == 8) selected @endif>8 ngày</option>
                                        <option value="10" @if (old('contract_cycle') == 10) selected @endif>10 ngày</option>
                                        <option value="20" @if (old('contract_cycle') == 20) selected @endif>20 ngày</option>
                                        <option value="30" @if (old('contract_cycle') == 30) selected @endif>30 ngày</option>
                                        <option value="60" @if (old('contract_cycle') == 60) selected @endif>60 ngày</option>
                                        <option value="90" @if (old('contract_cycle') == 90) selected @endif>90 ngày</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="">Chu kỳ trích nợ (n)</label>
                                    <input type="number" class="form-control" name="contract_intervals" min="1"
                                        value="{{ old('contract_intervals', 1) }}">
                                    <span class="badge badge-info">Cứ n ngày, hệ thống sẽ thu hồi</span>
                                </div>


                                <div class="form-group">
                                    <label for="">Số tiền cần thu hồi</label>
                                    <input type="text" class="form-control" oninput="formatNumberCurrency(event)"
                                        placeholder="Nhập số tiền cần thu hồi" name="amount"
                                        value="{{ old('amount', 10000000) }}">
                                </div>

                                <div class="form-group">
                                    <label for="">Kênh thu hồi</label>
                                    <fieldset class="scheduler-border">
                                        <legend class="scheduler-border">MPOS</legend>
                                        <div class="d-flex justify-content-between">
                                            <div class="control-group">
                                                <label>Phương thức thu:</label>
                                                <input readonly type="text" class="form-control"
                                                    name="payment_guide[0][payment_method_code]" value="MPOS">
                                            </div>

                                            <div class="control-group">
                                                <label>Kênh thu:</label>
                                                <input type="text" class="form-control"
                                                    name="payment_guide[0][payment_channel_code]" value="MPOS">
                                            </div>

                                            <div class="control-group">
                                                <label>ID MC:</label>
                                                <input type="text" class="form-control"
                                                    name="payment_guide[0][payment_account_id]" value="{{ env('PARTNER_MPOS_SAMPLE_MERCHANT_ID', ********) }}">
                                            </div>
                                        </div>
                                    </fieldset>

                                    <fieldset class="scheduler-border">
                                        <legend class="scheduler-border">Chuyển khoản IB_OFF</legend>
                                        <div class="d-flex justify-content-between">
                                            <div class="control-group">
                                                <label>Phương thức thu:</label>
                                                <input readonly type="text" class="form-control"
                                                    name="payment_guide[1][payment_method_code]" value="IB_OFF">
                                            </div>

                                            <div class="control-group">
                                                <label>Kênh thu:</label>
                                                <input type="text" class="form-control"
                                                    name="payment_guide[1][payment_channel_code]" value="IB_OFF">
                                            </div>

                                            <div class="control-group">
                                                <label>ID MC:</label>
                                                <input type="text" class="form-control"
                                                    name="payment_guide[1][payment_account_id]" value="{{ $ib_off_rand }}">
                                            </div>
                                        </div>
                                    </fieldset>

                                    <fieldset class="scheduler-border">
                                        <legend class="scheduler-border">VA (VIRTUAL ACCOUNT)</legend>
                                        <div class="d-flex justify-content-between">
                                            <div class="control-group">
                                                <label>Phương thức thu:</label>
                                                <input readonly type="text" class="form-control"
                                                    name="payment_guide[2][payment_method_code]" value="VIRTUALACCOUNT">
                                            </div>

                                            <div class="control-group mx-2">
                                                <label>Kênh thu:</label>
                                                <input type="text" class="form-control"
                                                    name="payment_guide[2][payment_channel_code]" value="PAYON">
                                            </div>

                                            <div class="control-group mr-2">
                                                <label>ID VA:</label>
                                                <input type="text" class="form-control"
                                                    name="payment_guide[2][payment_account_id]" value="{{ strtoupper(\Str::random(12)) }}">
                                            </div>

                                            <div class="control-group">
                                                <label for="">Thu VA thành công</label>
                                                <input type="text" class="form-control"
                                                    oninput="formatNumberCurrency(event)"
                                                    name="payment_guide[2][amount_reveiver]" value="0">
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>

                                <div class="form-group">
                                    <label for="">Cấu hình phí</label>
                                    <fieldset class="scheduler-border">
                                        <legend class="scheduler-border">Phí chậm kỳ</legend>
                                        <div class="d-flex justify-content-between">
                                            <div class="control-group">
                                                <label>Type:</label>
                                                <input type="text" readonly class="form-control"
                                                    name="list_fee[0][type]" value="8">
                                            </div>

                                            <div class="control-group mx-2">
                                                <label>Phần trăm phí:</label>
                                                <input type="text" class="form-control"
                                                    name="list_fee[0][percent_fee]" min="0" value="0.1"
                                                    >
                                            </div>

                                            <div class="control-group">
                                                <label>Số tiền phí cứng:</label>
                                                <input type="number" class="form-control" name="list_fee[0][flat_fee]"
                                                    min="0" value="0" >
                                            </div>

                                            <div class="control-group mx-2">
                                                <label>Phí max:</label>
                                                <input type="text" class="form-control" name="list_fee[0][fee_max]"
                                                    value="0" min="0" >
                                            </div>

                                            <div class="control-group">
                                                <label>Phí min:</label>
                                                <input type="text" class="form-control" name="list_fee[0][fee_min]"
                                                    value="0" min="0" >
                                            </div>
                                        </div>
                                    </fieldset>

                                    <fieldset class="scheduler-border">
                                        <legend class="scheduler-border">Phí quá hạn</legend>
                                        <div class="d-flex justify-content-between">
                                            <div class="control-group">
                                                <label>Type:</label>
                                                <input type="text" readonly class="form-control"
                                                    name="list_fee[1][type]" value="3">
                                            </div>

                                            <div class="control-group mx-2">
                                                <label>Phần trăm phí:</label>
                                                <input type="text" class="form-control"
                                                    name="list_fee[1][percent_fee]" min="0" value="0.2"
                                                    >
                                            </div>

                                            <div class="control-group">
                                                <label>Số tiền phí cứng:</label>
                                                <input type="number" class="form-control" name="list_fee[1][flat_fee]"
                                                    min="0" value="0" >
                                            </div>

                                            <div class="control-group mx-2">
                                                <label>Phí max:</label>
                                                <input type="text" class="form-control" name="list_fee[1][fee_max]"
                                                    value="0" min="0" >
                                            </div>

                                            <div class="control-group">
                                                <label>Phí min:</label>
                                                <input type="text" class="form-control" name="list_fee[1][fee_min]"
                                                    value="0" min="0" >
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>

                                <button type="submit" class="btn btn-primary">Tạo chỉ dẫn</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>
    <script>
        function formatNumberCurrency(event) {
            const amountInput = event.target;
            const value = amountInput.value;

            // Loại bỏ tất cả các ký tự không phải là chữ số và dấu phẩy
            const cleanedValue = value.replace(/[^0-9,]/g, "");

            // Loại bỏ dấu phẩy trong chuỗi trừ dấu phẩy đầu tiên (nếu có)
            const normalizedValue = cleanedValue.replace(/,(?!$)/g, "");

            // Định dạng số tiền với dấu phẩy
            const formattedValue = normalizedValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

            amountInput.value = formattedValue;
        }
    </script>
    <!-- /.content -->
@endsection
