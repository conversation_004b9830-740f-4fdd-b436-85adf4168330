<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideAutoApprovedAction\SubAction\TrichNgayTask;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;

class DongBoTaoLichThuGocTrichNgayTask
{
  public function run(CollectDebtGuide $collectDebtGuide, float $soTienNoGocConPhaiThu)
  {
    $paramPlans = [
      'cycle_number'         => 0,
      'profile_id'           => $collectDebtGuide->profile_id,
      'contract_code'        => $collectDebtGuide->contract_code,
      'contract_type'        => $collectDebtGuide->contract_type,
      'type'                 => CollectDebtEnum::SCHEDULE_LOAI_LICH_THU_CHINH,
      'isfee'                => CollectDebtEnum::SCHEDULE_LA_LICH_THU_NO_GOC,
      'debit_begin'          => $soTienNoGocConPhaiThu,
      'debit_end'            => 0,
      'rundate'              => now()->addDay()->format('Ymd'),
      'time_start'           => now()->startOfDay()->timestamp,
      'time_end'             => now()->setTime(22, 0)->timestamp,
      'amount_period_debit'  => $soTienNoGocConPhaiThu,
      'request_amount_debit' => $soTienNoGocConPhaiThu,
      'success_amount_debit' => 0,
      'is_settlement'        => 1,
      'status'               => CollectDebtEnum::SCHEDULE_STT_MOI,
      'created_by'           => Helper::getCronJobUser(),
      'time_created'         => time(),
      'other_data'           => json_encode([
        [
          'type' => 'OTHER',
          'note' => 'Tạo lịch từ chỉ dẫn',
          'time_modified' => time(),
          'data' => [
            'request_created_channel' => ''
          ]
        ]
      ])
    ];

    $plan = CollectDebtSchedule::forceCreate($paramPlans);
    $plan->master_id = $plan->id;
    $plan->save();

    return $plan;
  }
} // End class